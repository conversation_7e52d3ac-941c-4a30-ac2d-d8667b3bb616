{"cSpell.words": ["accessid", "ALOG", "ALRM", "antlr", "applyto", "asint", "astype", "basekv", "bituint", "BITWISEOR", "bufferio", "byname", "columnbyname", "DAYOFYEAR", "decompressor", "deviceid", "DFDT", "DGRAM", "DIGI", "dtype", "elif", "ENDOFSTREAM", "fracsecpart", "freqid", "<PERSON><PERSON><PERSON>", "fsparts", "Gbtc", "GPLAINS", "guidliteral", "guidvalue", "idlist", "idset", "iinfo", "INLIST", "inlistexpression", "IPHA", "IPHM", "IPPROTO", "isconnected", "ISDATE", "ISGUID", "ISINTEGER", "ISNOTNULL", "issubscribed", "IVINDEX", "keyid", "lastoosreport", "MAXOF", "MINOF", "msdata", "nodeid", "NOFLAGS", "nopep", "NTHINDEXOF", "orderbyterm", "<PERSON><PERSON><PERSON>", "phasorrecord", "phasors", "phasortype", "pointid", "POINTIDXOR", "prevnextpointid", "primarytableidfields", "pyapi", "RDWR", "readmutex", "RECEIVEDASBAD", "<PERSON>", "runtimeid", "r<PERSON><PERSON>", "Shel", "signalid", "signalidcache", "signalidcolumn", "signalid<PERSON>", "signalidlist", "signalidset", "sourcery", "statid", "stringbyname", "STTP", "sttpapi", "tableidfields", "TRUEVAUE", "TSSC", "tsscdecoder", "tzlocal", "tzoffset", "tzparts", "udpdatachannel", "ud<PERSON>ter<PERSON>", "udpport", "UMAXINT", "unsynchronized", "uselocalclockasrealtime", "valueas", "valueasint", "VPHA", "VPHM", "wlock", "xsddatatype", "xsdformat", "xsdprefix", "xsdtypename", "yday", "Δfreq", "Δtime"], "python.analysis.extraPaths": ["./src/sttp"], "python.formatting.autopep8Args": ["--max-line-length=200"], "python.testing.unittestArgs": ["-v", "-s", "./test", "-p", "test_*.py"], "python.testing.pytestEnabled": false, "python.testing.unittestEnabled": true}