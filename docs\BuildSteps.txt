Note: run these steps from root source directory (where setup.cfg is located).

Make sure root-level README.md exists, if not:
    Run CreateReadMeSymLink.cmd to create symbolic link to docs/README.md markdown.
    The .gitignore file ensures that this file will not be checked in since the one in
    the "docs" folder is considered the primary source. If the README.md link in the root
    does not exist or the symbolic link relationship to its parent in the docs folder
    gets broken, delete README.md in the root and re-run CreateReadMeSymLink.cmd again
    before a new build to ensure the README.md will contain the latest updates.

For updates, always increment version number in:
    setup.cfg
    src/sttp/version.py
    docs/conf.py

Any new dependencies should be sync'd and added to:
    setup.cfg ([options] / install_requires)
    requirements.txt

It is always best to delete the following folders before a new build:
    build/
    dist/

Notes: In order for next steps to work, make sure to:
  * run steps from Windows admin "cmd" prompt
  * run "pip install build"
  * run "pip install twine"

Build Distribution: ./
    python -m build

Validate Distribution:
    twine check dist/*

Upload to PiPy:
    twine upload dist/*

    >> You will need credentials for this step,
    >> Copy password to clipboard and paste when prompted via "Edit > Paste" menu / or right-click
    >> This may be a token when using two factor authentication (check account)

Update Sphinx Documentation:
    ./BuildDocs.cmd
    Push updates to GitHub