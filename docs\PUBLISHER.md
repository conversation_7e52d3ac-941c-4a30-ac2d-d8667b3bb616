# STTP Publisher Documentation

The STTP Python API now includes comprehensive publisher functionality, allowing you to create STTP data publishers that can serve measurement data to multiple subscribers.

## Overview

The publisher implementation provides both low-level and high-level interfaces for publishing STTP data:

- **Publisher**: High-level, easy-to-use interface similar to the existing Subscriber class
- **DataPublisher**: Low-level transport layer implementation with full protocol control
- **PublisherConnector**: Connection management for handling multiple subscriber connections

## Quick Start

### Basic Publisher Example

```python
import numpy as np
from uuid import uuid4
from sttp import Publisher, Measurement
from sttp.metadata.record.measurement import MeasurementRecord
from sttp.ticks import Ticks

# Create publisher on port 7175
publisher = Publisher(port=np.uint16(7175))

# Set up logging callbacks
publisher.set_statusmessage_logger(lambda msg: print(f"STATUS: {msg}"))
publisher.set_errormessage_logger(lambda msg: print(f"ERROR: {msg}"))

# Create measurement metadata
signal_id = uuid4()
metadata = MeasurementRecord(
    signalid=signal_id,
    id=np.uint64(1),
    source="TESTPUB",
    signaltypename="FREQ",
    pointtag="TESTPUB.DEV1.FREQ",
    description="Frequency Measurement"
)

# Add metadata to publisher
publisher.add_measurement_metadata(metadata)

# Start the publisher
error = publisher.start()
if error:
    print(f"Failed to start publisher: {error}")
    exit(1)

# Create and publish measurements
measurement = publisher.create_measurement(signal_id, 60.0)
publisher.publish_measurement(measurement)

# Clean up
publisher.dispose()
```

## Publisher Class

The `Publisher` class provides a simplified interface for common publishing scenarios.

### Constructor

```python
Publisher(port: np.uint16 = 7175)
```

- `port`: Port number to listen on for subscriber connections (default: 7175)

### Key Methods

#### Connection Management

- `start() -> Optional[Exception]`: Starts the publisher and begins listening for connections
- `stop() -> None`: Stops the publisher and disconnects all subscribers
- `dispose() -> None`: Releases all resources

#### Metadata Management

- `add_measurement_metadata(record: MeasurementRecord) -> None`: Adds measurement metadata

#### Publishing

- `publish_measurement(measurement: Measurement) -> None`: Publishes a single measurement
- `publish_measurements(measurements: List[Measurement]) -> None`: Publishes multiple measurements
- `create_measurement(signalid: UUID, value: float, timestamp: Optional[np.uint64] = None) -> Measurement`: Helper to create measurements

#### Event Callbacks

- `set_statusmessage_logger(callback: Callable[[str], None]) -> None`: Sets status message callback
- `set_errormessage_logger(callback: Callable[[str], None]) -> None`: Sets error message callback
- `set_clientconnected_logger(callback: Callable[[UUID, str], None]) -> None`: Sets client connection callback
- `set_clientdisconnected_logger(callback: Callable[[UUID, str], None]) -> None`: Sets client disconnection callback

### Properties

- `listening: bool`: Whether the publisher is currently listening
- `subscriber_count: int`: Number of connected subscribers
- `port: np.uint16`: Port number the publisher is listening on

## DataPublisher Class

The `DataPublisher` class provides low-level access to the STTP publisher protocol implementation.

### Key Features

- **Connection Management**: Handles multiple subscriber connections simultaneously
- **Protocol Implementation**: Full STTP server-side protocol support
- **Compression Support**: TSSC (Time-Series Special Compression) and metadata compression
- **UDP Data Channels**: Support for high-performance UDP data transmission
- **Signal Index Caching**: Efficient mapping of signal IDs to runtime indices
- **Subscription Filtering**: Support for subscriber filter expressions

### Constructor

```python
DataPublisher(port: np.uint16 = 7175, max_connections: int = 100)
```

### Key Methods

- `start() -> Optional[Exception]`: Starts the publisher
- `stop() -> None`: Stops the publisher
- `add_measurement_metadata(record: MeasurementRecord) -> None`: Adds metadata
- `publish_measurements(measurements: List[Measurement]) -> None`: Publishes measurements

## PublisherConnector Class

The `PublisherConnector` class manages incoming subscriber connections.

### Features

- **Connection Limiting**: Configurable maximum connection count
- **Thread Management**: Handles each connection in a separate thread
- **Socket Management**: TCP socket handling with timeouts
- **Event Callbacks**: Connection and disconnection notifications

## Measurement Metadata

Publishers require measurement metadata to be defined before publishing data. Use the `MeasurementRecord` class:

```python
from sttp.metadata.record.measurement import MeasurementRecord

record = MeasurementRecord(
    signalid=uuid4(),           # Unique signal identifier
    id=np.uint64(1),           # Numeric ID
    source="PUBLISHER",         # Source identifier
    signaltypename="FREQ",      # Signal type (FREQ, VPHM, etc.)
    pointtag="PUB.DEV1.FREQ",  # Point tag
    description="Frequency",    # Human-readable description
    deviceacronym="DEV1",       # Device identifier
    signalreference="PUB-DEV1:FREQ"  # Signal reference
)
```

## TSSC Compression

The publisher supports Time-Series Special Compression (TSSC) for efficient data transmission:

- **Automatic Encoding**: Measurements are automatically compressed using TSSC when subscribers request it
- **Delta Compression**: Timestamps and values use delta compression
- **XOR Encoding**: Floating-point values use XOR encoding for better compression
- **Runtime ID Mapping**: 128-bit UUIDs are mapped to 32-bit runtime IDs

## Protocol Support

The publisher implements the full STTP server-side protocol:

### Supported Commands

- **DEFINEOPERATIONALMODES**: Configures compression and encoding settings
- **METADATAREFRESH**: Sends measurement metadata to subscribers
- **SUBSCRIBE**: Handles subscription requests with filter expressions
- **UNSUBSCRIBE**: Handles unsubscription requests

### Data Transmission

- **Compact Format**: Uncompressed measurement transmission
- **TSSC Format**: Compressed measurement transmission
- **UDP Channels**: High-performance data channels
- **TCP Fallback**: Reliable data transmission when UDP is not available

## Examples

### Complete Publisher Application

See `examples/simplepublish/main.py` for a complete example that:

- Creates synthetic measurement data
- Publishes at 10 Hz
- Handles multiple subscribers
- Provides real-time status updates

### Integration with Subscribers

The publisher is compatible with:

- STTP Python API subscribers
- Grid Solutions Framework (GSF) tools
- openHistorian STTP Gateway
- Any STTP-compliant subscriber implementation

## Testing

Run the publisher tests:

```bash
python -m pytest test/test_publisher.py -v
```

The test suite includes:

- Publisher creation and lifecycle tests
- Metadata management tests
- Measurement publishing tests
- Integration tests with subscribers
- Error handling tests

## Performance Considerations

### Optimization Tips

1. **Batch Publishing**: Use `publish_measurements()` with multiple measurements rather than individual calls
2. **TSSC Compression**: Enable compression for high-frequency data
3. **UDP Channels**: Use UDP for high-throughput scenarios
4. **Connection Limits**: Set appropriate maximum connection limits
5. **Threading**: The publisher handles multiple subscribers concurrently

### Scalability

- **Multiple Subscribers**: Supports hundreds of concurrent subscribers
- **High Frequency**: Can publish at rates up to several kHz
- **Large Datasets**: Efficient handling of thousands of measurements
- **Memory Management**: Automatic cleanup of disconnected subscribers

## Error Handling

The publisher provides comprehensive error handling:

- **Connection Errors**: Automatic cleanup of failed connections
- **Protocol Errors**: Graceful handling of malformed commands
- **Resource Limits**: Protection against resource exhaustion
- **Callback Exceptions**: Isolated error handling for user callbacks

## Thread Safety

The publisher implementation is thread-safe:

- **Concurrent Publishing**: Multiple threads can publish measurements simultaneously
- **Connection Management**: Thread-safe subscriber connection handling
- **Metadata Updates**: Safe metadata updates during operation
- **Resource Cleanup**: Proper synchronization during shutdown
