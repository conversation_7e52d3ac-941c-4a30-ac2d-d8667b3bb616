## Security

The Grid Protection Alliance (GPA) takes the security of our software products and services seriously. This includes all source code repositories managed through our GitHub organizational sites, specifically: [Grid Protection Alliance](https://github.com/GridProtectionAlliance), [Gemstone Libraries](https://github.com/gemstone), and [Streaming Telemetry Transport Protocol](https://github.com/sttp).

GPA has adopted the following "[definition of security vulnerability](https://gridprotectionalliance.org/VulnerabilityDefinition.asp)" when considering what constitutes a security vulnerability. If you believe you have found a security vulnerability meeting this definition in any GPA-owned repository, please report it to us as described below.

## Reporting Security Issues

> **_Please report security vulnerabilities as described below, not through public GitHub issues._**

To report a security vulnerability in any GPA-owned open source repository, please send an email to [<EMAIL>](mailto:<EMAIL>?subject=Security%20Vulnerability%20Report).

When submitting the security vulnerability report, please include the requested information listed below to help us better understand the nature and scope of the possible issue:

  * Type of issue (e.g. buffer overflow, SQL injection, cross-site scripting, etc.)
  * Full paths of source file(s) related to the manifestation of the issue
  * The location of the affected source code (tag/branch/commit or direct URL)
  * Any special configuration required to reproduce the issue
  * Step-by-step instructions to reproduce the issue
  * Proof-of-concept or exploit code (if possible)
  * Impact of the issue, including how an attacker might exploit the issue

You should receive a response within 24 hours. If for some reason you do not, please follow up via email to ensure we received your original message.

## Preferred Languages

We prefer all communications to be in English.

## Policy

The Grid Protection Alliance follows the principle of [Coordinated Vulnerability Disclosure](https://gridprotectionalliance.org/cvd.asp).
