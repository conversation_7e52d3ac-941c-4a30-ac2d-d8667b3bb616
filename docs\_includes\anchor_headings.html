{% capture headingsWorkspace %}
  {% comment %}
    Copyright (c) 2018 Vladimir "allejo" Jimenez

    Permission is hereby granted, free of charge, to any person
    obtaining a copy of this software and associated documentation
    files (the "Software"), to deal in the Software without
    restriction, including without limitation the rights to use,
    copy, modify, merge, publish, distribute, sublicense, and/or sell
    copies of the Software, and to permit persons to whom the
    Software is furnished to do so, subject to the following
    conditions:

    The above copyright notice and this permission notice shall be
    included in all copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
    EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
    OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
    NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
    HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
    WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
    OTHER DEALINGS IN THE SOFTWARE.
  {% endcomment %}
  {% comment %}
    Version 1.0.6
      https://github.com/allejo/jekyll-anchor-headings

    "Be the pull request you wish to see in the world." ~Ben Balter

    Usage:
      {% include anchor_headings.html html=content anchorBody="#" %}

    Parameters:
      * html          (string) - the HTML of compiled markdown generated by kramdown in Jekyll

    Optional Parameters:
      * beforeHeading (bool)   : false  - Set to true if the anchor should be placed _before_ the heading's content
      * anchorAttrs   (string) :  ''    - Any custom HTML attributes that will be added to the `<a>` tag; you may NOT use `href`, `class` or `title`;
                                          the `%heading%` placeholder is available
      * anchorBody    (string) :  ''    - The content that will be placed inside the anchor; the `%heading%` placeholder is available
      * anchorClass   (string) :  ''    - The class(es) that will be used for each anchor. Separate multiple classes with a space
      * anchorTitle   (string) :  ''    - The `title` attribute that will be used for anchors
      * h_min         (int)    :  1     - The minimum header level to build an anchor for; any header lower than this value will be ignored
      * h_max         (int)    :  6     - The maximum header level to build an anchor for; any header greater than this value will be ignored
      * bodyPrefix    (string) :  ''    - Anything that should be inserted inside of the heading tag _before_ its anchor and content
      * bodySuffix    (string) :  ''    - Anything that should be inserted inside of the heading tag _after_ its anchor and content

    Output:
      The original HTML with the addition of anchors inside of all of the h1-h6 headings.
  {% endcomment %}

  {% assign minHeader = include.h_min | default: 1 %}
  {% assign maxHeader = include.h_max | default: 6 %}
  {% assign beforeHeading = include.beforeHeading %}
  {% assign nodes = include.html | split: '<h' %}

  {% capture edited_headings %}{% endcapture %}

  {% for _node in nodes %}
    {% capture node %}{{ _node | strip }}{% endcapture %}

    {% if node == "" %}
      {% continue %}
    {% endif %}

    {% assign nextChar = node | replace: '"', '' | strip | slice: 0, 1 %}
    {% assign headerLevel = nextChar | times: 1 %}

    <!-- If the level is cast to 0, it means it's not a h1-h6 tag, so let's see if we need to fix it -->
    {% if headerLevel == 0 %}
      <!-- Split up the node based on closing angle brackets and get the first one. -->
      {% assign firstChunk = node | split: '>' | first %}

      <!-- If the first chunk does NOT contain a '<', that means we've broken another HTML tag that starts with 'h' -->
      {% unless firstChunk contains '<' %}
        {% capture node %}<h{{ node }}{% endcapture %}
      {% endunless %}

      {% capture edited_headings %}{{ edited_headings }}{{ node }}{% endcapture %}
      {% continue %}
    {% endif %}

    {% assign _workspace = node | split: '</h' %}
    {% assign _idWorkspace = _workspace[0] | split: 'id="' %}
    {% assign _idWorkspace = _idWorkspace[1] | split: '"' %}
    {% assign html_id = _idWorkspace[0] %}

    {% capture _hAttrToStrip %}{{ _workspace[0] | split: '>' | first }}>{% endcapture %}
    {% assign header = _workspace[0] | replace: _hAttrToStrip, '' %}

    <!-- Build the anchor to inject for our heading -->
    {% capture anchor %}{% endcapture %}

    {% if html_id and headerLevel >= minHeader and headerLevel <= maxHeader %}
      {% capture anchor %}href="#{{ html_id }}"{% endcapture %}

      {% if include.anchorClass %}
        {% capture anchor %}{{ anchor }} class="{{ include.anchorClass }}"{% endcapture %}
      {% endif %}

      {% if include.anchorTitle %}
        {% capture anchor %}{{ anchor }} title="{{ include.anchorTitle | replace: '%heading%', header }}"{% endcapture %}
      {% endif %}

      {% if include.anchorAttrs %}
        {% capture anchor %}{{ anchor }} {{ include.anchorAttrs | replace: '%heading%', header }}{% endcapture %}
      {% endif %}

      {% capture anchor %}<a {{ anchor }}>{{ include.anchorBody | replace: '%heading%', header | default: '' }}</a>{% endcapture %}

      <!-- In order to prevent adding extra space after a heading, we'll let the 'anchor' value contain it -->
      {% if beforeHeading %}
        {% capture anchor %}{{ anchor }} {% endcapture %}
      {% else %}
        {% capture anchor %} {{ anchor }}{% endcapture %}
      {% endif %}
    {% endif %}

    {% capture new_heading %}
      <h{{ _hAttrToStrip }}
        {{ include.bodyPrefix }}
        {% if beforeHeading %}
          {{ anchor }}{{ header }}
        {% else %}
          {{ header }}{{ anchor }}
        {% endif %}
        {{ include.bodySuffix }}
      </h{{ _workspace | last }}
    {% endcapture %}
    {% capture edited_headings %}{{ edited_headings }}{{ new_heading }}{% endcapture %}
  {% endfor %}
{% endcapture %}{% assign headingsWorkspace = '' %}{{ edited_headings | strip }}