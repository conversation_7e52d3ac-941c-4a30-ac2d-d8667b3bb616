

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>gsf.binarystream &mdash; sttp/pyapi 0.6.4 documentation</title>
      <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=92fd9be5" />
      <link rel="stylesheet" type="text/css" href="../../_static/css/theme.css?v=8209df7b" />

  
    <link rel="shortcut icon" href="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/favicon.ico"/>
      <script src="../../_static/jquery.js?v=5d32c60e"></script>
      <script src="../../_static/_sphinx_javascript_frameworks_compat.js?v=bbec6902"></script>
      <script src="../../_static/documentation_options.js?v=b1f64a84"></script>
      <script src="../../_static/doctools.js?v=92e14aea"></script>
      <script src="../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="../../index.html" class="icon icon-home">
            sttp/pyapi
              <img src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/sttp.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <!-- Local TOC -->
              <div class="local-toc"></div>
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../index.html">sttp/pyapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../index.html">Module code</a></li>
          <li class="breadcrumb-item"><a href="../gsf.html">gsf</a></li>
      <li class="breadcrumb-item active">gsf.binarystream</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for gsf.binarystream</h1><div class="highlight"><pre>
<span></span><span class="c1">#******************************************************************************************************</span>
<span class="c1">#  binarystream.py - Gbtc</span>
<span class="c1">#</span>
<span class="c1">#  Copyright © 2021, Grid Protection Alliance.  All Rights Reserved.</span>
<span class="c1">#</span>
<span class="c1">#  Licensed to the Grid Protection Alliance (GPA) under one or more contributor license agreements. See</span>
<span class="c1">#  the NOTICE file distributed with this work for additional information regarding copyright ownership.</span>
<span class="c1">#  The GPA licenses this file to you under the MIT License (MIT), the &quot;License&quot;; you may not use this</span>
<span class="c1">#  file except in compliance with the License. You may obtain a copy of the License at:</span>
<span class="c1">#</span>
<span class="c1">#      http://opensource.org/licenses/MIT</span>
<span class="c1">#</span>
<span class="c1">#  Unless agreed to in writing, the subject software distributed under the License is distributed on an</span>
<span class="c1">#  &quot;AS-IS&quot; BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. Refer to the</span>
<span class="c1">#  License for the specific language governing permissions and limitations.</span>
<span class="c1">#</span>
<span class="c1">#  Code Modification History:</span>
<span class="c1">#  ----------------------------------------------------------------------------------------------------</span>
<span class="c1">#  01/31/2021 - J. Ritchie Carroll</span>
<span class="c1">#       Generated original version of source code.</span>
<span class="c1">#</span>
<span class="c1">#******************************************************************************************************</span>

<span class="kn">from</span> <span class="nn">.streamencoder</span> <span class="kn">import</span> <span class="n">StreamEncoder</span>
<span class="kn">from</span> <span class="nn">.encoding7bit</span> <span class="kn">import</span> <span class="n">Encoding7Bit</span>
<span class="kn">from</span> <span class="nn">.</span> <span class="kn">import</span> <span class="n">ByteSize</span><span class="p">,</span> <span class="n">Validate</span>
<span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">Optional</span>
<span class="kn">from</span> <span class="nn">uuid</span> <span class="kn">import</span> <span class="n">UUID</span>
<span class="kn">import</span> <span class="nn">sys</span>
<span class="kn">import</span> <span class="nn">numpy</span> <span class="k">as</span> <span class="nn">np</span>


<div class="viewcode-block" id="BinaryStream">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream">[docs]</a>
<span class="k">class</span> <span class="nc">BinaryStream</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Establishes buffered I/O around a base stream, e.g., a socket.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1"># Source C# references:</span>
    <span class="c1">#     RemoteBinaryStream</span>
    <span class="c1">#     BinaryStreamBase</span>

    <span class="n">IO_BUFFERSIZE</span> <span class="o">=</span> <span class="mi">1420</span>
    <span class="n">VALUE_BUFFERSIZE</span> <span class="o">=</span> <span class="mi">16</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">stream</span><span class="p">:</span> <span class="n">StreamEncoder</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_stream</span> <span class="o">=</span> <span class="n">stream</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_default_byteorder</span> <span class="o">=</span> <span class="n">stream</span><span class="o">.</span><span class="n">default_byteorder</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_default_is_native</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_default_byteorder</span> <span class="o">==</span> <span class="n">sys</span><span class="o">.</span><span class="n">byteorder</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_buffer</span> <span class="o">=</span> <span class="nb">bytearray</span><span class="p">(</span><span class="n">BinaryStream</span><span class="o">.</span><span class="n">VALUE_BUFFERSIZE</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_receive_buffer</span> <span class="o">=</span> <span class="nb">bytearray</span><span class="p">(</span><span class="n">BinaryStream</span><span class="o">.</span><span class="n">IO_BUFFERSIZE</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_send_buffer</span> <span class="o">=</span> <span class="nb">bytearray</span><span class="p">(</span><span class="n">BinaryStream</span><span class="o">.</span><span class="n">IO_BUFFERSIZE</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_send_length</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_receive_length</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_receive_position</span> <span class="o">=</span> <span class="mi">0</span>

    <span class="k">def</span> <span class="nf">_send_buffer_freespace</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">BinaryStream</span><span class="o">.</span><span class="n">IO_BUFFERSIZE</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">_send_length</span>

    <span class="k">def</span> <span class="nf">_receive_buffer_available</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_receive_length</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">_receive_position</span>

<div class="viewcode-block" id="BinaryStream.flush">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.flush">[docs]</a>
    <span class="k">def</span> <span class="nf">flush</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_send_length</span> <span class="o">&lt;=</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">return</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_stream</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="nb">bytes</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_send_buffer</span><span class="p">),</span> <span class="mi">0</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">_send_length</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_send_length</span> <span class="o">=</span> <span class="mi">0</span></div>


<div class="viewcode-block" id="BinaryStream.read">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.read">[docs]</a>
    <span class="k">def</span> <span class="nf">read</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">buffer</span><span class="p">:</span> <span class="nb">bytearray</span><span class="p">,</span> <span class="n">offset</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">count</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>  <span class="c1"># sourcery skip: low-code-quality</span>
        <span class="k">if</span> <span class="n">count</span> <span class="o">&lt;=</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">return</span> <span class="mi">0</span>

        <span class="n">receive_buffer_length</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_receive_buffer_available</span><span class="p">()</span>

        <span class="c1"># Check if there is enough in the receive buffer to handle request</span>
        <span class="k">if</span> <span class="n">count</span> <span class="o">&lt;=</span> <span class="n">receive_buffer_length</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">count</span><span class="p">):</span>
                <span class="n">buffer</span><span class="p">[</span><span class="n">offset</span> <span class="o">+</span> <span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_receive_buffer</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_receive_position</span> <span class="o">+</span> <span class="n">i</span><span class="p">]</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">_receive_position</span> <span class="o">+=</span> <span class="n">count</span>
            <span class="k">return</span> <span class="n">count</span>

        <span class="n">original_count</span> <span class="o">=</span> <span class="n">count</span>

        <span class="c1"># Empty existing receive buffer</span>
        <span class="k">if</span> <span class="n">receive_buffer_length</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">receive_buffer_length</span><span class="p">):</span>
                <span class="n">buffer</span><span class="p">[</span><span class="n">offset</span> <span class="o">+</span> <span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_receive_buffer</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_receive_position</span> <span class="o">+</span> <span class="n">i</span><span class="p">]</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">_receive_position</span> <span class="o">=</span> <span class="mi">0</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_receive_length</span> <span class="o">=</span> <span class="mi">0</span>
            <span class="n">offset</span> <span class="o">+=</span> <span class="n">receive_buffer_length</span>
            <span class="n">count</span> <span class="o">-=</span> <span class="n">receive_buffer_length</span>

        <span class="c1"># If more than 100 bytes remain, skip receive buffer</span>
        <span class="c1"># and copy directly to the destination</span>
        <span class="k">if</span> <span class="n">count</span> <span class="o">&gt;</span> <span class="mi">100</span><span class="p">:</span>
            <span class="c1"># Loop since socket reads can return partial results</span>
            <span class="k">while</span> <span class="n">count</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="n">receive_buffer_length</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_stream</span><span class="o">.</span><span class="n">read</span><span class="p">(</span><span class="n">buffer</span><span class="p">,</span> <span class="n">offset</span><span class="p">,</span> <span class="n">count</span><span class="p">)</span>

                <span class="k">if</span> <span class="n">receive_buffer_length</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                    <span class="k">raise</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="s2">&quot;End of stream&quot;</span><span class="p">)</span>

                <span class="n">offset</span> <span class="o">+=</span> <span class="n">receive_buffer_length</span>
                <span class="n">count</span> <span class="o">-=</span> <span class="n">receive_buffer_length</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="c1"># With fewer than 100 bytes requested, fill receive buffer</span>
            <span class="c1"># then copy to destination</span>
            <span class="n">pre_buffer_length</span> <span class="o">=</span> <span class="n">BinaryStream</span><span class="o">.</span><span class="n">IO_BUFFERSIZE</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_receive_length</span> <span class="o">=</span> <span class="mi">0</span>

            <span class="k">while</span> <span class="bp">self</span><span class="o">.</span><span class="n">_receive_length</span> <span class="o">&lt;</span> <span class="n">count</span><span class="p">:</span>
                <span class="n">receive_buffer_length</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_stream</span><span class="o">.</span><span class="n">read</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_receive_buffer</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">_receive_length</span><span class="p">,</span> <span class="n">pre_buffer_length</span><span class="p">)</span>

                <span class="k">if</span> <span class="n">receive_buffer_length</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                    <span class="k">raise</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="s2">&quot;End of stream&quot;</span><span class="p">)</span>

                <span class="bp">self</span><span class="o">.</span><span class="n">_receive_length</span> <span class="o">+=</span> <span class="n">receive_buffer_length</span>
                <span class="n">pre_buffer_length</span> <span class="o">-=</span> <span class="n">receive_buffer_length</span>

            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">count</span><span class="p">):</span>
                <span class="n">buffer</span><span class="p">[</span><span class="n">offset</span> <span class="o">+</span> <span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_receive_buffer</span><span class="p">[</span><span class="n">i</span><span class="p">]</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">_receive_position</span> <span class="o">=</span> <span class="n">count</span>

        <span class="k">return</span> <span class="n">original_count</span></div>


<div class="viewcode-block" id="BinaryStream.write">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.write">[docs]</a>
    <span class="k">def</span> <span class="nf">write</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">buffer</span><span class="p">:</span> <span class="nb">bytes</span><span class="p">,</span> <span class="n">offset</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">count</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_send_buffer_freespace</span><span class="p">()</span> <span class="o">&lt;</span> <span class="n">count</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">flush</span><span class="p">()</span>

        <span class="k">if</span> <span class="n">count</span> <span class="o">&gt;</span> <span class="mi">100</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">flush</span><span class="p">()</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_stream</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">buffer</span><span class="p">,</span> <span class="n">offset</span><span class="p">,</span> <span class="n">count</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">count</span><span class="p">):</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_send_buffer</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_send_length</span> <span class="o">+</span> <span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="n">buffer</span><span class="p">[</span><span class="n">offset</span> <span class="o">+</span> <span class="n">i</span><span class="p">]</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">_send_length</span> <span class="o">+=</span> <span class="n">count</span>

        <span class="k">return</span> <span class="n">count</span></div>


<div class="viewcode-block" id="BinaryStream.read_all">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.read_all">[docs]</a>
    <span class="k">def</span> <span class="nf">read_all</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">buffer</span><span class="p">:</span> <span class="nb">bytearray</span><span class="p">,</span> <span class="n">position</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">length</span><span class="p">:</span> <span class="nb">int</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Reads all of the provided bytes. Will not return prematurely, continues</span>
<span class="sd">        to execute `Read` operation until the entire `length` has been read.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">Validate</span><span class="o">.</span><span class="n">parameters</span><span class="p">(</span><span class="n">buffer</span><span class="p">,</span> <span class="n">position</span><span class="p">,</span> <span class="n">length</span><span class="p">)</span>

        <span class="k">while</span> <span class="n">length</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="n">bytes_read</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">read</span><span class="p">(</span><span class="n">buffer</span><span class="p">,</span> <span class="n">position</span><span class="p">,</span> <span class="n">length</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">bytes_read</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="s2">&quot;End of stream&quot;</span><span class="p">)</span>

            <span class="n">length</span> <span class="o">-=</span> <span class="n">bytes_read</span>
            <span class="n">position</span> <span class="o">+=</span> <span class="n">bytes_read</span></div>


<div class="viewcode-block" id="BinaryStream.read_bytes">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.read_bytes">[docs]</a>
    <span class="k">def</span> <span class="nf">read_bytes</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">count</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bytes</span><span class="p">:</span>
        <span class="n">buffer</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_buffer</span> <span class="k">if</span> <span class="n">count</span> <span class="o">&lt;=</span> <span class="n">BinaryStream</span><span class="o">.</span><span class="n">VALUE_BUFFERSIZE</span> <span class="k">else</span> <span class="nb">bytearray</span><span class="p">(</span><span class="n">count</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">read_all</span><span class="p">(</span><span class="n">buffer</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="n">count</span><span class="p">)</span>
        <span class="k">return</span> <span class="nb">bytes</span><span class="p">(</span><span class="n">buffer</span><span class="p">[:</span><span class="n">count</span><span class="p">])</span></div>


<div class="viewcode-block" id="BinaryStream.read_buffer">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.read_buffer">[docs]</a>
    <span class="k">def</span> <span class="nf">read_buffer</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bytes</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">read_bytes</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">read7bit_uint32</span><span class="p">())</span></div>


<div class="viewcode-block" id="BinaryStream.read_string">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.read_string">[docs]</a>
    <span class="k">def</span> <span class="nf">read_string</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">read_buffer</span><span class="p">()</span><span class="o">.</span><span class="n">decode</span><span class="p">(</span><span class="s2">&quot;utf-8&quot;</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.read_guid">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.read_guid">[docs]</a>
    <span class="k">def</span> <span class="nf">read_guid</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">UUID</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">UUID</span><span class="p">(</span><span class="n">bytes_le</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">read_bytes</span><span class="p">(</span><span class="mi">16</span><span class="p">))</span></div>


<div class="viewcode-block" id="BinaryStream.read7bit_int32">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.read7bit_int32">[docs]</a>
    <span class="k">def</span> <span class="nf">read7bit_int32</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Encoding7Bit</span><span class="o">.</span><span class="n">read_int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">read_byte</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.read7bit_uint32">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.read7bit_uint32">[docs]</a>
    <span class="k">def</span> <span class="nf">read7bit_uint32</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Encoding7Bit</span><span class="o">.</span><span class="n">read_uint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">read_byte</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.read7bit_int64">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.read7bit_int64">[docs]</a>
    <span class="k">def</span> <span class="nf">read7bit_int64</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">int64</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Encoding7Bit</span><span class="o">.</span><span class="n">read_int64</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">read_byte</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.read7bit_uint64">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.read7bit_uint64">[docs]</a>
    <span class="k">def</span> <span class="nf">read7bit_uint64</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">:</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_receive_position</span> <span class="o">&lt;=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_receive_length</span> <span class="o">-</span> <span class="mi">9</span><span class="p">:</span>
            <span class="n">stream</span> <span class="o">=</span> <span class="n">StreamEncoder</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_send_buffer_read</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">_send_buffer_write</span><span class="p">)</span>
            <span class="k">return</span> <span class="n">stream</span><span class="o">.</span><span class="n">read7bit_uint64</span><span class="p">()</span>

        <span class="k">return</span> <span class="n">Encoding7Bit</span><span class="o">.</span><span class="n">read_uint64</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">read_byte</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.read_byte">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.read_byte">[docs]</a>
    <span class="k">def</span> <span class="nf">read_byte</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">uint8</span><span class="p">:</span>
        <span class="n">size</span> <span class="o">=</span> <span class="n">ByteSize</span><span class="o">.</span><span class="n">UINT8</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_receive_position</span> <span class="o">&lt;</span> <span class="bp">self</span><span class="o">.</span><span class="n">_receive_length</span><span class="p">:</span>
            <span class="n">value</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_receive_buffer</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_receive_position</span><span class="p">]</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_receive_position</span> <span class="o">+=</span> <span class="n">size</span>
            <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">uint8</span><span class="p">(</span><span class="n">value</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">read_all</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_buffer</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="n">size</span><span class="p">)</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_buffer</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span></div>


<div class="viewcode-block" id="BinaryStream.write_buffer">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.write_buffer">[docs]</a>
    <span class="k">def</span> <span class="nf">write_buffer</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="nb">bytes</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="n">count</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">value</span><span class="p">)</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">write7bit_uint32</span><span class="p">(</span><span class="n">count</span><span class="p">)</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="n">count</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.write_string">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.write_string">[docs]</a>
    <span class="k">def</span> <span class="nf">write_string</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">write_buffer</span><span class="p">(</span><span class="n">value</span><span class="o">.</span><span class="n">encode</span><span class="p">(</span><span class="s2">&quot;utf-8&quot;</span><span class="p">))</span></div>


<div class="viewcode-block" id="BinaryStream.write_guid">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.write_guid">[docs]</a>
    <span class="k">def</span> <span class="nf">write_guid</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">UUID</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">value</span><span class="o">.</span><span class="n">bytes_le</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">16</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.write7bit_int32">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.write7bit_int32">[docs]</a>
    <span class="k">def</span> <span class="nf">write7bit_int32</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Encoding7Bit</span><span class="o">.</span><span class="n">write_int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">write_byte</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.write7bit_uint32">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.write7bit_uint32">[docs]</a>
    <span class="k">def</span> <span class="nf">write7bit_uint32</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Encoding7Bit</span><span class="o">.</span><span class="n">write_uint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">write_byte</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.write7bit_int64">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.write7bit_int64">[docs]</a>
    <span class="k">def</span> <span class="nf">write7bit_int64</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">int64</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Encoding7Bit</span><span class="o">.</span><span class="n">write_int64</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">write_byte</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.write7bit_uint64">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.write7bit_uint64">[docs]</a>
    <span class="k">def</span> <span class="nf">write7bit_uint64</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_send_length</span> <span class="o">&lt;=</span> <span class="n">BinaryStream</span><span class="o">.</span><span class="n">IO_BUFFERSIZE</span> <span class="o">-</span> <span class="mi">9</span><span class="p">:</span>
            <span class="n">stream</span> <span class="o">=</span> <span class="n">StreamEncoder</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_send_buffer_read</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">_send_buffer_write</span><span class="p">)</span>
            <span class="k">return</span> <span class="n">stream</span><span class="o">.</span><span class="n">write7bit_uint64</span><span class="p">(</span><span class="n">value</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">Encoding7Bit</span><span class="o">.</span><span class="n">write_uint64</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">write_byte</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.write_byte">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.write_byte">[docs]</a>
    <span class="k">def</span> <span class="nf">write_byte</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">uint8</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="n">size</span> <span class="o">=</span> <span class="n">ByteSize</span><span class="o">.</span><span class="n">UINT8</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_send_length</span> <span class="o">&lt;</span> <span class="n">BinaryStream</span><span class="o">.</span><span class="n">IO_BUFFERSIZE</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_send_buffer</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_send_length</span><span class="p">]</span> <span class="o">=</span> <span class="n">value</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_send_length</span> <span class="o">+=</span> <span class="n">size</span>
            <span class="k">return</span> <span class="n">size</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_buffer</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">=</span> <span class="n">value</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_buffer</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="n">size</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.read_boolean">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.read_boolean">[docs]</a>
    <span class="k">def</span> <span class="nf">read_boolean</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">read_byte</span><span class="p">()</span> <span class="o">!=</span> <span class="mi">0</span></div>


<div class="viewcode-block" id="BinaryStream.write_boolean">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.write_boolean">[docs]</a>
    <span class="k">def</span> <span class="nf">write_boolean</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="nb">bool</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">if</span> <span class="n">value</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">write_byte</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">write_byte</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>

        <span class="k">return</span> <span class="mi">1</span></div>


    <span class="k">def</span> <span class="nf">_read_int</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">size</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">dtype</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">dtype</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="p">(</span><span class="n">byteorder</span> <span class="ow">is</span> <span class="kc">None</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">_default_is_native</span><span class="p">)</span> <span class="ow">and</span> <span class="n">byteorder</span> <span class="o">!=</span> <span class="n">sys</span><span class="o">.</span><span class="n">byteorder</span><span class="p">:</span>
            <span class="n">dtype</span> <span class="o">=</span> <span class="n">dtype</span><span class="o">.</span><span class="n">newbyteorder</span><span class="p">()</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_receive_position</span> <span class="o">&lt;=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_receive_length</span> <span class="o">-</span> <span class="n">size</span><span class="p">:</span>
            <span class="n">value</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">frombuffer</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_receive_buffer</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_receive_position</span><span class="p">:</span><span class="bp">self</span><span class="o">.</span><span class="n">_receive_position</span> <span class="o">+</span> <span class="n">size</span><span class="p">],</span> <span class="n">dtype</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_receive_position</span> <span class="o">+=</span> <span class="n">size</span>
            <span class="k">return</span> <span class="n">value</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">ReadAll</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_buffer</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="n">size</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">frombuffer</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_buffer</span><span class="p">[:</span><span class="n">size</span><span class="p">],</span> <span class="n">dtype</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>

    <span class="k">def</span> <span class="nf">_write_int</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">size</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">signed</span><span class="p">:</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="c1"># sourcery skip: class-extract-method, remove-unnecessary-cast</span>
        <span class="n">buffer</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">value</span><span class="p">)</span><span class="o">.</span><span class="n">to_bytes</span><span class="p">(</span><span class="n">size</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">_default_byteorder</span> <span class="k">if</span> <span class="n">byteorder</span> <span class="ow">is</span> <span class="kc">None</span> <span class="k">else</span> <span class="n">byteorder</span><span class="p">,</span> <span class="n">signed</span><span class="o">=</span><span class="n">signed</span><span class="p">)</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_send_length</span> <span class="o">&lt;=</span> <span class="n">BinaryStream</span><span class="o">.</span><span class="n">IO_BUFFERSIZE</span> <span class="o">-</span> <span class="n">size</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">size</span><span class="p">):</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_send_buffer</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_send_length</span> <span class="o">+</span> <span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="n">buffer</span><span class="p">[</span><span class="n">i</span><span class="p">]</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">_send_length</span> <span class="o">+=</span> <span class="n">size</span>
            <span class="k">return</span> <span class="n">size</span>

        <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">size</span><span class="p">):</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_buffer</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="n">buffer</span><span class="p">[</span><span class="n">i</span><span class="p">]</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_buffer</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="n">size</span><span class="p">)</span>

<div class="viewcode-block" id="BinaryStream.read_int16">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.read_int16">[docs]</a>
    <span class="k">def</span> <span class="nf">read_int16</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">int16</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">INT16</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">dtype</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">int16</span><span class="p">),</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.write_int16">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.write_int16">[docs]</a>
    <span class="k">def</span> <span class="nf">write_int16</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">int16</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_write_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">INT16</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="kc">True</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.read_uint16">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.read_uint16">[docs]</a>
    <span class="k">def</span> <span class="nf">read_uint16</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">uint16</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">UINT16</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">dtype</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">uint16</span><span class="p">),</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.write_uint16">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.write_uint16">[docs]</a>
    <span class="k">def</span> <span class="nf">write_uint16</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">uint16</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_write_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">UINT16</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="kc">False</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.read_int32">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.read_int32">[docs]</a>
    <span class="k">def</span> <span class="nf">read_int32</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">INT32</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">dtype</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">),</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.write_int32">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.write_int32">[docs]</a>
    <span class="k">def</span> <span class="nf">write_int32</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_write_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">INT32</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="kc">True</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.read_uint32">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.read_uint32">[docs]</a>
    <span class="k">def</span> <span class="nf">read_uint32</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">UINT32</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">dtype</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">),</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.write_uint32">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.write_uint32">[docs]</a>
    <span class="k">def</span> <span class="nf">write_uint32</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_write_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">UINT32</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="kc">False</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.read_int64">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.read_int64">[docs]</a>
    <span class="k">def</span> <span class="nf">read_int64</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">int64</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">INT64</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">dtype</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">int64</span><span class="p">),</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.write_int64">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.write_int64">[docs]</a>
    <span class="k">def</span> <span class="nf">write_int64</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">int64</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_write_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">INT64</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="kc">True</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.read_uint64">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.read_uint64">[docs]</a>
    <span class="k">def</span> <span class="nf">read_uint64</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">UINT64</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">dtype</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">),</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="BinaryStream.write_uint64">
<a class="viewcode-back" href="../../gsf.html#gsf.binarystream.BinaryStream.write_uint64">[docs]</a>
    <span class="k">def</span> <span class="nf">write_uint64</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_write_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">UINT64</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="kc">False</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">)</span></div>


    <span class="k">def</span> <span class="nf">_send_buffer_read</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">length</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bytes</span><span class="p">:</span>
        <span class="n">buffer</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_buffer</span> <span class="k">if</span> <span class="n">length</span> <span class="o">&lt;=</span> <span class="n">BinaryStream</span><span class="o">.</span><span class="n">VALUE_BUFFERSIZE</span> <span class="k">else</span> <span class="nb">bytearray</span><span class="p">(</span><span class="n">length</span><span class="p">)</span>

        <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">length</span><span class="p">):</span>
            <span class="n">buffer</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_receive_buffer</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_receive_position</span> <span class="o">+</span> <span class="n">i</span><span class="p">]</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_receive_position</span> <span class="o">+=</span> <span class="n">length</span>
        <span class="k">return</span> <span class="nb">bytes</span><span class="p">(</span><span class="n">buffer</span><span class="p">[:</span><span class="n">length</span><span class="p">])</span>

    <span class="k">def</span> <span class="nf">_send_buffer_write</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">buffer</span><span class="p">:</span> <span class="nb">bytes</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="n">length</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">buffer</span><span class="p">)</span>

        <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">length</span><span class="p">):</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_send_buffer</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_send_length</span> <span class="o">+</span> <span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="n">buffer</span><span class="p">[</span><span class="n">i</span><span class="p">]</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_send_length</span> <span class="o">+=</span> <span class="n">length</span>
        <span class="k">return</span> <span class="n">length</span></div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
  
 
<hr />
<p>
    <a href="https://github.com/sttp/pyapi">GitHub Home</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://pypi.org/project/sttpapi/" target="_blank">PyPi Package: sttpapi</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://sttp.github.io/documentation/" target="_blank">General STTP Documentation</a>
</p>
<br /><br />
Copyright © 2022, <a href="https://gridprotectionalliance.org/" target="_blank">Grid&nbsp;Protection&nbsp;Alliance</a>
<a href="https://github.com/sttp/pyapi">
    <img align="right" style="margin-top: -50px"
        src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/LockPython_64High.png">
</a>


</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>