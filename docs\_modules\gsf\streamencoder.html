

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>gsf.streamencoder &mdash; sttp/pyapi 0.6.4 documentation</title>
      <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=92fd9be5" />
      <link rel="stylesheet" type="text/css" href="../../_static/css/theme.css?v=8209df7b" />

  
    <link rel="shortcut icon" href="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/favicon.ico"/>
      <script src="../../_static/jquery.js?v=5d32c60e"></script>
      <script src="../../_static/_sphinx_javascript_frameworks_compat.js?v=bbec6902"></script>
      <script src="../../_static/documentation_options.js?v=b1f64a84"></script>
      <script src="../../_static/doctools.js?v=92e14aea"></script>
      <script src="../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="../../index.html" class="icon icon-home">
            sttp/pyapi
              <img src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/sttp.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <!-- Local TOC -->
              <div class="local-toc"></div>
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../index.html">sttp/pyapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../index.html">Module code</a></li>
          <li class="breadcrumb-item"><a href="../gsf.html">gsf</a></li>
      <li class="breadcrumb-item active">gsf.streamencoder</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for gsf.streamencoder</h1><div class="highlight"><pre>
<span></span><span class="c1">#******************************************************************************************************</span>
<span class="c1">#  streamencoder.py - Gbtc</span>
<span class="c1">#</span>
<span class="c1">#  Copyright © 2021, Grid Protection Alliance.  All Rights Reserved.</span>
<span class="c1">#</span>
<span class="c1">#  Licensed to the Grid Protection Alliance (GPA) under one or more contributor license agreements. See</span>
<span class="c1">#  the NOTICE file distributed with this work for additional information regarding copyright ownership.</span>
<span class="c1">#  The GPA licenses this file to you under the MIT License (MIT), the &quot;License&quot;; you may not use this</span>
<span class="c1">#  file except in compliance with the License. You may obtain a copy of the License at:</span>
<span class="c1">#</span>
<span class="c1">#      http://opensource.org/licenses/MIT</span>
<span class="c1">#</span>
<span class="c1">#  Unless agreed to in writing, the subject software distributed under the License is distributed on an</span>
<span class="c1">#  &quot;AS-IS&quot; BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. Refer to the</span>
<span class="c1">#  License for the specific language governing permissions and limitations.</span>
<span class="c1">#</span>
<span class="c1">#  Code Modification History:</span>
<span class="c1">#  ----------------------------------------------------------------------------------------------------</span>
<span class="c1">#  01/31/2021 - J. Ritchie Carroll</span>
<span class="c1">#       Generated original version of source code.</span>
<span class="c1">#</span>
<span class="c1">#******************************************************************************************************</span>

<span class="kn">from</span> <span class="nn">.encoding7bit</span> <span class="kn">import</span> <span class="n">Encoding7Bit</span>
<span class="kn">from</span> <span class="nn">.</span> <span class="kn">import</span> <span class="n">ByteSize</span>
<span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">Callable</span><span class="p">,</span> <span class="n">Optional</span>
<span class="kn">import</span> <span class="nn">sys</span>
<span class="kn">import</span> <span class="nn">numpy</span> <span class="k">as</span> <span class="nn">np</span>


<div class="viewcode-block" id="StreamEncoder">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder">[docs]</a>
<span class="k">class</span> <span class="nc">StreamEncoder</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Defines functions for encoding and decoding native types to and from a stream.</span>
<span class="sd">    For this class, a stream is simply an abstract notion based on provided functions</span>
<span class="sd">    that read and write byte buffers as Python `bytes` objects advancing a position</span>
<span class="sd">    in the base stream. The read/write functions simply wrap a base object that can</span>
<span class="sd">    handle input and output as bytes, e.g., a `bytearray` or a `socket`.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="c1"># Source C# reference: GSF.IO.StreamExtensions</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">read</span><span class="p">:</span> <span class="n">Callable</span><span class="p">[[</span><span class="nb">int</span><span class="p">],</span> <span class="nb">bytes</span><span class="p">],</span> <span class="n">write</span><span class="p">:</span> <span class="n">Callable</span><span class="p">[[</span><span class="nb">bytes</span><span class="p">],</span> <span class="nb">int</span><span class="p">],</span> <span class="n">default_byteorder</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">sys</span><span class="o">.</span><span class="n">byteorder</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        read : func(length: int) -&gt; bytes</span>
<span class="sd">            Read function that accepts desired number of bytes to read and returns `bytes` object of read bytes.</span>
<span class="sd">            Actual length of returned `bytes` object may be less than desired number of bytes.</span>
<span class="sd">        write: func(buffer: bytes) -&gt; int</span>
<span class="sd">            Write function that accepts a `bytes` object and returns count of bytes written. It is expected that</span>
<span class="sd">            call to write will successfully write all bytes, i.e., returned length should match `buffer` length.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_read</span> <span class="o">=</span> <span class="n">read</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_write</span> <span class="o">=</span> <span class="n">write</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_default_byteorder</span> <span class="o">=</span> <span class="n">default_byteorder</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_default_is_native</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_default_byteorder</span> <span class="o">==</span> <span class="n">sys</span><span class="o">.</span><span class="n">byteorder</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">default_byteorder</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_default_byteorder</span>

<div class="viewcode-block" id="StreamEncoder.write">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder.write">[docs]</a>
    <span class="k">def</span> <span class="nf">write</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">source_buffer</span><span class="p">:</span> <span class="nb">bytes</span><span class="p">,</span> <span class="n">offset</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">count</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_write</span><span class="p">(</span><span class="n">source_buffer</span><span class="p">[</span><span class="n">offset</span><span class="p">:</span><span class="n">offset</span> <span class="o">+</span> <span class="n">count</span><span class="p">])</span> <span class="o">!=</span> <span class="n">count</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Failed to write </span><span class="si">{</span><span class="n">count</span><span class="si">:</span><span class="s2">,</span><span class="si">}</span><span class="s2"> bytes to stream&quot;</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">count</span></div>


<div class="viewcode-block" id="StreamEncoder.read">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder.read">[docs]</a>
    <span class="k">def</span> <span class="nf">read</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">target_buffer</span><span class="p">:</span> <span class="nb">bytearray</span><span class="p">,</span> <span class="n">offset</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">count</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="c1"># `count` is requested size, value is treated as max return size</span>
        <span class="n">buffer</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read</span><span class="p">(</span><span class="n">count</span><span class="p">)</span>
        <span class="n">read_length</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">buffer</span><span class="p">)</span>

        <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">read_length</span><span class="p">):</span>
            <span class="n">target_buffer</span><span class="p">[</span><span class="n">offset</span> <span class="o">+</span> <span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="n">buffer</span><span class="p">[</span><span class="n">i</span><span class="p">]</span>

        <span class="k">return</span> <span class="n">read_length</span></div>


<div class="viewcode-block" id="StreamEncoder.write_byte">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder.write_byte">[docs]</a>
    <span class="k">def</span> <span class="nf">write_byte</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">uint8</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="n">size</span> <span class="o">=</span> <span class="n">ByteSize</span><span class="o">.</span><span class="n">UINT8</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_write</span><span class="p">(</span><span class="nb">int</span><span class="p">(</span><span class="n">value</span><span class="p">)</span><span class="o">.</span><span class="n">to_bytes</span><span class="p">(</span><span class="n">size</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">_default_byteorder</span><span class="p">))</span> <span class="o">!=</span> <span class="n">size</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="s2">&quot;Failed to write 1-byte to stream&quot;</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">size</span></div>


<div class="viewcode-block" id="StreamEncoder.read_byte">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder.read_byte">[docs]</a>
    <span class="k">def</span> <span class="nf">read_byte</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">uint8</span><span class="p">:</span>
        <span class="n">size</span> <span class="o">=</span> <span class="n">ByteSize</span><span class="o">.</span><span class="n">UINT8</span>

        <span class="c1"># call expects one byte to be available in base stream</span>
        <span class="n">buffer</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read</span><span class="p">(</span><span class="n">size</span><span class="p">)</span>

        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">buffer</span><span class="p">)</span> <span class="o">!=</span> <span class="n">size</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="s2">&quot;Failed to read 1-byte from stream&quot;</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">uint8</span><span class="p">(</span><span class="n">buffer</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span></div>


<div class="viewcode-block" id="StreamEncoder.write_bool">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder.write_bool">[docs]</a>
    <span class="k">def</span> <span class="nf">write_bool</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="nb">bool</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">if</span> <span class="n">value</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">write_byte</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">write_byte</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>

        <span class="k">return</span> <span class="mi">1</span></div>


<div class="viewcode-block" id="StreamEncoder.read_bool">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder.read_bool">[docs]</a>
    <span class="k">def</span> <span class="nf">read_bool</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
        <span class="c1"># call expects one byte to be available in base stream</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">read_byte</span><span class="p">()</span> <span class="o">!=</span> <span class="mi">0</span></div>


    <span class="k">def</span> <span class="nf">_write_int</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">size</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">signed</span><span class="p">:</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="c1"># sourcery skip: remove-unnecessary-cast</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_write</span><span class="p">(</span><span class="nb">int</span><span class="p">(</span><span class="n">value</span><span class="p">)</span><span class="o">.</span><span class="n">to_bytes</span><span class="p">(</span><span class="n">size</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">_default_byteorder</span> <span class="k">if</span> <span class="n">byteorder</span> <span class="ow">is</span> <span class="kc">None</span> <span class="k">else</span> <span class="n">byteorder</span><span class="p">,</span> <span class="n">signed</span><span class="o">=</span><span class="n">signed</span><span class="p">))</span> <span class="o">!=</span> <span class="n">size</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Failed to write </span><span class="si">{</span><span class="n">size</span><span class="si">}</span><span class="s2">-bytes to stream&quot;</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">size</span>

    <span class="k">def</span> <span class="nf">_read_int</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">size</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">dtype</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">dtype</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="c1"># call expects needed bytes to be available in base stream</span>
        <span class="n">buffer</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read</span><span class="p">(</span><span class="n">size</span><span class="p">)</span>

        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">buffer</span><span class="p">)</span> <span class="o">!=</span> <span class="n">size</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Failed to read </span><span class="si">{</span><span class="n">size</span><span class="si">}</span><span class="s2">-bytes from stream&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="ow">not</span> <span class="p">(</span><span class="n">byteorder</span> <span class="ow">is</span> <span class="kc">None</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">_default_is_native</span><span class="p">)</span> <span class="ow">and</span> <span class="n">byteorder</span> <span class="o">!=</span> <span class="n">sys</span><span class="o">.</span><span class="n">byteorder</span><span class="p">:</span>
            <span class="n">dtype</span> <span class="o">=</span> <span class="n">dtype</span><span class="o">.</span><span class="n">newbyteorder</span><span class="p">()</span>

        <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">frombuffer</span><span class="p">(</span><span class="n">buffer</span><span class="p">,</span> <span class="n">dtype</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>

<div class="viewcode-block" id="StreamEncoder.write_int16">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder.write_int16">[docs]</a>
    <span class="k">def</span> <span class="nf">write_int16</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">int16</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_write_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">INT16</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="kc">True</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="StreamEncoder.read_int16">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder.read_int16">[docs]</a>
    <span class="k">def</span> <span class="nf">read_int16</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">int16</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">INT16</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">dtype</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">int16</span><span class="p">),</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="StreamEncoder.write_uint16">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder.write_uint16">[docs]</a>
    <span class="k">def</span> <span class="nf">write_uint16</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">uint16</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_write_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">UINT16</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="kc">False</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="StreamEncoder.read_uint16">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder.read_uint16">[docs]</a>
    <span class="k">def</span> <span class="nf">read_uint16</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">uint16</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">UINT16</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">dtype</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">uint16</span><span class="p">),</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="StreamEncoder.write_int32">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder.write_int32">[docs]</a>
    <span class="k">def</span> <span class="nf">write_int32</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_write_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">INT32</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="kc">True</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="StreamEncoder.read_int32">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder.read_int32">[docs]</a>
    <span class="k">def</span> <span class="nf">read_int32</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">INT32</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">dtype</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">),</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="StreamEncoder.write_uint32">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder.write_uint32">[docs]</a>
    <span class="k">def</span> <span class="nf">write_uint32</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_write_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">UINT32</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="kc">False</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="StreamEncoder.read_uint32">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder.read_uint32">[docs]</a>
    <span class="k">def</span> <span class="nf">read_uint32</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">UINT32</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">dtype</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">),</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="StreamEncoder.write_int64">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder.write_int64">[docs]</a>
    <span class="k">def</span> <span class="nf">write_int64</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">int64</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_write_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">INT64</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="kc">True</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="StreamEncoder.read_int64">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder.read_int64">[docs]</a>
    <span class="k">def</span> <span class="nf">read_int64</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">int64</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">INT64</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">dtype</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">int64</span><span class="p">),</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="StreamEncoder.write_uint64">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder.write_uint64">[docs]</a>
    <span class="k">def</span> <span class="nf">write_uint64</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_write_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">UINT64</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="kc">False</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="StreamEncoder.read_uint64">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder.read_uint64">[docs]</a>
    <span class="k">def</span> <span class="nf">read_uint64</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">byteorder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_int</span><span class="p">(</span><span class="n">ByteSize</span><span class="o">.</span><span class="n">UINT64</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">dtype</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">),</span> <span class="n">byteorder</span><span class="p">)</span></div>


<div class="viewcode-block" id="StreamEncoder.write7bit_uint32">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder.write7bit_uint32">[docs]</a>
    <span class="k">def</span> <span class="nf">write7bit_uint32</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Encoding7Bit</span><span class="o">.</span><span class="n">WriteUInt32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">write_byte</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span></div>


<div class="viewcode-block" id="StreamEncoder.read7bit_uint32">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder.read7bit_uint32">[docs]</a>
    <span class="k">def</span> <span class="nf">read7bit_uint32</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">:</span>
        <span class="c1"># call expects one to five bytes to be available in base stream</span>
        <span class="k">return</span> <span class="n">Encoding7Bit</span><span class="o">.</span><span class="n">ReadUInt32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">read_byte</span><span class="p">)</span></div>


<div class="viewcode-block" id="StreamEncoder.write7bit_uint64">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder.write7bit_uint64">[docs]</a>
    <span class="k">def</span> <span class="nf">write7bit_uint64</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Encoding7Bit</span><span class="o">.</span><span class="n">WriteUInt64</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">write_byte</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span></div>


<div class="viewcode-block" id="StreamEncoder.read7bit_uint64">
<a class="viewcode-back" href="../../gsf.html#gsf.streamencoder.StreamEncoder.read7bit_uint64">[docs]</a>
    <span class="k">def</span> <span class="nf">read7bit_uint64</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">:</span>
        <span class="c1"># call expects one to nine bytes to be available in base stream</span>
        <span class="k">return</span> <span class="n">Encoding7Bit</span><span class="o">.</span><span class="n">ReadUInt64</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">read_byte</span><span class="p">)</span></div>
</div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
  
 
<hr />
<p>
    <a href="https://github.com/sttp/pyapi">GitHub Home</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://pypi.org/project/sttpapi/" target="_blank">PyPi Package: sttpapi</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://sttp.github.io/documentation/" target="_blank">General STTP Documentation</a>
</p>
<br /><br />
Copyright © 2022, <a href="https://gridprotectionalliance.org/" target="_blank">Grid&nbsp;Protection&nbsp;Alliance</a>
<a href="https://github.com/sttp/pyapi">
    <img align="right" style="margin-top: -50px"
        src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/LockPython_64High.png">
</a>


</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>