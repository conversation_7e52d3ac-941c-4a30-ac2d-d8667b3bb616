

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Overview: module code &mdash; sttp/pyapi 0.6.4 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=92fd9be5" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=8209df7b" />

  
    <link rel="shortcut icon" href="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/favicon.ico"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=bbec6902"></script>
      <script src="../_static/documentation_options.js?v=b1f64a84"></script>
      <script src="../_static/doctools.js?v=92e14aea"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="../index.html" class="icon icon-home">
            sttp/pyapi
              <img src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/sttp.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <!-- Local TOC -->
              <div class="local-toc"></div>
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">sttp/pyapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Overview: module code</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>All modules for which code is available</h1>
<ul><li><a href="gsf.html">gsf</a></li>
<ul><li><a href="gsf/binarystream.html">gsf.binarystream</a></li>
<li><a href="gsf/encoding7bit.html">gsf.encoding7bit</a></li>
<li><a href="gsf/endianorder.html">gsf.endianorder</a></li>
<li><a href="gsf/streamencoder.html">gsf.streamencoder</a></li>
</ul><li><a href="sttp/config.html">sttp.config</a></li>
<li><a href="sttp/data/callbackerrorlistener.html">sttp.data.callbackerrorlistener</a></li>
<li><a href="sttp/data/columnexpression.html">sttp.data.columnexpression</a></li>
<li><a href="sttp/data/constants.html">sttp.data.constants</a></li>
<li><a href="sttp/data/datacolumn.html">sttp.data.datacolumn</a></li>
<li><a href="sttp/data/datarow.html">sttp.data.datarow</a></li>
<li><a href="sttp/data/dataset.html">sttp.data.dataset</a></li>
<li><a href="sttp/data/datatable.html">sttp.data.datatable</a></li>
<li><a href="sttp/data/datatype.html">sttp.data.datatype</a></li>
<li><a href="sttp/data/errors.html">sttp.data.errors</a></li>
<li><a href="sttp/data/expression.html">sttp.data.expression</a></li>
<li><a href="sttp/data/expressiontree.html">sttp.data.expressiontree</a></li>
<li><a href="sttp/data/filterexpressionparser.html">sttp.data.filterexpressionparser</a></li>
<li><a href="sttp/data/functionexpression.html">sttp.data.functionexpression</a></li>
<li><a href="sttp/data/inlistexpression.html">sttp.data.inlistexpression</a></li>
<li><a href="sttp/data/operatorexpression.html">sttp.data.operatorexpression</a></li>
<li><a href="sttp/data/orderbyterm.html">sttp.data.orderbyterm</a></li>
<li><a href="sttp/data/tableidfields.html">sttp.data.tableidfields</a></li>
<li><a href="sttp/data/unaryexpression.html">sttp.data.unaryexpression</a></li>
<li><a href="sttp/data/valueexpression.html">sttp.data.valueexpression</a></li>
<li><a href="sttp/metadata/cache.html">sttp.metadata.cache</a></li>
<li><a href="sttp/metadata/record/device.html">sttp.metadata.record.device</a></li>
<li><a href="sttp/metadata/record/measurement.html">sttp.metadata.record.measurement</a></li>
<li><a href="sttp/metadata/record/phasor.html">sttp.metadata.record.phasor</a></li>
<li><a href="sttp/reader.html">sttp.reader</a></li>
<li><a href="sttp/settings.html">sttp.settings</a></li>
<li><a href="sttp/subscriber.html">sttp.subscriber</a></li>
<li><a href="sttp/ticks.html">sttp.ticks</a></li>
<li><a href="sttp/transport/bufferblock.html">sttp.transport.bufferblock</a></li>
<li><a href="sttp/transport/compactmeasurement.html">sttp.transport.compactmeasurement</a></li>
<li><a href="sttp/transport/constants.html">sttp.transport.constants</a></li>
<li><a href="sttp/transport/datasubscriber.html">sttp.transport.datasubscriber</a></li>
<li><a href="sttp/transport/measurement.html">sttp.transport.measurement</a></li>
<li><a href="sttp/transport/signalindexcache.html">sttp.transport.signalindexcache</a></li>
<li><a href="sttp/transport/signalkind.html">sttp.transport.signalkind</a></li>
<li><a href="sttp/transport/subscriberconnector.html">sttp.transport.subscriberconnector</a></li>
<li><a href="sttp/transport/subscriptioninfo.html">sttp.transport.subscriptioninfo</a></li>
<li><a href="sttp/transport/tssc/decoder.html">sttp.transport.tssc.decoder</a></li>
<li><a href="sttp/transport/tssc/pointmetadata.html">sttp.transport.tssc.pointmetadata</a></li>
<li><a href="sttp/version.html">sttp.version</a></li>
</ul>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
  
 
<hr />
<p>
    <a href="https://github.com/sttp/pyapi">GitHub Home</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://pypi.org/project/sttpapi/" target="_blank">PyPi Package: sttpapi</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://sttp.github.io/documentation/" target="_blank">General STTP Documentation</a>
</p>
<br /><br />
Copyright © 2022, <a href="https://gridprotectionalliance.org/" target="_blank">Grid&nbsp;Protection&nbsp;Alliance</a>
<a href="https://github.com/sttp/pyapi">
    <img align="right" style="margin-top: -50px"
        src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/LockPython_64High.png">
</a>


</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>