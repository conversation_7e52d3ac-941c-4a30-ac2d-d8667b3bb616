

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../../../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>sttp.data.datarow &mdash; sttp/pyapi 0.6.4 documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=92fd9be5" />
      <link rel="stylesheet" type="text/css" href="../../../_static/css/theme.css?v=8209df7b" />

  
    <link rel="shortcut icon" href="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/favicon.ico"/>
      <script src="../../../_static/jquery.js?v=5d32c60e"></script>
      <script src="../../../_static/_sphinx_javascript_frameworks_compat.js?v=bbec6902"></script>
      <script src="../../../_static/documentation_options.js?v=b1f64a84"></script>
      <script src="../../../_static/doctools.js?v=92e14aea"></script>
      <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="../../../index.html" class="icon icon-home">
            sttp/pyapi
              <img src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/sttp.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <!-- Local TOC -->
              <div class="local-toc"></div>
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../../index.html">sttp/pyapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../../index.html">Module code</a></li>
      <li class="breadcrumb-item active">sttp.data.datarow</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for sttp.data.datarow</h1><div class="highlight"><pre>
<span></span><span class="c1"># ******************************************************************************************************</span>
<span class="c1">#  datarow.py - Gbtc</span>
<span class="c1">#</span>
<span class="c1">#  Copyright © 2022, Grid Protection Alliance.  All Rights Reserved.</span>
<span class="c1">#</span>
<span class="c1">#  Licensed to the Grid Protection Alliance (GPA) under one or more contributor license agreements. See</span>
<span class="c1">#  the NOTICE file distributed with this work for additional information regarding copyright ownership.</span>
<span class="c1">#  The GPA licenses this file to you under the MIT License (MIT), the &quot;License&quot;; you may not use this</span>
<span class="c1">#  file except in compliance with the License. You may obtain a copy of the License at:</span>
<span class="c1">#</span>
<span class="c1">#      http://opensource.org/licenses/MIT</span>
<span class="c1">#</span>
<span class="c1">#  Unless agreed to in writing, the subject software distributed under the License is distributed on an</span>
<span class="c1">#  &quot;AS-IS&quot; BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. Refer to the</span>
<span class="c1">#  License for the specific language governing permissions and limitations.</span>
<span class="c1">#</span>
<span class="c1">#  Code Modification History:</span>
<span class="c1">#  ----------------------------------------------------------------------------------------------------</span>
<span class="c1">#  08/26/2022 - J. Ritchie Carroll</span>
<span class="c1">#       Generated original version of source code.</span>
<span class="c1">#</span>
<span class="c1"># ******************************************************************************************************</span>

<span class="kn">from</span> <span class="nn">__future__</span> <span class="kn">import</span> <span class="n">annotations</span>
<span class="kn">from</span> <span class="nn">gsf</span> <span class="kn">import</span> <span class="n">Convert</span><span class="p">,</span> <span class="n">Empty</span><span class="p">,</span> <span class="n">normalize_enumname</span>
<span class="kn">from</span> <span class="nn">.datatype</span> <span class="kn">import</span> <span class="n">DataType</span><span class="p">,</span> <span class="n">default_datatype</span>
<span class="kn">from</span> <span class="nn">.datacolumn</span> <span class="kn">import</span> <span class="n">DataColumn</span>
<span class="kn">from</span> <span class="nn">.constants</span> <span class="kn">import</span> <span class="n">ExpressionValueType</span>
<span class="kn">from</span> <span class="nn">.errors</span> <span class="kn">import</span> <span class="n">EvaluateError</span>
<span class="kn">from</span> <span class="nn">decimal</span> <span class="kn">import</span> <span class="n">Decimal</span>
<span class="kn">from</span> <span class="nn">datetime</span> <span class="kn">import</span> <span class="n">datetime</span>
<span class="kn">from</span> <span class="nn">uuid</span> <span class="kn">import</span> <span class="n">UUID</span>
<span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">Callable</span><span class="p">,</span> <span class="n">Iterator</span><span class="p">,</span> <span class="n">Optional</span><span class="p">,</span> <span class="n">Tuple</span><span class="p">,</span> <span class="n">Union</span><span class="p">,</span> <span class="n">TYPE_CHECKING</span>
<span class="kn">import</span> <span class="nn">numpy</span> <span class="k">as</span> <span class="nn">np</span>

<span class="k">if</span> <span class="n">TYPE_CHECKING</span><span class="p">:</span>
    <span class="kn">from</span> <span class="nn">.dataset</span> <span class="kn">import</span> <span class="n">xsdformat</span>
    <span class="kn">from</span> <span class="nn">.datatable</span> <span class="kn">import</span> <span class="n">DataTable</span>
    <span class="kn">from</span> <span class="nn">.expressiontree</span> <span class="kn">import</span> <span class="n">ExpressionTree</span>


<div class="viewcode-block" id="DataRow">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow">[docs]</a>
<span class="k">class</span> <span class="nc">DataRow</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents a row, i.e., a record, in a `DataTable` defining a set of values for each</span>
<span class="sd">    defined `DataColumn` field in the `DataTable` columns collection.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                 <span class="n">parent</span><span class="p">:</span> <span class="n">DataTable</span><span class="p">,</span>
                 <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Creates a new `DataRow`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_parent</span> <span class="o">=</span> <span class="n">parent</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_values</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">empty</span><span class="p">(</span><span class="n">parent</span><span class="o">.</span><span class="n">columncount</span><span class="p">,</span> <span class="nb">object</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__getitem__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">key</span><span class="p">:</span> <span class="n">Union</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="nb">object</span><span class="p">:</span>
        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">key</span><span class="p">,</span> <span class="nb">str</span><span class="p">):</span>
            <span class="n">value</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value_byname</span><span class="p">(</span><span class="n">key</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">value</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span><span class="p">(</span><span class="n">key</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">err</span>

        <span class="k">return</span> <span class="n">value</span>

    <span class="k">def</span> <span class="fm">__setitem__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">key</span><span class="p">:</span> <span class="n">Union</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">],</span> <span class="n">value</span><span class="p">:</span> <span class="nb">object</span><span class="p">):</span>
        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">key</span><span class="p">,</span> <span class="nb">str</span><span class="p">):</span>
            <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">set_value_byname</span><span class="p">(</span><span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">set_value</span><span class="p">(</span><span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">err</span>

    <span class="k">def</span> <span class="fm">__len__</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="k">return</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_values</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__contains__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">item</span><span class="p">:</span> <span class="nb">object</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">item</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_values</span>

    <span class="k">def</span> <span class="fm">__iter__</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Iterator</span><span class="p">[</span><span class="nb">object</span><span class="p">]:</span>
        <span class="k">return</span> <span class="nb">iter</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_values</span><span class="p">)</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">parent</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">DataTable</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the parent `DataTable` of the `DataRow`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_parent</span>

    <span class="k">def</span> <span class="nf">_get_columnindex</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnname</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
        <span class="k">if</span> <span class="p">(</span><span class="n">column</span> <span class="o">:=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_parent</span><span class="o">.</span><span class="n">column_byname</span><span class="p">(</span><span class="n">columnname</span><span class="p">))</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="o">-</span><span class="mi">1</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;column name </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">columnname</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2"> was not found in table </span><span class="se">\&quot;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">_parent</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">column</span><span class="o">.</span><span class="n">index</span><span class="p">,</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">_validate_columntype</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">targettype</span><span class="p">:</span> <span class="n">Union</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="n">DataType</span><span class="p">],</span> <span class="n">read</span><span class="p">:</span> <span class="nb">bool</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">DataColumn</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
        <span class="k">if</span> <span class="p">(</span><span class="n">column</span> <span class="o">:=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_parent</span><span class="o">.</span><span class="n">column</span><span class="p">(</span><span class="n">columnindex</span><span class="p">))</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">IndexError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;column index </span><span class="si">{</span><span class="n">columnindex</span><span class="si">}</span><span class="s2"> is out of range for table </span><span class="se">\&quot;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">_parent</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">targettype</span> <span class="o">&gt;</span> <span class="o">-</span><span class="mi">1</span> <span class="ow">and</span> <span class="n">column</span><span class="o">.</span><span class="n">datatype</span> <span class="o">!=</span> <span class="n">targettype</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">read</span><span class="p">:</span>
                <span class="n">action</span> <span class="o">=</span> <span class="s2">&quot;read&quot;</span>
                <span class="n">preposition</span> <span class="o">=</span> <span class="s2">&quot;from&quot;</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">action</span> <span class="o">=</span> <span class="s2">&quot;assign&quot;</span>
                <span class="n">preposition</span> <span class="o">=</span> <span class="s2">&quot;to&quot;</span>

            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;cannot </span><span class="si">{</span><span class="n">action</span><span class="si">}</span><span class="s2"> </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">normalize_enumname</span><span class="p">(</span><span class="n">DataType</span><span class="p">(</span><span class="n">targettype</span><span class="p">))</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2"> value </span><span class="si">{</span><span class="n">preposition</span><span class="si">}</span><span class="s2"> DataColumn </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">column</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2"> for table </span><span class="se">\&quot;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">_parent</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">, column data type is </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">normalize_enumname</span><span class="p">(</span><span class="n">column</span><span class="o">.</span><span class="n">datatype</span><span class="p">)</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="ow">not</span> <span class="n">read</span> <span class="ow">and</span> <span class="n">column</span><span class="o">.</span><span class="n">computed</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;cannot assign value to DataColumn </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">column</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2"> for table </span><span class="se">\&quot;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">_parent</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">, column is computed with an expression&quot;</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">column</span><span class="p">,</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">_expressiontree</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">column</span><span class="p">:</span> <span class="n">DataColumn</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">ExpressionTree</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
        <span class="n">columnindex</span> <span class="o">=</span> <span class="n">column</span><span class="o">.</span><span class="n">index</span>
        <span class="n">value</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_values</span><span class="p">[</span><span class="n">columnindex</span><span class="p">]</span>

        <span class="k">if</span> <span class="n">value</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="kn">from</span> <span class="nn">.filterexpressionparser</span> <span class="kn">import</span> <span class="n">FilterExpressionParser</span>

            <span class="n">expressiontree</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="n">FilterExpressionParser</span><span class="o">.</span><span class="n">generate_expressiontree</span><span class="p">(</span><span class="n">column</span><span class="o">.</span><span class="n">parent</span><span class="p">,</span> <span class="n">column</span><span class="o">.</span><span class="n">expression</span><span class="p">,</span> <span class="kc">True</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to parse expression </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">column</span><span class="o">.</span><span class="n">expression</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2"> defined for computed DataColumn </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">column</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2"> for table </span><span class="se">\&quot;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">_parent</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">: </span><span class="si">{</span><span class="n">err</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">_values</span><span class="p">[</span><span class="n">columnindex</span><span class="p">]</span> <span class="o">=</span> <span class="n">expressiontree</span>
            <span class="k">return</span> <span class="n">expressiontree</span><span class="p">,</span> <span class="kc">None</span>

        <span class="k">return</span> <span class="n">value</span><span class="p">,</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">_get_computedvalue</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">column</span><span class="p">:</span> <span class="n">DataColumn</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="nb">object</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
        <span class="n">expressiontree</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_expressiontree</span><span class="p">(</span><span class="n">column</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="n">err</span>

        <span class="k">try</span><span class="p">:</span>
            <span class="n">sourcevalue</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="n">expressiontree</span><span class="o">.</span><span class="n">evaluate</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span>
        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">ex</span><span class="p">:</span>
            <span class="n">err</span> <span class="o">=</span> <span class="n">ex</span>

        <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to evaluate expression </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">column</span><span class="o">.</span><span class="n">expression</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2"> defined for computed DataColumn </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">column</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2"> for table </span><span class="se">\&quot;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">_parent</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">: </span><span class="si">{</span><span class="n">err</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

        <span class="n">sourcetype</span> <span class="o">=</span> <span class="n">sourcevalue</span><span class="o">.</span><span class="n">valuetype</span>
        <span class="n">targettype</span> <span class="o">=</span> <span class="n">column</span><span class="o">.</span><span class="n">datatype</span>

        <span class="k">if</span> <span class="n">sourcetype</span> <span class="o">==</span> <span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">BOOLEAN</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_convert_frombool</span><span class="p">(</span><span class="n">sourcevalue</span><span class="o">.</span><span class="n">_booleanvalue</span><span class="p">(),</span> <span class="n">targettype</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">sourcetype</span> <span class="o">==</span> <span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">INT32</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_convert_fromint32</span><span class="p">(</span><span class="n">sourcevalue</span><span class="o">.</span><span class="n">_int32value</span><span class="p">(),</span> <span class="n">targettype</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">sourcetype</span> <span class="o">==</span> <span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">INT64</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_convert_fromint64</span><span class="p">(</span><span class="n">sourcevalue</span><span class="o">.</span><span class="n">_int64value</span><span class="p">(),</span> <span class="n">targettype</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">sourcetype</span> <span class="o">==</span> <span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">DECIMAL</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_convert_fromdecimal</span><span class="p">(</span><span class="n">sourcevalue</span><span class="o">.</span><span class="n">_decimalvalue</span><span class="p">(),</span> <span class="n">targettype</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">sourcetype</span> <span class="o">==</span> <span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">DOUBLE</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_convert_fromdouble</span><span class="p">(</span><span class="n">sourcevalue</span><span class="o">.</span><span class="n">_doublevalue</span><span class="p">(),</span> <span class="n">targettype</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">sourcetype</span> <span class="o">==</span> <span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">STRING</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_convert_fromstring</span><span class="p">(</span><span class="n">sourcevalue</span><span class="o">.</span><span class="n">_stringvalue</span><span class="p">(),</span> <span class="n">targettype</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">sourcetype</span> <span class="o">==</span> <span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">GUID</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_convert_fromguid</span><span class="p">(</span><span class="n">sourcevalue</span><span class="o">.</span><span class="n">_guidvalue</span><span class="p">(),</span> <span class="n">targettype</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">sourcetype</span> <span class="o">==</span> <span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">DATETIME</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_convert_fromdatetime</span><span class="p">(</span><span class="n">sourcevalue</span><span class="o">.</span><span class="n">_datetimevalue</span><span class="p">(),</span> <span class="n">targettype</span><span class="p">)</span>

        <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">TypeError</span><span class="p">(</span><span class="s2">&quot;unexpected expression value type encountered&quot;</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">_convert_fromstring</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">targettype</span><span class="p">:</span> <span class="n">DataType</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="nb">object</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">STRING</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">value</span><span class="p">,</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">BOOLEAN</span><span class="p">:</span>
                <span class="k">return</span> <span class="nb">bool</span><span class="p">(</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DATETIME</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">Convert</span><span class="o">.</span><span class="n">from_str</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">datetime</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">GUID</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">UUID</span><span class="p">(</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">SINGLE</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">Convert</span><span class="o">.</span><span class="n">from_str</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">float32</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DOUBLE</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">Convert</span><span class="o">.</span><span class="n">from_str</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">float64</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DECIMAL</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">Decimal</span><span class="p">(</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT8</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">Convert</span><span class="o">.</span><span class="n">from_str</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">int8</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT16</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">Convert</span><span class="o">.</span><span class="n">from_str</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">int16</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT32</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">Convert</span><span class="o">.</span><span class="n">from_str</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT64</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">Convert</span><span class="o">.</span><span class="n">from_str</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">int64</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT8</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">Convert</span><span class="o">.</span><span class="n">from_str</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">uint8</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT16</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">Convert</span><span class="o">.</span><span class="n">from_str</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">uint16</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT32</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">Convert</span><span class="o">.</span><span class="n">from_str</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT64</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">Convert</span><span class="o">.</span><span class="n">from_str</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">),</span> <span class="kc">None</span>

            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">TypeError</span><span class="p">(</span><span class="s2">&quot;unexpected column data type encountered&quot;</span><span class="p">)</span>
        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">ex</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to convert </span><span class="se">\&quot;</span><span class="s2">String</span><span class="se">\&quot;</span><span class="s2"> expression value to </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">normalize_enumname</span><span class="p">(</span><span class="n">targettype</span><span class="p">)</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2"> column: </span><span class="si">{</span><span class="n">ex</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">_convert_fromguid</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">UUID</span><span class="p">,</span> <span class="n">targettype</span><span class="p">:</span> <span class="n">DataType</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="nb">object</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">STRING</span><span class="p">:</span>
                <span class="k">return</span> <span class="nb">str</span><span class="p">(</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">GUID</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">value</span><span class="p">,</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="ow">in</span> <span class="p">[</span><span class="n">DataType</span><span class="o">.</span><span class="n">BOOLEAN</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DATETIME</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">SINGLE</span><span class="p">,</span>
                              <span class="n">DataType</span><span class="o">.</span><span class="n">DOUBLE</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DECIMAL</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT8</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT16</span><span class="p">,</span>
                              <span class="n">DataType</span><span class="o">.</span><span class="n">INT32</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT64</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT8</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT16</span><span class="p">,</span>
                              <span class="n">DataType</span><span class="o">.</span><span class="n">UINT32</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT64</span><span class="p">]:</span>
                <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;cannot convert </span><span class="se">\&quot;</span><span class="s1">Guid</span><span class="se">\&quot;</span><span class="s1"> expression value to </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">normalize_enumname</span><span class="p">(</span><span class="n">targettype</span><span class="p">)</span><span class="si">}</span><span class="se">\&quot;</span><span class="s1"> column&#39;</span><span class="p">)</span>

            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">TypeError</span><span class="p">(</span><span class="s2">&quot;unexpected column data type encountered&quot;</span><span class="p">)</span>
        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">ex</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;failed to convert </span><span class="se">\&quot;</span><span class="s1">Guid</span><span class="se">\&quot;</span><span class="s1"> expression value to </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">normalize_enumname</span><span class="p">(</span><span class="n">targettype</span><span class="p">)</span><span class="si">}</span><span class="se">\&quot;</span><span class="s1"> column: </span><span class="si">{</span><span class="n">ex</span><span class="si">}</span><span class="s1">&#39;</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">_convert_fromvalue</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="nb">object</span><span class="p">,</span> <span class="n">sourcetype</span><span class="p">:</span> <span class="n">DataType</span><span class="p">,</span> <span class="n">targettype</span><span class="p">:</span> <span class="n">DataType</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="nb">object</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">STRING</span><span class="p">:</span>
                <span class="k">return</span> <span class="nb">str</span><span class="p">(</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">BOOLEAN</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">value</span> <span class="o">!=</span> <span class="mi">0</span><span class="p">,</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">SINGLE</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">float32</span><span class="p">(</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DOUBLE</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">float64</span><span class="p">(</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DECIMAL</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">Decimal</span><span class="p">(</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT8</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">int8</span><span class="p">(</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT16</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">int16</span><span class="p">(</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT32</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT64</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">int64</span><span class="p">(</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT8</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">uint8</span><span class="p">(</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT16</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">uint16</span><span class="p">(</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT32</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT64</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="ow">in</span> <span class="p">[</span><span class="n">DataType</span><span class="o">.</span><span class="n">DATETIME</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">GUID</span><span class="p">]:</span>
                <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;cannot convert </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">normalize_enumname</span><span class="p">(</span><span class="n">sourcetype</span><span class="p">)</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2"> expression value to </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">normalize_enumname</span><span class="p">(</span><span class="n">targettype</span><span class="p">)</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2"> column&quot;</span><span class="p">)</span>

            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">TypeError</span><span class="p">(</span><span class="s2">&quot;unexpected column data type encountered&quot;</span><span class="p">)</span>
        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">ex</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to convert </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">normalize_enumname</span><span class="p">(</span><span class="n">sourcetype</span><span class="p">)</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2"> expression value to </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">normalize_enumname</span><span class="p">(</span><span class="n">targettype</span><span class="p">)</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2"> column: </span><span class="si">{</span><span class="n">ex</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">_convert_frombool</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">targettype</span><span class="p">:</span> <span class="n">DataType</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="nb">object</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_convert_fromvalue</span><span class="p">(</span><span class="mi">1</span> <span class="k">if</span> <span class="n">value</span> <span class="k">else</span> <span class="mi">0</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">BOOLEAN</span><span class="p">,</span> <span class="n">targettype</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">_convert_fromint32</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">,</span> <span class="n">targettype</span><span class="p">:</span> <span class="n">DataType</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="nb">object</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_convert_fromvalue</span><span class="p">(</span><span class="n">value</span><span class="o">.</span><span class="n">item</span><span class="p">(),</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT32</span><span class="p">,</span> <span class="n">targettype</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">_convert_fromint64</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">int64</span><span class="p">,</span> <span class="n">targettype</span><span class="p">:</span> <span class="n">DataType</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="nb">object</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_convert_fromvalue</span><span class="p">(</span><span class="n">value</span><span class="o">.</span><span class="n">item</span><span class="p">(),</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT64</span><span class="p">,</span> <span class="n">targettype</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">_convert_fromdecimal</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">Decimal</span><span class="p">,</span> <span class="n">targettype</span><span class="p">:</span> <span class="n">DataType</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="nb">object</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_convert_fromvalue</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DECIMAL</span><span class="p">,</span> <span class="n">targettype</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">_convert_fromdouble</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">float64</span><span class="p">,</span> <span class="n">targettype</span><span class="p">:</span> <span class="n">DataType</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="nb">object</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_convert_fromvalue</span><span class="p">(</span><span class="n">value</span><span class="o">.</span><span class="n">item</span><span class="p">(),</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DOUBLE</span><span class="p">,</span> <span class="n">targettype</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">_convert_fromdatetime</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">datetime</span><span class="p">,</span> <span class="n">targettype</span><span class="p">:</span> <span class="n">DataType</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="nb">object</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">STRING</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">xsdformat</span><span class="p">(</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">targettype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DATETIME</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">value</span><span class="p">,</span> <span class="kc">None</span>
        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">ex</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to convert </span><span class="se">\&quot;</span><span class="s2">DateTime</span><span class="se">\&quot;</span><span class="s2"> expression value to </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">normalize_enumname</span><span class="p">(</span><span class="n">targettype</span><span class="p">)</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2"> column: </span><span class="si">{</span><span class="n">ex</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_convert_fromvalue</span><span class="p">(</span><span class="nb">int</span><span class="p">(</span><span class="n">value</span><span class="o">.</span><span class="n">timestamp</span><span class="p">()),</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DATETIME</span><span class="p">,</span> <span class="n">targettype</span><span class="p">)</span>

<div class="viewcode-block" id="DataRow.value">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.value">[docs]</a>
    <span class="k">def</span> <span class="nf">value</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="nb">object</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Reads the record value at the specified column index.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">column</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_validate_columntype</span><span class="p">(</span><span class="n">columnindex</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">,</span> <span class="kc">True</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="n">err</span>

        <span class="k">if</span> <span class="n">column</span><span class="o">.</span><span class="n">computed</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_computedvalue</span><span class="p">(</span><span class="n">column</span><span class="p">)</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_values</span><span class="p">[</span><span class="n">columnindex</span><span class="p">],</span> <span class="kc">None</span></div>


<div class="viewcode-block" id="DataRow.value_byname">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.value_byname">[docs]</a>
    <span class="k">def</span> <span class="nf">value_byname</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnname</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="nb">object</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Reads the record value for the specified column name.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">index</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_columnindex</span><span class="p">(</span><span class="n">columnname</span><span class="p">)</span>
        <span class="k">return</span> <span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">err</span><span class="p">)</span> <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_values</span><span class="p">[</span><span class="n">index</span><span class="p">],</span> <span class="kc">None</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.set_value">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.set_value">[docs]</a>
    <span class="k">def</span> <span class="nf">set_value</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="nb">object</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]:</span>
        <span class="c1"># sourcery skip: assign-if-exp</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Assigns the record value at the specified column index.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">_</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_validate_columntype</span><span class="p">(</span><span class="n">columnindex</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">,</span> <span class="kc">False</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">err</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_values</span><span class="p">[</span><span class="n">columnindex</span><span class="p">]</span> <span class="o">=</span> <span class="n">value</span>
        <span class="k">return</span> <span class="kc">None</span></div>


<div class="viewcode-block" id="DataRow.set_value_byname">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.set_value_byname">[docs]</a>
    <span class="k">def</span> <span class="nf">set_value_byname</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnname</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="nb">object</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Assigns the record value for the specified column name.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">index</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_columnindex</span><span class="p">(</span><span class="n">columnname</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">err</span> <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="bp">self</span><span class="o">.</span><span class="n">set_value</span><span class="p">(</span><span class="n">index</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.value_as_string">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.value_as_string">[docs]</a>
    <span class="k">def</span> <span class="nf">value_as_string</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Reads the record value at the specified columnIndex converted to a string.</span>
<span class="sd">        For column index out of range or any other errors, an empty string will be returned.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">columnvalue_as_string</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_parent</span><span class="o">.</span><span class="n">column</span><span class="p">(</span><span class="n">columnindex</span><span class="p">))</span></div>


<div class="viewcode-block" id="DataRow.value_as_string_byname">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.value_as_string_byname">[docs]</a>
    <span class="k">def</span> <span class="nf">value_as_string_byname</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnname</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Reads the record value for the specified columnName converted to a string.</span>
<span class="sd">        For column name not found or any other errors, an empty string will be returned.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">columnvalue_as_string</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_parent</span><span class="o">.</span><span class="n">column_byname</span><span class="p">(</span><span class="n">columnname</span><span class="p">))</span></div>


<div class="viewcode-block" id="DataRow.columnvalue_as_string">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.columnvalue_as_string">[docs]</a>
    <span class="k">def</span> <span class="nf">columnvalue_as_string</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">column</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">DataColumn</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Reads the record value for the specified data column converted</span>
<span class="sd">        to a string. For any errors, an empty string will be returned.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="n">column</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">Empty</span><span class="o">.</span><span class="n">STRING</span>

        <span class="n">index</span> <span class="o">=</span> <span class="n">column</span><span class="o">.</span><span class="n">index</span>
        <span class="n">datatype</span> <span class="o">=</span> <span class="n">column</span><span class="o">.</span><span class="n">datatype</span>

        <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">STRING</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_string_from_typevalue</span><span class="p">(</span><span class="n">index</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">stringvalue</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">BOOLEAN</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_string_from_typevalue</span><span class="p">(</span><span class="n">index</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">booleanvalue</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DATETIME</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_string_from_typevalue</span><span class="p">(</span><span class="n">index</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">datetimevalue</span><span class="p">,</span> <span class="n">xsdformat</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">SINGLE</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_string_from_typevalue</span><span class="p">(</span><span class="n">index</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">singlevalue</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DOUBLE</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_string_from_typevalue</span><span class="p">(</span><span class="n">index</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">doublevalue</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DECIMAL</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_string_from_typevalue</span><span class="p">(</span><span class="n">index</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">decimalvalue</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">GUID</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_string_from_typevalue</span><span class="p">(</span><span class="n">index</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">guidvalue</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT8</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_string_from_typevalue</span><span class="p">(</span><span class="n">index</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">int8value</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT16</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_string_from_typevalue</span><span class="p">(</span><span class="n">index</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">int16value</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT32</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_string_from_typevalue</span><span class="p">(</span><span class="n">index</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">int32value</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT64</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_string_from_typevalue</span><span class="p">(</span><span class="n">index</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">int64value</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT8</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_string_from_typevalue</span><span class="p">(</span><span class="n">index</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">uint8value</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT16</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_string_from_typevalue</span><span class="p">(</span><span class="n">index</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">uint16value</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT32</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_string_from_typevalue</span><span class="p">(</span><span class="n">index</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">uint32value</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT64</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_string_from_typevalue</span><span class="p">(</span><span class="n">index</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">uint64value</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">Empty</span><span class="o">.</span><span class="n">STRING</span></div>


    <span class="k">def</span> <span class="nf">_checkstate</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">null</span><span class="p">:</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">err</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="nb">bool</span><span class="p">,</span> <span class="nb">str</span><span class="p">]:</span>
        <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">True</span><span class="p">,</span> <span class="n">Empty</span><span class="o">.</span><span class="n">STRING</span>

        <span class="k">return</span> <span class="p">(</span><span class="kc">True</span><span class="p">,</span> <span class="s2">&quot;&lt;NULL&gt;&quot;</span><span class="p">)</span> <span class="k">if</span> <span class="n">null</span> <span class="k">else</span> <span class="p">(</span><span class="kc">False</span><span class="p">,</span> <span class="n">Empty</span><span class="o">.</span><span class="n">STRING</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">_string_from_typevalue</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">index</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">getvalue</span><span class="p">:</span> <span class="n">Callable</span><span class="p">[[</span><span class="nb">int</span><span class="p">],</span> <span class="n">Tuple</span><span class="p">[</span><span class="nb">object</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]],</span> <span class="n">strconv</span><span class="p">:</span> <span class="n">Callable</span><span class="p">[[</span><span class="nb">object</span><span class="p">],</span> <span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="n">value</span><span class="p">,</span> <span class="n">null</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="n">getvalue</span><span class="p">(</span><span class="n">index</span><span class="p">)</span>
        <span class="n">invalid</span><span class="p">,</span> <span class="n">result</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_checkstate</span><span class="p">(</span><span class="n">null</span><span class="p">,</span> <span class="n">err</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="n">result</span> <span class="k">if</span> <span class="n">invalid</span> <span class="k">else</span> <span class="n">strconv</span><span class="p">(</span><span class="n">value</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">_typevalue</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">targettype</span><span class="p">:</span> <span class="n">DataType</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="nb">object</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
        <span class="n">column</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_validate_columntype</span><span class="p">(</span><span class="n">columnindex</span><span class="p">,</span> <span class="n">targettype</span><span class="p">,</span> <span class="kc">True</span><span class="p">)</span>
        <span class="n">default</span> <span class="o">=</span> <span class="n">default_datatype</span><span class="p">(</span><span class="n">targettype</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">default</span><span class="p">,</span> <span class="kc">False</span><span class="p">,</span> <span class="n">err</span>

        <span class="k">if</span> <span class="n">column</span><span class="o">.</span><span class="n">computed</span><span class="p">:</span>
            <span class="n">value</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_computedvalue</span><span class="p">(</span><span class="n">column</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">default</span><span class="p">,</span> <span class="kc">False</span><span class="p">,</span> <span class="n">err</span>

            <span class="k">return</span> <span class="p">(</span><span class="n">default</span><span class="p">,</span> <span class="kc">True</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="k">if</span> <span class="n">value</span> <span class="ow">is</span> <span class="kc">None</span> <span class="k">else</span> <span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="kc">False</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span>

        <span class="n">value</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_values</span><span class="p">[</span><span class="n">columnindex</span><span class="p">]</span>

        <span class="k">return</span> <span class="p">(</span><span class="n">default</span><span class="p">,</span> <span class="kc">True</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="k">if</span> <span class="n">value</span> <span class="ow">is</span> <span class="kc">None</span> <span class="k">else</span> <span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="kc">False</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">_typevalue_byname</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnname</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">targettype</span><span class="p">:</span> <span class="n">DataType</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="nb">object</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
        <span class="n">index</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_columnindex</span><span class="p">(</span><span class="n">columnname</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">default_datatype</span><span class="p">(</span><span class="n">targettype</span><span class="p">),</span> <span class="kc">False</span><span class="p">,</span> <span class="n">err</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue</span><span class="p">(</span><span class="n">index</span><span class="p">,</span> <span class="n">targettype</span><span class="p">)</span>

<div class="viewcode-block" id="DataRow.stringvalue">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.stringvalue">[docs]</a>
    <span class="k">def</span> <span class="nf">stringvalue</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the string-based record value at the specified column index.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.STRING`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue</span><span class="p">(</span><span class="n">columnindex</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">STRING</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.stringvalue_byname">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.stringvalue_byname">[docs]</a>
    <span class="k">def</span> <span class="nf">stringvalue_byname</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnname</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the string-based record value for the specified column name.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.STRING`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue_byname</span><span class="p">(</span><span class="n">columnname</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">STRING</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.booleanvalue">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.booleanvalue">[docs]</a>
    <span class="k">def</span> <span class="nf">booleanvalue</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="nb">bool</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the boolean-based record value at the specified column index.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.BOOLEAN`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue</span><span class="p">(</span><span class="n">columnindex</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">BOOLEAN</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.booleanvalue_byname">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.booleanvalue_byname">[docs]</a>
    <span class="k">def</span> <span class="nf">booleanvalue_byname</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnname</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="nb">bool</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the boolean-based record value for the specified column name.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.BOOLEAN`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue_byname</span><span class="p">(</span><span class="n">columnname</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">BOOLEAN</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.datetimevalue">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.datetimevalue">[docs]</a>
    <span class="k">def</span> <span class="nf">datetimevalue</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">datetime</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the datetime-based record value at the specified column index.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.DATETIME`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue</span><span class="p">(</span><span class="n">columnindex</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DATETIME</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.datetimevalue_byname">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.datetimevalue_byname">[docs]</a>
    <span class="k">def</span> <span class="nf">datetimevalue_byname</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnname</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">datetime</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the datetime-based record value for the specified column name.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.DATETIME`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue_byname</span><span class="p">(</span><span class="n">columnname</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DATETIME</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.singlevalue">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.singlevalue">[docs]</a>
    <span class="k">def</span> <span class="nf">singlevalue</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">float32</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the single-based record value at the specified column index.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.SINGLE`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue</span><span class="p">(</span><span class="n">columnindex</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">SINGLE</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.singlevalue_byname">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.singlevalue_byname">[docs]</a>
    <span class="k">def</span> <span class="nf">singlevalue_byname</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnname</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">float32</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the single-based record value for the specified column name.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.SINGLE`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue_byname</span><span class="p">(</span><span class="n">columnname</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">SINGLE</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.doublevalue">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.doublevalue">[docs]</a>
    <span class="k">def</span> <span class="nf">doublevalue</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">float64</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the double-based record value at the specified column index.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.DOUBLE`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue</span><span class="p">(</span><span class="n">columnindex</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DOUBLE</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.doublevalue_byname">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.doublevalue_byname">[docs]</a>
    <span class="k">def</span> <span class="nf">doublevalue_byname</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnname</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">float64</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the double-based record value for the specified column name.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.DOUBLE`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue_byname</span><span class="p">(</span><span class="n">columnname</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DOUBLE</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.decimalvalue">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.decimalvalue">[docs]</a>
    <span class="k">def</span> <span class="nf">decimalvalue</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Decimal</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the decimal-based record value at the specified column index.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.DECIMAL`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue</span><span class="p">(</span><span class="n">columnindex</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DECIMAL</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.decimalvalue_byname">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.decimalvalue_byname">[docs]</a>
    <span class="k">def</span> <span class="nf">decimalvalue_byname</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnname</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Decimal</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the decimal-based record value for the specified column name.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.DECIMAL`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue_byname</span><span class="p">(</span><span class="n">columnname</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DECIMAL</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.guidvalue">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.guidvalue">[docs]</a>
    <span class="k">def</span> <span class="nf">guidvalue</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">UUID</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the guid-based record value at the specified column index.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.GUID`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue</span><span class="p">(</span><span class="n">columnindex</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">GUID</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.guidvalue_byname">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.guidvalue_byname">[docs]</a>
    <span class="k">def</span> <span class="nf">guidvalue_byname</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnname</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">UUID</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the guid-based record value for the specified column name.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.GUID`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue_byname</span><span class="p">(</span><span class="n">columnname</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">GUID</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.int8value">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.int8value">[docs]</a>
    <span class="k">def</span> <span class="nf">int8value</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">int8</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the int8-based record value at the specified column index.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.INT8`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue</span><span class="p">(</span><span class="n">columnindex</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT8</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.int8value_byname">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.int8value_byname">[docs]</a>
    <span class="k">def</span> <span class="nf">int8value_byname</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnname</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">int8</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the int8-based record value for the specified column name.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.INT8`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue_byname</span><span class="p">(</span><span class="n">columnname</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT8</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.int16value">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.int16value">[docs]</a>
    <span class="k">def</span> <span class="nf">int16value</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">int16</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the int16-based record value at the specified column index.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.INT16`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue</span><span class="p">(</span><span class="n">columnindex</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT16</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.int16value_byname">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.int16value_byname">[docs]</a>
    <span class="k">def</span> <span class="nf">int16value_byname</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnname</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">int16</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the int16-based record value for the specified column name.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.INT16`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue_byname</span><span class="p">(</span><span class="n">columnname</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT16</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.int32value">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.int32value">[docs]</a>
    <span class="k">def</span> <span class="nf">int32value</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the int32-based record value at the specified column index.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.INT32`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue</span><span class="p">(</span><span class="n">columnindex</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT32</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.int32value_byname">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.int32value_byname">[docs]</a>
    <span class="k">def</span> <span class="nf">int32value_byname</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnname</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the int32-based record value for the specified column name.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.INT32`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue_byname</span><span class="p">(</span><span class="n">columnname</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT32</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.int64value">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.int64value">[docs]</a>
    <span class="k">def</span> <span class="nf">int64value</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">int64</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the int64-based record value at the specified column index.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.INT64`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue</span><span class="p">(</span><span class="n">columnindex</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT64</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.int64value_byname">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.int64value_byname">[docs]</a>
    <span class="k">def</span> <span class="nf">int64value_byname</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnname</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">int64</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the int64-based record value for the specified column name.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.INT64`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue_byname</span><span class="p">(</span><span class="n">columnname</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT64</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.uint8value">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.uint8value">[docs]</a>
    <span class="k">def</span> <span class="nf">uint8value</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">uint8</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the uint8-based record value at the specified column index.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.UINT8`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue</span><span class="p">(</span><span class="n">columnindex</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT8</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.uint8value_byname">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.uint8value_byname">[docs]</a>
    <span class="k">def</span> <span class="nf">uint8value_byname</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnname</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">uint8</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the uint8-based record value for the specified column name.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.UINT8`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue_byname</span><span class="p">(</span><span class="n">columnname</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT8</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.uint16value">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.uint16value">[docs]</a>
    <span class="k">def</span> <span class="nf">uint16value</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">uint16</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the uint16-based record value at the specified column index.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.UINT16`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue</span><span class="p">(</span><span class="n">columnindex</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT16</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.uint16value_byname">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.uint16value_byname">[docs]</a>
    <span class="k">def</span> <span class="nf">uint16value_byname</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnname</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">uint16</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the uint16-based record value for the specified column name.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.UINT16`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue_byname</span><span class="p">(</span><span class="n">columnname</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT16</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.uint32value">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.uint32value">[docs]</a>
    <span class="k">def</span> <span class="nf">uint32value</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the uint32-based record value at the specified column index.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.UINT32`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue</span><span class="p">(</span><span class="n">columnindex</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT32</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.uint32value_byname">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.uint32value_byname">[docs]</a>
    <span class="k">def</span> <span class="nf">uint32value_byname</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnname</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the uint32-based record value for the specified column name.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.UINT32`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue_byname</span><span class="p">(</span><span class="n">columnname</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT32</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.uint64value">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.uint64value">[docs]</a>
    <span class="k">def</span> <span class="nf">uint64value</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the uint64-based record value at the specified column index.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.UINT64`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue</span><span class="p">(</span><span class="n">columnindex</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT64</span><span class="p">)</span></div>


<div class="viewcode-block" id="DataRow.uint64value_byname">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.uint64value_byname">[docs]</a>
    <span class="k">def</span> <span class="nf">uint64value_byname</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">columnname</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the uint64-based record value for the specified column name.</span>
<span class="sd">        Second parameter in tuple return value indicates if original value was None.</span>
<span class="sd">        An error will be returned if column type is not `DataType.UINT64`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_typevalue_byname</span><span class="p">(</span><span class="n">columnname</span><span class="p">,</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT64</span><span class="p">)</span></div>


    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="n">image</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;[&quot;</span><span class="p">]</span>

        <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_parent</span><span class="o">.</span><span class="n">columncount</span><span class="p">):</span>
            <span class="k">if</span> <span class="n">i</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="n">image</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s2">&quot;, &quot;</span><span class="p">)</span>

            <span class="n">strcol</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_parent</span><span class="o">.</span><span class="n">column</span><span class="p">(</span><span class="n">i</span><span class="p">)</span><span class="o">.</span><span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">STRING</span>

            <span class="k">if</span> <span class="n">strcol</span><span class="p">:</span>
                <span class="n">image</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="n">image</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">value_as_string</span><span class="p">(</span><span class="n">i</span><span class="p">))</span>

            <span class="k">if</span> <span class="n">strcol</span><span class="p">:</span>
                <span class="n">image</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

        <span class="n">image</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s2">&quot;]&quot;</span><span class="p">)</span>

        <span class="k">return</span> <span class="s2">&quot;&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">image</span><span class="p">)</span>

<div class="viewcode-block" id="DataRow.compare_datarowcolumns">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datarow.DataRow.compare_datarowcolumns">[docs]</a>
    <span class="nd">@staticmethod</span>
    <span class="k">def</span> <span class="nf">compare_datarowcolumns</span><span class="p">(</span><span class="n">leftrow</span><span class="p">:</span> <span class="n">DataRow</span><span class="p">,</span> <span class="n">rightrow</span><span class="p">:</span> <span class="n">DataRow</span><span class="p">,</span> <span class="n">columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">exactmatch</span><span class="p">:</span> <span class="nb">bool</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>  <span class="c1"># sourcery skip: low-code-quality</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Returns an integer comparing two `DataRow` column values for the specified column index.</span>
<span class="sd">        The result will be 0 if `leftrow`==`rightrow`, -1 if `leftrow` &lt; `rightrow`, and +1 if `leftrow` &gt; `rightrow`.</span>
<span class="sd">        An error will br returned if column index is out of range of either row, or row types do not match.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">leftcolumn</span> <span class="o">=</span> <span class="n">leftrow</span><span class="o">.</span><span class="n">parent</span><span class="o">.</span><span class="n">column</span><span class="p">(</span><span class="n">columnindex</span><span class="p">)</span>
        <span class="n">rightcolumn</span> <span class="o">=</span> <span class="n">rightrow</span><span class="o">.</span><span class="n">parent</span><span class="o">.</span><span class="n">column</span><span class="p">(</span><span class="n">columnindex</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">leftcolumn</span> <span class="ow">is</span> <span class="kc">None</span> <span class="ow">or</span> <span class="n">rightcolumn</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="mi">0</span><span class="p">,</span> <span class="ne">IndexError</span><span class="p">(</span><span class="s2">&quot;cannot compare, column index out of range&quot;</span><span class="p">)</span>

        <span class="n">lefttype</span> <span class="o">=</span> <span class="n">leftcolumn</span><span class="o">.</span><span class="n">datatype</span>
        <span class="n">righttype</span> <span class="o">=</span> <span class="n">rightcolumn</span><span class="o">.</span><span class="n">datatype</span>

        <span class="k">if</span> <span class="n">lefttype</span> <span class="o">!=</span> <span class="n">righttype</span><span class="p">:</span>
            <span class="k">return</span> <span class="mi">0</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;cannot compare, types do not match&quot;</span><span class="p">)</span>

        <span class="k">def</span> <span class="nf">nullcompare</span><span class="p">(</span><span class="n">lefthasvalue</span><span class="p">:</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">righthasvalue</span><span class="p">:</span> <span class="nb">bool</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">lefthasvalue</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">righthasvalue</span><span class="p">:</span>
                <span class="k">return</span> <span class="mi">0</span><span class="p">,</span> <span class="kc">None</span>

            <span class="k">return</span> <span class="mi">1</span> <span class="k">if</span> <span class="n">lefthasvalue</span> <span class="k">else</span> <span class="o">-</span><span class="mi">1</span><span class="p">,</span> <span class="kc">None</span>

        <span class="k">def</span> <span class="nf">typecompare</span><span class="p">(</span>
                <span class="n">leftrow_getvalue</span><span class="p">:</span> <span class="n">Callable</span><span class="p">[[</span><span class="nb">int</span><span class="p">],</span> <span class="n">Tuple</span><span class="p">[</span><span class="nb">object</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]],</span>
                <span class="n">rightrow_getvalue</span><span class="p">:</span> <span class="n">Callable</span><span class="p">[[</span><span class="nb">int</span><span class="p">],</span> <span class="n">Tuple</span><span class="p">[</span><span class="nb">object</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]])</span> <span class="o">-&gt;</span> \
                            <span class="n">Tuple</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>

            <span class="n">leftvalue</span><span class="p">,</span> <span class="n">leftnull</span><span class="p">,</span> <span class="n">lefterr</span> <span class="o">=</span> <span class="n">leftrow_getvalue</span><span class="p">(</span><span class="n">columnindex</span><span class="p">)</span>
            <span class="n">rightvalue</span><span class="p">,</span> <span class="n">rightnull</span><span class="p">,</span> <span class="n">righterr</span> <span class="o">=</span> <span class="n">rightrow_getvalue</span><span class="p">(</span><span class="n">columnindex</span><span class="p">)</span>

            <span class="n">lefthasvalue</span> <span class="o">=</span> <span class="ow">not</span> <span class="n">leftnull</span> <span class="ow">and</span> <span class="n">lefterr</span> <span class="ow">is</span> <span class="kc">None</span>
            <span class="n">righthasvalue</span> <span class="o">=</span> <span class="ow">not</span> <span class="n">rightnull</span> <span class="ow">and</span> <span class="n">righterr</span> <span class="ow">is</span> <span class="kc">None</span>

            <span class="k">if</span> <span class="n">lefthasvalue</span> <span class="ow">and</span> <span class="n">righthasvalue</span><span class="p">:</span>
                <span class="k">if</span> <span class="n">leftvalue</span> <span class="o">&lt;</span> <span class="n">rightvalue</span><span class="p">:</span>
                    <span class="k">return</span> <span class="o">-</span><span class="mi">1</span><span class="p">,</span> <span class="kc">None</span>

                <span class="k">return</span> <span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="k">if</span> <span class="n">leftvalue</span> <span class="o">&gt;</span> <span class="n">rightvalue</span> <span class="k">else</span> <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span>

            <span class="k">return</span> <span class="n">nullcompare</span><span class="p">(</span><span class="n">lefthasvalue</span><span class="p">,</span> <span class="n">righthasvalue</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">lefttype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">STRING</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">exactmatch</span><span class="p">:</span>
                <span class="k">def</span> <span class="nf">upperstringvalue</span><span class="p">(</span><span class="n">index</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">getvalue</span><span class="p">:</span> <span class="n">Callable</span><span class="p">[[</span><span class="nb">int</span><span class="p">],</span> <span class="n">Tuple</span><span class="p">[</span><span class="nb">object</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]])</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
                    <span class="n">value</span><span class="p">,</span> <span class="n">null</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="n">getvalue</span><span class="p">(</span><span class="n">index</span><span class="p">)</span>

                    <span class="k">if</span> <span class="ow">not</span> <span class="n">null</span> <span class="ow">and</span> <span class="n">err</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                        <span class="k">return</span> <span class="n">value</span><span class="o">.</span><span class="n">upper</span><span class="p">(),</span> <span class="kc">False</span><span class="p">,</span> <span class="kc">None</span>

                    <span class="k">return</span> <span class="n">value</span><span class="p">,</span> <span class="n">null</span><span class="p">,</span> <span class="n">err</span>

                <span class="k">def</span> <span class="nf">leftrowvalue</span><span class="p">(</span><span class="n">index</span><span class="p">):</span>
                    <span class="k">return</span> <span class="n">upperstringvalue</span><span class="p">(</span><span class="n">index</span><span class="p">,</span> <span class="n">leftrow</span><span class="o">.</span><span class="n">stringvalue</span><span class="p">)</span>

                <span class="k">def</span> <span class="nf">rightrowvalue</span><span class="p">(</span><span class="n">index</span><span class="p">):</span>
                    <span class="k">return</span> <span class="n">upperstringvalue</span><span class="p">(</span><span class="n">index</span><span class="p">,</span> <span class="n">rightrow</span><span class="o">.</span><span class="n">stringvalue</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">leftrowvalue</span> <span class="o">=</span> <span class="n">leftrow</span><span class="o">.</span><span class="n">stringvalue</span>
                <span class="n">rightrowvalue</span> <span class="o">=</span> <span class="n">rightrow</span><span class="o">.</span><span class="n">stringvalue</span>

            <span class="k">return</span> <span class="n">typecompare</span><span class="p">(</span><span class="n">leftrowvalue</span><span class="p">,</span> <span class="n">rightrowvalue</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">lefttype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">BOOLEAN</span><span class="p">:</span>
            <span class="n">leftvalue</span><span class="p">,</span> <span class="n">leftnull</span><span class="p">,</span> <span class="n">lefterr</span> <span class="o">=</span> <span class="n">leftrow</span><span class="o">.</span><span class="n">booleanvalue</span><span class="p">(</span><span class="n">columnindex</span><span class="p">)</span>
            <span class="n">rightvalue</span><span class="p">,</span> <span class="n">rightnull</span><span class="p">,</span> <span class="n">righterr</span> <span class="o">=</span> <span class="n">rightrow</span><span class="o">.</span><span class="n">booleanvalue</span><span class="p">(</span><span class="n">columnindex</span><span class="p">)</span>

            <span class="n">lefthasvalue</span> <span class="o">=</span> <span class="ow">not</span> <span class="n">leftnull</span> <span class="ow">and</span> <span class="n">lefterr</span> <span class="ow">is</span> <span class="kc">None</span>
            <span class="n">righthasvalue</span> <span class="o">=</span> <span class="ow">not</span> <span class="n">rightnull</span> <span class="ow">and</span> <span class="n">righterr</span> <span class="ow">is</span> <span class="kc">None</span>

            <span class="k">if</span> <span class="n">lefthasvalue</span> <span class="ow">and</span> <span class="n">righthasvalue</span><span class="p">:</span>
                <span class="k">if</span> <span class="n">leftvalue</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">rightvalue</span><span class="p">:</span>
                    <span class="k">return</span> <span class="o">-</span><span class="mi">1</span><span class="p">,</span> <span class="kc">None</span>

                <span class="k">return</span> <span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="k">if</span> <span class="ow">not</span> <span class="n">leftvalue</span> <span class="ow">and</span> <span class="n">rightvalue</span> <span class="k">else</span> <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span>

            <span class="k">return</span> <span class="n">nullcompare</span><span class="p">(</span><span class="n">lefthasvalue</span><span class="p">,</span> <span class="n">righthasvalue</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">lefttype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DATETIME</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">typecompare</span><span class="p">(</span><span class="n">leftrow</span><span class="o">.</span><span class="n">datetimevalue</span><span class="p">,</span> <span class="n">rightrow</span><span class="o">.</span><span class="n">datetimevalue</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">lefttype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">SINGLE</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">typecompare</span><span class="p">(</span><span class="n">leftrow</span><span class="o">.</span><span class="n">singlevalue</span><span class="p">,</span> <span class="n">rightrow</span><span class="o">.</span><span class="n">singlevalue</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">lefttype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DOUBLE</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">typecompare</span><span class="p">(</span><span class="n">leftrow</span><span class="o">.</span><span class="n">doublevalue</span><span class="p">,</span> <span class="n">rightrow</span><span class="o">.</span><span class="n">doublevalue</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">lefttype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DECIMAL</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">typecompare</span><span class="p">(</span><span class="n">leftrow</span><span class="o">.</span><span class="n">decimalvalue</span><span class="p">,</span> <span class="n">rightrow</span><span class="o">.</span><span class="n">decimalvalue</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">lefttype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">GUID</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">typecompare</span><span class="p">(</span><span class="n">leftrow</span><span class="o">.</span><span class="n">guidvalue</span><span class="p">,</span> <span class="n">rightrow</span><span class="o">.</span><span class="n">guidvalue</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">lefttype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT8</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">typecompare</span><span class="p">(</span><span class="n">leftrow</span><span class="o">.</span><span class="n">int8value</span><span class="p">,</span> <span class="n">rightrow</span><span class="o">.</span><span class="n">int8value</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">lefttype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT16</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">typecompare</span><span class="p">(</span><span class="n">leftrow</span><span class="o">.</span><span class="n">int16value</span><span class="p">,</span> <span class="n">rightrow</span><span class="o">.</span><span class="n">int16value</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">lefttype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT32</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">typecompare</span><span class="p">(</span><span class="n">leftrow</span><span class="o">.</span><span class="n">int32value</span><span class="p">,</span> <span class="n">rightrow</span><span class="o">.</span><span class="n">int32value</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">lefttype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT64</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">typecompare</span><span class="p">(</span><span class="n">leftrow</span><span class="o">.</span><span class="n">int64value</span><span class="p">,</span> <span class="n">rightrow</span><span class="o">.</span><span class="n">int64value</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">lefttype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT8</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">typecompare</span><span class="p">(</span><span class="n">leftrow</span><span class="o">.</span><span class="n">uint8value</span><span class="p">,</span> <span class="n">rightrow</span><span class="o">.</span><span class="n">uint8value</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">lefttype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT16</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">typecompare</span><span class="p">(</span><span class="n">leftrow</span><span class="o">.</span><span class="n">uint16value</span><span class="p">,</span> <span class="n">rightrow</span><span class="o">.</span><span class="n">uint16value</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">lefttype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT32</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">typecompare</span><span class="p">(</span><span class="n">leftrow</span><span class="o">.</span><span class="n">uint32value</span><span class="p">,</span> <span class="n">rightrow</span><span class="o">.</span><span class="n">uint32value</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">lefttype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT64</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">typecompare</span><span class="p">(</span><span class="n">leftrow</span><span class="o">.</span><span class="n">uint64value</span><span class="p">,</span> <span class="n">rightrow</span><span class="o">.</span><span class="n">uint64value</span><span class="p">)</span>

        <span class="k">return</span> <span class="mi">0</span><span class="p">,</span> <span class="ne">TypeError</span><span class="p">(</span><span class="s2">&quot;unexpected column data type encountered&quot;</span><span class="p">)</span></div>
</div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
  
 
<hr />
<p>
    <a href="https://github.com/sttp/pyapi">GitHub Home</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://pypi.org/project/sttpapi/" target="_blank">PyPi Package: sttpapi</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://sttp.github.io/documentation/" target="_blank">General STTP Documentation</a>
</p>
<br /><br />
Copyright © 2022, <a href="https://gridprotectionalliance.org/" target="_blank">Grid&nbsp;Protection&nbsp;Alliance</a>
<a href="https://github.com/sttp/pyapi">
    <img align="right" style="margin-top: -50px"
        src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/LockPython_64High.png">
</a>


</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>