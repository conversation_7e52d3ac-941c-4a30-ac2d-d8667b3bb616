

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../../../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>sttp.data.datatype &mdash; sttp/pyapi 0.6.4 documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=92fd9be5" />
      <link rel="stylesheet" type="text/css" href="../../../_static/css/theme.css?v=8209df7b" />

  
    <link rel="shortcut icon" href="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/favicon.ico"/>
      <script src="../../../_static/jquery.js?v=5d32c60e"></script>
      <script src="../../../_static/_sphinx_javascript_frameworks_compat.js?v=bbec6902"></script>
      <script src="../../../_static/documentation_options.js?v=b1f64a84"></script>
      <script src="../../../_static/doctools.js?v=92e14aea"></script>
      <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="../../../index.html" class="icon icon-home">
            sttp/pyapi
              <img src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/sttp.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <!-- Local TOC -->
              <div class="local-toc"></div>
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../../index.html">sttp/pyapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../../index.html">Module code</a></li>
      <li class="breadcrumb-item active">sttp.data.datatype</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for sttp.data.datatype</h1><div class="highlight"><pre>
<span></span><span class="c1"># ******************************************************************************************************</span>
<span class="c1">#  datatype.py - Gbtc</span>
<span class="c1">#</span>
<span class="c1">#  Copyright © 2022, Grid Protection Alliance.  All Rights Reserved.</span>
<span class="c1">#</span>
<span class="c1">#  Licensed to the Grid Protection Alliance (GPA) under one or more contributor license agreements. See</span>
<span class="c1">#  the NOTICE file distributed with this work for additional information regarding copyright ownership.</span>
<span class="c1">#  The GPA licenses this file to you under the MIT License (MIT), the &quot;License&quot;; you may not use this</span>
<span class="c1">#  file except in compliance with the License. You may obtain a copy of the License at:</span>
<span class="c1">#</span>
<span class="c1">#      http://opensource.org/licenses/MIT</span>
<span class="c1">#</span>
<span class="c1">#  Unless agreed to in writing, the subject software distributed under the License is distributed on an</span>
<span class="c1">#  &quot;AS-IS&quot; BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. Refer to the</span>
<span class="c1">#  License for the specific language governing permissions and limitations.</span>
<span class="c1">#</span>
<span class="c1">#  Code Modification History:</span>
<span class="c1">#  ----------------------------------------------------------------------------------------------------</span>
<span class="c1">#  08/26/2022 - J. Ritchie Carroll</span>
<span class="c1">#       Generated original version of source code.</span>
<span class="c1">#</span>
<span class="c1"># ******************************************************************************************************</span>

<span class="kn">from</span> <span class="nn">gsf</span> <span class="kn">import</span> <span class="n">Empty</span>
<span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">IntEnum</span>
<span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">Optional</span><span class="p">,</span> <span class="n">Tuple</span>


<div class="viewcode-block" id="DataType">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datatype.DataType">[docs]</a>
<span class="k">class</span> <span class="nc">DataType</span><span class="p">(</span><span class="n">IntEnum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Enumeration of the possible data types for a `DataColumn`.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">STRING</span> <span class="o">=</span> <span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents a Python `str` data type.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">BOOLEAN</span> <span class="o">=</span> <span class="mi">1</span><span class="p">,</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents a Python `bool` data type.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">DATETIME</span> <span class="o">=</span> <span class="mi">2</span><span class="p">,</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents a Python `datetime` data type.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">SINGLE</span> <span class="o">=</span> <span class="mi">3</span><span class="p">,</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents a Python `numpy.float32` data type.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">DOUBLE</span> <span class="o">=</span> <span class="mi">4</span><span class="p">,</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents a Python `numpy.float64` data type.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">DECIMAL</span> <span class="o">=</span> <span class="mi">5</span><span class="p">,</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents a Python `decimal.Decimal` data type.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">GUID</span> <span class="o">=</span> <span class="mi">6</span><span class="p">,</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents a Python `uuid.UUID` data type.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">INT8</span> <span class="o">=</span> <span class="mi">7</span><span class="p">,</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents a Python `numpy.int8` data type.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">INT16</span> <span class="o">=</span> <span class="mi">8</span><span class="p">,</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents a Python `numpy.int16` data type.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">INT32</span> <span class="o">=</span> <span class="mi">9</span><span class="p">,</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents a Python `numpy.int32` data type.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">INT64</span> <span class="o">=</span> <span class="mi">10</span><span class="p">,</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents a Python `numpy.int64` data type.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">UINT8</span> <span class="o">=</span> <span class="mi">11</span><span class="p">,</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents a Python `numpy.uint8` data type.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">UINT16</span> <span class="o">=</span> <span class="mi">12</span><span class="p">,</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents a Python `numpy.uint16` data type.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">UINT32</span> <span class="o">=</span> <span class="mi">13</span><span class="p">,</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents a Python `numpy.uint32` data type.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">UINT64</span> <span class="o">=</span> <span class="mi">14</span><span class="p">,</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents a Python `numpy.uint64` data type.</span>
<span class="sd">    &quot;&quot;&quot;</span></div>



<div class="viewcode-block" id="default_datatype">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datatype.default_datatype">[docs]</a>
<span class="k">def</span> <span class="nf">default_datatype</span><span class="p">(</span><span class="n">datatype</span><span class="p">:</span> <span class="n">DataType</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">object</span><span class="p">:</span>  <span class="c1"># sourcery skip: assign-if-exp, reintroduce-else</span>
    <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">STRING</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Empty</span><span class="o">.</span><span class="n">STRING</span>
    <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">BOOLEAN</span><span class="p">:</span>
        <span class="k">return</span> <span class="kc">False</span>
    <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DATETIME</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Empty</span><span class="o">.</span><span class="n">DATETIME</span>
    <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">SINGLE</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Empty</span><span class="o">.</span><span class="n">SINGLE</span>
    <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DOUBLE</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Empty</span><span class="o">.</span><span class="n">DOUBLE</span>
    <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DECIMAL</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Empty</span><span class="o">.</span><span class="n">DECIMAL</span>
    <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">GUID</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Empty</span><span class="o">.</span><span class="n">GUID</span>
    <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT8</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Empty</span><span class="o">.</span><span class="n">INT8</span>
    <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT16</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Empty</span><span class="o">.</span><span class="n">INT16</span>
    <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT32</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Empty</span><span class="o">.</span><span class="n">INT32</span>
    <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT64</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Empty</span><span class="o">.</span><span class="n">INT64</span>
    <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT8</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Empty</span><span class="o">.</span><span class="n">UINT8</span>
    <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT16</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Empty</span><span class="o">.</span><span class="n">UINT16</span>
    <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT32</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Empty</span><span class="o">.</span><span class="n">UINT32</span>
    <span class="k">if</span> <span class="n">datatype</span> <span class="o">==</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT64</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">Empty</span><span class="o">.</span><span class="n">UINT64</span>

    <span class="k">return</span> <span class="kc">None</span></div>


<div class="viewcode-block" id="parse_xsddatatype">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.datatype.parse_xsddatatype">[docs]</a>
<span class="k">def</span> <span class="nf">parse_xsddatatype</span><span class="p">(</span><span class="n">xsdtypename</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">extdatatype</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">DataType</span><span class="p">,</span> <span class="nb">bool</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Gets the `DataType` from the provided XSD data type. Return tuple includes boolean</span>
<span class="sd">    value that determines if parse was successful. See XML Schema Language Datatypes</span>
<span class="sd">    for possible xsd type name values: https://www.w3.org/TR/xmlschema-2/</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">if</span> <span class="n">xsdtypename</span> <span class="o">==</span> <span class="s2">&quot;string&quot;</span><span class="p">:</span>
        <span class="k">if</span> <span class="n">extdatatype</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">extdatatype</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s2">&quot;System.Guid&quot;</span><span class="p">):</span>
            <span class="k">return</span> <span class="n">DataType</span><span class="o">.</span><span class="n">GUID</span><span class="p">,</span> <span class="kc">True</span>

        <span class="k">return</span> <span class="n">DataType</span><span class="o">.</span><span class="n">STRING</span><span class="p">,</span> <span class="kc">True</span>

    <span class="k">if</span> <span class="n">xsdtypename</span> <span class="o">==</span> <span class="s2">&quot;boolean&quot;</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">DataType</span><span class="o">.</span><span class="n">BOOLEAN</span><span class="p">,</span> <span class="kc">True</span>

    <span class="k">if</span> <span class="n">xsdtypename</span> <span class="o">==</span> <span class="s2">&quot;dateTime&quot;</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DATETIME</span><span class="p">,</span> <span class="kc">True</span>

    <span class="k">if</span> <span class="n">xsdtypename</span> <span class="o">==</span> <span class="s2">&quot;float&quot;</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">DataType</span><span class="o">.</span><span class="n">SINGLE</span><span class="p">,</span> <span class="kc">True</span>

    <span class="k">if</span> <span class="n">xsdtypename</span> <span class="o">==</span> <span class="s2">&quot;double&quot;</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DOUBLE</span><span class="p">,</span> <span class="kc">True</span>

    <span class="k">if</span> <span class="n">xsdtypename</span> <span class="o">==</span> <span class="s2">&quot;decimal&quot;</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">DataType</span><span class="o">.</span><span class="n">DECIMAL</span><span class="p">,</span> <span class="kc">True</span>

    <span class="k">if</span> <span class="n">xsdtypename</span> <span class="o">==</span> <span class="s2">&quot;byte&quot;</span><span class="p">:</span>  <span class="c1"># XSD defines byte as signed 8-bit int</span>
        <span class="k">return</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT8</span><span class="p">,</span> <span class="kc">True</span>

    <span class="k">if</span> <span class="n">xsdtypename</span> <span class="o">==</span> <span class="s2">&quot;short&quot;</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT16</span><span class="p">,</span> <span class="kc">True</span>

    <span class="k">if</span> <span class="n">xsdtypename</span> <span class="o">==</span> <span class="s2">&quot;int&quot;</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT32</span><span class="p">,</span> <span class="kc">True</span>

    <span class="k">if</span> <span class="n">xsdtypename</span> <span class="o">==</span> <span class="s2">&quot;long&quot;</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">DataType</span><span class="o">.</span><span class="n">INT64</span><span class="p">,</span> <span class="kc">True</span>

    <span class="k">if</span> <span class="n">xsdtypename</span> <span class="o">==</span> <span class="s2">&quot;unsignedByte&quot;</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT8</span><span class="p">,</span> <span class="kc">True</span>

    <span class="k">if</span> <span class="n">xsdtypename</span> <span class="o">==</span> <span class="s2">&quot;unsignedShort&quot;</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT16</span><span class="p">,</span> <span class="kc">True</span>

    <span class="k">if</span> <span class="n">xsdtypename</span> <span class="o">==</span> <span class="s2">&quot;unsignedInt&quot;</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT32</span><span class="p">,</span> <span class="kc">True</span>

    <span class="k">if</span> <span class="n">xsdtypename</span> <span class="o">==</span> <span class="s2">&quot;unsignedLong&quot;</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">DataType</span><span class="o">.</span><span class="n">UINT64</span><span class="p">,</span> <span class="kc">True</span>

    <span class="k">return</span> <span class="n">DataType</span><span class="o">.</span><span class="n">STRING</span><span class="p">,</span> <span class="kc">False</span></div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
  
 
<hr />
<p>
    <a href="https://github.com/sttp/pyapi">GitHub Home</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://pypi.org/project/sttpapi/" target="_blank">PyPi Package: sttpapi</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://sttp.github.io/documentation/" target="_blank">General STTP Documentation</a>
</p>
<br /><br />
Copyright © 2022, <a href="https://gridprotectionalliance.org/" target="_blank">Grid&nbsp;Protection&nbsp;Alliance</a>
<a href="https://github.com/sttp/pyapi">
    <img align="right" style="margin-top: -50px"
        src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/LockPython_64High.png">
</a>


</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>