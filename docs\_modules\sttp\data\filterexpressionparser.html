

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../../../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>sttp.data.filterexpressionparser &mdash; sttp/pyapi 0.6.4 documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=92fd9be5" />
      <link rel="stylesheet" type="text/css" href="../../../_static/css/theme.css?v=8209df7b" />

  
    <link rel="shortcut icon" href="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/favicon.ico"/>
      <script src="../../../_static/jquery.js?v=5d32c60e"></script>
      <script src="../../../_static/_sphinx_javascript_frameworks_compat.js?v=bbec6902"></script>
      <script src="../../../_static/documentation_options.js?v=b1f64a84"></script>
      <script src="../../../_static/doctools.js?v=92e14aea"></script>
      <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="../../../index.html" class="icon icon-home">
            sttp/pyapi
              <img src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/sttp.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <!-- Local TOC -->
              <div class="local-toc"></div>
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../../index.html">sttp/pyapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../../index.html">Module code</a></li>
      <li class="breadcrumb-item active">sttp.data.filterexpressionparser</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for sttp.data.filterexpressionparser</h1><div class="highlight"><pre>
<span></span><span class="c1"># ******************************************************************************************************</span>
<span class="c1">#  filterexpressionparser.py - Gbtc</span>
<span class="c1">#</span>
<span class="c1">#  Copyright © 2022, Grid Protection Alliance.  All Rights Reserved.</span>
<span class="c1">#</span>
<span class="c1">#  Licensed to the Grid Protection Alliance (GPA) under one or more contributor license agreements. See</span>
<span class="c1">#  the NOTICE file distributed with this work for additional information regarding copyright ownership.</span>
<span class="c1">#  The GPA licenses this file to you under the MIT License (MIT), the &quot;License&quot;; you may not use this</span>
<span class="c1">#  file except in compliance with the License. You may obtain a copy of the License at:</span>
<span class="c1">#</span>
<span class="c1">#      http://opensource.org/licenses/MIT</span>
<span class="c1">#</span>
<span class="c1">#  Unless agreed to in writing, the subject software distributed under the License is distributed on an</span>
<span class="c1">#  &quot;AS-IS&quot; BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. Refer to the</span>
<span class="c1">#  License for the specific language governing permissions and limitations.</span>
<span class="c1">#</span>
<span class="c1">#  Code Modification History:</span>
<span class="c1">#  ----------------------------------------------------------------------------------------------------</span>
<span class="c1">#  09/07/2022 - J. Ritchie Carroll</span>
<span class="c1">#       Generated original version of source code.</span>
<span class="c1">#</span>
<span class="c1"># ******************************************************************************************************</span>

<span class="kn">from</span> <span class="nn">gsf</span> <span class="kn">import</span> <span class="n">Convert</span><span class="p">,</span> <span class="n">Empty</span><span class="p">,</span> <span class="n">Limits</span>
<span class="kn">from</span> <span class="nn">.dataset</span> <span class="kn">import</span> <span class="n">DataSet</span>
<span class="kn">from</span> <span class="nn">.datatable</span> <span class="kn">import</span> <span class="n">DataTable</span>
<span class="kn">from</span> <span class="nn">.datarow</span> <span class="kn">import</span> <span class="n">DataRow</span>
<span class="kn">from</span> <span class="nn">.tableidfields</span> <span class="kn">import</span> <span class="n">TableIDFields</span><span class="p">,</span> <span class="n">DEFAULT_TABLEIDFIELDS</span>
<span class="kn">from</span> <span class="nn">.expressiontree</span> <span class="kn">import</span> <span class="n">ExpressionTree</span>
<span class="kn">from</span> <span class="nn">.callbackerrorlistener</span> <span class="kn">import</span> <span class="n">CallbackErrorListener</span>
<span class="kn">from</span> <span class="nn">.expression</span> <span class="kn">import</span> <span class="n">Expression</span>
<span class="kn">from</span> <span class="nn">.columnexpression</span> <span class="kn">import</span> <span class="n">ColumnExpression</span>
<span class="kn">from</span> <span class="nn">.inlistexpression</span> <span class="kn">import</span> <span class="n">InListExpression</span>
<span class="kn">from</span> <span class="nn">.functionexpression</span> <span class="kn">import</span> <span class="n">FunctionExpression</span>
<span class="kn">from</span> <span class="nn">.operatorexpression</span> <span class="kn">import</span> <span class="n">OperatorExpression</span>
<span class="kn">from</span> <span class="nn">.unaryexpression</span> <span class="kn">import</span> <span class="n">UnaryExpression</span>
<span class="kn">from</span> <span class="nn">.valueexpression</span> <span class="kn">import</span> <span class="n">ValueExpression</span><span class="p">,</span> <span class="n">TRUEVALUE</span><span class="p">,</span> <span class="n">FALSEVALUE</span><span class="p">,</span> <span class="n">NULLVALUE</span>
<span class="kn">from</span> <span class="nn">.constants</span> <span class="kn">import</span> <span class="n">ExpressionValueType</span><span class="p">,</span> <span class="n">ExpressionUnaryType</span>
<span class="kn">from</span> <span class="nn">.constants</span> <span class="kn">import</span> <span class="n">ExpressionFunctionType</span><span class="p">,</span> <span class="n">ExpressionOperatorType</span>
<span class="kn">from</span> <span class="nn">.orderbyterm</span> <span class="kn">import</span> <span class="n">OrderByTerm</span>
<span class="kn">from</span> <span class="nn">.errors</span> <span class="kn">import</span> <span class="n">EvaluateError</span>
<span class="kn">from</span> <span class="nn">.parser.FilterExpressionSyntaxListener</span> <span class="kn">import</span> <span class="n">FilterExpressionSyntaxListener</span> <span class="k">as</span> <span class="n">ExpressionListener</span>
<span class="kn">from</span> <span class="nn">.parser.FilterExpressionSyntaxLexer</span> <span class="kn">import</span> <span class="n">FilterExpressionSyntaxLexer</span> <span class="k">as</span> <span class="n">ExpressionLexer</span>
<span class="kn">from</span> <span class="nn">.parser.FilterExpressionSyntaxParser</span> <span class="kn">import</span> <span class="n">FilterExpressionSyntaxParser</span> <span class="k">as</span> <span class="n">ExpressionParser</span>
<span class="kn">from</span> <span class="nn">antlr4</span> <span class="kn">import</span> <span class="n">CommonTokenStream</span><span class="p">,</span> <span class="n">InputStream</span><span class="p">,</span> <span class="n">ParseTreeWalker</span>
<span class="kn">from</span> <span class="nn">antlr4.ParserRuleContext</span> <span class="kn">import</span> <span class="n">ParserRuleContext</span>
<span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">Callable</span><span class="p">,</span> <span class="n">Dict</span><span class="p">,</span> <span class="n">List</span><span class="p">,</span> <span class="n">Optional</span><span class="p">,</span> <span class="n">Set</span><span class="p">,</span> <span class="n">Tuple</span>
<span class="kn">from</span> <span class="nn">decimal</span> <span class="kn">import</span> <span class="n">Decimal</span>
<span class="kn">from</span> <span class="nn">datetime</span> <span class="kn">import</span> <span class="n">datetime</span>
<span class="kn">from</span> <span class="nn">uuid</span> <span class="kn">import</span> <span class="n">UUID</span>


<div class="viewcode-block" id="FilterExpressionParser">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser">[docs]</a>
<span class="k">class</span> <span class="nc">FilterExpressionParser</span><span class="p">(</span><span class="n">ExpressionListener</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents a parser for STTP filter expressions.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                 <span class="n">filterexpression</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
                 <span class="n">suppress_console_erroroutput</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">,</span>
                 <span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_inputstream</span> <span class="o">=</span> <span class="n">InputStream</span><span class="p">(</span><span class="n">filterexpression</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_lexer</span> <span class="o">=</span> <span class="n">ExpressionLexer</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_inputstream</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_tokens</span> <span class="o">=</span> <span class="n">CommonTokenStream</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_lexer</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_parser</span> <span class="o">=</span> <span class="n">ExpressionParser</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_tokens</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_errorlistener</span><span class="p">:</span> <span class="n">CallbackErrorListener</span> <span class="o">=</span> <span class="n">CallbackErrorListener</span><span class="p">()</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_rows</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">DataRow</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_rowset</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Set</span><span class="p">[</span><span class="n">DataRow</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_signalids</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">UUID</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_signalidset</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Set</span><span class="p">[</span><span class="n">UUID</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_filterexpression_statementcount</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">0</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_active_expressiontree</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">ExpressionTree</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_expressiontrees</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">ExpressionTree</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_expressions</span><span class="p">:</span> <span class="n">Dict</span><span class="p">[</span><span class="n">ParserRuleContext</span><span class="p">,</span> <span class="n">Expression</span><span class="p">]</span> <span class="o">=</span> <span class="p">{}</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">dataset</span><span class="p">:</span> <span class="n">DataSet</span> <span class="o">=</span> <span class="kc">None</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Defines the source metadata used for parsing the filter expression.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">primary_tablename</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Empty</span><span class="o">.</span><span class="n">STRING</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Defines the name of the table to use in the DataSet when filter expressions do not specify</span>
<span class="sd">        a table name, e.g., direct signal identification. See:</span>
<span class="sd">        https://sttp.github.io/documentation/filter-expressions/#direct-signal-identification</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">tableidfields_map</span><span class="p">:</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">TableIDFields</span><span class="p">]</span> <span class="o">=</span> <span class="p">{}</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Defines a map of table ID fields associated with table names.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">track_filteredrows</span> <span class="o">=</span> <span class="kc">True</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Defines a flag that enables tracking of matching rows during filter expression evaluation.</span>
<span class="sd">        Value defaults to True. Set value to False and set `track_filteredsignalids` to True if</span>
<span class="sd">        only signal IDs are needed post filter expression evaluation.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">track_filteredsignalids</span> <span class="o">=</span> <span class="kc">False</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Defines a flag that enables tracking of matching signal IDs during filter expression evaluation.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="n">suppress_console_erroroutput</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_parser</span><span class="o">.</span><span class="n">removeErrorListeners</span><span class="p">()</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_parser</span><span class="o">.</span><span class="n">addErrorListener</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_errorlistener</span><span class="p">)</span>

<div class="viewcode-block" id="FilterExpressionParser.from_dataset">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.from_dataset">[docs]</a>
    <span class="nd">@staticmethod</span>
    <span class="k">def</span> <span class="nf">from_dataset</span><span class="p">(</span><span class="n">dataset</span><span class="p">:</span> <span class="n">DataSet</span><span class="p">,</span>
                     <span class="n">filterexpression</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
                     <span class="n">primary_table</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
                     <span class="n">tableidfields</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">TableIDFields</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
                     <span class="n">suppress_console_erroroutput</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">,</span>
                     <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="s2">&quot;FilterExpressionParser&quot;</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Creates a new filter expression parser associated with the provided `dataSet`</span>
<span class="sd">        and provided table details. Error will be returned if `dataset` parameter is</span>
<span class="sd">        None or the `filterexpression` is empty.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="n">dataset</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;dataset parameter is None&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="ow">not</span> <span class="n">filterexpression</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;filterexpression parameter is empty&quot;</span><span class="p">)</span>

        <span class="n">parser</span> <span class="o">=</span> <span class="n">FilterExpressionParser</span><span class="p">(</span><span class="n">filterexpression</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">)</span>
        <span class="n">parser</span><span class="o">.</span><span class="n">dataset</span> <span class="o">=</span> <span class="n">dataset</span>

        <span class="k">if</span> <span class="n">primary_table</span><span class="p">:</span>
            <span class="n">parser</span><span class="o">.</span><span class="n">primary_tablename</span> <span class="o">=</span> <span class="n">primary_table</span>

            <span class="k">if</span> <span class="n">tableidfields</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="n">parser</span><span class="o">.</span><span class="n">tableidfields_map</span><span class="p">[</span><span class="n">primary_table</span><span class="p">]</span> <span class="o">=</span> <span class="n">DEFAULT_TABLEIDFIELDS</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">parser</span><span class="o">.</span><span class="n">tableidfields_map</span><span class="p">[</span><span class="n">primary_table</span><span class="p">]</span> <span class="o">=</span> <span class="n">tableidfields</span>

        <span class="k">return</span> <span class="n">parser</span><span class="p">,</span> <span class="kc">None</span></div>


<div class="viewcode-block" id="FilterExpressionParser.set_parsingexception_callback">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.set_parsingexception_callback">[docs]</a>
    <span class="k">def</span> <span class="nf">set_parsingexception_callback</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">callback</span><span class="p">:</span> <span class="n">Callable</span><span class="p">[[</span><span class="nb">str</span><span class="p">],</span> <span class="kc">None</span><span class="p">]):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Registers a callback for receiving parsing exception messages.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_errorlistener</span><span class="o">.</span><span class="n">parsingexception_callback</span> <span class="o">=</span> <span class="n">callback</span></div>


    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">expressiontrees</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">List</span><span class="p">[</span><span class="n">ExpressionTree</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Returns the list of expression trees parsed from the filter expression.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_expressiontrees</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
            <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_visit_parsetreenodes</span><span class="p">()</span>

            <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="n">err</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_expressiontrees</span><span class="p">,</span> <span class="kc">None</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">filtered_rows</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">DataRow</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the rows matching the parsed filter expression.</span>

<span class="sd">        Results could contain duplicates if source `DataSet` has duplicated rows.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_rows</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">filtered_rowset</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Set</span><span class="p">[</span><span class="n">DataRow</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the unique row set matching the parsed filter expression.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_initialize_set_operations</span><span class="p">()</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_rowset</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">filtered_signalids</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">UUID</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the Guid-based signal IDs matching the parsed filter expression.</span>

<span class="sd">        Results could contain duplicates if source `DataSet` has duplicated rows.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_signalids</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">filtered_signalidset</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Set</span><span class="p">[</span><span class="n">UUID</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the unique Guid-based signal ID set matching the parsed filter expression.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_initialize_set_operations</span><span class="p">()</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_signalidset</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">filterexpression_statementcount</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the number of filter expression statements encountered while parsing.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_filterexpression_statementcount</span>

<div class="viewcode-block" id="FilterExpressionParser.table">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.table">[docs]</a>
    <span class="k">def</span> <span class="nf">table</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">tablename</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">DataTable</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the DataTable for the specified tableName from the FilterExpressionParser DataSet.</span>

<span class="sd">        An error will be returned if no DataSet has been defined or the tableName cannot be found.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">dataset</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;no DataSet has been defined&quot;</span><span class="p">)</span>

        <span class="n">table</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">dataset</span><span class="o">.</span><span class="n">table</span><span class="p">(</span><span class="n">tablename</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">table</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to find table </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">tablename</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2"> in DataSet&quot;</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">table</span><span class="p">,</span> <span class="kc">None</span></div>


<div class="viewcode-block" id="FilterExpressionParser.evaluate">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.evaluate">[docs]</a>
    <span class="k">def</span> <span class="nf">evaluate</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">applylimit</span><span class="p">:</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">applysort</span><span class="p">:</span> <span class="nb">bool</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Evaluate parses each statement in the filter expression and tracks the results.</span>

<span class="sd">        Filter expressions can contain multiple statements, separated by semi-colons, where each</span>
<span class="sd">        statement results in a unique expression tree; this function returns the combined results</span>
<span class="sd">        of each encountered filter expression statement, yielding all filtered rows and/or signal</span>
<span class="sd">        IDs that match the target filter expression. The `applylimit` and `applysort` flags</span>
<span class="sd">        determine if any encountered &quot;TOP&quot; limit and &quot;ORDER BY&quot; sorting clauses will be respected.</span>
<span class="sd">        Access matching results via `filtered_rows` and/or `filtered_signalids`, or related set</span>
<span class="sd">        functions. An error will be returned if expression fails to parse or any row expression</span>
<span class="sd">        evaluation fails.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">dataset</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;no DataSet has been defined&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">track_filteredrows</span> <span class="ow">and</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">track_filteredsignalids</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;no use in evaluating filter expression, neither filtered rows nor signal IDs have been set for tracking&quot;</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_filterexpression_statementcount</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_rows</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_rowset</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_signalids</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_signalidset</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_expressiontrees</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_expressions</span> <span class="o">=</span> <span class="p">{}</span>

        <span class="c1"># Visiting tree nodes will automatically add literals to the the filtered results</span>
        <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_visit_parsetreenodes</span><span class="p">()</span>

        <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">err</span>

        <span class="c1"># Each statement in the filter expression will have its own expression tree, evaluate each</span>
        <span class="k">for</span> <span class="n">expressiontree</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_expressiontrees</span><span class="p">:</span>
            <span class="n">tablename</span> <span class="o">=</span> <span class="n">expressiontree</span><span class="o">.</span><span class="n">tablename</span>

            <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">tablename</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">primary_tablename</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                    <span class="k">return</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;no table name defined for expression tree nor is any PrimaryTableName defined&quot;</span><span class="p">)</span>

                <span class="n">tablename</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">primary_tablename</span>

            <span class="n">table</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">table</span><span class="p">(</span><span class="n">tablename</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">err</span>

            <span class="k">def</span> <span class="nf">where_predicate</span><span class="p">(</span><span class="n">result_expression</span><span class="p">:</span> <span class="n">ValueExpression</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
                <span class="k">if</span> <span class="n">result_expression</span><span class="o">.</span><span class="n">valuetype</span> <span class="o">==</span> <span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">BOOLEAN</span><span class="p">:</span>
                    <span class="k">return</span> <span class="n">result_expression</span><span class="o">.</span><span class="n">_booleanvalue</span><span class="p">(),</span> <span class="kc">None</span>

                <span class="c1"># Filtered results will already have any matched literals</span>
                <span class="k">return</span> <span class="n">FALSEVALUE</span><span class="o">.</span><span class="n">_booleanvalue</span><span class="p">(),</span> <span class="kc">None</span>

            <span class="c1"># Select all matching boolean results from expression tree evaluated for each table row</span>
            <span class="n">matchedrows</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="n">expressiontree</span><span class="o">.</span><span class="n">selectwhere</span><span class="p">(</span><span class="n">table</span><span class="p">,</span> <span class="n">where_predicate</span><span class="p">,</span> <span class="n">applylimit</span><span class="p">,</span> <span class="n">applysort</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">err</span>

            <span class="n">signalid_columnindex</span> <span class="o">=</span> <span class="o">-</span><span class="mi">1</span>

            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">track_filteredsignalids</span><span class="p">:</span>
                <span class="n">primary_tableidfields</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">tableidfields_map</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">table</span><span class="o">.</span><span class="n">name</span><span class="p">)</span>

                <span class="k">if</span> <span class="n">primary_tableidfields</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                    <span class="k">return</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to find ID fields record for table </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">table</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

                <span class="n">signalid_column</span> <span class="o">=</span> <span class="n">table</span><span class="o">.</span><span class="n">column_byname</span><span class="p">(</span><span class="n">primary_tableidfields</span><span class="o">.</span><span class="n">signalid_fieldname</span><span class="p">)</span>

                <span class="k">if</span> <span class="n">signalid_column</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                    <span class="k">return</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to find signal ID column </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">primary_tableidfields</span><span class="o">.</span><span class="n">signalid_fieldname</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2"> in table </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">table</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

                <span class="n">signalid_columnindex</span> <span class="o">=</span> <span class="n">signalid_column</span><span class="o">.</span><span class="n">index</span>

            <span class="k">for</span> <span class="n">matchedrow</span> <span class="ow">in</span> <span class="n">matchedrows</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_add_matchedrow</span><span class="p">(</span><span class="n">matchedrow</span><span class="p">,</span> <span class="n">signalid_columnindex</span><span class="p">)</span>

        <span class="k">return</span> <span class="kc">None</span></div>


    <span class="k">def</span> <span class="nf">_visit_parsetreenodes</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]:</span>
        <span class="n">err</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

        <span class="k">try</span><span class="p">:</span>
            <span class="c1"># Create a parse tree and start visiting listener methods</span>
            <span class="n">walker</span> <span class="o">=</span> <span class="n">ParseTreeWalker</span><span class="p">()</span>
            <span class="n">parsetree</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_parser</span><span class="o">.</span><span class="n">parse</span><span class="p">()</span>
            <span class="n">walker</span><span class="o">.</span><span class="n">walk</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">parsetree</span><span class="p">)</span>
        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">ex</span><span class="p">:</span>
            <span class="n">err</span> <span class="o">=</span> <span class="n">ex</span>

        <span class="k">return</span> <span class="n">err</span>

    <span class="k">def</span> <span class="nf">_initialize_set_operations</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="c1"># As an optimization, set operations are not engaged until second filter expression statement</span>
        <span class="c1"># is encountered, only then will duplicate results be a concern. Note that only using a set</span>
        <span class="c1"># is not an option because results can be sorted with the &quot;ORDER BY&quot; clause.</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">track_filteredrows</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_rowset</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_rowset</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_filtered_rows</span><span class="p">)</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">track_filteredsignalids</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_signalidset</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_signalidset</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_filtered_signalids</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">_add_matchedrow</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">matchedrow</span><span class="p">:</span> <span class="n">DataRow</span><span class="p">,</span> <span class="n">signalid_columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">):</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_filterexpression_statementcount</span> <span class="o">&gt;</span> <span class="mi">1</span><span class="p">:</span>
            <span class="c1"># Set operations</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">track_filteredrows</span><span class="p">:</span>
                <span class="n">startlen</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_filtered_rowset</span><span class="p">)</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_rowset</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">matchedrow</span><span class="p">)</span>

                <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_filtered_rowset</span><span class="p">)</span> <span class="o">&gt;</span> <span class="n">startlen</span><span class="p">:</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_rows</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">matchedrow</span><span class="p">)</span>

            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">track_filteredsignalids</span><span class="p">:</span>
                <span class="n">signalidfield</span><span class="p">,</span> <span class="n">null</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="n">matchedrow</span><span class="o">.</span><span class="n">guidvalue</span><span class="p">(</span><span class="n">signalid_columnindex</span><span class="p">)</span>

                <span class="k">if</span> <span class="ow">not</span> <span class="n">null</span> <span class="ow">and</span> <span class="n">err</span> <span class="ow">is</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">signalidfield</span> <span class="o">!=</span> <span class="n">Empty</span><span class="o">.</span><span class="n">GUID</span><span class="p">:</span>
                    <span class="n">startlen</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_filtered_signalidset</span><span class="p">)</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_signalidset</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">signalidfield</span><span class="p">)</span>

                    <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_filtered_signalidset</span><span class="p">)</span> <span class="o">&gt;</span> <span class="n">startlen</span><span class="p">:</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_signalids</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">signalidfield</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="c1"># Vector only operations</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">track_filteredrows</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_rows</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">matchedrow</span><span class="p">)</span>

            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">track_filteredsignalids</span><span class="p">:</span>
                <span class="n">signalidfield</span><span class="p">,</span> <span class="n">null</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="n">matchedrow</span><span class="o">.</span><span class="n">guidvalue</span><span class="p">(</span><span class="n">signalid_columnindex</span><span class="p">)</span>

                <span class="k">if</span> <span class="ow">not</span> <span class="n">null</span> <span class="ow">and</span> <span class="n">err</span> <span class="ow">is</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">signalidfield</span> <span class="o">!=</span> <span class="n">Empty</span><span class="o">.</span><span class="n">GUID</span><span class="p">:</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_signalids</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">signalidfield</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">_map_matchedfieldrow</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">primarytable</span><span class="p">:</span> <span class="n">DataTable</span><span class="p">,</span> <span class="n">columnname</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">matchvalue</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">signalid_columnindex</span><span class="p">:</span> <span class="nb">int</span><span class="p">):</span>
        <span class="n">column</span> <span class="o">=</span> <span class="n">primarytable</span><span class="o">.</span><span class="n">column_byname</span><span class="p">(</span><span class="n">columnname</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">column</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span>

        <span class="n">matchvalue</span> <span class="o">=</span> <span class="n">matchvalue</span><span class="o">.</span><span class="n">upper</span><span class="p">()</span>
        <span class="n">columnindex</span> <span class="o">=</span> <span class="n">column</span><span class="o">.</span><span class="n">index</span>

        <span class="k">for</span> <span class="n">row</span> <span class="ow">in</span> <span class="n">primarytable</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">row</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">continue</span>

            <span class="n">value</span><span class="p">,</span> <span class="n">null</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="n">row</span><span class="o">.</span><span class="n">stringvalue</span><span class="p">(</span><span class="n">columnindex</span><span class="p">)</span>

            <span class="k">if</span> <span class="ow">not</span> <span class="n">null</span> <span class="ow">and</span> <span class="n">err</span> <span class="ow">is</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">value</span><span class="o">.</span><span class="n">upper</span><span class="p">()</span> <span class="o">==</span> <span class="n">matchvalue</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_add_matchedrow</span><span class="p">(</span><span class="n">row</span><span class="p">,</span> <span class="n">signalid_columnindex</span><span class="p">)</span>
                <span class="k">return</span>

    <span class="k">def</span> <span class="nf">_get_expr</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">ctx</span><span class="p">:</span> <span class="n">ParserRuleContext</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Expression</span><span class="p">]:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_expressions</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">ctx</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">_add_expr</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">ctx</span><span class="p">:</span> <span class="n">ParserRuleContext</span><span class="p">,</span> <span class="n">expression</span><span class="p">:</span> <span class="n">Expression</span><span class="p">):</span>
        <span class="c1"># Track expression in parser rule context map</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_expressions</span><span class="p">[</span><span class="n">ctx</span><span class="p">]</span> <span class="o">=</span> <span class="n">expression</span>

        <span class="c1"># Update active expression tree root</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_active_expressiontree</span><span class="o">.</span><span class="n">root</span> <span class="o">=</span> <span class="n">expression</span>

    <span class="c1">#    filterExpressionStatement</span>
    <span class="c1">#     : identifierStatement</span>
    <span class="c1">#     | filterStatement</span>
    <span class="c1">#     | expression</span>
    <span class="c1">#     ;</span>
<div class="viewcode-block" id="FilterExpressionParser.enterFilterExpressionStatement">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.enterFilterExpressionStatement">[docs]</a>
    <span class="k">def</span> <span class="nf">enterFilterExpressionStatement</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">ctx</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">FilterExpressionStatementContext</span><span class="p">):</span>
        <span class="c1"># One filter expression can contain multiple filter statements separated by semi-colon,</span>
        <span class="c1"># so we track each as an independent expression tree</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_expressions</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_active_expressiontree</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_filterexpression_statementcount</span> <span class="o">+=</span> <span class="mi">1</span>

        <span class="c1"># Encountering second filter expression statement necessitates the use of set operations</span>
        <span class="c1"># to prevent possible result duplications</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_filterexpression_statementcount</span> <span class="o">==</span> <span class="mi">2</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_initialize_set_operations</span><span class="p">()</span></div>


    <span class="c1">#    filterStatement</span>
    <span class="c1">#     : K_FILTER ( K_TOP topLimit )? tableName K_WHERE expression ( K_ORDER K_BY orderingTerm ( &#39;,&#39; orderingTerm )* )?</span>
    <span class="c1">#     ;</span>

    <span class="c1">#    topLimit</span>
    <span class="c1">#     : ( &#39;-&#39; | &#39;+&#39; )? INTEGER_LITERAL</span>
    <span class="c1">#     ;</span>

    <span class="c1">#    orderingTerm</span>
    <span class="c1">#     : exactMatchModifier? columnName ( K_ASC | K_DESC )?</span>
    <span class="c1">#     ;</span>
<div class="viewcode-block" id="FilterExpressionParser.enterFilterStatement">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.enterFilterStatement">[docs]</a>
    <span class="k">def</span> <span class="nf">enterFilterStatement</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">ctx</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">FilterStatementContext</span><span class="p">):</span>
        <span class="n">tablename</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">tableName</span><span class="p">()</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span>

        <span class="n">table</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">table</span><span class="p">(</span><span class="n">tablename</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;cannot parse filter expression statement, </span><span class="si">{</span><span class="n">err</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_active_expressiontree</span> <span class="o">=</span> <span class="n">ExpressionTree</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_active_expressiontree</span><span class="o">.</span><span class="n">tablename</span> <span class="o">=</span> <span class="n">tablename</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_expressiontrees</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_active_expressiontree</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">ctx</span><span class="o">.</span><span class="n">K_TOP</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_active_expressiontree</span><span class="o">.</span><span class="n">toplimit</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">ctx</span><span class="o">.</span><span class="n">topLimit</span><span class="p">()</span><span class="o">.</span><span class="n">getText</span><span class="p">())</span>

        <span class="k">if</span> <span class="n">ctx</span><span class="o">.</span><span class="n">K_ORDER</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">ctx</span><span class="o">.</span><span class="n">K_BY</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">orderingterms</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">orderingTerm</span><span class="p">()</span>

            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">orderingterms</span><span class="p">)):</span>
                <span class="n">orderingterm</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">OrderingTermContext</span> <span class="o">=</span> <span class="n">orderingterms</span><span class="p">[</span><span class="n">i</span><span class="p">]</span>
                <span class="n">orderby_columnname</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">orderingterm</span><span class="o">.</span><span class="n">orderByColumnName</span><span class="p">()</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span>
                <span class="n">orderby_column</span> <span class="o">=</span> <span class="n">table</span><span class="o">.</span><span class="n">column_byname</span><span class="p">(</span><span class="n">orderby_columnname</span><span class="p">)</span>

                <span class="k">if</span> <span class="n">orderby_column</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                    <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;cannot parse filter expression statement, failed to find order by field </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">orderby_columnname</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2"> for table </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">table</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

                <span class="bp">self</span><span class="o">.</span><span class="n">_active_expressiontree</span><span class="o">.</span><span class="n">orderbyterms</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">OrderByTerm</span><span class="p">(</span>
                    <span class="n">orderby_column</span><span class="p">,</span>
                    <span class="n">orderingterm</span><span class="o">.</span><span class="n">K_DESC</span><span class="p">()</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">,</span>
                    <span class="n">orderingterm</span><span class="o">.</span><span class="n">exactMatchModifier</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">))</span></div>


    <span class="c1">#    identifierStatement</span>
    <span class="c1">#     : GUID_LITERAL</span>
    <span class="c1">#     | MEASUREMENT_KEY_LITERAL</span>
    <span class="c1">#     | POINT_TAG_LITERAL</span>
    <span class="c1">#     ;</span>
<div class="viewcode-block" id="FilterExpressionParser.exitIdentifierStatement">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.exitIdentifierStatement">[docs]</a>
    <span class="k">def</span> <span class="nf">exitIdentifierStatement</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">ctx</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">IdentifierStatementContext</span><span class="p">):</span>
        <span class="c1"># sourcery skip</span>
        <span class="n">signalid</span> <span class="o">=</span> <span class="n">Empty</span><span class="o">.</span><span class="n">GUID</span>

        <span class="k">if</span> <span class="n">ctx</span><span class="o">.</span><span class="n">GUID_LITERAL</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">signalid</span> <span class="o">=</span> <span class="n">FilterExpressionParser</span><span class="o">.</span><span class="n">_parse_guidliteral</span><span class="p">(</span><span class="n">ctx</span><span class="o">.</span><span class="n">GUID_LITERAL</span><span class="p">()</span><span class="o">.</span><span class="n">getText</span><span class="p">())</span>

            <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">track_filteredrows</span> <span class="ow">and</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">track_filteredsignalids</span><span class="p">:</span>
                <span class="c1"># Handle edge case of encountering standalone Guid when not tracking rows or table identifiers.</span>
                <span class="c1"># In this scenario the filter expression parser would only be used to generate expression trees</span>
                <span class="c1"># for general expression parsing, e.g., for a DataColumn expression, so here the Guid should be</span>
                <span class="c1"># treated as a literal expression value instead of an identifier to track:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">enterExpression</span><span class="p">(</span><span class="kc">None</span><span class="p">)</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_active_expressiontree</span><span class="o">.</span><span class="n">root</span> <span class="o">=</span> <span class="n">ValueExpression</span><span class="p">(</span><span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">GUID</span><span class="p">,</span> <span class="n">signalid</span><span class="p">)</span>
                <span class="k">return</span>

            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">track_filteredsignalids</span> <span class="ow">and</span> <span class="n">signalid</span> <span class="o">!=</span> <span class="n">Empty</span><span class="o">.</span><span class="n">GUID</span><span class="p">:</span>
                <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_filterexpression_statementcount</span> <span class="o">&gt;</span> <span class="mi">1</span><span class="p">:</span>
                    <span class="n">startlen</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_filtered_signalidset</span><span class="p">)</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_signalidset</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">signalid</span><span class="p">)</span>

                    <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_filtered_signalidset</span><span class="p">)</span> <span class="o">&gt;</span> <span class="n">startlen</span><span class="p">:</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_signalids</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">signalid</span><span class="p">)</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_signalids</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">signalid</span><span class="p">)</span>

            <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">track_filteredrows</span><span class="p">:</span>
                <span class="k">return</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">dataset</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span>

        <span class="n">primary_table</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">dataset</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">primary_tablename</span><span class="p">]</span>

        <span class="k">if</span> <span class="n">primary_table</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span>

        <span class="n">primary_tableidfields</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">tableidfields_map</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">primary_tablename</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">primary_tableidfields</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span>

        <span class="n">signalidcolumn</span> <span class="o">=</span> <span class="n">primary_table</span><span class="o">.</span><span class="n">column_byname</span><span class="p">(</span><span class="n">primary_tableidfields</span><span class="o">.</span><span class="n">signalid_fieldname</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">signalidcolumn</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span>

        <span class="n">signalid_columnindex</span> <span class="o">=</span> <span class="n">signalidcolumn</span><span class="o">.</span><span class="n">index</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">track_filteredrows</span> <span class="ow">and</span> <span class="n">signalid</span> <span class="o">!=</span> <span class="n">Empty</span><span class="o">.</span><span class="n">GUID</span><span class="p">:</span>
            <span class="c1"># Map matching row for manually specified Guid</span>
            <span class="k">for</span> <span class="n">row</span> <span class="ow">in</span> <span class="n">primary_table</span><span class="p">:</span>
                <span class="k">if</span> <span class="n">row</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                    <span class="k">continue</span>

                <span class="n">value</span><span class="p">,</span> <span class="n">null</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="n">row</span><span class="o">.</span><span class="n">guidvalue</span><span class="p">(</span><span class="n">signalid_columnindex</span><span class="p">)</span>

                <span class="k">if</span> <span class="ow">not</span> <span class="n">null</span> <span class="ow">and</span> <span class="n">err</span> <span class="ow">is</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">value</span> <span class="o">==</span> <span class="n">signalid</span><span class="p">:</span>
                    <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">filterexpression_statementcount</span> <span class="o">&gt;</span> <span class="mi">1</span><span class="p">:</span>
                        <span class="n">startlen</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_filtered_rowset</span><span class="p">)</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_rowset</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">row</span><span class="p">)</span>

                        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_filtered_rowset</span><span class="p">)</span> <span class="o">&gt;</span> <span class="n">startlen</span><span class="p">:</span>
                            <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_rows</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">row</span><span class="p">)</span>
                    <span class="k">else</span><span class="p">:</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">_filtered_rows</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">row</span><span class="p">)</span>

                    <span class="k">return</span>

            <span class="k">return</span>

        <span class="k">if</span> <span class="n">ctx</span><span class="o">.</span><span class="n">MEASUREMENT_KEY_LITERAL</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_map_matchedfieldrow</span><span class="p">(</span><span class="n">primary_table</span><span class="p">,</span> <span class="n">primary_tableidfields</span><span class="o">.</span><span class="n">measurementkey_fieldname</span><span class="p">,</span> <span class="n">ctx</span><span class="o">.</span><span class="n">MEASUREMENT_KEY_LITERAL</span><span class="p">()</span><span class="o">.</span><span class="n">getText</span><span class="p">(),</span> <span class="n">signalid_columnindex</span><span class="p">)</span>
            <span class="k">return</span>

        <span class="k">if</span> <span class="n">ctx</span><span class="o">.</span><span class="n">POINT_TAG_LITERAL</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_map_matchedfieldrow</span><span class="p">(</span><span class="n">primary_table</span><span class="p">,</span> <span class="n">primary_tableidfields</span><span class="o">.</span><span class="n">pointtag_fieldname</span><span class="p">,</span> <span class="n">ctx</span><span class="o">.</span><span class="n">POINT_TAG_LITERAL</span><span class="p">()</span><span class="o">.</span><span class="n">getText</span><span class="p">(),</span> <span class="n">signalid_columnindex</span><span class="p">)</span>
            <span class="k">return</span></div>


    <span class="c1">#    expression</span>
    <span class="c1">#     : notOperator expression</span>
    <span class="c1">#     | expression logicalOperator expression</span>
    <span class="c1">#     | predicateExpression</span>
    <span class="c1">#     ;</span>
<div class="viewcode-block" id="FilterExpressionParser.enterExpression">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.enterExpression">[docs]</a>
    <span class="k">def</span> <span class="nf">enterExpression</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">ctx</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">ExpressionContext</span><span class="p">):</span>
        <span class="c1"># Handle case of encountering a standalone expression, i.e., an expression not</span>
        <span class="c1"># within a filter statement context</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_active_expressiontree</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_active_expressiontree</span> <span class="o">=</span> <span class="n">ExpressionTree</span><span class="p">()</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_expressiontrees</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_active_expressiontree</span><span class="p">)</span></div>


    <span class="c1">#    expression</span>
    <span class="c1">#     : notOperator expression</span>
    <span class="c1">#     | expression logicalOperator expression</span>
    <span class="c1">#     | predicateExpression</span>
    <span class="c1">#     ;</span>
<div class="viewcode-block" id="FilterExpressionParser.exitExpression">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.exitExpression">[docs]</a>
    <span class="k">def</span> <span class="nf">exitExpression</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">ctx</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">ExpressionContext</span><span class="p">):</span>
        <span class="n">value</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Expression</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

        <span class="c1"># Check for predicate expressions (see explicit visit function)</span>
        <span class="n">predicate_expression</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">PredicateExpressionContext</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">predicateExpression</span><span class="p">()</span>

        <span class="k">if</span> <span class="n">predicate_expression</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">value</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_expr</span><span class="p">(</span><span class="n">predicate_expression</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">value</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to parse predicate expression </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">predicate_expression</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">_add_expr</span><span class="p">(</span><span class="n">ctx</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
            <span class="k">return</span>

        <span class="c1"># Check for not operator expressions</span>
        <span class="n">not_operator</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">notOperator</span><span class="p">()</span>

        <span class="k">if</span> <span class="n">not_operator</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">expressions</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">ExpressionParser</span><span class="o">.</span><span class="n">ExpressionContext</span><span class="p">]</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">expression</span><span class="p">()</span>

            <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">expressions</span><span class="p">)</span> <span class="o">!=</span> <span class="mi">1</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;not operator expression is malformed: </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">ctx</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="n">value</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_expr</span><span class="p">(</span><span class="n">expressions</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>

            <span class="k">if</span> <span class="n">value</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to find not operator expression </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">ctx</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">_add_expr</span><span class="p">(</span><span class="n">ctx</span><span class="p">,</span> <span class="n">UnaryExpression</span><span class="p">(</span><span class="n">ExpressionUnaryType</span><span class="o">.</span><span class="n">NOT</span><span class="p">,</span> <span class="n">value</span><span class="p">))</span>
            <span class="k">return</span>

        <span class="c1"># Check for logical operator expressions</span>
        <span class="n">logical_operator</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">LogicalOperatorContext</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">logicalOperator</span><span class="p">()</span>

        <span class="k">if</span> <span class="n">logical_operator</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">expressions</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">ExpressionParser</span><span class="o">.</span><span class="n">ExpressionContext</span><span class="p">]</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">expression</span><span class="p">()</span>

            <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">expressions</span><span class="p">)</span> <span class="o">!=</span> <span class="mi">2</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;operator expression, in logical operator expression context, is malformed: </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">ctx</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="n">left</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_expr</span><span class="p">(</span><span class="n">expressions</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>

            <span class="k">if</span> <span class="n">left</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to find left logical operator expression </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">ctx</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="n">right</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_expr</span><span class="p">(</span><span class="n">expressions</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span>

            <span class="k">if</span> <span class="n">right</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to find right logical operator expression </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">ctx</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="n">operator_symbol</span> <span class="o">=</span> <span class="n">logical_operator</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span>

            <span class="k">if</span> <span class="n">logical_operator</span><span class="o">.</span><span class="n">K_AND</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">or</span> <span class="n">operator_symbol</span> <span class="o">==</span> <span class="s2">&quot;&amp;&amp;&quot;</span><span class="p">:</span>
                <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">AND</span>
            <span class="k">elif</span> <span class="n">logical_operator</span><span class="o">.</span><span class="n">K_OR</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">or</span> <span class="n">operator_symbol</span> <span class="o">==</span> <span class="s2">&quot;||&quot;</span><span class="p">:</span>
                <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">OR</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;unexpected logical operator </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">operator_symbol</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">_add_expr</span><span class="p">(</span><span class="n">ctx</span><span class="p">,</span> <span class="n">OperatorExpression</span><span class="p">(</span><span class="n">operatortype</span><span class="p">,</span> <span class="n">left</span><span class="p">,</span> <span class="n">right</span><span class="p">))</span>
            <span class="k">return</span>

        <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;unexpected expression </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">ctx</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span></div>


    <span class="c1">#    predicateExpression</span>
    <span class="c1">#     : predicateExpression notOperator? K_IN exactMatchModifier? &#39;(&#39; expressionList &#39;)&#39;</span>
    <span class="c1">#     | predicateExpression K_IS notOperator? K_NULL</span>
    <span class="c1">#     | predicateExpression comparisonOperator predicateExpression</span>
    <span class="c1">#     | predicateExpression notOperator? K_LIKE exactMatchModifier? predicateExpression</span>
    <span class="c1">#     | valueExpression</span>
    <span class="c1">#     ;</span>
<div class="viewcode-block" id="FilterExpressionParser.exitPredicateExpression">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.exitPredicateExpression">[docs]</a>
    <span class="k">def</span> <span class="nf">exitPredicateExpression</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">ctx</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">PredicateExpressionContext</span><span class="p">):</span>
        <span class="c1"># sourcery skip</span>
        <span class="n">value_expression</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">ValueExpressionContext</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">valueExpression</span><span class="p">()</span>

        <span class="c1"># Check for value expressions (see explicit visit function)</span>
        <span class="k">if</span> <span class="n">value_expression</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">value</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_expr</span><span class="p">(</span><span class="n">value_expression</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">value</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to find value expression </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">value_expression</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">_add_expr</span><span class="p">(</span><span class="n">ctx</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
            <span class="k">return</span>

        <span class="n">has_notkeyword</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">notOperator</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
        <span class="n">exactmatch</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">exactMatchModifier</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>

        <span class="c1"># Check for IN expressions</span>
        <span class="k">if</span> <span class="n">ctx</span><span class="o">.</span><span class="n">K_IN</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">predicates</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">ExpressionParser</span><span class="o">.</span><span class="n">PredicateExpressionContext</span><span class="p">]</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">predicateExpression</span><span class="p">()</span>

            <span class="c1"># IN expression expects one predicate</span>
            <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">predicates</span><span class="p">)</span> <span class="o">!=</span> <span class="mi">1</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="se">\&quot;</span><span class="s2">IN</span><span class="se">\&quot;</span><span class="s2"> expression is malformed: </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">ctx</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="n">value</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_expr</span><span class="p">(</span><span class="n">predicates</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>

            <span class="k">if</span> <span class="n">value</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to find </span><span class="se">\&quot;</span><span class="s2">IN</span><span class="se">\&quot;</span><span class="s2"> predicate expression </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">ctx</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="n">expressionlist</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">ExpressionListContext</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">expressionList</span><span class="p">()</span>
            <span class="n">expressions</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">ExpressionParser</span><span class="o">.</span><span class="n">ExpressionContext</span><span class="p">]</span> <span class="o">=</span> <span class="n">expressionlist</span><span class="o">.</span><span class="n">expression</span><span class="p">()</span>
            <span class="n">argumentcount</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">expressions</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">argumentcount</span> <span class="o">&lt;</span> <span class="mi">1</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="s2">&quot;not enough expressions found for </span><span class="se">\&quot;</span><span class="s2">IN</span><span class="se">\&quot;</span><span class="s2"> operation&quot;</span><span class="p">)</span>

            <span class="n">arguments</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">Expression</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>

            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">argumentcount</span><span class="p">):</span>
                <span class="n">argument</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_expr</span><span class="p">(</span><span class="n">expressions</span><span class="p">[</span><span class="n">i</span><span class="p">])</span>

                <span class="k">if</span> <span class="n">argument</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                    <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to find argument expression </span><span class="si">{</span><span class="n">i</span><span class="si">}</span><span class="s2"> </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">expressions</span><span class="p">[</span><span class="n">i</span><span class="p">]</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2"> for </span><span class="se">\&quot;</span><span class="s2">IN</span><span class="se">\&quot;</span><span class="s2"> operation&quot;</span><span class="p">)</span>

                <span class="n">arguments</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">argument</span><span class="p">)</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">_add_expr</span><span class="p">(</span><span class="n">ctx</span><span class="p">,</span> <span class="n">InListExpression</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">arguments</span><span class="p">,</span> <span class="n">has_notkeyword</span><span class="p">,</span> <span class="n">exactmatch</span><span class="p">))</span>
            <span class="k">return</span>

        <span class="c1"># Check for IS NULL expressions</span>
        <span class="k">if</span> <span class="n">ctx</span><span class="o">.</span><span class="n">K_IS</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">ctx</span><span class="o">.</span><span class="n">K_NULL</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">has_notkeyword</span><span class="p">:</span>
                <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">ISNOTNULL</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">ISNULL</span>

            <span class="n">predicates</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">ExpressionParser</span><span class="o">.</span><span class="n">PredicateExpressionContext</span><span class="p">]</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">predicateExpression</span><span class="p">()</span>

            <span class="c1"># IS NULL expression expects one predicate</span>
            <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">predicates</span><span class="p">)</span> <span class="o">!=</span> <span class="mi">1</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="se">\&quot;</span><span class="s2">IS NULL</span><span class="se">\&quot;</span><span class="s2"> expression is malformed: </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">ctx</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="n">value</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_expr</span><span class="p">(</span><span class="n">predicates</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>

            <span class="k">if</span> <span class="n">value</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to find </span><span class="se">\&quot;</span><span class="s2">IS NULL</span><span class="se">\&quot;</span><span class="s2"> predicate expression </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">ctx</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">_add_expr</span><span class="p">(</span><span class="n">ctx</span><span class="p">,</span> <span class="n">OperatorExpression</span><span class="p">(</span><span class="n">operatortype</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="kc">None</span><span class="p">))</span>
            <span class="k">return</span>

        <span class="c1"># Remaining operators require two predicate expressions</span>
        <span class="n">predicates</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">ExpressionParser</span><span class="o">.</span><span class="n">PredicateExpressionContext</span><span class="p">]</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">predicateExpression</span><span class="p">()</span>

        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">predicates</span><span class="p">)</span> <span class="o">!=</span> <span class="mi">2</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;operator expression, in predicate expression context, is malformed: </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">ctx</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

        <span class="n">left</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_expr</span><span class="p">(</span><span class="n">predicates</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>

        <span class="k">if</span> <span class="n">left</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to find left operator predicate expression </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">ctx</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

        <span class="n">right</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_expr</span><span class="p">(</span><span class="n">predicates</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span>

        <span class="k">if</span> <span class="n">right</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to find right operator predicate expression </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">ctx</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

        <span class="c1"># Check for comparison operator expressions</span>
        <span class="n">comparison_operator</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">ComparisonOperatorContext</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">comparisonOperator</span><span class="p">()</span>

        <span class="k">if</span> <span class="n">comparison_operator</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">operatorsymbol</span> <span class="o">=</span> <span class="n">comparison_operator</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span>

            <span class="k">if</span> <span class="n">operatorsymbol</span> <span class="o">==</span> <span class="s2">&quot;&lt;&quot;</span><span class="p">:</span>
                <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">LESSTHAN</span>
            <span class="k">elif</span> <span class="n">operatorsymbol</span> <span class="o">==</span> <span class="s2">&quot;&lt;=&quot;</span><span class="p">:</span>
                <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">LESSTHANOREQUAL</span>
            <span class="k">elif</span> <span class="n">operatorsymbol</span> <span class="o">==</span> <span class="s2">&quot;&gt;&quot;</span><span class="p">:</span>
                <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">GREATERTHAN</span>
            <span class="k">elif</span> <span class="n">operatorsymbol</span> <span class="o">==</span> <span class="s2">&quot;&gt;=&quot;</span><span class="p">:</span>
                <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">GREATERTHANOREQUAL</span>
            <span class="k">elif</span> <span class="n">operatorsymbol</span> <span class="ow">in</span> <span class="p">[</span><span class="s2">&quot;=&quot;</span><span class="p">,</span> <span class="s2">&quot;==&quot;</span><span class="p">]:</span>
                <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">EQUAL</span>
            <span class="k">elif</span> <span class="n">operatorsymbol</span> <span class="o">==</span> <span class="s2">&quot;===&quot;</span><span class="p">:</span>
                <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">EQUALEXACTMATCH</span>
            <span class="k">elif</span> <span class="n">operatorsymbol</span> <span class="ow">in</span> <span class="p">[</span><span class="s2">&quot;!=&quot;</span><span class="p">,</span> <span class="s2">&quot;&lt;&gt;&quot;</span><span class="p">]:</span>
                <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">NOTEQUAL</span>
            <span class="k">elif</span> <span class="n">operatorsymbol</span> <span class="o">==</span> <span class="s2">&quot;!==&quot;</span><span class="p">:</span>
                <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">NOTEQUALEXACTMATCH</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;unexpected comparison operator </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">operatorsymbol</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">_add_expr</span><span class="p">(</span><span class="n">ctx</span><span class="p">,</span> <span class="n">OperatorExpression</span><span class="p">(</span><span class="n">operatortype</span><span class="p">,</span> <span class="n">left</span><span class="p">,</span> <span class="n">right</span><span class="p">))</span>
            <span class="k">return</span>

        <span class="c1"># Check for LIKE expressions</span>
        <span class="k">if</span> <span class="n">ctx</span><span class="o">.</span><span class="n">K_LIKE</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">exactmatch</span><span class="p">:</span>
                <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">NOTLIKEEXACTMATCH</span> <span class="k">if</span> <span class="n">has_notkeyword</span> <span class="ow">is</span> <span class="kc">None</span> <span class="k">else</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">LIKEEXACTMATCH</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">NOTLIKE</span> <span class="k">if</span> <span class="n">has_notkeyword</span> <span class="ow">is</span> <span class="kc">None</span> <span class="k">else</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">LIKE</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">_add_expr</span><span class="p">(</span><span class="n">ctx</span><span class="p">,</span> <span class="n">OperatorExpression</span><span class="p">(</span><span class="n">operatortype</span><span class="p">,</span> <span class="n">left</span><span class="p">,</span> <span class="n">right</span><span class="p">))</span>
            <span class="k">return</span>

        <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;unexpected predicate expression </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">ctx</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span></div>


    <span class="c1">#    valueExpression</span>
    <span class="c1">#     : literalValue</span>
    <span class="c1">#     | columnName</span>
    <span class="c1">#     | functionExpression</span>
    <span class="c1">#     | unaryOperator valueExpression</span>
    <span class="c1">#     | &#39;(&#39; expression &#39;)&#39;</span>
    <span class="c1">#     | valueExpression mathOperator valueExpression</span>
    <span class="c1">#     | valueExpression bitwiseOperator valueExpression</span>
    <span class="c1"># 	  ;</span>
<div class="viewcode-block" id="FilterExpressionParser.exitValueExpression">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.exitValueExpression">[docs]</a>
    <span class="k">def</span> <span class="nf">exitValueExpression</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">ctx</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">ValueExpressionContext</span><span class="p">):</span>
        <span class="c1"># sourcery skip</span>
        <span class="n">literal_value</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">LiteralValueContext</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">literalValue</span><span class="p">()</span>

        <span class="c1"># Check for literal values (see explicit visit function)</span>
        <span class="k">if</span> <span class="n">literal_value</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">value</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_expr</span><span class="p">(</span><span class="n">literal_value</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">value</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to find literal value </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">literal_value</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">_add_expr</span><span class="p">(</span><span class="n">ctx</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
            <span class="k">return</span>

        <span class="c1"># Check for column names (see explicit visit function)</span>
        <span class="n">columnname</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">ColumnNameContext</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">columnName</span><span class="p">()</span>

        <span class="k">if</span> <span class="n">columnname</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">value</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_expr</span><span class="p">(</span><span class="n">columnname</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">value</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to find column name </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">columnname</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">_add_expr</span><span class="p">(</span><span class="n">ctx</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
            <span class="k">return</span>

        <span class="c1"># Check for function expressions (see explicit visit function)</span>
        <span class="n">function_expression</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">FunctionExpressionContext</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">functionExpression</span><span class="p">()</span>

        <span class="k">if</span> <span class="n">function_expression</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">value</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_expr</span><span class="p">(</span><span class="n">function_expression</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">value</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to find function expression </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">function_expression</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">_add_expr</span><span class="p">(</span><span class="n">ctx</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
            <span class="k">return</span>

        <span class="c1"># Check for unary operators</span>
        <span class="n">unary_operator</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">UnaryOperatorContext</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">unaryOperator</span><span class="p">()</span>

        <span class="k">if</span> <span class="n">unary_operator</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">values</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">ExpressionParser</span><span class="o">.</span><span class="n">ValueExpressionContext</span><span class="p">]</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">valueExpression</span><span class="p">()</span>

            <span class="c1"># Unary operator expects one value expression</span>
            <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">values</span><span class="p">)</span> <span class="o">!=</span> <span class="mi">1</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;unary operator value expression is malformed: </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">ctx</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="n">value</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_expr</span><span class="p">(</span><span class="n">values</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>

            <span class="k">if</span> <span class="n">value</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to find unary operator value expression </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">ctx</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">unary_operator</span><span class="o">.</span><span class="n">K_NOT</span><span class="p">()</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="n">operatorsymbol</span> <span class="o">=</span> <span class="n">unary_operator</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span>

                <span class="k">if</span> <span class="n">operatorsymbol</span> <span class="o">==</span> <span class="s2">&quot;+&quot;</span><span class="p">:</span>
                    <span class="n">unarytype</span> <span class="o">=</span> <span class="n">ExpressionUnaryType</span><span class="o">.</span><span class="n">PLUS</span>
                <span class="k">elif</span> <span class="n">operatorsymbol</span> <span class="o">==</span> <span class="s2">&quot;-&quot;</span><span class="p">:</span>
                    <span class="n">unarytype</span> <span class="o">=</span> <span class="n">ExpressionUnaryType</span><span class="o">.</span><span class="n">MINUS</span>
                <span class="k">elif</span> <span class="n">operatorsymbol</span> <span class="ow">in</span> <span class="p">[</span><span class="s2">&quot;~&quot;</span><span class="p">,</span> <span class="s2">&quot;!&quot;</span><span class="p">]:</span>
                    <span class="n">unarytype</span> <span class="o">=</span> <span class="n">ExpressionUnaryType</span><span class="o">.</span><span class="n">NOT</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;unexpected unary type </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">operatorsymbol</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">unarytype</span> <span class="o">=</span> <span class="n">ExpressionUnaryType</span><span class="o">.</span><span class="n">NOT</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">_add_expr</span><span class="p">(</span><span class="n">ctx</span><span class="p">,</span> <span class="n">UnaryExpression</span><span class="p">(</span><span class="n">unarytype</span><span class="p">,</span> <span class="n">value</span><span class="p">))</span>
            <span class="k">return</span>

        <span class="c1"># Check for sub-expressions, i.e., &quot;(&quot; expression &quot;)&quot;</span>
        <span class="n">expression</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">ExpressionContext</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">expression</span><span class="p">()</span>

        <span class="k">if</span> <span class="n">expression</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">value</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_expr</span><span class="p">(</span><span class="n">expression</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">value</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to find sub-expression </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">ctx</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">_add_expr</span><span class="p">(</span><span class="n">ctx</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
            <span class="k">return</span>

        <span class="c1"># Remaining operators require two value expressions</span>
        <span class="n">values</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">ExpressionParser</span><span class="o">.</span><span class="n">ValueExpressionContext</span><span class="p">]</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">valueExpression</span><span class="p">()</span>

        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">values</span><span class="p">)</span> <span class="o">!=</span> <span class="mi">2</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;operator expression, in value expression context, is malformed: </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">ctx</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

        <span class="n">left</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_expr</span><span class="p">(</span><span class="n">values</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>

        <span class="k">if</span> <span class="n">left</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to find left operator value expression </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">ctx</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

        <span class="n">right</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_expr</span><span class="p">(</span><span class="n">values</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span>

        <span class="k">if</span> <span class="n">right</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to find right operator value expression </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">ctx</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

        <span class="c1"># Check for math operator expressions</span>
        <span class="n">math_operator</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">MathOperatorContext</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">mathOperator</span><span class="p">()</span>

        <span class="k">if</span> <span class="n">math_operator</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">operatorsymbol</span> <span class="o">=</span> <span class="n">math_operator</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span>

            <span class="k">if</span> <span class="n">operatorsymbol</span> <span class="o">==</span> <span class="s2">&quot;+&quot;</span><span class="p">:</span>
                <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">ADD</span>
            <span class="k">elif</span> <span class="n">operatorsymbol</span> <span class="o">==</span> <span class="s2">&quot;-&quot;</span><span class="p">:</span>
                <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">SUBTRACT</span>
            <span class="k">elif</span> <span class="n">operatorsymbol</span> <span class="o">==</span> <span class="s2">&quot;*&quot;</span><span class="p">:</span>
                <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">MULTIPLY</span>
            <span class="k">elif</span> <span class="n">operatorsymbol</span> <span class="o">==</span> <span class="s2">&quot;/&quot;</span><span class="p">:</span>
                <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">DIVIDE</span>
            <span class="k">elif</span> <span class="n">operatorsymbol</span> <span class="o">==</span> <span class="s2">&quot;%&quot;</span><span class="p">:</span>
                <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">MODULUS</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;unexpected math operator </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">operatorsymbol</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">_add_expr</span><span class="p">(</span><span class="n">ctx</span><span class="p">,</span> <span class="n">OperatorExpression</span><span class="p">(</span><span class="n">operatortype</span><span class="p">,</span> <span class="n">left</span><span class="p">,</span> <span class="n">right</span><span class="p">))</span>
            <span class="k">return</span>

        <span class="c1"># Check for bitwise operator expressions</span>
        <span class="n">bitwise_operator</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">BitwiseOperatorContext</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">bitwiseOperator</span><span class="p">()</span>

        <span class="k">if</span> <span class="n">bitwise_operator</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="c1"># Check for bitwise operators</span>
            <span class="k">if</span> <span class="n">bitwise_operator</span><span class="o">.</span><span class="n">K_XOR</span><span class="p">()</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="n">operatorsymbol</span> <span class="o">=</span> <span class="n">bitwise_operator</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span>

                <span class="k">if</span> <span class="n">operatorsymbol</span> <span class="o">==</span> <span class="s2">&quot;&amp;&quot;</span><span class="p">:</span>
                    <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">BITWISEAND</span>
                <span class="k">elif</span> <span class="n">operatorsymbol</span> <span class="o">==</span> <span class="s2">&quot;|&quot;</span><span class="p">:</span>
                    <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">BITWISEOR</span>
                <span class="k">elif</span> <span class="n">operatorsymbol</span> <span class="o">==</span> <span class="s2">&quot;^&quot;</span><span class="p">:</span>
                    <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">BITWISEXOR</span>
                <span class="k">elif</span> <span class="n">operatorsymbol</span> <span class="o">==</span> <span class="s2">&quot;&lt;&lt;&quot;</span><span class="p">:</span>
                    <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">BITSHIFTLEFT</span>
                <span class="k">elif</span> <span class="n">operatorsymbol</span> <span class="o">==</span> <span class="s2">&quot;&gt;&gt;&quot;</span><span class="p">:</span>
                    <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">BITSHIFTRIGHT</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;unexpected bitwise operator </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">operatorsymbol</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">operatortype</span> <span class="o">=</span> <span class="n">ExpressionOperatorType</span><span class="o">.</span><span class="n">BITWISEXOR</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">_add_expr</span><span class="p">(</span><span class="n">ctx</span><span class="p">,</span> <span class="n">OperatorExpression</span><span class="p">(</span><span class="n">operatortype</span><span class="p">,</span> <span class="n">left</span><span class="p">,</span> <span class="n">right</span><span class="p">))</span>
            <span class="k">return</span>

        <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;unexpected value expression </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">ctx</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span></div>


    <span class="c1">#    literalValue</span>
    <span class="c1">#     : INTEGER_LITERAL</span>
    <span class="c1">#     | NUMERIC_LITERAL</span>
    <span class="c1">#     | STRING_LITERAL</span>
    <span class="c1">#     | DATETIME_LITERAL</span>
    <span class="c1">#     | GUID_LITERAL</span>
    <span class="c1">#     | BOOLEAN_LITERAL</span>
    <span class="c1">#     | K_NULL</span>
    <span class="c1">#     ;</span>
<div class="viewcode-block" id="FilterExpressionParser.exitLiteralValue">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.exitLiteralValue">[docs]</a>
    <span class="k">def</span> <span class="nf">exitLiteralValue</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">ctx</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">LiteralValueContext</span><span class="p">):</span>
        <span class="n">result</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">ValueExpression</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

        <span class="c1"># Literal numeric values will not be negative, unary operators will handle negative values</span>
        <span class="k">if</span> <span class="n">ctx</span><span class="o">.</span><span class="n">INTEGER_LITERAL</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">literal</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">INTEGER_LITERAL</span><span class="p">()</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span>

            <span class="k">try</span><span class="p">:</span>
                <span class="n">value</span> <span class="o">=</span> <span class="n">Convert</span><span class="o">.</span><span class="n">from_str</span><span class="p">(</span><span class="n">literal</span><span class="p">,</span> <span class="nb">int</span><span class="p">)</span>

                <span class="k">if</span> <span class="n">value</span> <span class="o">&gt;</span> <span class="n">Limits</span><span class="o">.</span><span class="n">MAXINT32</span><span class="p">:</span>
                    <span class="k">if</span> <span class="n">value</span> <span class="o">&gt;</span> <span class="n">Limits</span><span class="o">.</span><span class="n">MAXINT64</span><span class="p">:</span>
                        <span class="n">result</span> <span class="o">=</span> <span class="n">FilterExpressionParser</span><span class="o">.</span><span class="n">_parse_numericliteral</span><span class="p">(</span><span class="n">literal</span><span class="p">)</span>
                    <span class="k">else</span><span class="p">:</span>
                        <span class="n">result</span> <span class="o">=</span> <span class="n">ValueExpression</span><span class="p">(</span><span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">INT64</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="n">result</span> <span class="o">=</span> <span class="n">ValueExpression</span><span class="p">(</span><span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">INT32</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
            <span class="k">except</span> <span class="ne">Exception</span><span class="p">:</span>
                <span class="n">result</span> <span class="o">=</span> <span class="n">FilterExpressionParser</span><span class="o">.</span><span class="n">_parse_numericliteral</span><span class="p">(</span><span class="n">literal</span><span class="p">)</span>
        <span class="k">elif</span> <span class="n">ctx</span><span class="o">.</span><span class="n">NUMERIC_LITERAL</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">literal</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">NUMERIC_LITERAL</span><span class="p">()</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span>

            <span class="k">try</span><span class="p">:</span>
                <span class="c1"># Real literals using scientific notation are parsed as double</span>
                <span class="k">if</span> <span class="s2">&quot;E&quot;</span> <span class="ow">in</span> <span class="n">literal</span><span class="o">.</span><span class="n">upper</span><span class="p">():</span>
                    <span class="n">result</span> <span class="o">=</span> <span class="n">ValueExpression</span><span class="p">(</span><span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">DOUBLE</span><span class="p">,</span> <span class="nb">float</span><span class="p">(</span><span class="n">literal</span><span class="p">))</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="n">result</span> <span class="o">=</span> <span class="n">FilterExpressionParser</span><span class="o">.</span><span class="n">_parse_numericliteral</span><span class="p">(</span><span class="n">literal</span><span class="p">)</span>
            <span class="k">except</span> <span class="ne">Exception</span><span class="p">:</span>
                <span class="n">result</span> <span class="o">=</span> <span class="n">FilterExpressionParser</span><span class="o">.</span><span class="n">_parse_numericliteral</span><span class="p">(</span><span class="n">literal</span><span class="p">)</span>
        <span class="k">elif</span> <span class="n">ctx</span><span class="o">.</span><span class="n">STRING_LITERAL</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">result</span> <span class="o">=</span> <span class="n">ValueExpression</span><span class="p">(</span><span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">STRING</span><span class="p">,</span> <span class="n">FilterExpressionParser</span><span class="o">.</span><span class="n">_parse_stringliteral</span><span class="p">(</span><span class="n">ctx</span><span class="o">.</span><span class="n">STRING_LITERAL</span><span class="p">()</span><span class="o">.</span><span class="n">getText</span><span class="p">()))</span>
        <span class="k">elif</span> <span class="n">ctx</span><span class="o">.</span><span class="n">DATETIME_LITERAL</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">result</span> <span class="o">=</span> <span class="n">ValueExpression</span><span class="p">(</span><span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">DATETIME</span><span class="p">,</span> <span class="n">FilterExpressionParser</span><span class="o">.</span><span class="n">_parse_datetimeliteral</span><span class="p">(</span><span class="n">ctx</span><span class="o">.</span><span class="n">DATETIME_LITERAL</span><span class="p">()</span><span class="o">.</span><span class="n">getText</span><span class="p">()))</span>
        <span class="k">elif</span> <span class="n">ctx</span><span class="o">.</span><span class="n">GUID_LITERAL</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">result</span> <span class="o">=</span> <span class="n">ValueExpression</span><span class="p">(</span><span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">GUID</span><span class="p">,</span> <span class="n">FilterExpressionParser</span><span class="o">.</span><span class="n">_parse_guidliteral</span><span class="p">(</span><span class="n">ctx</span><span class="o">.</span><span class="n">GUID_LITERAL</span><span class="p">()</span><span class="o">.</span><span class="n">getText</span><span class="p">()))</span>
        <span class="k">elif</span> <span class="n">ctx</span><span class="o">.</span><span class="n">BOOLEAN_LITERAL</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">literal</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">BOOLEAN_LITERAL</span><span class="p">()</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span>
            <span class="n">result</span> <span class="o">=</span> <span class="n">TRUEVALUE</span> <span class="k">if</span> <span class="n">literal</span><span class="o">.</span><span class="n">upper</span><span class="p">()</span> <span class="o">==</span> <span class="s2">&quot;TRUE&quot;</span> <span class="k">else</span> <span class="n">FALSEVALUE</span>
        <span class="k">elif</span> <span class="n">ctx</span><span class="o">.</span><span class="n">K_NULL</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">result</span> <span class="o">=</span> <span class="n">NULLVALUE</span>

        <span class="k">if</span> <span class="n">result</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_add_expr</span><span class="p">(</span><span class="n">ctx</span><span class="p">,</span> <span class="n">result</span><span class="p">)</span></div>


    <span class="nd">@staticmethod</span>
    <span class="k">def</span> <span class="nf">_parse_numericliteral</span><span class="p">(</span><span class="n">literal</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ValueExpression</span><span class="p">:</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="n">value</span> <span class="o">=</span> <span class="n">Decimal</span><span class="p">(</span><span class="n">literal</span><span class="p">)</span>
            <span class="k">return</span> <span class="n">ValueExpression</span><span class="p">(</span><span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">DECIMAL</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
        <span class="k">except</span> <span class="ne">Exception</span><span class="p">:</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="n">value</span> <span class="o">=</span> <span class="nb">float</span><span class="p">(</span><span class="n">literal</span><span class="p">)</span>
                <span class="k">return</span> <span class="n">ValueExpression</span><span class="p">(</span><span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">DOUBLE</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
            <span class="k">except</span> <span class="ne">Exception</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">ValueExpression</span><span class="p">(</span><span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">STRING</span><span class="p">,</span> <span class="n">literal</span><span class="p">)</span>

    <span class="nd">@staticmethod</span>
    <span class="k">def</span> <span class="nf">_parse_stringliteral</span><span class="p">(</span><span class="n">literal</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
        <span class="c1"># Remove any surrounding quotes from string, ANTLR grammar already</span>
        <span class="c1"># ensures strings starting with quote also ends with one</span>
        <span class="k">return</span> <span class="n">literal</span><span class="p">[</span><span class="mi">1</span><span class="p">:</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span> <span class="k">if</span> <span class="n">literal</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;&#39;&quot;</span> <span class="k">else</span> <span class="n">literal</span>

    <span class="nd">@staticmethod</span>
    <span class="k">def</span> <span class="nf">_parse_guidliteral</span><span class="p">(</span><span class="n">literal</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">UUID</span><span class="p">:</span>
        <span class="c1"># Remove any quotes from GUID (boost currently only handles optional braces),</span>
        <span class="c1"># ANTLR grammar already ensures GUID starting with quote also ends with one</span>
        <span class="k">return</span> <span class="n">UUID</span><span class="p">(</span><span class="n">literal</span><span class="p">[</span><span class="mi">1</span><span class="p">:</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span> <span class="k">if</span> <span class="n">literal</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="ow">in</span> <span class="p">[</span><span class="s2">&quot;{&quot;</span><span class="p">,</span> <span class="s2">&quot;&#39;&quot;</span><span class="p">]</span> <span class="k">else</span> <span class="n">literal</span><span class="p">)</span>

    <span class="nd">@staticmethod</span>
    <span class="k">def</span> <span class="nf">_parse_datetimeliteral</span><span class="p">(</span><span class="n">literal</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">datetime</span><span class="p">:</span>
        <span class="c1"># Remove any surrounding &#39;#&#39; symbols from date/time, ANTLR grammar already</span>
        <span class="c1"># ensures date/time starting with &#39;#&#39; symbol will also end with one</span>
        <span class="n">literal</span> <span class="o">=</span> <span class="n">literal</span><span class="p">[</span><span class="mi">1</span><span class="p">:</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span> <span class="k">if</span> <span class="n">literal</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;#&quot;</span> <span class="k">else</span> <span class="n">literal</span>

        <span class="k">try</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">Convert</span><span class="o">.</span><span class="n">from_str</span><span class="p">(</span><span class="n">literal</span><span class="p">,</span> <span class="n">datetime</span><span class="p">)</span>
        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">ex</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to parse datetime literal #</span><span class="si">{</span><span class="n">literal</span><span class="si">}</span><span class="s2">#: </span><span class="si">{</span><span class="n">ex</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span> <span class="kn">from</span> <span class="nn">ex</span>

    <span class="c1">#    columnName</span>
    <span class="c1">#     : IDENTIFIER</span>
    <span class="c1">#     ;</span>
<div class="viewcode-block" id="FilterExpressionParser.exitColumnName">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.exitColumnName">[docs]</a>
    <span class="k">def</span> <span class="nf">exitColumnName</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">ctx</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">ColumnNameContext</span><span class="p">):</span>
        <span class="n">tablename</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_active_expressiontree</span><span class="o">.</span><span class="n">tablename</span>

        <span class="k">if</span> <span class="n">tablename</span> <span class="ow">is</span> <span class="kc">None</span> <span class="ow">or</span> <span class="nb">len</span><span class="p">(</span><span class="n">tablename</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">primary_tablename</span> <span class="ow">is</span> <span class="kc">None</span> <span class="ow">or</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">primary_tablename</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="s2">&quot;cannot parse column name in filter expression, no table name defined for expression tree nor is any PrimaryTableName defined.&quot;</span><span class="p">)</span>

            <span class="n">tablename</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">primary_tablename</span>

        <span class="n">table</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">table</span><span class="p">(</span><span class="n">tablename</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;cannot parse column name in filter expression, </span><span class="si">{</span><span class="n">err</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

        <span class="n">columnname</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">IDENTIFIER</span><span class="p">()</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span>
        <span class="n">datacolumn</span> <span class="o">=</span> <span class="n">table</span><span class="o">.</span><span class="n">column_byname</span><span class="p">(</span><span class="n">columnname</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">datacolumn</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;cannot parse column name in filter expression, failed to find column </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">columnname</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2"> in table </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">tablename</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_add_expr</span><span class="p">(</span><span class="n">ctx</span><span class="p">,</span> <span class="n">ColumnExpression</span><span class="p">(</span><span class="n">datacolumn</span><span class="p">))</span></div>


    <span class="c1">#    functionExpression</span>
    <span class="c1">#     : functionName &#39;(&#39; expressionList? &#39;)&#39;</span>
    <span class="c1">#     ;</span>
<div class="viewcode-block" id="FilterExpressionParser.exitFunctionExpression">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.exitFunctionExpression">[docs]</a>
    <span class="k">def</span> <span class="nf">exitFunctionExpression</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">ctx</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">FunctionExpressionContext</span><span class="p">):</span>
        <span class="c1"># sourcery skip</span>
        <span class="n">functionname</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">FunctionNameContext</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">functionName</span><span class="p">()</span>

        <span class="k">if</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_ABS</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">ABS</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_CEILING</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">CEILING</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_COALESCE</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">COALESCE</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_CONVERT</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">CONVERT</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_CONTAINS</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">CONTAINS</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_DATEADD</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">DATEADD</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_DATEDIFF</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">DATEDIFF</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_DATEPART</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">DATEPART</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_ENDSWITH</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">ENDSWITH</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_FLOOR</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">FLOOR</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_IIF</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">IIF</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_INDEXOF</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">INDEXOF</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_ISDATE</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">ISDATE</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_ISINTEGER</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">ISINTEGER</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_ISGUID</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">ISGUID</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_ISNULL</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">ISNULL</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_ISNUMERIC</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">ISNUMERIC</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_LASTINDEXOF</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">LASTINDEXOF</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_LEN</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">LEN</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_LOWER</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">LOWER</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_MAXOF</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">MAXOF</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_MINOF</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">MINOF</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_NOW</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">NOW</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_NTHINDEXOF</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">NTHINDEXOF</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_POWER</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">POWER</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_REGEXMATCH</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">REGEXMATCH</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_REGEXVAL</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">REGEXVAL</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_REPLACE</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">REPLACE</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_REVERSE</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">REVERSE</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_ROUND</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">ROUND</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_SPLIT</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">SPLIT</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_SQRT</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">SQRT</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_STARTSWITH</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">STARTSWITH</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_STRCOUNT</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">STRCOUNT</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_STRCMP</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">STRCMP</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_SUBSTR</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">SUBSTR</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_TRIM</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">TRIM</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_TRIMLEFT</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">TRIMLEFT</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_TRIMRIGHT</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">TRIMRIGHT</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_UPPER</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">UPPER</span>
        <span class="k">elif</span> <span class="n">functionname</span><span class="o">.</span><span class="n">K_UTCNOW</span><span class="p">()</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">functiontype</span> <span class="o">=</span> <span class="n">ExpressionFunctionType</span><span class="o">.</span><span class="n">UTCNOW</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;unknown function </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">functionname</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

        <span class="n">expressionlist</span><span class="p">:</span> <span class="n">ExpressionParser</span><span class="o">.</span><span class="n">ExpressionListContext</span> <span class="o">=</span> <span class="n">ctx</span><span class="o">.</span><span class="n">expressionList</span><span class="p">()</span>
        <span class="n">arguments</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">Expression</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>

        <span class="k">if</span> <span class="n">expressionlist</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">expressions</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">ExpressionParser</span><span class="o">.</span><span class="n">ExpressionContext</span><span class="p">]</span> <span class="o">=</span> <span class="n">expressionlist</span><span class="o">.</span><span class="n">expression</span><span class="p">()</span>
            <span class="n">argumentcount</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">expressions</span><span class="p">)</span>

            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">argumentcount</span><span class="p">):</span>
                <span class="n">argument</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_expr</span><span class="p">(</span><span class="n">expressions</span><span class="p">[</span><span class="n">i</span><span class="p">])</span>

                <span class="k">if</span> <span class="n">argument</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                    <span class="k">raise</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to find argument expression </span><span class="si">{</span><span class="n">i</span><span class="si">}</span><span class="s2"> </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">expressions</span><span class="p">[</span><span class="n">i</span><span class="p">]</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2"> for function </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">functionname</span><span class="o">.</span><span class="n">getText</span><span class="p">()</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span>

                <span class="n">arguments</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">argument</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_add_expr</span><span class="p">(</span><span class="n">ctx</span><span class="p">,</span> <span class="n">FunctionExpression</span><span class="p">(</span><span class="n">functiontype</span><span class="p">,</span> <span class="n">arguments</span><span class="p">))</span></div>


<div class="viewcode-block" id="FilterExpressionParser.generate_expressiontrees">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.generate_expressiontrees">[docs]</a>
    <span class="nd">@staticmethod</span>
    <span class="k">def</span> <span class="nf">generate_expressiontrees</span><span class="p">(</span><span class="n">dataset</span><span class="p">:</span> <span class="n">DataSet</span><span class="p">,</span> <span class="n">primarytable</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">filterexpression</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">List</span><span class="p">[</span><span class="n">ExpressionTree</span><span class="p">]],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Produces a set of expression trees for the provided `filterexpression` and `dataset`.</span>

<span class="sd">        One expression tree will be produced per filter expression statement encountered in the specified `filterexpression`.</span>
<span class="sd">        If `primarytable` parameter is not defined, then filter expression should not contain directly defined signal IDs.</span>
<span class="sd">        An error will be returned if `dataSet` parameter is None, the `filterexpression` is empty or expression fails to parse.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">parser</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="n">FilterExpressionParser</span><span class="o">.</span><span class="n">from_dataset</span><span class="p">(</span><span class="n">dataset</span><span class="p">,</span> <span class="n">filterexpression</span><span class="p">,</span> <span class="n">primarytable</span><span class="p">,</span> <span class="kc">None</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="n">err</span>

        <span class="n">parser</span><span class="o">.</span><span class="n">track_filteredrows</span> <span class="o">=</span> <span class="kc">False</span>

        <span class="k">return</span> <span class="n">parser</span><span class="o">.</span><span class="n">expressiontrees</span></div>


<div class="viewcode-block" id="FilterExpressionParser.generate_expressiontrees_fromtable">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.generate_expressiontrees_fromtable">[docs]</a>
    <span class="nd">@staticmethod</span>
    <span class="k">def</span> <span class="nf">generate_expressiontrees_fromtable</span><span class="p">(</span><span class="n">datatable</span><span class="p">:</span> <span class="n">DataTable</span><span class="p">,</span> <span class="n">filterexpression</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">List</span><span class="p">[</span><span class="n">ExpressionTree</span><span class="p">]],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Produces a set of expression trees for the provided `filterexpression` and `datatable`.</span>

<span class="sd">        One expression tree will be produced per filter expression statement encountered in the specified `filterexpression`.</span>
<span class="sd">        An error will be returned if `datatable` parameter is None, the `filterexpression` is empty or expression fails to parse.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="n">datatable</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;datatable parameter cannot be None&quot;</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">FilterExpressionParser</span><span class="o">.</span><span class="n">generate_expressiontrees</span><span class="p">(</span><span class="n">datatable</span><span class="o">.</span><span class="n">parent</span><span class="p">,</span> <span class="n">datatable</span><span class="o">.</span><span class="n">name</span><span class="p">,</span> <span class="n">filterexpression</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">)</span></div>


<div class="viewcode-block" id="FilterExpressionParser.generate_expressiontree">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.generate_expressiontree">[docs]</a>
    <span class="nd">@staticmethod</span>
    <span class="k">def</span> <span class="nf">generate_expressiontree</span><span class="p">(</span><span class="n">datatable</span><span class="p">:</span> <span class="n">DataTable</span><span class="p">,</span> <span class="n">filterexpression</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">ExpressionTree</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the first produced expression tree for the provided `filterexpression` and `datatable`.</span>

<span class="sd">        If `filterexpression` contains multiple semi-colon separated statements, only the first expression is returned.</span>
<span class="sd">        An error will be returned if `datatable` parameter is None, the `filterexpression` is empty or expression fails to parse.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">expressiontrees</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="n">FilterExpressionParser</span><span class="o">.</span><span class="n">generate_expressiontrees_fromtable</span><span class="p">(</span><span class="n">datatable</span><span class="p">,</span> <span class="n">filterexpression</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="n">err</span>

        <span class="k">if</span> <span class="n">expressiontrees</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">and</span> <span class="nb">len</span><span class="p">(</span><span class="n">expressiontrees</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">expressiontrees</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span> <span class="kc">None</span>

        <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;no expression trees generated with filter expression </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">filterexpression</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2"> for table </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">datatable</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span></div>


<div class="viewcode-block" id="FilterExpressionParser.evaluate_expression">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.evaluate_expression">[docs]</a>
    <span class="nd">@staticmethod</span>
    <span class="k">def</span> <span class="nf">evaluate_expression</span><span class="p">(</span><span class="n">filterexpression</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">ValueExpression</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Returns the result of the evaluated `filterexpression`.</span>

<span class="sd">        This expression evaluation function is only for simple expressions that do not reference any `DataSet` columns.</span>
<span class="sd">        Use `evaluate_datarowexpression` for evaluating filter expressions that contain column references.</span>
<span class="sd">        If `filterexpression` contains multiple semi-colon separated statements, only the first expression is evaluated.</span>
<span class="sd">        An error will be returned if the `filterexpression` is empty or expression fails to parse.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="n">filterexpression</span> <span class="ow">is</span> <span class="kc">None</span> <span class="ow">or</span> <span class="ow">not</span> <span class="n">filterexpression</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="s2">&quot;filterexpression is empty&quot;</span><span class="p">)</span>

        <span class="n">parser</span> <span class="o">=</span> <span class="n">FilterExpressionParser</span><span class="p">(</span><span class="n">filterexpression</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">)</span>
        <span class="n">parser</span><span class="o">.</span><span class="n">track_filteredrows</span> <span class="o">=</span> <span class="kc">False</span>

        <span class="n">expressiontrees</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="n">parser</span><span class="o">.</span><span class="n">expressiontrees</span>

        <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="n">err</span>

        <span class="k">if</span> <span class="n">expressiontrees</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">and</span> <span class="nb">len</span><span class="p">(</span><span class="n">expressiontrees</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">expressiontrees</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span><span class="o">.</span><span class="n">evaluate</span><span class="p">()</span>

        <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;no expression trees generated with filter expression </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">filterexpression</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2">&quot;</span><span class="p">)</span></div>


<div class="viewcode-block" id="FilterExpressionParser.evaluate_datarowexpression">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.evaluate_datarowexpression">[docs]</a>
    <span class="nd">@staticmethod</span>
    <span class="k">def</span> <span class="nf">evaluate_datarowexpression</span><span class="p">(</span><span class="n">datarow</span><span class="p">:</span> <span class="n">DataRow</span><span class="p">,</span> <span class="n">filterexpression</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">ValueExpression</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Returns the result of the evaluated `filterexpression` using the specified `datarow`.</span>

<span class="sd">        If `filterexpression` contains multiple semi-colon separated statements, only the first expression is evaluated.</span>
<span class="sd">        An error will be returned if `datarow` parameter is None, the `filterexpression` is empty, expression fails to</span>
<span class="sd">        parse or row expression evaluation fails.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="n">datarow</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;datarow is None&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">filterexpression</span> <span class="ow">is</span> <span class="kc">None</span> <span class="ow">or</span> <span class="ow">not</span> <span class="n">filterexpression</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="s2">&quot;filterexpression is empty&quot;</span><span class="p">)</span>

        <span class="n">expressiontree</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="n">FilterExpressionParser</span><span class="o">.</span><span class="n">generate_expressiontree</span><span class="p">(</span><span class="n">datarow</span><span class="o">.</span><span class="n">parent</span><span class="p">,</span> <span class="n">filterexpression</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">)</span>

        <span class="k">return</span> <span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">err</span><span class="p">)</span> <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="n">expressiontree</span><span class="o">.</span><span class="n">evaluate</span><span class="p">(</span><span class="n">datarow</span><span class="p">)</span></div>


<div class="viewcode-block" id="FilterExpressionParser.select_datarows">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.select_datarows">[docs]</a>
    <span class="nd">@staticmethod</span>
    <span class="k">def</span> <span class="nf">select_datarows</span><span class="p">(</span><span class="n">dataset</span><span class="p">:</span> <span class="n">DataSet</span><span class="p">,</span> <span class="n">filterexpression</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">primarytable</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">tableidfields</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">TableIDFields</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">List</span><span class="p">[</span><span class="n">DataRow</span><span class="p">]],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Returns all rows matching the provided `filterexpression` and `dataset`.</span>

<span class="sd">        Filter expressions can contain multiple statements, separated by semi-colons, where each statement results in a</span>
<span class="sd">        unique expression tree; this function returns the combined results of each encountered filter expression statement.</span>
<span class="sd">        Returned `DataRow` list will contain all matching rows, order preserved. If `dataset` includes duplicated rows, it</span>
<span class="sd">        will be possible for the result set to contain duplicates. Any encountered &quot;TOP&quot; limit or &quot;ORDER BY&quot; clauses will</span>
<span class="sd">        be respected. An error will be returned if `dataset` parameter is None, the `filterexpression` is empty, expression</span>
<span class="sd">        fails to parse or any row expression evaluation fails.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">parser</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="n">FilterExpressionParser</span><span class="o">.</span><span class="n">from_dataset</span><span class="p">(</span><span class="n">dataset</span><span class="p">,</span> <span class="n">filterexpression</span><span class="p">,</span> <span class="n">primarytable</span><span class="p">,</span> <span class="n">tableidfields</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="n">err</span>

        <span class="n">err</span> <span class="o">=</span> <span class="n">parser</span><span class="o">.</span><span class="n">evaluate</span><span class="p">(</span><span class="kc">True</span><span class="p">,</span> <span class="kc">True</span><span class="p">)</span>

        <span class="k">return</span> <span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">err</span><span class="p">)</span> <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="p">(</span><span class="n">parser</span><span class="o">.</span><span class="n">filtered_rows</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span></div>


<div class="viewcode-block" id="FilterExpressionParser.select_datarows_fromtable">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.select_datarows_fromtable">[docs]</a>
    <span class="nd">@staticmethod</span>
    <span class="k">def</span> <span class="nf">select_datarows_fromtable</span><span class="p">(</span><span class="n">datatable</span><span class="p">:</span> <span class="n">DataTable</span><span class="p">,</span> <span class="n">filterexpression</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">tableidfields</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">TableIDFields</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">List</span><span class="p">[</span><span class="n">DataRow</span><span class="p">]],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Returns all rows matching the provided `filterexpression` and `datatable`.</span>

<span class="sd">        Filter expressions can contain multiple statements, separated by semi-colons, where each statement results</span>
<span class="sd">        in a unique expression tree; this function returns the combined results of each encountered filter expression</span>
<span class="sd">        statement. Returned `DataRow` list will contain all matching rows, order preserved. If dataSet includes</span>
<span class="sd">        duplicated rows, it will be possible for the result set to contain duplicates. Any encountered &quot;TOP&quot; limit or</span>
<span class="sd">        &quot;ORDER BY&quot; clauses will be respected. An error will be returned if `datatable` parameter (or its parent DataSet)</span>
<span class="sd">        is None, the `filterexpression` is empty, expression fails to parse or any row expression evaluation fails.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="n">datatable</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;datatable is None&quot;</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">FilterExpressionParser</span><span class="o">.</span><span class="n">select_datarows</span><span class="p">(</span><span class="n">datatable</span><span class="o">.</span><span class="n">parent</span><span class="p">,</span> <span class="n">filterexpression</span><span class="p">,</span> <span class="n">datatable</span><span class="o">.</span><span class="n">name</span><span class="p">,</span> <span class="n">tableidfields</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">)</span></div>


<div class="viewcode-block" id="FilterExpressionParser.select_datarowset">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.select_datarowset">[docs]</a>
    <span class="nd">@staticmethod</span>
    <span class="k">def</span> <span class="nf">select_datarowset</span><span class="p">(</span><span class="n">dataset</span><span class="p">:</span> <span class="n">DataSet</span><span class="p">,</span> <span class="n">filterexpression</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">primarytable</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">tableidfields</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">TableIDFields</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">Set</span><span class="p">[</span><span class="n">DataRow</span><span class="p">]],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Returns all unique rows matching the provided `filterexpression` and `dataset`.</span>

<span class="sd">        Filter expressions can contain multiple statements, separated by semi-colons, where each statement results</span>
<span class="sd">        in a unique expression tree; this function returns the combined results of each encountered filter expression</span>
<span class="sd">        statement. Returned `DataRow` set will contain only unique rows, in arbitrary order. Any encountered &quot;TOP&quot; limit</span>
<span class="sd">        clauses for individual filter expression statements will be respected, but &quot;ORDER BY&quot; clauses will be ignored.</span>
<span class="sd">        An error will be returned if `dataset` parameter is None, the `filterexpression` is empty, expression fails to</span>
<span class="sd">        parse or any row expression evaluation fails.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">parser</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="n">FilterExpressionParser</span><span class="o">.</span><span class="n">from_dataset</span><span class="p">(</span><span class="n">dataset</span><span class="p">,</span> <span class="n">filterexpression</span><span class="p">,</span> <span class="n">primarytable</span><span class="p">,</span> <span class="n">tableidfields</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="n">err</span>

        <span class="n">err</span> <span class="o">=</span> <span class="n">parser</span><span class="o">.</span><span class="n">evaluate</span><span class="p">(</span><span class="kc">True</span><span class="p">,</span> <span class="kc">False</span><span class="p">)</span>

        <span class="k">return</span> <span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">err</span><span class="p">)</span> <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="p">(</span><span class="n">parser</span><span class="o">.</span><span class="n">filtered_rowset</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span></div>


<div class="viewcode-block" id="FilterExpressionParser.select_datarowset_fromtable">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.select_datarowset_fromtable">[docs]</a>
    <span class="nd">@staticmethod</span>
    <span class="k">def</span> <span class="nf">select_datarowset_fromtable</span><span class="p">(</span><span class="n">datatable</span><span class="p">:</span> <span class="n">DataTable</span><span class="p">,</span> <span class="n">filterexpression</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">tableidfields</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">TableIDFields</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">Set</span><span class="p">[</span><span class="n">DataRow</span><span class="p">]],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Returns all unique rows matching the provided `filterexpression` and `datatable`.</span>

<span class="sd">        Filter expressions can contain multiple statements, separated by semi-colons, where each statement results</span>
<span class="sd">        in a unique expression tree; this function returns the combined results of each encountered filter expression</span>
<span class="sd">        statement. Returned `DataRow` set will contain only unique rows, in arbitrary order. Any encountered &quot;TOP&quot; limit</span>
<span class="sd">        clauses for individual filter expression statements will be respected, but &quot;ORDER BY&quot; clauses will be ignored.</span>
<span class="sd">        An error will be returned if `datatable` parameter (or its parent DataSet) is None, the `filterexpression` is</span>
<span class="sd">        empty, expression fails to parse or any row expression evaluation fails.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="n">datatable</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;datatable is None&quot;</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">FilterExpressionParser</span><span class="o">.</span><span class="n">select_datarowset</span><span class="p">(</span><span class="n">datatable</span><span class="o">.</span><span class="n">parent</span><span class="p">,</span> <span class="n">filterexpression</span><span class="p">,</span> <span class="n">datatable</span><span class="o">.</span><span class="n">name</span><span class="p">,</span> <span class="n">tableidfields</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">)</span></div>


<div class="viewcode-block" id="FilterExpressionParser.select_signalidset">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.select_signalidset">[docs]</a>
    <span class="nd">@staticmethod</span>
    <span class="k">def</span> <span class="nf">select_signalidset</span><span class="p">(</span><span class="n">dataset</span><span class="p">:</span> <span class="n">DataSet</span><span class="p">,</span> <span class="n">filterexpression</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">primarytable</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">tableidfields</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">TableIDFields</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">Set</span><span class="p">[</span><span class="n">UUID</span><span class="p">]],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Returns all unique signal IDs matching the provided `filterexpression` and `dataset`.</span>

<span class="sd">        Filter expressions can contain multiple statements, separated by semi-colons, where each statement results</span>
<span class="sd">        in a unique expression tree; this function returns the combined results of each encountered filter expression</span>
<span class="sd">        statement. Returned `UUID` set will contain only unique values, in arbitrary order. Any encountered &quot;TOP&quot; limit</span>
<span class="sd">        clauses for individual filter expression statements will be respected, but &quot;ORDER BY&quot; clauses will be ignored.</span>
<span class="sd">        An error will be returned if `dataset` parameter is None, the `filterexpression` is empty, expression fails to</span>
<span class="sd">        parse or any row expression evaluation fails.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">parser</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="n">FilterExpressionParser</span><span class="o">.</span><span class="n">from_dataset</span><span class="p">(</span><span class="n">dataset</span><span class="p">,</span> <span class="n">filterexpression</span><span class="p">,</span> <span class="n">primarytable</span><span class="p">,</span> <span class="n">tableidfields</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="n">err</span>

        <span class="n">parser</span><span class="o">.</span><span class="n">track_filteredrows</span> <span class="o">=</span> <span class="kc">False</span>
        <span class="n">parser</span><span class="o">.</span><span class="n">track_filteredsignalids</span> <span class="o">=</span> <span class="kc">True</span>

        <span class="n">err</span> <span class="o">=</span> <span class="n">parser</span><span class="o">.</span><span class="n">evaluate</span><span class="p">(</span><span class="kc">True</span><span class="p">,</span> <span class="kc">False</span><span class="p">)</span>

        <span class="k">return</span> <span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">err</span><span class="p">)</span> <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="p">(</span><span class="n">parser</span><span class="o">.</span><span class="n">filtered_signalidset</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span></div>


<div class="viewcode-block" id="FilterExpressionParser.select_signalidset_fromtable">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.select_signalidset_fromtable">[docs]</a>
    <span class="nd">@staticmethod</span>
    <span class="k">def</span> <span class="nf">select_signalidset_fromtable</span><span class="p">(</span><span class="n">datatable</span><span class="p">:</span> <span class="n">DataTable</span><span class="p">,</span> <span class="n">filterexpression</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">tableidfields</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">TableIDFields</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">Set</span><span class="p">[</span><span class="n">UUID</span><span class="p">]],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Returns all unique signal IDs matching the provided `filterexpression` and `datatable`.</span>

<span class="sd">        Filter expressions can contain multiple statements, separated by semi-colons, where each statement results</span>
<span class="sd">        in a unique expression tree; this function returns the combined results of each encountered filter expression</span>
<span class="sd">        statement. Returned `UUID` set will contain only unique values, in arbitrary order. Any encountered &quot;TOP&quot; limit</span>
<span class="sd">        clauses for individual filter expression statements will be respected, but &quot;ORDER BY&quot; clauses will be ignored.</span>
<span class="sd">        An error will be returned if `datatable` parameter (or its parent DataSet) is None, the `filterexpression` is</span>
<span class="sd">        empty, expression fails to parse or any row expression evaluation fails.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="n">datatable</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;datatable is None&quot;</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">FilterExpressionParser</span><span class="o">.</span><span class="n">select_signalidset</span><span class="p">(</span><span class="n">datatable</span><span class="o">.</span><span class="n">parent</span><span class="p">,</span> <span class="n">filterexpression</span><span class="p">,</span> <span class="n">datatable</span><span class="o">.</span><span class="n">name</span><span class="p">,</span> <span class="n">tableidfields</span><span class="p">,</span> <span class="n">suppress_console_erroroutput</span><span class="p">)</span></div>
</div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
  
 
<hr />
<p>
    <a href="https://github.com/sttp/pyapi">GitHub Home</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://pypi.org/project/sttpapi/" target="_blank">PyPi Package: sttpapi</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://sttp.github.io/documentation/" target="_blank">General STTP Documentation</a>
</p>
<br /><br />
Copyright © 2022, <a href="https://gridprotectionalliance.org/" target="_blank">Grid&nbsp;Protection&nbsp;Alliance</a>
<a href="https://github.com/sttp/pyapi">
    <img align="right" style="margin-top: -50px"
        src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/LockPython_64High.png">
</a>


</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>