

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../../../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>sttp.data.unaryexpression &mdash; sttp/pyapi 0.6.4 documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=92fd9be5" />
      <link rel="stylesheet" type="text/css" href="../../../_static/css/theme.css?v=8209df7b" />

  
    <link rel="shortcut icon" href="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/favicon.ico"/>
      <script src="../../../_static/jquery.js?v=5d32c60e"></script>
      <script src="../../../_static/_sphinx_javascript_frameworks_compat.js?v=bbec6902"></script>
      <script src="../../../_static/documentation_options.js?v=b1f64a84"></script>
      <script src="../../../_static/doctools.js?v=92e14aea"></script>
      <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="../../../index.html" class="icon icon-home">
            sttp/pyapi
              <img src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/sttp.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <!-- Local TOC -->
              <div class="local-toc"></div>
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../../index.html">sttp/pyapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../../index.html">Module code</a></li>
      <li class="breadcrumb-item active">sttp.data.unaryexpression</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for sttp.data.unaryexpression</h1><div class="highlight"><pre>
<span></span><span class="c1"># ******************************************************************************************************</span>
<span class="c1">#  unaryexpression.py - Gbtc</span>
<span class="c1">#</span>
<span class="c1">#  Copyright © 2022, Grid Protection Alliance.  All Rights Reserved.</span>
<span class="c1">#</span>
<span class="c1">#  Licensed to the Grid Protection Alliance (GPA) under one or more contributor license agreements. See</span>
<span class="c1">#  the NOTICE file distributed with this work for additional information regarding copyright ownership.</span>
<span class="c1">#  The GPA licenses this file to you under the MIT License (MIT), the &quot;License&quot;; you may not use this</span>
<span class="c1">#  file except in compliance with the License. You may obtain a copy of the License at:</span>
<span class="c1">#</span>
<span class="c1">#      http://opensource.org/licenses/MIT</span>
<span class="c1">#</span>
<span class="c1">#  Unless agreed to in writing, the subject software distributed under the License is distributed on an</span>
<span class="c1">#  &quot;AS-IS&quot; BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. Refer to the</span>
<span class="c1">#  License for the specific language governing permissions and limitations.</span>
<span class="c1">#</span>
<span class="c1">#  Code Modification History:</span>
<span class="c1">#  ----------------------------------------------------------------------------------------------------</span>
<span class="c1">#  09/03/2022 - J. Ritchie Carroll</span>
<span class="c1">#       Generated original version of source code.</span>
<span class="c1">#</span>
<span class="c1"># ******************************************************************************************************</span>

<span class="kn">from</span> <span class="nn">gsf</span> <span class="kn">import</span> <span class="n">override</span><span class="p">,</span> <span class="n">normalize_enumname</span>
<span class="kn">from</span> <span class="nn">.expression</span> <span class="kn">import</span> <span class="n">Expression</span>
<span class="kn">from</span> <span class="nn">.valueexpression</span> <span class="kn">import</span> <span class="n">ValueExpression</span>
<span class="kn">from</span> <span class="nn">.constants</span> <span class="kn">import</span> <span class="n">ExpressionType</span><span class="p">,</span> <span class="n">ExpressionUnaryType</span><span class="p">,</span> <span class="n">ExpressionValueType</span>
<span class="kn">from</span> <span class="nn">.errors</span> <span class="kn">import</span> <span class="n">EvaluateError</span>
<span class="kn">from</span> <span class="nn">decimal</span> <span class="kn">import</span> <span class="n">Decimal</span>
<span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">Optional</span><span class="p">,</span> <span class="n">Tuple</span>
<span class="kn">import</span> <span class="nn">numpy</span> <span class="k">as</span> <span class="nn">np</span>

<div class="viewcode-block" id="UnaryExpression">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.unaryexpression.UnaryExpression">[docs]</a>
<span class="k">class</span> <span class="nc">UnaryExpression</span><span class="p">(</span><span class="n">Expression</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents a unary expression.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">unarytype</span><span class="p">:</span> <span class="n">ExpressionUnaryType</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">Expression</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_unarytype</span> <span class="o">=</span> <span class="n">unarytype</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_value</span> <span class="o">=</span> <span class="n">value</span>

    <span class="nd">@override</span>
    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">expressiontype</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ExpressionType</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the type of this `UnaryExpression`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="n">ExpressionType</span><span class="o">.</span><span class="n">UNARY</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">unarytype</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ExpressionUnaryType</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the type of unary operation of this `UnaryExpression`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_unarytype</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">value</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Expression</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the value of this `UnaryExpression`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_value</span>

<div class="viewcode-block" id="UnaryExpression.applyto_bool">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.unaryexpression.UnaryExpression.applyto_bool">[docs]</a>
    <span class="k">def</span> <span class="nf">applyto_bool</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="nb">bool</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">ValueExpression</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Applies the `UnaryExpression` prefix to a boolean and returns `ValueExpression` of result, if possible.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_unarytype</span> <span class="o">==</span> <span class="n">ExpressionUnaryType</span><span class="o">.</span><span class="n">NOT</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">ValueExpression</span><span class="p">(</span><span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">BOOLEAN</span><span class="p">,</span> <span class="ow">not</span> <span class="n">value</span><span class="p">),</span> <span class="kc">None</span>

        <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;cannot apply unary type </span><span class="se">\&quot;</span><span class="si">{</span><span class="n">normalize_enumname</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_unarytype</span><span class="p">)</span><span class="si">}</span><span class="se">\&quot;</span><span class="s2"> to </span><span class="se">\&quot;</span><span class="s2">Boolean</span><span class="se">\&quot;</span><span class="s2"> value&quot;</span><span class="p">)</span></div>


<div class="viewcode-block" id="UnaryExpression.applyto_int32">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.unaryexpression.UnaryExpression.applyto_int32">[docs]</a>
    <span class="k">def</span> <span class="nf">applyto_int32</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">ValueExpression</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Applies the `UnaryExpression` prefix to a 32-bit integer value and returns `ValueExpression` of result.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_unarytype</span> <span class="o">==</span> <span class="n">ExpressionUnaryType</span><span class="o">.</span><span class="n">PLUS</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">ValueExpression</span><span class="p">(</span><span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">INT32</span><span class="p">,</span> <span class="o">+</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_unarytype</span> <span class="o">==</span> <span class="n">ExpressionUnaryType</span><span class="o">.</span><span class="n">MINUS</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">ValueExpression</span><span class="p">(</span><span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">INT32</span><span class="p">,</span> <span class="o">-</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>

        <span class="k">return</span> <span class="n">ValueExpression</span><span class="p">(</span><span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">INT32</span><span class="p">,</span> <span class="o">~</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span></div>


<div class="viewcode-block" id="UnaryExpression.applyto_int64">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.unaryexpression.UnaryExpression.applyto_int64">[docs]</a>
    <span class="k">def</span> <span class="nf">applyto_int64</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">int64</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">ValueExpression</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Applies the `UnaryExpression` prefix to a 64-bit integer value and returns `ValueExpression` of result.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_unarytype</span> <span class="o">==</span> <span class="n">ExpressionUnaryType</span><span class="o">.</span><span class="n">PLUS</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">ValueExpression</span><span class="p">(</span><span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">INT64</span><span class="p">,</span> <span class="o">+</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_unarytype</span> <span class="o">==</span> <span class="n">ExpressionUnaryType</span><span class="o">.</span><span class="n">MINUS</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">ValueExpression</span><span class="p">(</span><span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">INT64</span><span class="p">,</span> <span class="o">-</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>

        <span class="k">return</span> <span class="n">ValueExpression</span><span class="p">(</span><span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">INT64</span><span class="p">,</span> <span class="o">~</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span></div>


<div class="viewcode-block" id="UnaryExpression.applyto_decimal">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.unaryexpression.UnaryExpression.applyto_decimal">[docs]</a>
    <span class="k">def</span> <span class="nf">applyto_decimal</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">Decimal</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">ValueExpression</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Applies the `UnaryExpression` prefix to a `Decimal` value and returns `ValueExpression` of result, if possible.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_unarytype</span> <span class="o">==</span> <span class="n">ExpressionUnaryType</span><span class="o">.</span><span class="n">PLUS</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">ValueExpression</span><span class="p">(</span><span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">DECIMAL</span><span class="p">,</span> <span class="o">+</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_unarytype</span> <span class="o">==</span> <span class="n">ExpressionUnaryType</span><span class="o">.</span><span class="n">MINUS</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">ValueExpression</span><span class="p">(</span><span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">DECIMAL</span><span class="p">,</span> <span class="o">-</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>

        <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;cannot apply unary type </span><span class="se">\&quot;</span><span class="s2">~</span><span class="se">\&quot;</span><span class="s2"> to </span><span class="se">\&quot;</span><span class="s2">Decimal</span><span class="se">\&quot;</span><span class="s2"> value&quot;</span><span class="p">)</span></div>


<div class="viewcode-block" id="UnaryExpression.applyto_double">
<a class="viewcode-back" href="../../../sttp.data.html#sttp.data.unaryexpression.UnaryExpression.applyto_double">[docs]</a>
    <span class="k">def</span> <span class="nf">applyto_double</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">float64</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">ValueExpression</span><span class="p">],</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Applies the `UnaryExpression` prefix to a `Double` value and returns `ValueExpression` of result, if possible.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_unarytype</span> <span class="o">==</span> <span class="n">ExpressionUnaryType</span><span class="o">.</span><span class="n">PLUS</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">ValueExpression</span><span class="p">(</span><span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">DOUBLE</span><span class="p">,</span> <span class="o">+</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_unarytype</span> <span class="o">==</span> <span class="n">ExpressionUnaryType</span><span class="o">.</span><span class="n">MINUS</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">ValueExpression</span><span class="p">(</span><span class="n">ExpressionValueType</span><span class="o">.</span><span class="n">DOUBLE</span><span class="p">,</span> <span class="o">-</span><span class="n">value</span><span class="p">),</span> <span class="kc">None</span>

        <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="n">EvaluateError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;cannot apply unary type </span><span class="se">\&quot;</span><span class="s2">~</span><span class="se">\&quot;</span><span class="s2"> to </span><span class="se">\&quot;</span><span class="s2">Double</span><span class="se">\&quot;</span><span class="s2"> value&quot;</span><span class="p">)</span></div>
</div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
  
 
<hr />
<p>
    <a href="https://github.com/sttp/pyapi">GitHub Home</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://pypi.org/project/sttpapi/" target="_blank">PyPi Package: sttpapi</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://sttp.github.io/documentation/" target="_blank">General STTP Documentation</a>
</p>
<br /><br />
Copyright © 2022, <a href="https://gridprotectionalliance.org/" target="_blank">Grid&nbsp;Protection&nbsp;Alliance</a>
<a href="https://github.com/sttp/pyapi">
    <img align="right" style="margin-top: -50px"
        src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/LockPython_64High.png">
</a>


</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>