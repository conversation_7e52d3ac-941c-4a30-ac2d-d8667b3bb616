

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../../../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>sttp.transport.signalindexcache &mdash; sttp/pyapi 0.6.4 documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=92fd9be5" />
      <link rel="stylesheet" type="text/css" href="../../../_static/css/theme.css?v=8209df7b" />

  
    <link rel="shortcut icon" href="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/favicon.ico"/>
      <script src="../../../_static/jquery.js?v=5d32c60e"></script>
      <script src="../../../_static/_sphinx_javascript_frameworks_compat.js?v=bbec6902"></script>
      <script src="../../../_static/documentation_options.js?v=b1f64a84"></script>
      <script src="../../../_static/doctools.js?v=92e14aea"></script>
      <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="../../../index.html" class="icon icon-home">
            sttp/pyapi
              <img src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/sttp.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <!-- Local TOC -->
              <div class="local-toc"></div>
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../../index.html">sttp/pyapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../../index.html">Module code</a></li>
      <li class="breadcrumb-item active">sttp.transport.signalindexcache</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for sttp.transport.signalindexcache</h1><div class="highlight"><pre>
<span></span><span class="c1"># ******************************************************************************************************</span>
<span class="c1">#  signalindexcache.py - Gbtc</span>
<span class="c1">#</span>
<span class="c1">#  Copyright © 2022, Grid Protection Alliance.  All Rights Reserved.</span>
<span class="c1">#</span>
<span class="c1">#  Licensed to the Grid Protection Alliance (GPA) under one or more contributor license agreements. See</span>
<span class="c1">#  the NOTICE file distributed with this work for additional information regarding copyright ownership.</span>
<span class="c1">#  The GPA licenses this file to you under the MIT License (MIT), the &quot;License&quot;; you may not use this</span>
<span class="c1">#  file except in compliance with the License. You may obtain a copy of the License at:</span>
<span class="c1">#</span>
<span class="c1">#      http://opensource.org/licenses/MIT</span>
<span class="c1">#</span>
<span class="c1">#  Unless agreed to in writing, the subject software distributed under the License is distributed on an</span>
<span class="c1">#  &quot;AS-IS&quot; BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. Refer to the</span>
<span class="c1">#  License for the specific language governing permissions and limitations.</span>
<span class="c1">#</span>
<span class="c1">#  Code Modification History:</span>
<span class="c1">#  ----------------------------------------------------------------------------------------------------</span>
<span class="c1">#  08/15/2022 - J. Ritchie Carroll</span>
<span class="c1">#       Generated original version of source code.</span>
<span class="c1">#</span>
<span class="c1"># ******************************************************************************************************</span>

<span class="kn">from</span> <span class="nn">__future__</span> <span class="kn">import</span> <span class="n">annotations</span>
<span class="kn">from</span> <span class="nn">gsf</span> <span class="kn">import</span> <span class="n">Empty</span><span class="p">,</span> <span class="n">Limits</span>
<span class="kn">from</span> <span class="nn">gsf.endianorder</span> <span class="kn">import</span> <span class="n">BigEndian</span>
<span class="kn">from</span> <span class="nn">.tssc.decoder</span> <span class="kn">import</span> <span class="n">Decoder</span>
<span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">Dict</span><span class="p">,</span> <span class="n">List</span><span class="p">,</span> <span class="n">Set</span><span class="p">,</span> <span class="n">Tuple</span><span class="p">,</span> <span class="n">Optional</span><span class="p">,</span> <span class="n">TYPE_CHECKING</span>
<span class="kn">from</span> <span class="nn">uuid</span> <span class="kn">import</span> <span class="n">UUID</span>
<span class="kn">import</span> <span class="nn">numpy</span> <span class="k">as</span> <span class="nn">np</span>

<span class="k">if</span> <span class="n">TYPE_CHECKING</span><span class="p">:</span>
    <span class="kn">from</span> <span class="nn">datasubscriber</span> <span class="kn">import</span> <span class="n">DataSubscriber</span>


<div class="viewcode-block" id="SignalIndexCache">
<a class="viewcode-back" href="../../../sttp.transport.html#sttp.transport.signalindexcache.SignalIndexCache">[docs]</a>
<span class="k">class</span> <span class="nc">SignalIndexCache</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Represents a mapping of 32-bit runtime IDs to 128-bit globally unique measurement IDs. The class</span>
<span class="sd">    additionally provides reverse lookup and an extra mapping to human-readable measurement keys.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_reference</span><span class="p">:</span> <span class="n">Dict</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">]</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_signalidlist</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">UUID</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_sourcelist</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_idlist</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_signalidcache</span><span class="p">:</span> <span class="n">Dict</span><span class="p">[</span><span class="n">UUID</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">]</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_binarylength</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_tsscdecoder</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Decoder</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">_add_record</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">ds</span><span class="p">:</span> <span class="n">DataSubscriber</span><span class="p">,</span> <span class="n">signalindex</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">,</span> <span class="n">signalid</span><span class="p">:</span> <span class="n">UUID</span><span class="p">,</span> <span class="n">source</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="nb">id</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">,</span> <span class="n">charsizeestimate</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span> <span class="o">=</span> <span class="mi">1</span><span class="p">):</span>
        <span class="n">index</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_signalidlist</span><span class="p">))</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_reference</span><span class="p">[</span><span class="n">signalindex</span><span class="p">]</span> <span class="o">=</span> <span class="n">index</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_signalidlist</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">signalid</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_sourcelist</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">source</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_idlist</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="nb">id</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_signalidcache</span><span class="p">[</span><span class="n">signalid</span><span class="p">]</span> <span class="o">=</span> <span class="n">signalindex</span>

        <span class="c1"># Lookup measurement metadata, registering it if not defined already</span>
        <span class="n">metadata</span> <span class="o">=</span> <span class="n">ds</span><span class="o">.</span><span class="n">lookup_metadata</span><span class="p">(</span><span class="n">signalid</span><span class="p">,</span> <span class="n">source</span><span class="p">,</span> <span class="nb">id</span><span class="p">)</span>

        <span class="c1"># Char size here helps provide a rough-estimate on binary length used to reserve</span>
        <span class="c1"># bytes for a vector, if exact size is needed call RecalculateBinaryLength first</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_binarylength</span> <span class="o">+=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="mi">32</span> <span class="o">+</span> <span class="nb">len</span><span class="p">(</span><span class="n">source</span><span class="p">)</span> <span class="o">*</span> <span class="n">charsizeestimate</span><span class="p">)</span>

<div class="viewcode-block" id="SignalIndexCache.contains">
<a class="viewcode-back" href="../../../sttp.transport.html#sttp.transport.signalindexcache.SignalIndexCache.contains">[docs]</a>
    <span class="k">def</span> <span class="nf">contains</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">signalindex</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Determines if the specified signalindex exists with the `SignalIndexCache`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="n">signalindex</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_reference</span></div>


<div class="viewcode-block" id="SignalIndexCache.signalid">
<a class="viewcode-back" href="../../../sttp.transport.html#sttp.transport.signalindexcache.SignalIndexCache.signalid">[docs]</a>
    <span class="k">def</span> <span class="nf">signalid</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">signalindex</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">UUID</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Returns the signal ID Guid for the specified signalindex in the `SignalIndexCache`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="p">(</span><span class="n">index</span> <span class="o">:=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_reference</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">signalindex</span><span class="p">))</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_signalidlist</span><span class="p">[</span><span class="n">index</span><span class="p">]</span>

        <span class="k">return</span> <span class="n">Empty</span><span class="o">.</span><span class="n">GUID</span></div>


    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">signalids</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Set</span><span class="p">[</span><span class="n">UUID</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets a set for all the Guid values found in the `SignalIndexCache`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="nb">set</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_signalidlist</span><span class="p">)</span>

<div class="viewcode-block" id="SignalIndexCache.source">
<a class="viewcode-back" href="../../../sttp.transport.html#sttp.transport.signalindexcache.SignalIndexCache.source">[docs]</a>
    <span class="k">def</span> <span class="nf">source</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">signalindex</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Returns the `Measurement` source string for the specified signalindex in the `SignalIndexCache`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="p">(</span><span class="n">index</span> <span class="o">:=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_reference</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">signalindex</span><span class="p">))</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_sourcelist</span><span class="p">[</span><span class="n">index</span><span class="p">]</span>

        <span class="k">return</span> <span class="n">Empty</span><span class="o">.</span><span class="n">STRING</span></div>


<div class="viewcode-block" id="SignalIndexCache.id">
<a class="viewcode-back" href="../../../sttp.transport.html#sttp.transport.signalindexcache.SignalIndexCache.id">[docs]</a>
    <span class="k">def</span> <span class="nf">id</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">signalindex</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Returns the `Measurement` integer ID for the specified signalindex in the `SignalIndexCache`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="p">(</span><span class="n">index</span> <span class="o">:=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_reference</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">signalindex</span><span class="p">))</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_idlist</span><span class="p">[</span><span class="n">index</span><span class="p">]</span>

        <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="n">Limits</span><span class="o">.</span><span class="n">MAXUINT64</span><span class="p">)</span></div>


<div class="viewcode-block" id="SignalIndexCache.record">
<a class="viewcode-back" href="../../../sttp.transport.html#sttp.transport.signalindexcache.SignalIndexCache.record">[docs]</a>
    <span class="k">def</span> <span class="nf">record</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">signalindex</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">UUID</span><span class="p">,</span> <span class="nb">str</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">,</span> <span class="nb">bool</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Record returns the key `Measurement` values, signal ID Guid, source string, and integer ID and a</span>
<span class="sd">        final boolean value representing find success for the specified signalindex in the `SignalIndexCache`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="p">(</span><span class="n">index</span> <span class="o">:=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_reference</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">signalindex</span><span class="p">))</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_signalidlist</span><span class="p">[</span><span class="n">index</span><span class="p">],</span> <span class="bp">self</span><span class="o">.</span><span class="n">_sourcelist</span><span class="p">[</span><span class="n">index</span><span class="p">],</span> <span class="bp">self</span><span class="o">.</span><span class="n">_idlist</span><span class="p">[</span><span class="n">index</span><span class="p">],</span> <span class="kc">True</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">Empty</span><span class="o">.</span><span class="n">GUID</span><span class="p">,</span> <span class="n">Empty</span><span class="o">.</span><span class="n">STRING</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mi">0</span><span class="p">),</span> <span class="kc">False</span></div>


<div class="viewcode-block" id="SignalIndexCache.signalindex">
<a class="viewcode-back" href="../../../sttp.transport.html#sttp.transport.signalindexcache.SignalIndexCache.signalindex">[docs]</a>
    <span class="k">def</span> <span class="nf">signalindex</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">signalid</span><span class="p">:</span> <span class="n">UUID</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Returns the signal index for the specified signal ID Guid in the `SignalIndexCache`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="p">(</span><span class="n">signalindex</span> <span class="o">:=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_signalidcache</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">signalid</span><span class="p">))</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">signalindex</span>

        <span class="k">return</span> <span class="o">-</span><span class="mi">1</span></div>


    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">count</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Gets the number of `Measurement` records that can be found in the `SignalIndexCache`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_signalidcache</span><span class="p">))</span>

<div class="viewcode-block" id="SignalIndexCache.decode">
<a class="viewcode-back" href="../../../sttp.transport.html#sttp.transport.signalindexcache.SignalIndexCache.decode">[docs]</a>
    <span class="k">def</span> <span class="nf">decode</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">ds</span><span class="p">:</span> <span class="n">DataSubscriber</span><span class="p">,</span> <span class="n">buffer</span><span class="p">:</span> <span class="nb">bytes</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">UUID</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Parses a `SignalIndexCache` from the specified byte buffer received from a `DataPublisher`.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">length</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">buffer</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">length</span> <span class="o">&lt;</span> <span class="mi">4</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">Empty</span><span class="o">.</span><span class="n">GUID</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;not enough buffer provided to parse&quot;</span><span class="p">)</span>

        <span class="c1"># Byte size of cache</span>
        <span class="n">binarylength</span> <span class="o">=</span> <span class="n">BigEndian</span><span class="o">.</span><span class="n">to_uint32</span><span class="p">(</span><span class="n">buffer</span><span class="p">)</span>
        <span class="n">offset</span> <span class="o">=</span> <span class="mi">4</span>

        <span class="k">if</span> <span class="n">length</span> <span class="o">&lt;</span> <span class="n">binarylength</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">Empty</span><span class="o">.</span><span class="n">GUID</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;not enough buffer provided to parse&quot;</span><span class="p">)</span>

        <span class="n">subscriberid</span> <span class="o">=</span> <span class="n">UUID</span><span class="p">(</span><span class="nb">bytes</span><span class="o">=</span><span class="n">buffer</span><span class="p">[</span><span class="n">offset</span><span class="p">:</span><span class="n">offset</span> <span class="o">+</span> <span class="mi">16</span><span class="p">])</span>
        <span class="n">offset</span> <span class="o">+=</span> <span class="mi">16</span>

        <span class="c1"># Number of references</span>
        <span class="n">referencecount</span> <span class="o">=</span> <span class="n">BigEndian</span><span class="o">.</span><span class="n">to_uint32</span><span class="p">(</span><span class="n">buffer</span><span class="p">[</span><span class="n">offset</span><span class="p">:])</span>
        <span class="n">offset</span> <span class="o">+=</span> <span class="mi">4</span>

        <span class="k">for</span> <span class="n">_</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">referencecount</span><span class="p">):</span>
            <span class="c1"># Signal index</span>
            <span class="n">signalindex</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="n">BigEndian</span><span class="o">.</span><span class="n">to_uint32</span><span class="p">(</span><span class="n">buffer</span><span class="p">[</span><span class="n">offset</span><span class="p">:]))</span>
            <span class="n">offset</span> <span class="o">+=</span> <span class="mi">4</span>

            <span class="c1"># Signal ID</span>
            <span class="n">signalid</span> <span class="o">=</span> <span class="n">UUID</span><span class="p">(</span><span class="nb">bytes</span><span class="o">=</span><span class="n">buffer</span><span class="p">[</span><span class="n">offset</span><span class="p">:</span><span class="n">offset</span> <span class="o">+</span> <span class="mi">16</span><span class="p">])</span>

            <span class="n">offset</span> <span class="o">+=</span> <span class="mi">16</span>

            <span class="c1"># Measurement key Source</span>
            <span class="n">sourcesize</span> <span class="o">=</span> <span class="n">BigEndian</span><span class="o">.</span><span class="n">to_uint32</span><span class="p">(</span><span class="n">buffer</span><span class="p">[</span><span class="n">offset</span><span class="p">:])</span>
            <span class="n">offset</span> <span class="o">+=</span> <span class="mi">4</span>

            <span class="n">source</span> <span class="o">=</span> <span class="n">ds</span><span class="o">.</span><span class="n">decodestr</span><span class="p">(</span><span class="n">buffer</span><span class="p">[</span><span class="n">offset</span><span class="p">:</span> <span class="n">offset</span> <span class="o">+</span> <span class="n">sourcesize</span><span class="p">])</span>
            <span class="n">offset</span> <span class="o">+=</span> <span class="n">sourcesize</span>

            <span class="c1"># Measurement key ID</span>
            <span class="n">keyid</span> <span class="o">=</span> <span class="n">BigEndian</span><span class="o">.</span><span class="n">to_uint64</span><span class="p">(</span><span class="n">buffer</span><span class="p">[</span><span class="n">offset</span><span class="p">:])</span>
            <span class="n">offset</span> <span class="o">+=</span> <span class="mi">8</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">_add_record</span><span class="p">(</span><span class="n">ds</span><span class="p">,</span> <span class="n">signalindex</span><span class="p">,</span> <span class="n">signalid</span><span class="p">,</span> <span class="n">source</span><span class="p">,</span> <span class="n">keyid</span><span class="p">)</span>

        <span class="k">return</span> <span class="p">(</span><span class="n">subscriberid</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span></div>
</div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
  
 
<hr />
<p>
    <a href="https://github.com/sttp/pyapi">GitHub Home</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://pypi.org/project/sttpapi/" target="_blank">PyPi Package: sttpapi</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://sttp.github.io/documentation/" target="_blank">General STTP Documentation</a>
</p>
<br /><br />
Copyright © 2022, <a href="https://gridprotectionalliance.org/" target="_blank">Grid&nbsp;Protection&nbsp;Alliance</a>
<a href="https://github.com/sttp/pyapi">
    <img align="right" style="margin-top: -50px"
        src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/LockPython_64High.png">
</a>


</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>