

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../../../../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>sttp.transport.tssc.decoder &mdash; sttp/pyapi 0.6.4 documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../../_static/pygments.css?v=92fd9be5" />
      <link rel="stylesheet" type="text/css" href="../../../../_static/css/theme.css?v=8209df7b" />

  
    <link rel="shortcut icon" href="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/favicon.ico"/>
      <script src="../../../../_static/jquery.js?v=5d32c60e"></script>
      <script src="../../../../_static/_sphinx_javascript_frameworks_compat.js?v=bbec6902"></script>
      <script src="../../../../_static/documentation_options.js?v=b1f64a84"></script>
      <script src="../../../../_static/doctools.js?v=92e14aea"></script>
      <script src="../../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../../../../genindex.html" />
    <link rel="search" title="Search" href="../../../../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="../../../../index.html" class="icon icon-home">
            sttp/pyapi
              <img src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/sttp.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../../../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <!-- Local TOC -->
              <div class="local-toc"></div>
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../../../index.html">sttp/pyapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../../../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../../../index.html">Module code</a></li>
      <li class="breadcrumb-item active">sttp.transport.tssc.decoder</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for sttp.transport.tssc.decoder</h1><div class="highlight"><pre>
<span></span><span class="c1"># ******************************************************************************************************</span>
<span class="c1">#  decoder.py - Gbtc</span>
<span class="c1">#</span>
<span class="c1">#  Copyright © 2022, Grid Protection Alliance.  All Rights Reserved.</span>
<span class="c1">#</span>
<span class="c1">#  Licensed to the Grid Protection Alliance (GPA) under one or more contributor license agreements. See</span>
<span class="c1">#  the NOTICE file distributed with this work for additional information regarding copyright ownership.</span>
<span class="c1">#  The GPA licenses this file to you under the MIT License (MIT), the &quot;License&quot;; you may not use this</span>
<span class="c1">#  file except in compliance with the License. You may obtain a copy of the License at =</span>
<span class="c1">#</span>
<span class="c1">#      http =//opensource.org/licenses/MIT</span>
<span class="c1">#</span>
<span class="c1">#  Unless agreed to in writing, the subject software distributed under the License is distributed on an</span>
<span class="c1">#  &quot;AS-IS&quot; BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. Refer to the</span>
<span class="c1">#  License for the specific language governing permissions and limitations.</span>
<span class="c1">#</span>
<span class="c1">#  Code Modification History =</span>
<span class="c1">#  ----------------------------------------------------------------------------------------------------</span>
<span class="c1">#  08/30/2022 - J. Ritchie Carroll</span>
<span class="c1">#       Generated original version of source code.</span>
<span class="c1">#</span>
<span class="c1"># ******************************************************************************************************</span>

<span class="kn">from</span> <span class="nn">gsf</span> <span class="kn">import</span> <span class="n">Limits</span>
<span class="kn">from</span> <span class="nn">gsf.endianorder</span> <span class="kn">import</span> <span class="n">NativeEndian</span>
<span class="kn">from</span> <span class="nn">.pointmetadata</span> <span class="kn">import</span> <span class="n">PointMetadata</span><span class="p">,</span> <span class="n">CodeWords</span>
<span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">Dict</span><span class="p">,</span> <span class="n">Optional</span><span class="p">,</span> <span class="n">Tuple</span>
<span class="kn">import</span> <span class="nn">numpy</span> <span class="k">as</span> <span class="nn">np</span>

<span class="n">INT32_0</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
<span class="n">INT32_1</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
<span class="n">INT32_2</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
<span class="n">INT32_3</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span>
<span class="n">INT32_4</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="mi">4</span><span class="p">)</span>
<span class="n">INT32_8</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="mi">8</span><span class="p">)</span>
<span class="n">INT32_12</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="mi">12</span><span class="p">)</span>
<span class="n">INT32_16</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="mi">16</span><span class="p">)</span>
<span class="n">INT32_24</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="mi">24</span><span class="p">)</span>

<span class="n">UINT32_0</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
<span class="n">UINT32_4</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="mi">4</span><span class="p">)</span>
<span class="n">UINT32_7</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="mi">7</span><span class="p">)</span>
<span class="n">UINT32_8</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="mi">8</span><span class="p">)</span>
<span class="n">UINT32_12</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="mi">12</span><span class="p">)</span>
<span class="n">UINT32_14</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="mi">14</span><span class="p">)</span>
<span class="n">UINT32_16</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="mi">16</span><span class="p">)</span>
<span class="n">UINT32_20</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="mi">20</span><span class="p">)</span>
<span class="n">UINT32_21</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="mi">21</span><span class="p">)</span>
<span class="n">UINT32_24</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="mi">24</span><span class="p">)</span>
<span class="n">UINT32_28</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="mi">28</span><span class="p">)</span>
<span class="n">UINT32_128</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="mi">128</span><span class="p">)</span>

<span class="n">UINT32_16K</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="mi">16384</span><span class="p">)</span>
<span class="n">UINT32_2M</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="mi">2097152</span><span class="p">)</span>
<span class="n">UINT32_256M</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="mi">268435456</span><span class="p">)</span>

<span class="n">UINT32_1BYTEXOR</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="mh">0x00000080</span><span class="p">)</span>
<span class="n">UINT32_2BYTEXOR</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="mh">0x00004080</span><span class="p">)</span>
<span class="n">UINT32_3BYTEXOR</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="mh">0x00204080</span><span class="p">)</span>
<span class="n">UINT32_4BYTEXOR</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="mh">0x10204080</span><span class="p">)</span>

<span class="n">INT64_0</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int64</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
<span class="n">INT64_MAX</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int64</span><span class="p">(</span><span class="n">Limits</span><span class="o">.</span><span class="n">MAXINT64</span><span class="p">)</span>

<span class="n">UINT64_7</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mi">7</span><span class="p">)</span>
<span class="n">UINT64_14</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mi">14</span><span class="p">)</span>
<span class="n">UINT64_21</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mi">21</span><span class="p">)</span>
<span class="n">UINT64_28</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mi">28</span><span class="p">)</span>
<span class="n">UINT64_35</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mi">35</span><span class="p">)</span>
<span class="n">UINT64_42</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mi">42</span><span class="p">)</span>
<span class="n">UINT64_49</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mi">49</span><span class="p">)</span>
<span class="n">UINT64_56</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mi">56</span><span class="p">)</span>
<span class="n">UINT64_128</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mi">128</span><span class="p">)</span>

<span class="n">UINT64_16K</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mi">16384</span><span class="p">)</span>
<span class="n">UINT64_2M</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mi">2097152</span><span class="p">)</span>
<span class="n">UINT64_256M</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mi">268435456</span><span class="p">)</span>
<span class="n">UINT64_32G</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mi">34359738368</span><span class="p">)</span>
<span class="n">UINT64_4T</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mi">4398046511104</span><span class="p">)</span>
<span class="n">UINT64_512T</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mi">562949953421312</span><span class="p">)</span>
<span class="n">UINT64_64P</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mi">72057594037927936</span><span class="p">)</span>

<span class="n">UINT64_1BYTEXOR</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mh">0x000000000000080</span><span class="p">)</span>
<span class="n">UINT64_2BYTEXOR</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mh">0x000000000004080</span><span class="p">)</span>
<span class="n">UINT64_3BYTEXOR</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mh">0x000000000204080</span><span class="p">)</span>
<span class="n">UINT64_4BYTEXOR</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mh">0x000000010204080</span><span class="p">)</span>
<span class="n">UINT64_5BYTEXOR</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mh">0x000000810204080</span><span class="p">)</span>
<span class="n">UINT64_6BYTEXOR</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mh">0x000040810204080</span><span class="p">)</span>
<span class="n">UINT64_7BYTEXOR</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mh">0x002040810204080</span><span class="p">)</span>
<span class="n">UINT64_8BYTEXOR</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="mh">0x102040810204080</span><span class="p">)</span>


<div class="viewcode-block" id="Decoder">
<a class="viewcode-back" href="../../../../sttp.transport.tssc.html#sttp.transport.tssc.decoder.Decoder">[docs]</a>
<span class="k">class</span> <span class="nc">Decoder</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    The decoder for the Time-Series Special Compression (TSSC) algorithm of STTP.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Creates a new TSSC decoder.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_data</span> <span class="o">=</span> <span class="nb">bytes</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_lastposition</span> <span class="o">=</span> <span class="mi">0</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimestamp1</span> <span class="o">=</span> <span class="n">INT64_0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimestamp2</span> <span class="o">=</span> <span class="n">INT64_0</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta1</span> <span class="o">=</span> <span class="n">INT64_MAX</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta2</span> <span class="o">=</span> <span class="n">INT64_MAX</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta3</span> <span class="o">=</span> <span class="n">INT64_MAX</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta4</span> <span class="o">=</span> <span class="n">INT64_MAX</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_lastpoint</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_new_pointmetadata</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_points</span><span class="p">:</span> <span class="n">Dict</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="n">PointMetadata</span><span class="p">]]</span> <span class="o">=</span> <span class="p">{}</span>

        <span class="c1"># The number of bits in m_bitStreamCache that are valid. 0 Means the bitstream is empty</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_bitstreamcount</span> <span class="o">=</span> <span class="n">INT32_0</span>

        <span class="c1"># A cache of bits that need to be flushed to m_buffer when full. Bits filled starting from the right moving left</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_bitstreamcache</span> <span class="o">=</span> <span class="n">INT32_0</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">sequencenumber</span> <span class="o">=</span> <span class="mi">0</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        SequenceNumber is the sequence used to synchronize encoding and decoding.</span>
<span class="sd">        &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">_new_pointmetadata</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PointMetadata</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">PointMetadata</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_bit</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_bits5</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">_bitstream_isempty</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_bitstreamcount</span> <span class="o">==</span> <span class="n">INT32_0</span>

    <span class="k">def</span> <span class="nf">_clear_bitstream</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_bitstreamcount</span> <span class="o">=</span> <span class="n">INT32_0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_bitstreamcache</span> <span class="o">=</span> <span class="n">INT32_0</span>

<div class="viewcode-block" id="Decoder.set_buffer">
<a class="viewcode-back" href="../../../../sttp.transport.tssc.html#sttp.transport.tssc.decoder.Decoder.set_buffer">[docs]</a>
    <span class="k">def</span> <span class="nf">set_buffer</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">data</span><span class="p">:</span> <span class="nb">bytes</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Assigns the working buffer to use for decoding measurements.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_clear_bitstream</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_data</span> <span class="o">=</span> <span class="n">data</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_lastposition</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">data</span><span class="p">)</span></div>


<div class="viewcode-block" id="Decoder.try_get_measurement">
<a class="viewcode-back" href="../../../../sttp.transport.tssc.html#sttp.transport.tssc.decoder.Decoder.try_get_measurement">[docs]</a>
    <span class="k">def</span> <span class="nf">try_get_measurement</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">int64</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">float32</span><span class="p">,</span> <span class="nb">bool</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>  <span class="c1"># sourcery skip: low-code-quality</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">==</span> <span class="bp">self</span><span class="o">.</span><span class="n">_lastposition</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">_bitstream_isempty</span><span class="p">():</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_clear_bitstream</span><span class="p">()</span>
            <span class="k">return</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="kc">False</span><span class="p">,</span> <span class="kc">None</span>

        <span class="c1"># Given that the incoming pointID is not known in advance, the current</span>
        <span class="c1"># measurement will contain the encoding details for the next.</span>

        <span class="c1"># General compression strategy is to use delta-encoding for each</span>
        <span class="c1"># measurement component value that is received with the same identity.</span>
        <span class="c1"># See https://en.wikipedia.org/wiki/Delta_encoding</span>

        <span class="c1"># Delta-encoding sizes are embedded in the stream as type-specific</span>
        <span class="c1"># codes using as few bits as possible</span>

        <span class="c1"># Read next code for measurement ID decoding</span>
        <span class="n">code</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_lastpoint</span><span class="o">.</span><span class="n">read_code</span><span class="p">()</span>

        <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="kc">False</span><span class="p">,</span> <span class="n">err</span>

        <span class="k">if</span> <span class="n">code</span> <span class="o">==</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="n">CodeWords</span><span class="o">.</span><span class="n">ENDOFSTREAM</span><span class="p">):</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_clear_bitstream</span><span class="p">()</span>
            <span class="k">return</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="kc">False</span><span class="p">,</span> <span class="kc">None</span>

        <span class="c1"># Decode measurement ID and read next code for timestamp decoding</span>
        <span class="k">if</span> <span class="n">code</span> <span class="o">&lt;=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="n">CodeWords</span><span class="o">.</span><span class="n">POINTIDXOR32</span><span class="p">):</span>
            <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_decode_pointid</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="n">code</span><span class="p">),</span> <span class="bp">self</span><span class="o">.</span><span class="n">_lastpoint</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">return</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="kc">False</span><span class="p">,</span> <span class="n">err</span>

            <span class="n">code</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_lastpoint</span><span class="o">.</span><span class="n">read_code</span><span class="p">()</span>

            <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">return</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="kc">False</span><span class="p">,</span> <span class="n">err</span>

            <span class="k">if</span> <span class="n">err</span> <span class="o">:=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_validate_nextcode</span><span class="p">(</span><span class="n">code</span><span class="p">,</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">TIMEDELTA1FORWARD</span><span class="p">):</span>
                <span class="k">return</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="kc">False</span><span class="p">,</span> <span class="n">err</span>

        <span class="n">pointid</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_lastpoint</span><span class="o">.</span><span class="n">prevnextpointid1</span>

        <span class="c1"># Setup tracking for metadata associated with measurement ID and next point to decode</span>
        <span class="k">if</span> <span class="p">(</span><span class="n">nextpoint</span> <span class="o">:=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_points</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">pointid</span><span class="p">))</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">nextpoint</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_new_pointmetadata</span><span class="p">()</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_points</span><span class="p">[</span><span class="n">pointid</span><span class="p">]</span> <span class="o">=</span> <span class="n">nextpoint</span>
            <span class="n">nextpoint</span><span class="o">.</span><span class="n">prevnextpointid1</span> <span class="o">=</span> <span class="n">pointid</span> <span class="o">+</span> <span class="mi">1</span>

        <span class="c1"># Decode measurement timestamp and read next code for quality flags decoding</span>
        <span class="k">if</span> <span class="n">code</span> <span class="o">&lt;=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="n">CodeWords</span><span class="o">.</span><span class="n">TIMEXOR7BIT</span><span class="p">):</span>
            <span class="n">timestamp</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_decode_timestamp</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="n">code</span><span class="p">))</span>
            <span class="n">code</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_lastpoint</span><span class="o">.</span><span class="n">read_code</span><span class="p">()</span>

            <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">return</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="kc">False</span><span class="p">,</span> <span class="n">err</span>

            <span class="k">if</span> <span class="n">err</span> <span class="o">:=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_validate_nextcode</span><span class="p">(</span><span class="n">code</span><span class="p">,</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">STATEFLAGS2</span><span class="p">):</span>
                <span class="k">return</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="kc">False</span><span class="p">,</span> <span class="n">err</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">timestamp</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimestamp1</span>

        <span class="c1"># Decode measurement state flags and read next code for measurement value decoding</span>
        <span class="k">if</span> <span class="n">code</span> <span class="o">&lt;=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="n">CodeWords</span><span class="o">.</span><span class="n">STATEFLAGS7BIT32</span><span class="p">):</span>
            <span class="n">stateflags</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_decode_stateflags</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="n">code</span><span class="p">),</span> <span class="n">nextpoint</span><span class="p">)</span>
            <span class="n">code</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_lastpoint</span><span class="o">.</span><span class="n">read_code</span><span class="p">()</span>

            <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">return</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="kc">False</span><span class="p">,</span> <span class="n">err</span>

            <span class="k">if</span> <span class="n">err</span> <span class="o">:=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_validate_nextcode</span><span class="p">(</span><span class="n">code</span><span class="p">,</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">VALUE1</span><span class="p">):</span>
                <span class="k">return</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="kc">False</span><span class="p">,</span> <span class="n">err</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">stateflags</span> <span class="o">=</span> <span class="n">nextpoint</span><span class="o">.</span><span class="n">prevstateflags1</span>

        <span class="c1"># Decode measurement value</span>
        <span class="n">value</span><span class="p">,</span> <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_decode_value</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="n">code</span><span class="p">),</span> <span class="n">nextpoint</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">err</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="kc">False</span><span class="p">,</span> <span class="n">err</span>

        <span class="k">return</span> <span class="n">pointid</span><span class="p">,</span> <span class="n">timestamp</span><span class="p">,</span> <span class="n">stateflags</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="kc">True</span><span class="p">,</span> <span class="kc">None</span></div>


    <span class="k">def</span> <span class="nf">_validate_nextcode</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">code</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">,</span> <span class="n">nextcode</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]:</span>
        <span class="k">if</span> <span class="n">code</span> <span class="o">&lt;</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="n">nextcode</span><span class="p">):</span>
            <span class="n">message</span> <span class="o">=</span> <span class="p">[</span>
                <span class="sa">f</span><span class="s2">&quot;expecting code &gt;= </span><span class="si">{</span><span class="n">nextcode</span><span class="si">}</span><span class="s2">&quot;</span>
                <span class="sa">f</span><span class="s2">&quot; at position </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span><span class="si">}</span><span class="s2">&quot;</span>
                <span class="sa">f</span><span class="s2">&quot; with last position </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">_lastposition</span><span class="si">}</span><span class="s2">&quot;</span>
            <span class="p">]</span>

            <span class="k">return</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="s2">&quot;&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">message</span><span class="p">))</span>

        <span class="k">return</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">_decode_pointid</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">code</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">,</span> <span class="n">lastpoint</span><span class="p">:</span> <span class="n">PointMetadata</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]:</span>
        <span class="k">if</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">POINTIDXOR4</span><span class="p">:</span>
            <span class="n">lastpoint</span><span class="o">.</span><span class="n">prevnextpointid1</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_bits4</span><span class="p">()</span> <span class="o">^</span> <span class="n">lastpoint</span><span class="o">.</span><span class="n">prevnextpointid1</span>
        <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">POINTIDXOR8</span><span class="p">:</span>
            <span class="n">lastpoint</span><span class="o">.</span><span class="n">prevnextpointid1</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span><span class="p">])</span> <span class="o">^</span> <span class="n">lastpoint</span><span class="o">.</span><span class="n">prevnextpointid1</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+=</span> <span class="mi">1</span>
        <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">POINTIDXOR12</span><span class="p">:</span>
            <span class="n">lastpoint</span><span class="o">.</span><span class="n">prevnextpointid1</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_bits4</span><span class="p">()</span> <span class="o">^</span> <span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">INT32_4</span><span class="p">)</span> <span class="o">^</span> <span class="n">lastpoint</span><span class="o">.</span><span class="n">prevnextpointid1</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+=</span> <span class="mi">1</span>
        <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">POINTIDXOR16</span><span class="p">:</span>
            <span class="n">lastpoint</span><span class="o">.</span><span class="n">prevnextpointid1</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span><span class="p">])</span> <span class="o">^</span> <span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+</span> <span class="mi">1</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">INT32_8</span><span class="p">)</span> <span class="o">^</span> \
                <span class="n">lastpoint</span><span class="o">.</span><span class="n">prevnextpointid1</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+=</span> <span class="mi">2</span>
        <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">POINTIDXOR20</span><span class="p">:</span>
            <span class="n">lastpoint</span><span class="o">.</span><span class="n">prevnextpointid1</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_bits4</span><span class="p">()</span> <span class="o">^</span> <span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">INT32_4</span><span class="p">)</span> <span class="o">^</span> \
                <span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+</span> <span class="mi">1</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">INT32_12</span><span class="p">)</span> <span class="o">^</span> <span class="n">lastpoint</span><span class="o">.</span><span class="n">prevnextpointid1</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+=</span> <span class="mi">2</span>
        <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">POINTIDXOR24</span><span class="p">:</span>
            <span class="n">lastpoint</span><span class="o">.</span><span class="n">prevnextpointid1</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span><span class="p">])</span> <span class="o">^</span> <span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+</span> <span class="mi">1</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">INT32_8</span><span class="p">)</span> <span class="o">^</span> \
                <span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+</span> <span class="mi">2</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">INT32_16</span><span class="p">)</span> <span class="o">^</span> <span class="n">lastpoint</span><span class="o">.</span><span class="n">prevnextpointid1</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+=</span> <span class="mi">3</span>
        <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">POINTIDXOR32</span><span class="p">:</span>
            <span class="n">lastpoint</span><span class="o">.</span><span class="n">prevnextpointid1</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span><span class="p">])</span> <span class="o">^</span> <span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+</span> <span class="mi">1</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">INT32_8</span><span class="p">)</span> <span class="o">^</span> \
                <span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+</span> <span class="mi">2</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">INT32_16</span><span class="p">)</span> <span class="o">^</span> <span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+</span> <span class="mi">3</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">INT32_24</span><span class="p">)</span> <span class="o">^</span> <span class="n">lastpoint</span><span class="o">.</span><span class="n">prevnextpointid1</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+=</span> <span class="mi">4</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">message</span> <span class="o">=</span> <span class="p">[</span>
                <span class="sa">f</span><span class="s2">&quot;invalid code received </span><span class="si">{</span><span class="n">code</span><span class="si">}</span><span class="s2">&quot;</span>
                <span class="sa">f</span><span class="s2">&quot; at position </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span><span class="si">}</span><span class="s2">&quot;</span>
                <span class="sa">f</span><span class="s2">&quot; with last position </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">_lastposition</span><span class="si">}</span><span class="s2">&quot;</span>
            <span class="p">]</span>

            <span class="k">return</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="s2">&quot;&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">message</span><span class="p">))</span>

        <span class="k">return</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">_decode_timestamp</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">code</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">int64</span><span class="p">:</span>
        <span class="k">if</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">TIMEDELTA1FORWARD</span><span class="p">:</span>
            <span class="n">timestamp</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimestamp1</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta1</span>
        <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">TIMEDELTA2FORWARD</span><span class="p">:</span>
            <span class="n">timestamp</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimestamp1</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta2</span>
        <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">TIMEDELTA3FORWARD</span><span class="p">:</span>
            <span class="n">timestamp</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimestamp1</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta3</span>
        <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">TIMEDELTA4FORWARD</span><span class="p">:</span>
            <span class="n">timestamp</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimestamp1</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta4</span>
        <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">TIMEDELTA1REVERSE</span><span class="p">:</span>
            <span class="n">timestamp</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimestamp1</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta1</span>
        <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">TIMEDELTA2REVERSE</span><span class="p">:</span>
            <span class="n">timestamp</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimestamp1</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta2</span>
        <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">TIMEDELTA3REVERSE</span><span class="p">:</span>
            <span class="n">timestamp</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimestamp1</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta3</span>
        <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">TIMEDELTA4REVERSE</span><span class="p">:</span>
            <span class="n">timestamp</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimestamp1</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta4</span>
        <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">TIMESTAMP2</span><span class="p">:</span>
            <span class="n">timestamp</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimestamp2</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">value</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">=</span> <span class="n">Decoder</span><span class="o">.</span><span class="n">_decode_7bituint64</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">_position</span><span class="p">)</span>
            <span class="n">timestamp</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimestamp1</span> <span class="o">^</span> <span class="n">np</span><span class="o">.</span><span class="n">int64</span><span class="p">(</span><span class="n">value</span><span class="p">)</span>

        <span class="c1"># Save the smallest delta time</span>
        <span class="n">minDelta</span> <span class="o">=</span> <span class="nb">abs</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_prevtimestamp1</span> <span class="o">-</span> <span class="n">timestamp</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">minDelta</span> <span class="o">&lt;</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta4</span> <span class="ow">and</span> <span class="n">minDelta</span> <span class="o">!=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta1</span> <span class="ow">and</span> <span class="n">minDelta</span> <span class="o">!=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta2</span> <span class="ow">and</span> <span class="n">minDelta</span> <span class="o">!=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta3</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">minDelta</span> <span class="o">&lt;</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta1</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta4</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta3</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta3</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta2</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta2</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta1</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta1</span> <span class="o">=</span> <span class="n">minDelta</span>
            <span class="k">elif</span> <span class="n">minDelta</span> <span class="o">&lt;</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta2</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta4</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta3</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta3</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta2</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta2</span> <span class="o">=</span> <span class="n">minDelta</span>
            <span class="k">elif</span> <span class="n">minDelta</span> <span class="o">&lt;</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta3</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta4</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta3</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta3</span> <span class="o">=</span> <span class="n">minDelta</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimedelta4</span> <span class="o">=</span> <span class="n">minDelta</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimestamp2</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimestamp1</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_prevtimestamp1</span> <span class="o">=</span> <span class="n">timestamp</span>

        <span class="k">return</span> <span class="n">timestamp</span>

    <span class="k">def</span> <span class="nf">_decode_stateflags</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">code</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">,</span> <span class="n">nextPoint</span><span class="p">:</span> <span class="n">PointMetadata</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">:</span>
        <span class="k">if</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">STATEFLAGS2</span><span class="p">:</span>
            <span class="n">stateFlags</span> <span class="o">=</span> <span class="n">nextPoint</span><span class="o">.</span><span class="n">prevstateflags2</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">stateFlags</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_decode_7bituint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">_position</span><span class="p">)</span>

        <span class="n">nextPoint</span><span class="o">.</span><span class="n">prevstateflags2</span> <span class="o">=</span> <span class="n">nextPoint</span><span class="o">.</span><span class="n">prevstateflags1</span>
        <span class="n">nextPoint</span><span class="o">.</span><span class="n">prevstateflags1</span> <span class="o">=</span> <span class="n">stateFlags</span>

        <span class="k">return</span> <span class="n">stateFlags</span>

    <span class="k">def</span> <span class="nf">_decode_value</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">code</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">,</span> <span class="n">nextpoint</span><span class="p">:</span> <span class="n">PointMetadata</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">float32</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>
        <span class="n">valueraw</span> <span class="o">=</span> <span class="n">UINT32_0</span>

        <span class="k">def</span> <span class="nf">update_prevvalues</span><span class="p">(</span><span class="n">value</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">):</span>
            <span class="n">nextpoint</span><span class="o">.</span><span class="n">prevvalue3</span> <span class="o">=</span> <span class="n">nextpoint</span><span class="o">.</span><span class="n">prevvalue2</span>
            <span class="n">nextpoint</span><span class="o">.</span><span class="n">prevvalue2</span> <span class="o">=</span> <span class="n">nextpoint</span><span class="o">.</span><span class="n">prevvalue1</span>
            <span class="n">nextpoint</span><span class="o">.</span><span class="n">prevvalue1</span> <span class="o">=</span> <span class="n">value</span>

        <span class="k">if</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">VALUE1</span><span class="p">:</span>
            <span class="n">valueraw</span> <span class="o">=</span> <span class="n">nextpoint</span><span class="o">.</span><span class="n">prevvalue1</span>
        <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">VALUE2</span><span class="p">:</span>
            <span class="n">valueraw</span> <span class="o">=</span> <span class="n">nextpoint</span><span class="o">.</span><span class="n">prevvalue2</span>
            <span class="n">nextpoint</span><span class="o">.</span><span class="n">prevvalue2</span> <span class="o">=</span> <span class="n">nextpoint</span><span class="o">.</span><span class="n">prevvalue1</span>
            <span class="n">nextpoint</span><span class="o">.</span><span class="n">prevvalue1</span> <span class="o">=</span> <span class="n">valueraw</span>
        <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">VALUE3</span><span class="p">:</span>
            <span class="n">valueraw</span> <span class="o">=</span> <span class="n">nextpoint</span><span class="o">.</span><span class="n">prevvalue3</span>
            <span class="n">update_prevvalues</span><span class="p">(</span><span class="n">valueraw</span><span class="p">)</span>
        <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">VALUEZERO</span><span class="p">:</span>
            <span class="n">update_prevvalues</span><span class="p">(</span><span class="n">valueraw</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">VALUEXOR4</span><span class="p">:</span>
                <span class="n">valueraw</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_read_bits4</span><span class="p">())</span> <span class="o">^</span> <span class="n">nextpoint</span><span class="o">.</span><span class="n">prevvalue1</span>
            <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">VALUEXOR8</span><span class="p">:</span>
                <span class="n">valueraw</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span><span class="p">])</span> <span class="o">^</span> <span class="n">nextpoint</span><span class="o">.</span><span class="n">prevvalue1</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+=</span> <span class="mi">1</span>
            <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">VALUEXOR12</span><span class="p">:</span>
                <span class="n">valueraw</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_read_bits4</span><span class="p">())</span> <span class="o">^</span> <span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT32_4</span><span class="p">)</span> <span class="o">^</span> <span class="n">nextpoint</span><span class="o">.</span><span class="n">prevvalue1</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+=</span> <span class="mi">1</span>
            <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">VALUEXOR16</span><span class="p">:</span>
                <span class="n">valueraw</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span><span class="p">])</span> <span class="o">^</span> <span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+</span> <span class="mi">1</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT32_8</span><span class="p">)</span> <span class="o">^</span> <span class="n">nextpoint</span><span class="o">.</span><span class="n">prevvalue1</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+=</span> <span class="mi">2</span>
            <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">VALUEXOR20</span><span class="p">:</span>
                <span class="n">valueraw</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_read_bits4</span><span class="p">())</span> <span class="o">^</span> <span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT32_4</span><span class="p">)</span> <span class="o">^</span> \
                    <span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+</span> <span class="mi">1</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT32_12</span><span class="p">)</span> <span class="o">^</span> <span class="n">nextpoint</span><span class="o">.</span><span class="n">prevvalue1</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+=</span> <span class="mi">2</span>
            <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">VALUEXOR24</span><span class="p">:</span>
                <span class="n">valueraw</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span><span class="p">])</span> <span class="o">^</span> <span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+</span> <span class="mi">1</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT32_8</span><span class="p">)</span> <span class="o">^</span> \
                    <span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+</span> <span class="mi">2</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT32_16</span><span class="p">)</span> <span class="o">^</span> <span class="n">nextpoint</span><span class="o">.</span><span class="n">prevvalue1</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+=</span> <span class="mi">3</span>
            <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">VALUEXOR28</span><span class="p">:</span>
                <span class="n">valueraw</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_read_bits4</span><span class="p">())</span> <span class="o">^</span> <span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT32_4</span><span class="p">)</span> <span class="o">^</span> \
                    <span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+</span> <span class="mi">1</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT32_12</span><span class="p">)</span> <span class="o">^</span> <span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+</span> <span class="mi">2</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT32_20</span><span class="p">)</span> <span class="o">^</span> <span class="n">nextpoint</span><span class="o">.</span><span class="n">prevvalue1</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+=</span> <span class="mi">3</span>
            <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">VALUEXOR32</span><span class="p">:</span>
                <span class="n">valueraw</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span><span class="p">])</span> <span class="o">^</span> <span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+</span> <span class="mi">1</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT32_8</span><span class="p">)</span> <span class="o">^</span> \
                    <span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+</span> <span class="mi">2</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT32_16</span><span class="p">)</span> <span class="o">^</span> <span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+</span> <span class="mi">3</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT32_24</span><span class="p">)</span> <span class="o">^</span> <span class="n">nextpoint</span><span class="o">.</span><span class="n">prevvalue1</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+=</span> <span class="mi">4</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">message</span> <span class="o">=</span> <span class="p">[</span>
                    <span class="sa">f</span><span class="s2">&quot;invalid code received </span><span class="si">{</span><span class="n">code</span><span class="si">}</span><span class="s2">&quot;</span>
                    <span class="sa">f</span><span class="s2">&quot; at position </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span><span class="si">}</span><span class="s2">&quot;</span>
                    <span class="sa">f</span><span class="s2">&quot; with last position </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">_lastposition</span><span class="si">}</span><span class="s2">&quot;</span>
                <span class="p">]</span>

                <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">float32</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">nan</span><span class="p">),</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="s2">&quot;&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">message</span><span class="p">))</span>

            <span class="n">update_prevvalues</span><span class="p">(</span><span class="n">valueraw</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_lastpoint</span> <span class="o">=</span> <span class="n">nextpoint</span>

        <span class="k">return</span> <span class="n">NativeEndian</span><span class="o">.</span><span class="n">to_float32</span><span class="p">(</span><span class="n">NativeEndian</span><span class="o">.</span><span class="n">from_uint32</span><span class="p">(</span><span class="n">valueraw</span><span class="p">)),</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">_read_bit</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">:</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_bitstreamcount</span> <span class="o">==</span> <span class="n">INT32_0</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_bitstreamcount</span> <span class="o">=</span> <span class="n">INT32_8</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_bitstreamcache</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_position</span><span class="p">])</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_position</span> <span class="o">+=</span> <span class="mi">1</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_bitstreamcount</span> <span class="o">-=</span> <span class="n">INT32_1</span>

        <span class="k">return</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_bitstreamcache</span> <span class="o">&gt;&gt;</span> <span class="bp">self</span><span class="o">.</span><span class="n">_bitstreamcount</span><span class="p">)</span> <span class="o">&amp;</span> <span class="n">INT32_1</span>

    <span class="k">def</span> <span class="nf">_read_bits4</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_bit</span><span class="p">()</span> <span class="o">&lt;&lt;</span> <span class="n">INT32_3</span> <span class="o">|</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_bit</span><span class="p">()</span> <span class="o">&lt;&lt;</span> <span class="n">INT32_2</span> <span class="o">|</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_bit</span><span class="p">()</span> <span class="o">&lt;&lt;</span> <span class="n">INT32_1</span> <span class="o">|</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_bit</span><span class="p">()</span>

    <span class="k">def</span> <span class="nf">_read_bits5</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">:</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_bit</span><span class="p">()</span> <span class="o">&lt;&lt;</span> <span class="n">INT32_4</span> <span class="o">|</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_bit</span><span class="p">()</span> <span class="o">&lt;&lt;</span> <span class="n">INT32_3</span> <span class="o">|</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_bit</span><span class="p">()</span> <span class="o">&lt;&lt;</span> <span class="n">INT32_2</span> <span class="o">|</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_bit</span><span class="p">()</span> <span class="o">&lt;&lt;</span> <span class="n">INT32_1</span> <span class="o">|</span> <span class="bp">self</span><span class="o">.</span><span class="n">_read_bit</span><span class="p">()</span>

    <span class="nd">@staticmethod</span>
    <span class="k">def</span> <span class="nf">_decode_7bituint32</span><span class="p">(</span><span class="n">stream</span><span class="p">:</span> <span class="nb">bytes</span><span class="p">,</span> <span class="n">position</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">,</span> <span class="nb">int</span><span class="p">]:</span>
        <span class="n">stream</span> <span class="o">=</span> <span class="n">stream</span><span class="p">[</span><span class="n">position</span><span class="p">:]</span>
        <span class="n">value</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="n">stream</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>

        <span class="k">if</span> <span class="n">value</span> <span class="o">&lt;</span> <span class="n">UINT32_128</span><span class="p">:</span>
            <span class="n">position</span> <span class="o">+=</span> <span class="mi">1</span>
            <span class="k">return</span> <span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">position</span><span class="p">)</span>

        <span class="n">value</span> <span class="o">^=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="n">stream</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT32_7</span>

        <span class="k">if</span> <span class="n">value</span> <span class="o">&lt;</span> <span class="n">UINT32_16K</span><span class="p">:</span>
            <span class="n">position</span> <span class="o">+=</span> <span class="mi">2</span>
            <span class="k">return</span> <span class="p">(</span><span class="n">value</span> <span class="o">^</span> <span class="n">UINT32_1BYTEXOR</span><span class="p">,</span> <span class="n">position</span><span class="p">)</span>

        <span class="n">value</span> <span class="o">^=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="n">stream</span><span class="p">[</span><span class="mi">2</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT32_14</span>

        <span class="k">if</span> <span class="n">value</span> <span class="o">&lt;</span> <span class="n">UINT32_2M</span><span class="p">:</span>
            <span class="n">position</span> <span class="o">+=</span> <span class="mi">3</span>
            <span class="k">return</span> <span class="p">(</span><span class="n">value</span> <span class="o">^</span> <span class="n">UINT32_2BYTEXOR</span><span class="p">,</span> <span class="n">position</span><span class="p">)</span>

        <span class="n">value</span> <span class="o">^=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="n">stream</span><span class="p">[</span><span class="mi">3</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT32_21</span>

        <span class="k">if</span> <span class="n">value</span> <span class="o">&lt;</span> <span class="n">UINT32_256M</span><span class="p">:</span>
            <span class="n">position</span> <span class="o">+=</span> <span class="mi">4</span>
            <span class="k">return</span> <span class="p">(</span><span class="n">value</span> <span class="o">^</span> <span class="n">UINT32_3BYTEXOR</span><span class="p">,</span> <span class="n">position</span><span class="p">)</span>

        <span class="n">value</span> <span class="o">^=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="n">stream</span><span class="p">[</span><span class="mi">4</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT32_28</span>
        <span class="n">position</span> <span class="o">+=</span> <span class="mi">5</span>

        <span class="k">return</span> <span class="p">(</span><span class="n">value</span> <span class="o">^</span> <span class="n">UINT32_4BYTEXOR</span><span class="p">,</span> <span class="n">position</span><span class="p">)</span>

    <span class="nd">@staticmethod</span>
    <span class="k">def</span> <span class="nf">_decode_7bituint64</span><span class="p">(</span><span class="n">stream</span><span class="p">:</span> <span class="nb">bytes</span><span class="p">,</span> <span class="n">position</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">,</span> <span class="nb">int</span><span class="p">]:</span>
        <span class="n">stream</span> <span class="o">=</span> <span class="n">stream</span><span class="p">[</span><span class="n">position</span><span class="p">:]</span>
        <span class="n">value</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="n">stream</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>

        <span class="k">if</span> <span class="n">value</span> <span class="o">&lt;</span> <span class="n">UINT64_128</span><span class="p">:</span>
            <span class="n">position</span> <span class="o">+=</span> <span class="mi">1</span>
            <span class="k">return</span> <span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">position</span><span class="p">)</span>

        <span class="n">value</span> <span class="o">^=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="n">stream</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT64_7</span>

        <span class="k">if</span> <span class="n">value</span> <span class="o">&lt;</span> <span class="n">UINT64_16K</span><span class="p">:</span>
            <span class="n">position</span> <span class="o">+=</span> <span class="mi">2</span>
            <span class="k">return</span> <span class="p">(</span><span class="n">value</span> <span class="o">^</span> <span class="n">UINT64_1BYTEXOR</span><span class="p">,</span> <span class="n">position</span><span class="p">)</span>

        <span class="n">value</span> <span class="o">^=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="n">stream</span><span class="p">[</span><span class="mi">2</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT64_14</span>

        <span class="k">if</span> <span class="n">value</span> <span class="o">&lt;</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="n">UINT64_2M</span><span class="p">):</span>
            <span class="n">position</span> <span class="o">+=</span> <span class="mi">3</span>
            <span class="k">return</span> <span class="p">(</span><span class="n">value</span> <span class="o">^</span> <span class="n">UINT64_2BYTEXOR</span><span class="p">,</span> <span class="n">position</span><span class="p">)</span>

        <span class="n">value</span> <span class="o">^=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="n">stream</span><span class="p">[</span><span class="mi">3</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT64_21</span>

        <span class="k">if</span> <span class="n">value</span> <span class="o">&lt;</span> <span class="n">UINT64_256M</span><span class="p">:</span>
            <span class="n">position</span> <span class="o">+=</span> <span class="mi">4</span>
            <span class="k">return</span> <span class="p">(</span><span class="n">value</span> <span class="o">^</span> <span class="n">UINT64_3BYTEXOR</span><span class="p">,</span> <span class="n">position</span><span class="p">)</span>

        <span class="n">value</span> <span class="o">^=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="n">stream</span><span class="p">[</span><span class="mi">4</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT64_28</span>

        <span class="k">if</span> <span class="n">value</span> <span class="o">&lt;</span> <span class="n">UINT64_32G</span><span class="p">:</span>
            <span class="n">position</span> <span class="o">+=</span> <span class="mi">5</span>
            <span class="k">return</span> <span class="p">(</span><span class="n">value</span> <span class="o">^</span> <span class="n">UINT64_4BYTEXOR</span><span class="p">,</span> <span class="n">position</span><span class="p">)</span>

        <span class="n">value</span> <span class="o">^=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="n">stream</span><span class="p">[</span><span class="mi">5</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT64_35</span>

        <span class="k">if</span> <span class="n">value</span> <span class="o">&lt;</span> <span class="n">UINT64_4T</span><span class="p">:</span>
            <span class="n">position</span> <span class="o">+=</span> <span class="mi">6</span>
            <span class="k">return</span> <span class="p">(</span><span class="n">value</span> <span class="o">^</span> <span class="n">UINT64_5BYTEXOR</span><span class="p">,</span> <span class="n">position</span><span class="p">)</span>

        <span class="n">value</span> <span class="o">^=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="n">stream</span><span class="p">[</span><span class="mi">6</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT64_42</span>

        <span class="k">if</span> <span class="n">value</span> <span class="o">&lt;</span> <span class="n">UINT64_512T</span><span class="p">:</span>
            <span class="n">position</span> <span class="o">+=</span> <span class="mi">7</span>
            <span class="k">return</span> <span class="p">(</span><span class="n">value</span> <span class="o">^</span> <span class="n">UINT64_6BYTEXOR</span><span class="p">,</span> <span class="n">position</span><span class="p">)</span>

        <span class="n">value</span> <span class="o">^=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="n">stream</span><span class="p">[</span><span class="mi">7</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT64_49</span>

        <span class="k">if</span> <span class="n">value</span> <span class="o">&lt;</span> <span class="n">UINT64_64P</span><span class="p">:</span>
            <span class="n">position</span> <span class="o">+=</span> <span class="mi">8</span>
            <span class="k">return</span> <span class="p">(</span><span class="n">value</span> <span class="o">^</span> <span class="n">UINT64_7BYTEXOR</span><span class="p">,</span> <span class="n">position</span><span class="p">)</span>

        <span class="n">value</span> <span class="o">^=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint64</span><span class="p">(</span><span class="n">stream</span><span class="p">[</span><span class="mi">8</span><span class="p">])</span> <span class="o">&lt;&lt;</span> <span class="n">UINT64_56</span>
        <span class="n">position</span> <span class="o">+=</span> <span class="mi">9</span>

        <span class="k">return</span> <span class="p">(</span><span class="n">value</span> <span class="o">^</span> <span class="n">UINT64_8BYTEXOR</span><span class="p">,</span> <span class="n">position</span><span class="p">)</span></div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
  
 
<hr />
<p>
    <a href="https://github.com/sttp/pyapi">GitHub Home</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://pypi.org/project/sttpapi/" target="_blank">PyPi Package: sttpapi</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://sttp.github.io/documentation/" target="_blank">General STTP Documentation</a>
</p>
<br /><br />
Copyright © 2022, <a href="https://gridprotectionalliance.org/" target="_blank">Grid&nbsp;Protection&nbsp;Alliance</a>
<a href="https://github.com/sttp/pyapi">
    <img align="right" style="margin-top: -50px"
        src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/LockPython_64High.png">
</a>


</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>