

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../../../../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>sttp.transport.tssc.pointmetadata &mdash; sttp/pyapi 0.6.4 documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../../_static/pygments.css?v=92fd9be5" />
      <link rel="stylesheet" type="text/css" href="../../../../_static/css/theme.css?v=8209df7b" />

  
    <link rel="shortcut icon" href="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/favicon.ico"/>
      <script src="../../../../_static/jquery.js?v=5d32c60e"></script>
      <script src="../../../../_static/_sphinx_javascript_frameworks_compat.js?v=bbec6902"></script>
      <script src="../../../../_static/documentation_options.js?v=b1f64a84"></script>
      <script src="../../../../_static/doctools.js?v=92e14aea"></script>
      <script src="../../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../../../../genindex.html" />
    <link rel="search" title="Search" href="../../../../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="../../../../index.html" class="icon icon-home">
            sttp/pyapi
              <img src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/sttp.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../../../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <!-- Local TOC -->
              <div class="local-toc"></div>
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../../../index.html">sttp/pyapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../../../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../../../index.html">Module code</a></li>
      <li class="breadcrumb-item active">sttp.transport.tssc.pointmetadata</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for sttp.transport.tssc.pointmetadata</h1><div class="highlight"><pre>
<span></span><span class="c1"># ******************************************************************************************************</span>
<span class="c1">#  pointmetadata.py - Gbtc</span>
<span class="c1">#</span>
<span class="c1">#  Copyright © 2022, Grid Protection Alliance.  All Rights Reserved.</span>
<span class="c1">#</span>
<span class="c1">#  Licensed to the Grid Protection Alliance (GPA) under one or more contributor license agreements. See</span>
<span class="c1">#  the NOTICE file distributed with this work for additional information regarding copyright ownership.</span>
<span class="c1">#  The GPA licenses this file to you under the MIT License (MIT), the &quot;License&quot;; you may not use this</span>
<span class="c1">#  file except in compliance with the License. You may obtain a copy of the License at =</span>
<span class="c1">#</span>
<span class="c1">#      http =//opensource.org/licenses/MIT</span>
<span class="c1">#</span>
<span class="c1">#  Unless agreed to in writing, the subject software distributed under the License is distributed on an</span>
<span class="c1">#  &quot;AS-IS&quot; BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. Refer to the</span>
<span class="c1">#  License for the specific language governing permissions and limitations.</span>
<span class="c1">#</span>
<span class="c1">#  Code Modification History =</span>
<span class="c1">#  ----------------------------------------------------------------------------------------------------</span>
<span class="c1">#  08/30/2022 - J. Ritchie Carroll</span>
<span class="c1">#       Generated original version of source code.</span>
<span class="c1">#</span>
<span class="c1"># ******************************************************************************************************</span>

<span class="kn">from</span> <span class="nn">gsf</span> <span class="kn">import</span> <span class="n">Limits</span>
<span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">Callable</span><span class="p">,</span> <span class="n">Tuple</span><span class="p">,</span> <span class="n">Optional</span>
<span class="kn">import</span> <span class="nn">numpy</span> <span class="k">as</span> <span class="nn">np</span>


<div class="viewcode-block" id="CodeWords">
<a class="viewcode-back" href="../../../../sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords">[docs]</a>
<span class="k">class</span> <span class="nc">CodeWords</span><span class="p">:</span>
    <span class="n">ENDOFSTREAM</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
    <span class="n">POINTIDXOR4</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
    <span class="n">POINTIDXOR8</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
    <span class="n">POINTIDXOR12</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span>
    <span class="n">POINTIDXOR16</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">4</span><span class="p">)</span>
    <span class="n">POINTIDXOR20</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span>
    <span class="n">POINTIDXOR24</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">6</span><span class="p">)</span>
    <span class="n">POINTIDXOR32</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">7</span><span class="p">)</span>
    <span class="n">TIMEDELTA1FORWARD</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">8</span><span class="p">)</span>
    <span class="n">TIMEDELTA2FORWARD</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">9</span><span class="p">)</span>
    <span class="n">TIMEDELTA3FORWARD</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">10</span><span class="p">)</span>
    <span class="n">TIMEDELTA4FORWARD</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">11</span><span class="p">)</span>
    <span class="n">TIMEDELTA1REVERSE</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">12</span><span class="p">)</span>
    <span class="n">TIMEDELTA2REVERSE</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">13</span><span class="p">)</span>
    <span class="n">TIMEDELTA3REVERSE</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">14</span><span class="p">)</span>
    <span class="n">TIMEDELTA4REVERSE</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">15</span><span class="p">)</span>
    <span class="n">TIMESTAMP2</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">16</span><span class="p">)</span>
    <span class="n">TIMEXOR7BIT</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">17</span><span class="p">)</span>
    <span class="n">STATEFLAGS2</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">18</span><span class="p">)</span>
    <span class="n">STATEFLAGS7BIT32</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">19</span><span class="p">)</span>
    <span class="n">VALUE1</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">20</span><span class="p">)</span>
    <span class="n">VALUE2</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">21</span><span class="p">)</span>
    <span class="n">VALUE3</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">22</span><span class="p">)</span>
    <span class="n">VALUEZERO</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">23</span><span class="p">)</span>
    <span class="n">VALUEXOR4</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">24</span><span class="p">)</span>
    <span class="n">VALUEXOR8</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">25</span><span class="p">)</span>
    <span class="n">VALUEXOR12</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">26</span><span class="p">)</span>
    <span class="n">VALUEXOR16</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">27</span><span class="p">)</span>
    <span class="n">VALUEXOR20</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">28</span><span class="p">)</span>
    <span class="n">VALUEXOR24</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">29</span><span class="p">)</span>
    <span class="n">VALUEXOR28</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">30</span><span class="p">)</span>
    <span class="n">VALUEXOR32</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">31</span><span class="p">)</span></div>



<span class="n">BYTE_0</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
<span class="n">BYTE_1</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
<span class="n">BYTE_2</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>

<span class="n">INT32_0</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
<span class="n">INT32_1</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
<span class="n">INT32_2</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
<span class="n">INT32_3</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span>
<span class="n">INT32_5</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span>
<span class="n">INT32_6</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="mi">6</span><span class="p">)</span>
<span class="n">INT32_7</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="mi">7</span><span class="p">)</span>
<span class="n">INT32_8</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="mi">8</span><span class="p">)</span>

<span class="n">UINT32_0</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">uint32</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>


<div class="viewcode-block" id="PointMetadata">
<a class="viewcode-back" href="../../../../sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.PointMetadata">[docs]</a>
<span class="k">class</span> <span class="nc">PointMetadata</span><span class="p">:</span>
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                 <span class="n">writebits</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Callable</span><span class="p">[[</span><span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">],</span> <span class="kc">None</span><span class="p">]],</span>
                 <span class="n">readbit</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Callable</span><span class="p">[[],</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">]],</span>
                 <span class="n">readbits5</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Callable</span><span class="p">[[],</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">]]</span>
                 <span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">prevnextpointid1</span> <span class="o">=</span> <span class="n">INT32_0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">prevstateflags1</span> <span class="o">=</span> <span class="n">UINT32_0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">prevstateflags2</span> <span class="o">=</span> <span class="n">UINT32_0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">prevvalue1</span> <span class="o">=</span> <span class="n">UINT32_0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">prevvalue2</span> <span class="o">=</span> <span class="n">UINT32_0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">prevvalue3</span> <span class="o">=</span> <span class="n">UINT32_0</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_commandstats</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="mi">32</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_commands_sent_sincelastchange</span> <span class="o">=</span> <span class="mi">0</span>

        <span class="c1"># Bit codes for the 4 modes of encoding</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_mode</span> <span class="o">=</span> <span class="mi">4</span>

        <span class="c1"># Mode 1 means no prefix</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_mode21</span> <span class="o">=</span> <span class="n">BYTE_0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_mode31</span> <span class="o">=</span> <span class="n">BYTE_0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_mode301</span> <span class="o">=</span> <span class="n">BYTE_0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_mode41</span> <span class="o">=</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">VALUE1</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_mode401</span> <span class="o">=</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">VALUE2</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_mode4001</span> <span class="o">=</span> <span class="n">CodeWords</span><span class="o">.</span><span class="n">VALUE3</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_startupmode</span> <span class="o">=</span> <span class="mi">0</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_writebits</span> <span class="o">=</span> <span class="n">writebits</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_readbit</span> <span class="o">=</span> <span class="n">readbit</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_readbits5</span> <span class="o">=</span> <span class="n">readbits5</span>

<div class="viewcode-block" id="PointMetadata.write_code">
<a class="viewcode-back" href="../../../../sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.PointMetadata.write_code">[docs]</a>
    <span class="k">def</span> <span class="nf">write_code</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">code</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]:</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_mode</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_writebits</span><span class="p">(</span><span class="n">code</span><span class="p">,</span> <span class="n">INT32_5</span><span class="p">)</span>
        <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">_mode</span> <span class="o">==</span> <span class="mi">2</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">code</span> <span class="o">==</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_mode21</span><span class="p">):</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_writebits</span><span class="p">(</span><span class="n">INT32_1</span><span class="p">,</span> <span class="n">INT32_1</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_writebits</span><span class="p">(</span><span class="n">code</span><span class="p">,</span> <span class="n">INT32_6</span><span class="p">)</span>
        <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">_mode</span> <span class="o">==</span> <span class="mi">3</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">code</span> <span class="o">==</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_mode31</span><span class="p">):</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_writebits</span><span class="p">(</span><span class="n">INT32_1</span><span class="p">,</span> <span class="n">INT32_1</span><span class="p">)</span>
            <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_mode301</span><span class="p">):</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_writebits</span><span class="p">(</span><span class="n">INT32_1</span><span class="p">,</span> <span class="n">INT32_2</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_writebits</span><span class="p">(</span><span class="n">code</span><span class="p">,</span> <span class="n">INT32_7</span><span class="p">)</span>
        <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">_mode</span> <span class="o">==</span> <span class="mi">4</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">code</span> <span class="o">==</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_mode41</span><span class="p">):</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_writebits</span><span class="p">(</span><span class="n">INT32_1</span><span class="p">,</span> <span class="n">INT32_1</span><span class="p">)</span>
            <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_mode401</span><span class="p">):</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_writebits</span><span class="p">(</span><span class="n">INT32_1</span><span class="p">,</span> <span class="n">INT32_2</span><span class="p">)</span>
            <span class="k">elif</span> <span class="n">code</span> <span class="o">==</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_mode4001</span><span class="p">):</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_writebits</span><span class="p">(</span><span class="n">INT32_1</span><span class="p">,</span> <span class="n">INT32_3</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_writebits</span><span class="p">(</span><span class="n">code</span><span class="p">,</span> <span class="n">INT32_8</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="s2">&quot;coding Error&quot;</span><span class="p">)</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_update_codestatistics</span><span class="p">(</span><span class="n">code</span><span class="p">)</span></div>


<div class="viewcode-block" id="PointMetadata.read_code">
<a class="viewcode-back" href="../../../../sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.PointMetadata.read_code">[docs]</a>
    <span class="k">def</span> <span class="nf">read_code</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">,</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]]:</span>  <span class="c1"># sourcery skip: assign-if-exp</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_mode</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
            <span class="n">code</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_readbits5</span><span class="p">()</span>
        <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">_mode</span> <span class="o">==</span> <span class="mi">2</span><span class="p">:</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_readbit</span><span class="p">()</span> <span class="o">==</span> <span class="n">INT32_1</span><span class="p">:</span>
                <span class="n">code</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_mode21</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">code</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_readbits5</span><span class="p">()</span>
        <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">_mode</span> <span class="o">==</span> <span class="mi">3</span><span class="p">:</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_readbit</span><span class="p">()</span> <span class="o">==</span> <span class="n">INT32_1</span><span class="p">:</span>
                <span class="n">code</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_mode31</span><span class="p">)</span>
            <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">_readbit</span><span class="p">()</span> <span class="o">==</span> <span class="n">INT32_1</span><span class="p">:</span>
                <span class="n">code</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_mode301</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">code</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_readbits5</span><span class="p">()</span>
        <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">_mode</span> <span class="o">==</span> <span class="mi">4</span><span class="p">:</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_readbit</span><span class="p">()</span> <span class="o">==</span> <span class="n">INT32_1</span><span class="p">:</span>
                <span class="n">code</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_mode41</span><span class="p">)</span>
            <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">_readbit</span><span class="p">()</span> <span class="o">==</span> <span class="n">INT32_1</span><span class="p">:</span>
                <span class="n">code</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_mode401</span><span class="p">)</span>
            <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">_readbit</span><span class="p">()</span> <span class="o">==</span> <span class="n">INT32_1</span><span class="p">:</span>
                <span class="n">code</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_mode4001</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">code</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_readbits5</span><span class="p">()</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="mi">0</span><span class="p">,</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="s2">&quot;unsupported compression mode&quot;</span><span class="p">)</span>

        <span class="n">err</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_update_codestatistics</span><span class="p">(</span><span class="n">code</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">code</span><span class="p">,</span> <span class="n">err</span></div>


    <span class="k">def</span> <span class="nf">_update_codestatistics</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">code</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]:</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_commands_sent_sincelastchange</span> <span class="o">+=</span> <span class="mi">1</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_commandstats</span><span class="p">[</span><span class="n">code</span><span class="p">]</span> <span class="o">+=</span> <span class="n">BYTE_1</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_startupmode</span> <span class="o">==</span> <span class="mi">0</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">_commands_sent_sincelastchange</span> <span class="o">&gt;</span> <span class="mi">5</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_startupmode</span> <span class="o">+=</span> <span class="mi">1</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_adapt_commands</span><span class="p">()</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_startupmode</span> <span class="o">==</span> <span class="mi">1</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">_commands_sent_sincelastchange</span> <span class="o">&gt;</span> <span class="mi">20</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_startupmode</span> <span class="o">+=</span> <span class="mi">1</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_adapt_commands</span><span class="p">()</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_startupmode</span> <span class="o">==</span> <span class="mi">2</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">_commands_sent_sincelastchange</span> <span class="o">&gt;</span> <span class="mi">100</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_adapt_commands</span><span class="p">()</span>

        <span class="k">return</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">_adapt_commands</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="ne">Exception</span><span class="p">]:</span>
        <span class="n">code1</span> <span class="o">=</span> <span class="n">BYTE_0</span>
        <span class="n">count1</span> <span class="o">=</span> <span class="mi">0</span>

        <span class="n">code2</span> <span class="o">=</span> <span class="n">BYTE_1</span>
        <span class="n">count2</span> <span class="o">=</span> <span class="mi">0</span>

        <span class="n">code3</span> <span class="o">=</span> <span class="n">BYTE_2</span>
        <span class="n">count3</span> <span class="o">=</span> <span class="mi">0</span>

        <span class="n">total</span> <span class="o">=</span> <span class="mi">0</span>

        <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_commandstats</span><span class="p">)):</span>
            <span class="n">count</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_commandstats</span><span class="p">[</span><span class="n">i</span><span class="p">])</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_commandstats</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="n">BYTE_0</span>

            <span class="n">total</span> <span class="o">+=</span> <span class="n">count</span>

            <span class="k">if</span> <span class="n">count</span> <span class="o">&gt;</span> <span class="n">count3</span><span class="p">:</span>
                <span class="k">if</span> <span class="n">count</span> <span class="o">&gt;</span> <span class="n">count1</span><span class="p">:</span>
                    <span class="n">code3</span> <span class="o">=</span> <span class="n">code2</span>
                    <span class="n">count3</span> <span class="o">=</span> <span class="n">count2</span>

                    <span class="n">code2</span> <span class="o">=</span> <span class="n">code1</span>
                    <span class="n">count2</span> <span class="o">=</span> <span class="n">count1</span>

                    <span class="n">code1</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>
                    <span class="n">count1</span> <span class="o">=</span> <span class="n">count</span>
                <span class="k">elif</span> <span class="n">count</span> <span class="o">&gt;</span> <span class="n">count2</span><span class="p">:</span>
                    <span class="n">code3</span> <span class="o">=</span> <span class="n">code2</span>
                    <span class="n">count3</span> <span class="o">=</span> <span class="n">count2</span>

                    <span class="n">code2</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>
                    <span class="n">count2</span> <span class="o">=</span> <span class="n">count</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="n">code3</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">byte</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>
                    <span class="n">count3</span> <span class="o">=</span> <span class="n">count</span>

        <span class="n">mode1size</span> <span class="o">=</span> <span class="n">total</span> <span class="o">*</span> <span class="mi">5</span>
        <span class="n">mode2size</span> <span class="o">=</span> <span class="n">count1</span> <span class="o">+</span> <span class="p">(</span><span class="n">total</span> <span class="o">-</span> <span class="n">count1</span><span class="p">)</span> <span class="o">*</span> <span class="mi">6</span>
        <span class="n">mode3size</span> <span class="o">=</span> <span class="n">count1</span> <span class="o">+</span> <span class="n">count2</span> <span class="o">*</span> <span class="mi">2</span> <span class="o">+</span> <span class="p">(</span><span class="n">total</span> <span class="o">-</span> <span class="n">count1</span> <span class="o">-</span> <span class="n">count2</span><span class="p">)</span> <span class="o">*</span> <span class="mi">7</span>
        <span class="n">mode4size</span> <span class="o">=</span> <span class="n">count1</span> <span class="o">+</span> <span class="n">count2</span> <span class="o">*</span> <span class="mi">2</span> <span class="o">+</span> <span class="n">count3</span> <span class="o">*</span> <span class="mi">3</span> <span class="o">+</span> <span class="p">(</span><span class="n">total</span> <span class="o">-</span> <span class="n">count1</span> <span class="o">-</span> <span class="n">count2</span> <span class="o">-</span> <span class="n">count3</span><span class="p">)</span> <span class="o">*</span> <span class="mi">8</span>

        <span class="n">minsize</span> <span class="o">=</span> <span class="n">Limits</span><span class="o">.</span><span class="n">MAXINT32</span>
        <span class="n">minsize</span> <span class="o">=</span> <span class="nb">min</span><span class="p">(</span><span class="n">minsize</span><span class="p">,</span> <span class="n">mode1size</span><span class="p">)</span>
        <span class="n">minsize</span> <span class="o">=</span> <span class="nb">min</span><span class="p">(</span><span class="n">minsize</span><span class="p">,</span> <span class="n">mode2size</span><span class="p">)</span>
        <span class="n">minsize</span> <span class="o">=</span> <span class="nb">min</span><span class="p">(</span><span class="n">minsize</span><span class="p">,</span> <span class="n">mode3size</span><span class="p">)</span>
        <span class="n">minsize</span> <span class="o">=</span> <span class="nb">min</span><span class="p">(</span><span class="n">minsize</span><span class="p">,</span> <span class="n">mode4size</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">minsize</span> <span class="o">==</span> <span class="n">mode1size</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_mode</span> <span class="o">=</span> <span class="mi">1</span>
        <span class="k">elif</span> <span class="n">minsize</span> <span class="o">==</span> <span class="n">mode2size</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_mode</span> <span class="o">=</span> <span class="mi">2</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_mode21</span> <span class="o">=</span> <span class="n">code1</span>
        <span class="k">elif</span> <span class="n">minsize</span> <span class="o">==</span> <span class="n">mode3size</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_mode</span> <span class="o">=</span> <span class="mi">3</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_mode31</span> <span class="o">=</span> <span class="n">code1</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_mode301</span> <span class="o">=</span> <span class="n">code2</span>
        <span class="k">elif</span> <span class="n">minsize</span> <span class="o">==</span> <span class="n">mode4size</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_mode</span> <span class="o">=</span> <span class="mi">4</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_mode41</span> <span class="o">=</span> <span class="n">code1</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_mode401</span> <span class="o">=</span> <span class="n">code2</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_mode4001</span> <span class="o">=</span> <span class="n">code3</span>
        <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">_writebits</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="s2">&quot;subscriber coding error&quot;</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="s2">&quot;publisher coding error&quot;</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_commands_sent_sincelastchange</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="k">return</span> <span class="kc">None</span></div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
  
 
<hr />
<p>
    <a href="https://github.com/sttp/pyapi">GitHub Home</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://pypi.org/project/sttpapi/" target="_blank">PyPi Package: sttpapi</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://sttp.github.io/documentation/" target="_blank">General STTP Documentation</a>
</p>
<br /><br />
Copyright © 2022, <a href="https://gridprotectionalliance.org/" target="_blank">Grid&nbsp;Protection&nbsp;Alliance</a>
<a href="https://github.com/sttp/pyapi">
    <img align="right" style="margin-top: -50px"
        src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/LockPython_64High.png">
</a>


</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>