sttp.data package
=================

Submodules
----------

sttp.data.callbackerrorlistener module
--------------------------------------

.. automodule:: sttp.data.callbackerrorlistener
   :members:
   :undoc-members:
   :show-inheritance:

sttp.data.columnexpression module
---------------------------------

.. automodule:: sttp.data.columnexpression
   :members:
   :undoc-members:
   :show-inheritance:

sttp.data.constants module
--------------------------

.. automodule:: sttp.data.constants
   :members:
   :undoc-members:
   :show-inheritance:

sttp.data.datacolumn module
---------------------------

.. automodule:: sttp.data.datacolumn
   :members:
   :undoc-members:
   :show-inheritance:

sttp.data.datarow module
------------------------

.. automodule:: sttp.data.datarow
   :members:
   :undoc-members:
   :show-inheritance:

sttp.data.dataset module
------------------------

.. automodule:: sttp.data.dataset
   :members:
   :undoc-members:
   :show-inheritance:

sttp.data.datatable module
--------------------------

.. automodule:: sttp.data.datatable
   :members:
   :undoc-members:
   :show-inheritance:

sttp.data.datatype module
-------------------------

.. automodule:: sttp.data.datatype
   :members:
   :undoc-members:
   :show-inheritance:

sttp.data.errors module
-----------------------

.. automodule:: sttp.data.errors
   :members:
   :undoc-members:
   :show-inheritance:

sttp.data.expression module
---------------------------

.. automodule:: sttp.data.expression
   :members:
   :undoc-members:
   :show-inheritance:

sttp.data.expressiontree module
-------------------------------

.. automodule:: sttp.data.expressiontree
   :members:
   :undoc-members:
   :show-inheritance:

sttp.data.filterexpressionparser module
---------------------------------------

.. automodule:: sttp.data.filterexpressionparser
   :members:
   :undoc-members:
   :show-inheritance:

sttp.data.functionexpression module
-----------------------------------

.. automodule:: sttp.data.functionexpression
   :members:
   :undoc-members:
   :show-inheritance:

sttp.data.inlistexpression module
---------------------------------

.. automodule:: sttp.data.inlistexpression
   :members:
   :undoc-members:
   :show-inheritance:

sttp.data.operatorexpression module
-----------------------------------

.. automodule:: sttp.data.operatorexpression
   :members:
   :undoc-members:
   :show-inheritance:

sttp.data.orderbyterm module
----------------------------

.. automodule:: sttp.data.orderbyterm
   :members:
   :undoc-members:
   :show-inheritance:

sttp.data.tableidfields module
------------------------------

.. automodule:: sttp.data.tableidfields
   :members:
   :undoc-members:
   :show-inheritance:

sttp.data.unaryexpression module
--------------------------------

.. automodule:: sttp.data.unaryexpression
   :members:
   :undoc-members:
   :show-inheritance:

sttp.data.valueexpression module
--------------------------------

.. automodule:: sttp.data.valueexpression
   :members:
   :undoc-members:
   :show-inheritance:

Module contents
---------------

.. automodule:: sttp.data
   :members:
   :undoc-members:
   :show-inheritance:
