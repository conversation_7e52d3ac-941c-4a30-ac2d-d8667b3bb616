# Configuration file for the Sphinx documentation builder.
#
# For the full list of built-in configuration values, see the documentation:
# https://www.sphinx-doc.org/en/master/usage/configuration.html

import os
import sys
sys.path.insert(0, os.path.abspath("../src"))

# -- Project information -----------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#project-information

project = 'sttp/pyapi'
copyright = '2022, Grid Protection Alliance'
author = '<PERSON><PERSON> <PERSON>'

# The full version, including alpha/beta/rc tags
release = '0.6.4'

# -- General configuration ---------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#general-configuration

extensions = [
    "sphinx.ext.autodoc",
    "sphinx.ext.viewcode",
    "sphinx.ext.napoleon"
]

templates_path = ['_templates']
exclude_patterns = ['_build', 'Thumbs.db', '.DS_Store']


# -- Options for HTML output -------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#options-for-html-output

html_theme = 'sphinx_rtd_theme'
html_static_path = ['_static']
html_show_copyright = False
html_show_sphinx = True
html_logo = 'https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/sttp.png'
html_favicon = 'https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/favicon.ico'
