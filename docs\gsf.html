

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>gsf package &mdash; sttp/pyapi 0.6.4 documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=92fd9be5" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=8209df7b" />

  
    <link rel="shortcut icon" href="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/favicon.ico"/>
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=bbec6902"></script>
      <script src="_static/documentation_options.js?v=b1f64a84"></script>
      <script src="_static/doctools.js?v=92e14aea"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            sttp/pyapi
              <img src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/sttp.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <!-- Local TOC -->
              <div class="local-toc"><ul>
<li><a class="reference internal" href="#">gsf package</a><ul>
<li><a class="reference internal" href="#submodules">Submodules</a></li>
<li><a class="reference internal" href="#module-gsf.binarystream">gsf.binarystream module</a><ul>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream"><code class="docutils literal notranslate"><span class="pre">BinaryStream</span></code></a><ul>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.IO_BUFFERSIZE"><code class="docutils literal notranslate"><span class="pre">BinaryStream.IO_BUFFERSIZE</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.VALUE_BUFFERSIZE"><code class="docutils literal notranslate"><span class="pre">BinaryStream.VALUE_BUFFERSIZE</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.flush"><code class="docutils literal notranslate"><span class="pre">BinaryStream.flush()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.read"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.read7bit_int32"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read7bit_int32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.read7bit_int64"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read7bit_int64()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.read7bit_uint32"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read7bit_uint32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.read7bit_uint64"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read7bit_uint64()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.read_all"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_all()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.read_boolean"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_boolean()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.read_buffer"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_buffer()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.read_byte"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_byte()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.read_bytes"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_bytes()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.read_guid"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_guid()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.read_int16"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_int16()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.read_int32"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_int32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.read_int64"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_int64()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.read_string"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_string()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.read_uint16"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_uint16()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.read_uint32"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_uint32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.read_uint64"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_uint64()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.write"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.write7bit_int32"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write7bit_int32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.write7bit_int64"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write7bit_int64()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.write7bit_uint32"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write7bit_uint32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.write7bit_uint64"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write7bit_uint64()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.write_boolean"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write_boolean()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.write_buffer"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write_buffer()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.write_byte"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write_byte()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.write_guid"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write_guid()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.write_int16"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write_int16()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.write_int32"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write_int32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.write_int64"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write_int64()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.write_string"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write_string()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.write_uint16"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write_uint16()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.write_uint32"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write_uint32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.binarystream.BinaryStream.write_uint64"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write_uint64()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-gsf.encoding7bit">gsf.encoding7bit module</a><ul>
<li><a class="reference internal" href="#gsf.encoding7bit.Encoding7Bit"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit</span></code></a><ul>
<li><a class="reference internal" href="#gsf.encoding7bit.Encoding7Bit.read_int16"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.read_int16()</span></code></a></li>
<li><a class="reference internal" href="#gsf.encoding7bit.Encoding7Bit.read_int32"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.read_int32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.encoding7bit.Encoding7Bit.read_int64"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.read_int64()</span></code></a></li>
<li><a class="reference internal" href="#gsf.encoding7bit.Encoding7Bit.read_uint16"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.read_uint16()</span></code></a></li>
<li><a class="reference internal" href="#gsf.encoding7bit.Encoding7Bit.read_uint32"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.read_uint32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.encoding7bit.Encoding7Bit.read_uint64"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.read_uint64()</span></code></a></li>
<li><a class="reference internal" href="#gsf.encoding7bit.Encoding7Bit.write_int16"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.write_int16()</span></code></a></li>
<li><a class="reference internal" href="#gsf.encoding7bit.Encoding7Bit.write_int32"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.write_int32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.encoding7bit.Encoding7Bit.write_int64"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.write_int64()</span></code></a></li>
<li><a class="reference internal" href="#gsf.encoding7bit.Encoding7Bit.write_uint16"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.write_uint16()</span></code></a></li>
<li><a class="reference internal" href="#gsf.encoding7bit.Encoding7Bit.write_uint32"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.write_uint32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.encoding7bit.Encoding7Bit.write_uint64"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.write_uint64()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-gsf.endianorder">gsf.endianorder module</a><ul>
<li><a class="reference internal" href="#gsf.endianorder.BigEndian"><code class="docutils literal notranslate"><span class="pre">BigEndian</span></code></a><ul>
<li><a class="reference internal" href="#gsf.endianorder.BigEndian.static_init"><code class="docutils literal notranslate"><span class="pre">BigEndian.static_init()</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.BigEndian.swaporder"><code class="docutils literal notranslate"><span class="pre">BigEndian.swaporder</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.BigEndian.target_byteorder"><code class="docutils literal notranslate"><span class="pre">BigEndian.target_byteorder</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#gsf.endianorder.LittleEndian"><code class="docutils literal notranslate"><span class="pre">LittleEndian</span></code></a><ul>
<li><a class="reference internal" href="#gsf.endianorder.LittleEndian.static_init"><code class="docutils literal notranslate"><span class="pre">LittleEndian.static_init()</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.LittleEndian.swaporder"><code class="docutils literal notranslate"><span class="pre">LittleEndian.swaporder</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.LittleEndian.target_byteorder"><code class="docutils literal notranslate"><span class="pre">LittleEndian.target_byteorder</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#gsf.endianorder.NativeEndian"><code class="docutils literal notranslate"><span class="pre">NativeEndian</span></code></a><ul>
<li><a class="reference internal" href="#gsf.endianorder.NativeEndian.from_float16"><code class="docutils literal notranslate"><span class="pre">NativeEndian.from_float16()</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.NativeEndian.from_float32"><code class="docutils literal notranslate"><span class="pre">NativeEndian.from_float32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.NativeEndian.from_float64"><code class="docutils literal notranslate"><span class="pre">NativeEndian.from_float64()</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.NativeEndian.from_int16"><code class="docutils literal notranslate"><span class="pre">NativeEndian.from_int16()</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.NativeEndian.from_int32"><code class="docutils literal notranslate"><span class="pre">NativeEndian.from_int32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.NativeEndian.from_int64"><code class="docutils literal notranslate"><span class="pre">NativeEndian.from_int64()</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.NativeEndian.from_uint16"><code class="docutils literal notranslate"><span class="pre">NativeEndian.from_uint16()</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.NativeEndian.from_uint32"><code class="docutils literal notranslate"><span class="pre">NativeEndian.from_uint32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.NativeEndian.from_uint64"><code class="docutils literal notranslate"><span class="pre">NativeEndian.from_uint64()</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.NativeEndian.static_init"><code class="docutils literal notranslate"><span class="pre">NativeEndian.static_init()</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.NativeEndian.swaporder"><code class="docutils literal notranslate"><span class="pre">NativeEndian.swaporder</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.NativeEndian.target_byteorder"><code class="docutils literal notranslate"><span class="pre">NativeEndian.target_byteorder</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.NativeEndian.to_float16"><code class="docutils literal notranslate"><span class="pre">NativeEndian.to_float16()</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.NativeEndian.to_float32"><code class="docutils literal notranslate"><span class="pre">NativeEndian.to_float32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.NativeEndian.to_float64"><code class="docutils literal notranslate"><span class="pre">NativeEndian.to_float64()</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.NativeEndian.to_int16"><code class="docutils literal notranslate"><span class="pre">NativeEndian.to_int16()</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.NativeEndian.to_int32"><code class="docutils literal notranslate"><span class="pre">NativeEndian.to_int32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.NativeEndian.to_int64"><code class="docutils literal notranslate"><span class="pre">NativeEndian.to_int64()</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.NativeEndian.to_uint16"><code class="docutils literal notranslate"><span class="pre">NativeEndian.to_uint16()</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.NativeEndian.to_uint32"><code class="docutils literal notranslate"><span class="pre">NativeEndian.to_uint32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.endianorder.NativeEndian.to_uint64"><code class="docutils literal notranslate"><span class="pre">NativeEndian.to_uint64()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-gsf.streamencoder">gsf.streamencoder module</a><ul>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder"><code class="docutils literal notranslate"><span class="pre">StreamEncoder</span></code></a><ul>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.default_byteorder"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.default_byteorder</span></code></a></li>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.read"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.read()</span></code></a></li>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.read7bit_uint32"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.read7bit_uint32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.read7bit_uint64"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.read7bit_uint64()</span></code></a></li>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.read_bool"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.read_bool()</span></code></a></li>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.read_byte"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.read_byte()</span></code></a></li>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.read_int16"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.read_int16()</span></code></a></li>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.read_int32"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.read_int32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.read_int64"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.read_int64()</span></code></a></li>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.read_uint16"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.read_uint16()</span></code></a></li>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.read_uint32"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.read_uint32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.read_uint64"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.read_uint64()</span></code></a></li>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.write"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.write()</span></code></a></li>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.write7bit_uint32"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.write7bit_uint32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.write7bit_uint64"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.write7bit_uint64()</span></code></a></li>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.write_bool"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.write_bool()</span></code></a></li>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.write_byte"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.write_byte()</span></code></a></li>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.write_int16"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.write_int16()</span></code></a></li>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.write_int32"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.write_int32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.write_int64"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.write_int64()</span></code></a></li>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.write_uint16"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.write_uint16()</span></code></a></li>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.write_uint32"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.write_uint32()</span></code></a></li>
<li><a class="reference internal" href="#gsf.streamencoder.StreamEncoder.write_uint64"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.write_uint64()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-gsf">Module contents</a><ul>
<li><a class="reference internal" href="#gsf.ByteSize"><code class="docutils literal notranslate"><span class="pre">ByteSize</span></code></a><ul>
<li><a class="reference internal" href="#gsf.ByteSize.FLOAT16"><code class="docutils literal notranslate"><span class="pre">ByteSize.FLOAT16</span></code></a></li>
<li><a class="reference internal" href="#gsf.ByteSize.FLOAT32"><code class="docutils literal notranslate"><span class="pre">ByteSize.FLOAT32</span></code></a></li>
<li><a class="reference internal" href="#gsf.ByteSize.FLOAT64"><code class="docutils literal notranslate"><span class="pre">ByteSize.FLOAT64</span></code></a></li>
<li><a class="reference internal" href="#gsf.ByteSize.INT16"><code class="docutils literal notranslate"><span class="pre">ByteSize.INT16</span></code></a></li>
<li><a class="reference internal" href="#gsf.ByteSize.INT32"><code class="docutils literal notranslate"><span class="pre">ByteSize.INT32</span></code></a></li>
<li><a class="reference internal" href="#gsf.ByteSize.INT64"><code class="docutils literal notranslate"><span class="pre">ByteSize.INT64</span></code></a></li>
<li><a class="reference internal" href="#gsf.ByteSize.INT8"><code class="docutils literal notranslate"><span class="pre">ByteSize.INT8</span></code></a></li>
<li><a class="reference internal" href="#gsf.ByteSize.UINT16"><code class="docutils literal notranslate"><span class="pre">ByteSize.UINT16</span></code></a></li>
<li><a class="reference internal" href="#gsf.ByteSize.UINT32"><code class="docutils literal notranslate"><span class="pre">ByteSize.UINT32</span></code></a></li>
<li><a class="reference internal" href="#gsf.ByteSize.UINT64"><code class="docutils literal notranslate"><span class="pre">ByteSize.UINT64</span></code></a></li>
<li><a class="reference internal" href="#gsf.ByteSize.UINT8"><code class="docutils literal notranslate"><span class="pre">ByteSize.UINT8</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#gsf.Convert"><code class="docutils literal notranslate"><span class="pre">Convert</span></code></a><ul>
<li><a class="reference internal" href="#gsf.Convert.from_str"><code class="docutils literal notranslate"><span class="pre">Convert.from_str()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#gsf.Empty"><code class="docutils literal notranslate"><span class="pre">Empty</span></code></a><ul>
<li><a class="reference internal" href="#gsf.Empty.DATETIME"><code class="docutils literal notranslate"><span class="pre">Empty.DATETIME</span></code></a></li>
<li><a class="reference internal" href="#gsf.Empty.DECIMAL"><code class="docutils literal notranslate"><span class="pre">Empty.DECIMAL</span></code></a></li>
<li><a class="reference internal" href="#gsf.Empty.DOUBLE"><code class="docutils literal notranslate"><span class="pre">Empty.DOUBLE</span></code></a></li>
<li><a class="reference internal" href="#gsf.Empty.GUID"><code class="docutils literal notranslate"><span class="pre">Empty.GUID</span></code></a></li>
<li><a class="reference internal" href="#gsf.Empty.INT16"><code class="docutils literal notranslate"><span class="pre">Empty.INT16</span></code></a></li>
<li><a class="reference internal" href="#gsf.Empty.INT32"><code class="docutils literal notranslate"><span class="pre">Empty.INT32</span></code></a></li>
<li><a class="reference internal" href="#gsf.Empty.INT64"><code class="docutils literal notranslate"><span class="pre">Empty.INT64</span></code></a></li>
<li><a class="reference internal" href="#gsf.Empty.INT8"><code class="docutils literal notranslate"><span class="pre">Empty.INT8</span></code></a></li>
<li><a class="reference internal" href="#gsf.Empty.SINGLE"><code class="docutils literal notranslate"><span class="pre">Empty.SINGLE</span></code></a></li>
<li><a class="reference internal" href="#gsf.Empty.STRING"><code class="docutils literal notranslate"><span class="pre">Empty.STRING</span></code></a></li>
<li><a class="reference internal" href="#gsf.Empty.TICKS"><code class="docutils literal notranslate"><span class="pre">Empty.TICKS</span></code></a></li>
<li><a class="reference internal" href="#gsf.Empty.UINT16"><code class="docutils literal notranslate"><span class="pre">Empty.UINT16</span></code></a></li>
<li><a class="reference internal" href="#gsf.Empty.UINT32"><code class="docutils literal notranslate"><span class="pre">Empty.UINT32</span></code></a></li>
<li><a class="reference internal" href="#gsf.Empty.UINT64"><code class="docutils literal notranslate"><span class="pre">Empty.UINT64</span></code></a></li>
<li><a class="reference internal" href="#gsf.Empty.UINT8"><code class="docutils literal notranslate"><span class="pre">Empty.UINT8</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#gsf.Limits"><code class="docutils literal notranslate"><span class="pre">Limits</span></code></a><ul>
<li><a class="reference internal" href="#gsf.Limits.MAXBYTE"><code class="docutils literal notranslate"><span class="pre">Limits.MAXBYTE</span></code></a></li>
<li><a class="reference internal" href="#gsf.Limits.MAXINT16"><code class="docutils literal notranslate"><span class="pre">Limits.MAXINT16</span></code></a></li>
<li><a class="reference internal" href="#gsf.Limits.MAXINT32"><code class="docutils literal notranslate"><span class="pre">Limits.MAXINT32</span></code></a></li>
<li><a class="reference internal" href="#gsf.Limits.MAXINT64"><code class="docutils literal notranslate"><span class="pre">Limits.MAXINT64</span></code></a></li>
<li><a class="reference internal" href="#gsf.Limits.MAXTICKS"><code class="docutils literal notranslate"><span class="pre">Limits.MAXTICKS</span></code></a></li>
<li><a class="reference internal" href="#gsf.Limits.MAXUINT16"><code class="docutils literal notranslate"><span class="pre">Limits.MAXUINT16</span></code></a></li>
<li><a class="reference internal" href="#gsf.Limits.MAXUINT32"><code class="docutils literal notranslate"><span class="pre">Limits.MAXUINT32</span></code></a></li>
<li><a class="reference internal" href="#gsf.Limits.MAXUINT64"><code class="docutils literal notranslate"><span class="pre">Limits.MAXUINT64</span></code></a></li>
<li><a class="reference internal" href="#gsf.Limits.MININT16"><code class="docutils literal notranslate"><span class="pre">Limits.MININT16</span></code></a></li>
<li><a class="reference internal" href="#gsf.Limits.MININT32"><code class="docutils literal notranslate"><span class="pre">Limits.MININT32</span></code></a></li>
<li><a class="reference internal" href="#gsf.Limits.MININT64"><code class="docutils literal notranslate"><span class="pre">Limits.MININT64</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#gsf.Validate"><code class="docutils literal notranslate"><span class="pre">Validate</span></code></a><ul>
<li><a class="reference internal" href="#gsf.Validate.parameters"><code class="docutils literal notranslate"><span class="pre">Validate.parameters()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#gsf.normalize_enumname"><code class="docutils literal notranslate"><span class="pre">normalize_enumname()</span></code></a></li>
<li><a class="reference internal" href="#gsf.override"><code class="docutils literal notranslate"><span class="pre">override()</span></code></a></li>
<li><a class="reference internal" href="#gsf.static_init"><code class="docutils literal notranslate"><span class="pre">static_init()</span></code></a></li>
<li><a class="reference internal" href="#gsf.virtual"><code class="docutils literal notranslate"><span class="pre">virtual()</span></code></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">sttp/pyapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">gsf package</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/gsf.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="gsf-package">
<h1>gsf package<a class="headerlink" href="#gsf-package" title="Link to this heading"></a></h1>
<section id="submodules">
<h2>Submodules<a class="headerlink" href="#submodules" title="Link to this heading"></a></h2>
</section>
<section id="module-gsf.binarystream">
<span id="gsf-binarystream-module"></span><h2>gsf.binarystream module<a class="headerlink" href="#module-gsf.binarystream" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">gsf.binarystream.</span></span><span class="sig-name descname"><span class="pre">BinaryStream</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stream</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#gsf.streamencoder.StreamEncoder" title="gsf.streamencoder.StreamEncoder"><span class="pre">StreamEncoder</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Establishes buffered I/O around a base stream, e.g., a socket.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.IO_BUFFERSIZE">
<span class="sig-name descname"><span class="pre">IO_BUFFERSIZE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1420</span></em><a class="headerlink" href="#gsf.binarystream.BinaryStream.IO_BUFFERSIZE" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.VALUE_BUFFERSIZE">
<span class="sig-name descname"><span class="pre">VALUE_BUFFERSIZE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">16</span></em><a class="headerlink" href="#gsf.binarystream.BinaryStream.VALUE_BUFFERSIZE" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.flush">
<span class="sig-name descname"><span class="pre">flush</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.flush"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.flush" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.read">
<span class="sig-name descname"><span class="pre">read</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bytearray</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">offset</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">count</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.read"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.read" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.read7bit_int32">
<span class="sig-name descname"><span class="pre">read7bit_int32</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int32</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.read7bit_int32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.read7bit_int32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.read7bit_int64">
<span class="sig-name descname"><span class="pre">read7bit_int64</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int64</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.read7bit_int64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.read7bit_int64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.read7bit_uint32">
<span class="sig-name descname"><span class="pre">read7bit_uint32</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint32</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.read7bit_uint32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.read7bit_uint32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.read7bit_uint64">
<span class="sig-name descname"><span class="pre">read7bit_uint64</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint64</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.read7bit_uint64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.read7bit_uint64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.read_all">
<span class="sig-name descname"><span class="pre">read_all</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bytearray</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">position</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">length</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.read_all"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.read_all" title="Link to this definition"></a></dt>
<dd><p>Reads all of the provided bytes. Will not return prematurely, continues
to execute <cite>Read</cite> operation until the entire <cite>length</cite> has been read.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.read_boolean">
<span class="sig-name descname"><span class="pre">read_boolean</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">bool</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.read_boolean"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.read_boolean" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.read_buffer">
<span class="sig-name descname"><span class="pre">read_buffer</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">bytes</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.read_buffer"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.read_buffer" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.read_byte">
<span class="sig-name descname"><span class="pre">read_byte</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint8</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.read_byte"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.read_byte" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.read_bytes">
<span class="sig-name descname"><span class="pre">read_bytes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">count</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">bytes</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.read_bytes"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.read_bytes" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.read_guid">
<span class="sig-name descname"><span class="pre">read_guid</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">UUID</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.read_guid"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.read_guid" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.read_int16">
<span class="sig-name descname"><span class="pre">read_int16</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int16</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.read_int16"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.read_int16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.read_int32">
<span class="sig-name descname"><span class="pre">read_int32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int32</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.read_int32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.read_int32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.read_int64">
<span class="sig-name descname"><span class="pre">read_int64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int64</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.read_int64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.read_int64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.read_string">
<span class="sig-name descname"><span class="pre">read_string</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.read_string"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.read_string" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.read_uint16">
<span class="sig-name descname"><span class="pre">read_uint16</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint16</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.read_uint16"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.read_uint16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.read_uint32">
<span class="sig-name descname"><span class="pre">read_uint32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint32</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.read_uint32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.read_uint32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.read_uint64">
<span class="sig-name descname"><span class="pre">read_uint64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint64</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.read_uint64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.read_uint64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.write">
<span class="sig-name descname"><span class="pre">write</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bytes</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">offset</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">count</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.write"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.write" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.write7bit_int32">
<span class="sig-name descname"><span class="pre">write7bit_int32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int32</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.write7bit_int32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.write7bit_int32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.write7bit_int64">
<span class="sig-name descname"><span class="pre">write7bit_int64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int64</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.write7bit_int64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.write7bit_int64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.write7bit_uint32">
<span class="sig-name descname"><span class="pre">write7bit_uint32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint32</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.write7bit_uint32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.write7bit_uint32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.write7bit_uint64">
<span class="sig-name descname"><span class="pre">write7bit_uint64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint64</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.write7bit_uint64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.write7bit_uint64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.write_boolean">
<span class="sig-name descname"><span class="pre">write_boolean</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.write_boolean"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.write_boolean" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.write_buffer">
<span class="sig-name descname"><span class="pre">write_buffer</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bytes</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.write_buffer"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.write_buffer" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.write_byte">
<span class="sig-name descname"><span class="pre">write_byte</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint8</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.write_byte"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.write_byte" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.write_guid">
<span class="sig-name descname"><span class="pre">write_guid</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">UUID</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.write_guid"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.write_guid" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.write_int16">
<span class="sig-name descname"><span class="pre">write_int16</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int16</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.write_int16"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.write_int16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.write_int32">
<span class="sig-name descname"><span class="pre">write_int32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int32</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.write_int32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.write_int32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.write_int64">
<span class="sig-name descname"><span class="pre">write_int64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int64</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.write_int64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.write_int64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.write_string">
<span class="sig-name descname"><span class="pre">write_string</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.write_string"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.write_string" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.write_uint16">
<span class="sig-name descname"><span class="pre">write_uint16</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint16</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.write_uint16"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.write_uint16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.write_uint32">
<span class="sig-name descname"><span class="pre">write_uint32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint32</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.write_uint32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.write_uint32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.binarystream.BinaryStream.write_uint64">
<span class="sig-name descname"><span class="pre">write_uint64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint64</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/binarystream.html#BinaryStream.write_uint64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.binarystream.BinaryStream.write_uint64" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-gsf.encoding7bit">
<span id="gsf-encoding7bit-module"></span><h2>gsf.encoding7bit module<a class="headerlink" href="#module-gsf.encoding7bit" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="gsf.encoding7bit.Encoding7Bit">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">gsf.encoding7bit.</span></span><span class="sig-name descname"><span class="pre">Encoding7Bit</span></span><a class="reference internal" href="_modules/gsf/encoding7bit.html#Encoding7Bit"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.encoding7bit.Encoding7Bit" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Defines 7-bit encoding/decoding functions.</p>
<dl class="py method">
<dt class="sig sig-object py" id="gsf.encoding7bit.Encoding7Bit.read_int16">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">read_int16</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stream_reader</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">uint8</span><span class="p"><span class="pre">]</span></span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int16</span></span></span><a class="reference internal" href="_modules/gsf/encoding7bit.html#Encoding7Bit.read_int16"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.encoding7bit.Encoding7Bit.read_int16" title="Link to this definition"></a></dt>
<dd><p>Reads 16-bit signed integer value using 7-bit encoding from the provided stream reader.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>stream_reader</strong> (<em>function to read a byte value from a stream</em>)</p>
</dd>
</dl>
<p class="rubric">Notes</p>
<p>Call expects one to two bytes to be available in base stream.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.encoding7bit.Encoding7Bit.read_int32">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">read_int32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stream_reader</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">uint8</span><span class="p"><span class="pre">]</span></span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int32</span></span></span><a class="reference internal" href="_modules/gsf/encoding7bit.html#Encoding7Bit.read_int32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.encoding7bit.Encoding7Bit.read_int32" title="Link to this definition"></a></dt>
<dd><p>Reads 32-bit signed integer value using 7-bit encoding from the provided stream reader.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>stream_reader</strong> (<em>function to read a byte value from a stream</em>)</p>
</dd>
</dl>
<p class="rubric">Notes</p>
<p>Call expects one to five bytes to be available in base stream.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.encoding7bit.Encoding7Bit.read_int64">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">read_int64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stream_reader</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">uint8</span><span class="p"><span class="pre">]</span></span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int64</span></span></span><a class="reference internal" href="_modules/gsf/encoding7bit.html#Encoding7Bit.read_int64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.encoding7bit.Encoding7Bit.read_int64" title="Link to this definition"></a></dt>
<dd><p>Reads 64-bit signed integer value using 7-bit encoding from the provided stream reader.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>stream_writer</strong> (<em>function to write a byte value to a stream</em>)</p></li>
<li><p><strong>value</strong> (<em>64-bit value to write</em>)</p></li>
</ul>
</dd>
</dl>
<p class="rubric">Notes</p>
<p>Call expects one to nine bytes to be available in base stream.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.encoding7bit.Encoding7Bit.read_uint16">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">read_uint16</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stream_reader</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">uint8</span><span class="p"><span class="pre">]</span></span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint16</span></span></span><a class="reference internal" href="_modules/gsf/encoding7bit.html#Encoding7Bit.read_uint16"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.encoding7bit.Encoding7Bit.read_uint16" title="Link to this definition"></a></dt>
<dd><p>Reads 16-bit unsigned integer value using 7-bit encoding from the provided stream reader.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>stream_reader</strong> (<em>function to read a byte value from a stream</em>)</p>
</dd>
</dl>
<p class="rubric">Notes</p>
<p>Call expects one to two bytes to be available in base stream.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.encoding7bit.Encoding7Bit.read_uint32">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">read_uint32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stream_reader</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">uint8</span><span class="p"><span class="pre">]</span></span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint32</span></span></span><a class="reference internal" href="_modules/gsf/encoding7bit.html#Encoding7Bit.read_uint32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.encoding7bit.Encoding7Bit.read_uint32" title="Link to this definition"></a></dt>
<dd><p>Reads 32-bit unsigned integer value using 7-bit encoding from the provided stream reader.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>stream_reader</strong> (<em>function to read a byte value from a stream</em>)</p>
</dd>
</dl>
<p class="rubric">Notes</p>
<p>Call expects one to five bytes to be available in base stream.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.encoding7bit.Encoding7Bit.read_uint64">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">read_uint64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stream_reader</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">uint8</span><span class="p"><span class="pre">]</span></span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint64</span></span></span><a class="reference internal" href="_modules/gsf/encoding7bit.html#Encoding7Bit.read_uint64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.encoding7bit.Encoding7Bit.read_uint64" title="Link to this definition"></a></dt>
<dd><p>Reads 64-bit unsigned integer value using 7-bit encoding from the provided stream reader.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>stream_writer</strong> (<em>function to write a byte value to a stream</em>)</p></li>
<li><p><strong>value</strong> (<em>64-bit value to write</em>)</p></li>
</ul>
</dd>
</dl>
<p class="rubric">Notes</p>
<p>Call expects one to nine bytes to be available in base stream.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.encoding7bit.Encoding7Bit.write_int16">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">write_int16</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stream_writer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">uint8</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">int</span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int16</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/encoding7bit.html#Encoding7Bit.write_int16"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.encoding7bit.Encoding7Bit.write_int16" title="Link to this definition"></a></dt>
<dd><p>Writes 16-bit signed integer value using 7-bit encoding to the provided stream writer.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>stream_writer</strong> (<em>function to write a byte value to a stream</em>)</p></li>
<li><p><strong>value</strong> (<em>16-bit value to write</em>)</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.encoding7bit.Encoding7Bit.write_int32">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">write_int32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stream_writer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">uint8</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">int</span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int32</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/encoding7bit.html#Encoding7Bit.write_int32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.encoding7bit.Encoding7Bit.write_int32" title="Link to this definition"></a></dt>
<dd><p>Writes 32-bit signed integer value using 7-bit encoding to the provided stream writer.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>stream_writer</strong> (<em>function to write a byte value to a stream</em>)</p></li>
<li><p><strong>value</strong> (<em>32-bit value to write</em>)</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.encoding7bit.Encoding7Bit.write_int64">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">write_int64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stream_writer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">uint8</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">int</span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int64</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/encoding7bit.html#Encoding7Bit.write_int64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.encoding7bit.Encoding7Bit.write_int64" title="Link to this definition"></a></dt>
<dd><p>Writes 64-bit signed integer value using 7-bit encoding to the provided stream writer.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>stream_writer</strong> (<em>function to write a byte value to a stream</em>)</p></li>
<li><p><strong>value</strong> (<em>64-bit value to write</em>)</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.encoding7bit.Encoding7Bit.write_uint16">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">write_uint16</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stream_writer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">uint8</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">int</span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint16</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/encoding7bit.html#Encoding7Bit.write_uint16"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.encoding7bit.Encoding7Bit.write_uint16" title="Link to this definition"></a></dt>
<dd><p>Writes 16-bit unsigned integer value using 7-bit encoding to the provided stream writer.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>stream_writer</strong> (<em>function to write a byte value to a stream</em>)</p></li>
<li><p><strong>value</strong> (<em>16-bit value to write</em>)</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.encoding7bit.Encoding7Bit.write_uint32">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">write_uint32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stream_writer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">uint8</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">int</span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint32</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/encoding7bit.html#Encoding7Bit.write_uint32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.encoding7bit.Encoding7Bit.write_uint32" title="Link to this definition"></a></dt>
<dd><p>Writes 32-bit unsigned integer value using 7-bit encoding to the provided stream writer.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>stream_writer</strong> (<em>function to write a byte value to a stream</em>)</p></li>
<li><p><strong>value</strong> (<em>32-bit value to write</em>)</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.encoding7bit.Encoding7Bit.write_uint64">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">write_uint64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stream_writer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">uint8</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">int</span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint64</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/encoding7bit.html#Encoding7Bit.write_uint64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.encoding7bit.Encoding7Bit.write_uint64" title="Link to this definition"></a></dt>
<dd><p>Writes 64-bit unsigned integer value using 7-bit encoding to the provided stream writer.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>stream_writer</strong> (<em>function to write a byte value to a stream</em>)</p></li>
<li><p><strong>value</strong> (<em>64-bit value to write</em>)</p></li>
</ul>
</dd>
</dl>
</dd></dl>

</dd></dl>

</section>
<section id="module-gsf.endianorder">
<span id="gsf-endianorder-module"></span><h2>gsf.endianorder module<a class="headerlink" href="#module-gsf.endianorder" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="gsf.endianorder.BigEndian">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">gsf.endianorder.</span></span><span class="sig-name descname"><span class="pre">BigEndian</span></span><a class="reference internal" href="_modules/gsf/endianorder.html#BigEndian"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.BigEndian" title="Link to this definition"></a></dt>
<dd><p>Bases: <a class="reference internal" href="#gsf.endianorder.NativeEndian" title="gsf.endianorder.NativeEndian"><code class="xref py py-class docutils literal notranslate"><span class="pre">NativeEndian</span></code></a></p>
<p>Manages conversion of bytes to basic types in big endian order.</p>
<dl class="py method">
<dt class="sig sig-object py" id="gsf.endianorder.BigEndian.static_init">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">static_init</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="_modules/gsf/endianorder.html#BigEndian.static_init"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.BigEndian.static_init" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.endianorder.BigEndian.swaporder">
<span class="sig-name descname"><span class="pre">swaporder</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">True</span></em><a class="headerlink" href="#gsf.endianorder.BigEndian.swaporder" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.endianorder.BigEndian.target_byteorder">
<span class="sig-name descname"><span class="pre">target_byteorder</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'big'</span></em><a class="headerlink" href="#gsf.endianorder.BigEndian.target_byteorder" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="gsf.endianorder.LittleEndian">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">gsf.endianorder.</span></span><span class="sig-name descname"><span class="pre">LittleEndian</span></span><a class="reference internal" href="_modules/gsf/endianorder.html#LittleEndian"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.LittleEndian" title="Link to this definition"></a></dt>
<dd><p>Bases: <a class="reference internal" href="#gsf.endianorder.NativeEndian" title="gsf.endianorder.NativeEndian"><code class="xref py py-class docutils literal notranslate"><span class="pre">NativeEndian</span></code></a></p>
<p>Manages conversion of bytes to basic types in little endian order.</p>
<dl class="py method">
<dt class="sig sig-object py" id="gsf.endianorder.LittleEndian.static_init">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">static_init</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="_modules/gsf/endianorder.html#LittleEndian.static_init"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.LittleEndian.static_init" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.endianorder.LittleEndian.swaporder">
<span class="sig-name descname"><span class="pre">swaporder</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">False</span></em><a class="headerlink" href="#gsf.endianorder.LittleEndian.swaporder" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.endianorder.LittleEndian.target_byteorder">
<span class="sig-name descname"><span class="pre">target_byteorder</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'little'</span></em><a class="headerlink" href="#gsf.endianorder.LittleEndian.target_byteorder" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="gsf.endianorder.NativeEndian">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">gsf.endianorder.</span></span><span class="sig-name descname"><span class="pre">NativeEndian</span></span><a class="reference internal" href="_modules/gsf/endianorder.html#NativeEndian"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.NativeEndian" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Manages conversion of bytes to basic types in native endian order.</p>
<dl class="py method">
<dt class="sig sig-object py" id="gsf.endianorder.NativeEndian.from_float16">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">from_float16</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">float16</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">bytes</span></span></span><a class="reference internal" href="_modules/gsf/endianorder.html#NativeEndian.from_float16"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.NativeEndian.from_float16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.endianorder.NativeEndian.from_float32">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">from_float32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">float32</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">bytes</span></span></span><a class="reference internal" href="_modules/gsf/endianorder.html#NativeEndian.from_float32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.NativeEndian.from_float32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.endianorder.NativeEndian.from_float64">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">from_float64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">float64</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">bytes</span></span></span><a class="reference internal" href="_modules/gsf/endianorder.html#NativeEndian.from_float64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.NativeEndian.from_float64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.endianorder.NativeEndian.from_int16">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">from_int16</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int16</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">bytes</span></span></span><a class="reference internal" href="_modules/gsf/endianorder.html#NativeEndian.from_int16"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.NativeEndian.from_int16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.endianorder.NativeEndian.from_int32">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">from_int32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int32</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">bytes</span></span></span><a class="reference internal" href="_modules/gsf/endianorder.html#NativeEndian.from_int32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.NativeEndian.from_int32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.endianorder.NativeEndian.from_int64">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">from_int64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int64</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">bytes</span></span></span><a class="reference internal" href="_modules/gsf/endianorder.html#NativeEndian.from_int64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.NativeEndian.from_int64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.endianorder.NativeEndian.from_uint16">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">from_uint16</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint16</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">bytes</span></span></span><a class="reference internal" href="_modules/gsf/endianorder.html#NativeEndian.from_uint16"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.NativeEndian.from_uint16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.endianorder.NativeEndian.from_uint32">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">from_uint32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint32</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">bytes</span></span></span><a class="reference internal" href="_modules/gsf/endianorder.html#NativeEndian.from_uint32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.NativeEndian.from_uint32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.endianorder.NativeEndian.from_uint64">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">from_uint64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint64</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">bytes</span></span></span><a class="reference internal" href="_modules/gsf/endianorder.html#NativeEndian.from_uint64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.NativeEndian.from_uint64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.endianorder.NativeEndian.static_init">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">static_init</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="_modules/gsf/endianorder.html#NativeEndian.static_init"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.NativeEndian.static_init" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.endianorder.NativeEndian.swaporder">
<span class="sig-name descname"><span class="pre">swaporder</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">False</span></em><a class="headerlink" href="#gsf.endianorder.NativeEndian.swaporder" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.endianorder.NativeEndian.target_byteorder">
<span class="sig-name descname"><span class="pre">target_byteorder</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'little'</span></em><a class="headerlink" href="#gsf.endianorder.NativeEndian.target_byteorder" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.endianorder.NativeEndian.to_float16">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">to_float16</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bytes</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">float16</span></span></span><a class="reference internal" href="_modules/gsf/endianorder.html#NativeEndian.to_float16"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.NativeEndian.to_float16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.endianorder.NativeEndian.to_float32">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">to_float32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bytes</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">float32</span></span></span><a class="reference internal" href="_modules/gsf/endianorder.html#NativeEndian.to_float32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.NativeEndian.to_float32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.endianorder.NativeEndian.to_float64">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">to_float64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bytes</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">float64</span></span></span><a class="reference internal" href="_modules/gsf/endianorder.html#NativeEndian.to_float64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.NativeEndian.to_float64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.endianorder.NativeEndian.to_int16">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">to_int16</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bytes</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int16</span></span></span><a class="reference internal" href="_modules/gsf/endianorder.html#NativeEndian.to_int16"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.NativeEndian.to_int16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.endianorder.NativeEndian.to_int32">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">to_int32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bytes</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int32</span></span></span><a class="reference internal" href="_modules/gsf/endianorder.html#NativeEndian.to_int32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.NativeEndian.to_int32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.endianorder.NativeEndian.to_int64">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">to_int64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bytes</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int64</span></span></span><a class="reference internal" href="_modules/gsf/endianorder.html#NativeEndian.to_int64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.NativeEndian.to_int64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.endianorder.NativeEndian.to_uint16">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">to_uint16</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bytes</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint16</span></span></span><a class="reference internal" href="_modules/gsf/endianorder.html#NativeEndian.to_uint16"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.NativeEndian.to_uint16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.endianorder.NativeEndian.to_uint32">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">to_uint32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bytes</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint32</span></span></span><a class="reference internal" href="_modules/gsf/endianorder.html#NativeEndian.to_uint32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.NativeEndian.to_uint32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.endianorder.NativeEndian.to_uint64">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">to_uint64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bytes</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint64</span></span></span><a class="reference internal" href="_modules/gsf/endianorder.html#NativeEndian.to_uint64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.endianorder.NativeEndian.to_uint64" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-gsf.streamencoder">
<span id="gsf-streamencoder-module"></span><h2>gsf.streamencoder module<a class="headerlink" href="#module-gsf.streamencoder" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">gsf.streamencoder.</span></span><span class="sig-name descname"><span class="pre">StreamEncoder</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">read</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">int</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bytes</span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">write</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">bytes</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">int</span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default_byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'little'</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Defines functions for encoding and decoding native types to and from a stream.
For this class, a stream is simply an abstract notion based on provided functions
that read and write byte buffers as Python <cite>bytes</cite> objects advancing a position
in the base stream. The read/write functions simply wrap a base object that can
handle input and output as bytes, e.g., a <cite>bytearray</cite> or a <cite>socket</cite>.</p>
<dl class="py property">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.default_byteorder">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">default_byteorder</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.default_byteorder" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.read">
<span class="sig-name descname"><span class="pre">read</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_buffer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bytearray</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">offset</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">count</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder.read"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.read" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.read7bit_uint32">
<span class="sig-name descname"><span class="pre">read7bit_uint32</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint32</span></span></span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder.read7bit_uint32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.read7bit_uint32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.read7bit_uint64">
<span class="sig-name descname"><span class="pre">read7bit_uint64</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint64</span></span></span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder.read7bit_uint64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.read7bit_uint64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.read_bool">
<span class="sig-name descname"><span class="pre">read_bool</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">bool</span></span></span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder.read_bool"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.read_bool" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.read_byte">
<span class="sig-name descname"><span class="pre">read_byte</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint8</span></span></span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder.read_byte"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.read_byte" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.read_int16">
<span class="sig-name descname"><span class="pre">read_int16</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int16</span></span></span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder.read_int16"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.read_int16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.read_int32">
<span class="sig-name descname"><span class="pre">read_int32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int32</span></span></span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder.read_int32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.read_int32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.read_int64">
<span class="sig-name descname"><span class="pre">read_int64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int64</span></span></span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder.read_int64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.read_int64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.read_uint16">
<span class="sig-name descname"><span class="pre">read_uint16</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint16</span></span></span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder.read_uint16"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.read_uint16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.read_uint32">
<span class="sig-name descname"><span class="pre">read_uint32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint32</span></span></span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder.read_uint32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.read_uint32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.read_uint64">
<span class="sig-name descname"><span class="pre">read_uint64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint64</span></span></span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder.read_uint64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.read_uint64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.write">
<span class="sig-name descname"><span class="pre">write</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">source_buffer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bytes</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">offset</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">count</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder.write"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.write" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.write7bit_uint32">
<span class="sig-name descname"><span class="pre">write7bit_uint32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint32</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder.write7bit_uint32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.write7bit_uint32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.write7bit_uint64">
<span class="sig-name descname"><span class="pre">write7bit_uint64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint64</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder.write7bit_uint64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.write7bit_uint64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.write_bool">
<span class="sig-name descname"><span class="pre">write_bool</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder.write_bool"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.write_bool" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.write_byte">
<span class="sig-name descname"><span class="pre">write_byte</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint8</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder.write_byte"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.write_byte" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.write_int16">
<span class="sig-name descname"><span class="pre">write_int16</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int16</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder.write_int16"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.write_int16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.write_int32">
<span class="sig-name descname"><span class="pre">write_int32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int32</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder.write_int32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.write_int32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.write_int64">
<span class="sig-name descname"><span class="pre">write_int64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int64</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder.write_int64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.write_int64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.write_uint16">
<span class="sig-name descname"><span class="pre">write_uint16</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint16</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder.write_uint16"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.write_uint16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.write_uint32">
<span class="sig-name descname"><span class="pre">write_uint32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint32</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder.write_uint32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.write_uint32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gsf.streamencoder.StreamEncoder.write_uint64">
<span class="sig-name descname"><span class="pre">write_uint64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint64</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">byteorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/gsf/streamencoder.html#StreamEncoder.write_uint64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.streamencoder.StreamEncoder.write_uint64" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-gsf">
<span id="module-contents"></span><h2>Module contents<a class="headerlink" href="#module-gsf" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="gsf.ByteSize">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">gsf.</span></span><span class="sig-name descname"><span class="pre">ByteSize</span></span><a class="reference internal" href="_modules/gsf.html#ByteSize"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.ByteSize" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.ByteSize.FLOAT16">
<span class="sig-name descname"><span class="pre">FLOAT16</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2</span></em><a class="headerlink" href="#gsf.ByteSize.FLOAT16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.ByteSize.FLOAT32">
<span class="sig-name descname"><span class="pre">FLOAT32</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">4</span></em><a class="headerlink" href="#gsf.ByteSize.FLOAT32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.ByteSize.FLOAT64">
<span class="sig-name descname"><span class="pre">FLOAT64</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">8</span></em><a class="headerlink" href="#gsf.ByteSize.FLOAT64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.ByteSize.INT16">
<span class="sig-name descname"><span class="pre">INT16</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2</span></em><a class="headerlink" href="#gsf.ByteSize.INT16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.ByteSize.INT32">
<span class="sig-name descname"><span class="pre">INT32</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">4</span></em><a class="headerlink" href="#gsf.ByteSize.INT32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.ByteSize.INT64">
<span class="sig-name descname"><span class="pre">INT64</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">8</span></em><a class="headerlink" href="#gsf.ByteSize.INT64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.ByteSize.INT8">
<span class="sig-name descname"><span class="pre">INT8</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1</span></em><a class="headerlink" href="#gsf.ByteSize.INT8" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.ByteSize.UINT16">
<span class="sig-name descname"><span class="pre">UINT16</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2</span></em><a class="headerlink" href="#gsf.ByteSize.UINT16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.ByteSize.UINT32">
<span class="sig-name descname"><span class="pre">UINT32</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">4</span></em><a class="headerlink" href="#gsf.ByteSize.UINT32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.ByteSize.UINT64">
<span class="sig-name descname"><span class="pre">UINT64</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">8</span></em><a class="headerlink" href="#gsf.ByteSize.UINT64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.ByteSize.UINT8">
<span class="sig-name descname"><span class="pre">UINT8</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1</span></em><a class="headerlink" href="#gsf.ByteSize.UINT8" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="gsf.Convert">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">gsf.</span></span><span class="sig-name descname"><span class="pre">Convert</span></span><a class="reference internal" href="_modules/gsf.html#Convert"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.Convert" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="gsf.Convert.from_str">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">from_str</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dtype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">dtype</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">object</span></span></span><a class="reference internal" href="_modules/gsf.html#Convert.from_str"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.Convert.from_str" title="Link to this definition"></a></dt>
<dd><p>Converts a string value to the specified type.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="gsf.Empty">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">gsf.</span></span><span class="sig-name descname"><span class="pre">Empty</span></span><a class="reference internal" href="_modules/gsf.html#Empty"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.Empty" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Empty.DATETIME">
<span class="sig-name descname"><span class="pre">DATETIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">datetime.datetime(1,</span> <span class="pre">1,</span> <span class="pre">1,</span> <span class="pre">0,</span> <span class="pre">0,</span> <span class="pre">tzinfo=datetime.timezone.utc)</span></em><a class="headerlink" href="#gsf.Empty.DATETIME" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Empty.DECIMAL">
<span class="sig-name descname"><span class="pre">DECIMAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">Decimal('0')</span></em><a class="headerlink" href="#gsf.Empty.DECIMAL" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Empty.DOUBLE">
<span class="sig-name descname"><span class="pre">DOUBLE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.float64(0.0)</span></em><a class="headerlink" href="#gsf.Empty.DOUBLE" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Empty.GUID">
<span class="sig-name descname"><span class="pre">GUID</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">UUID('00000000-0000-0000-0000-000000000000')</span></em><a class="headerlink" href="#gsf.Empty.GUID" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Empty.INT16">
<span class="sig-name descname"><span class="pre">INT16</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.int16(0)</span></em><a class="headerlink" href="#gsf.Empty.INT16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Empty.INT32">
<span class="sig-name descname"><span class="pre">INT32</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.int32(0)</span></em><a class="headerlink" href="#gsf.Empty.INT32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Empty.INT64">
<span class="sig-name descname"><span class="pre">INT64</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.int64(0)</span></em><a class="headerlink" href="#gsf.Empty.INT64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Empty.INT8">
<span class="sig-name descname"><span class="pre">INT8</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.int8(0)</span></em><a class="headerlink" href="#gsf.Empty.INT8" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Empty.SINGLE">
<span class="sig-name descname"><span class="pre">SINGLE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.float32(0.0)</span></em><a class="headerlink" href="#gsf.Empty.SINGLE" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Empty.STRING">
<span class="sig-name descname"><span class="pre">STRING</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#gsf.Empty.STRING" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Empty.TICKS">
<span class="sig-name descname"><span class="pre">TICKS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.uint64(0)</span></em><a class="headerlink" href="#gsf.Empty.TICKS" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Empty.UINT16">
<span class="sig-name descname"><span class="pre">UINT16</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.uint16(0)</span></em><a class="headerlink" href="#gsf.Empty.UINT16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Empty.UINT32">
<span class="sig-name descname"><span class="pre">UINT32</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.uint32(0)</span></em><a class="headerlink" href="#gsf.Empty.UINT32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Empty.UINT64">
<span class="sig-name descname"><span class="pre">UINT64</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.uint64(0)</span></em><a class="headerlink" href="#gsf.Empty.UINT64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Empty.UINT8">
<span class="sig-name descname"><span class="pre">UINT8</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.uint8(0)</span></em><a class="headerlink" href="#gsf.Empty.UINT8" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="gsf.Limits">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">gsf.</span></span><span class="sig-name descname"><span class="pre">Limits</span></span><a class="reference internal" href="_modules/gsf.html#Limits"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.Limits" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Limits.MAXBYTE">
<span class="sig-name descname"><span class="pre">MAXBYTE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">255</span></em><a class="headerlink" href="#gsf.Limits.MAXBYTE" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Limits.MAXINT16">
<span class="sig-name descname"><span class="pre">MAXINT16</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">32767</span></em><a class="headerlink" href="#gsf.Limits.MAXINT16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Limits.MAXINT32">
<span class="sig-name descname"><span class="pre">MAXINT32</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2147483647</span></em><a class="headerlink" href="#gsf.Limits.MAXINT32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Limits.MAXINT64">
<span class="sig-name descname"><span class="pre">MAXINT64</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">9223372036854775807</span></em><a class="headerlink" href="#gsf.Limits.MAXINT64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Limits.MAXTICKS">
<span class="sig-name descname"><span class="pre">MAXTICKS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">3155378975999999999</span></em><a class="headerlink" href="#gsf.Limits.MAXTICKS" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Limits.MAXUINT16">
<span class="sig-name descname"><span class="pre">MAXUINT16</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">65535</span></em><a class="headerlink" href="#gsf.Limits.MAXUINT16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Limits.MAXUINT32">
<span class="sig-name descname"><span class="pre">MAXUINT32</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">4294967295</span></em><a class="headerlink" href="#gsf.Limits.MAXUINT32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Limits.MAXUINT64">
<span class="sig-name descname"><span class="pre">MAXUINT64</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">18446744073709551615</span></em><a class="headerlink" href="#gsf.Limits.MAXUINT64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Limits.MININT16">
<span class="sig-name descname"><span class="pre">MININT16</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">-32768</span></em><a class="headerlink" href="#gsf.Limits.MININT16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Limits.MININT32">
<span class="sig-name descname"><span class="pre">MININT32</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">-2147483648</span></em><a class="headerlink" href="#gsf.Limits.MININT32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="gsf.Limits.MININT64">
<span class="sig-name descname"><span class="pre">MININT64</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">-9223372036854775808</span></em><a class="headerlink" href="#gsf.Limits.MININT64" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="gsf.Validate">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">gsf.</span></span><span class="sig-name descname"><span class="pre">Validate</span></span><a class="reference internal" href="_modules/gsf.html#Validate"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.Validate" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<dl class="py method">
<dt class="sig sig-object py" id="gsf.Validate.parameters">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">parameters</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">array</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Sequence</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">startIndex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">length</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/gsf.html#Validate.parameters"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.Validate.parameters" title="Link to this definition"></a></dt>
<dd><p>Validates array or buffer parameters.</p>
</dd></dl>

</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gsf.normalize_enumname">
<span class="sig-prename descclassname"><span class="pre">gsf.</span></span><span class="sig-name descname"><span class="pre">normalize_enumname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Enum</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="reference internal" href="_modules/gsf.html#normalize_enumname"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.normalize_enumname" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gsf.override">
<span class="sig-prename descclassname"><span class="pre">gsf.</span></span><span class="sig-name descname"><span class="pre">override</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/gsf.html#override"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.override" title="Link to this definition"></a></dt>
<dd><p>Marks a method as an override (for documentation purposes).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gsf.static_init">
<span class="sig-prename descclassname"><span class="pre">gsf.</span></span><span class="sig-name descname"><span class="pre">static_init</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cls</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/gsf.html#static_init"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.static_init" title="Link to this definition"></a></dt>
<dd><p>Marks a class as having a static initialization function and
executes the function when class is statically constructed.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gsf.virtual">
<span class="sig-prename descclassname"><span class="pre">gsf.</span></span><span class="sig-name descname"><span class="pre">virtual</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/gsf.html#virtual"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#gsf.virtual" title="Link to this definition"></a></dt>
<dd><p>Marks a method as overridable (for documentation purposes).</p>
</dd></dl>

</section>
</section>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
  
 
<hr />
<p>
    <a href="https://github.com/sttp/pyapi">GitHub Home</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://pypi.org/project/sttpapi/" target="_blank">PyPi Package: sttpapi</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://sttp.github.io/documentation/" target="_blank">General STTP Documentation</a>
</p>
<br /><br />
Copyright © 2022, <a href="https://gridprotectionalliance.org/" target="_blank">Grid&nbsp;Protection&nbsp;Alliance</a>
<a href="https://github.com/sttp/pyapi">
    <img align="right" style="margin-top: -50px"
        src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/LockPython_64High.png">
</a>


</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>