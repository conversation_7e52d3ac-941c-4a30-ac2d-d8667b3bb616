

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>src &mdash; sttp/pyapi 0.6.4 documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=92fd9be5" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=8209df7b" />

  
    <link rel="shortcut icon" href="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/favicon.ico"/>
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=bbec6902"></script>
      <script src="_static/documentation_options.js?v=b1f64a84"></script>
      <script src="_static/doctools.js?v=92e14aea"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            sttp/pyapi
              <img src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/sttp.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <!-- Local TOC -->
              <div class="local-toc"><ul>
<li><a class="reference internal" href="#">src</a></li>
</ul>
</div>
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">sttp/pyapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">src</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/modules.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="src">
<h1>src<a class="headerlink" href="#src" title="Link to this heading"></a></h1>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="gsf.html">gsf package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="gsf.html#submodules">Submodules</a></li>
<li class="toctree-l2"><a class="reference internal" href="gsf.html#module-gsf.binarystream">gsf.binarystream module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream"><code class="docutils literal notranslate"><span class="pre">BinaryStream</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.IO_BUFFERSIZE"><code class="docutils literal notranslate"><span class="pre">BinaryStream.IO_BUFFERSIZE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.VALUE_BUFFERSIZE"><code class="docutils literal notranslate"><span class="pre">BinaryStream.VALUE_BUFFERSIZE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.flush"><code class="docutils literal notranslate"><span class="pre">BinaryStream.flush()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.read"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.read7bit_int32"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read7bit_int32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.read7bit_int64"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read7bit_int64()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.read7bit_uint32"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read7bit_uint32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.read7bit_uint64"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read7bit_uint64()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.read_all"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_all()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.read_boolean"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_boolean()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.read_buffer"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_buffer()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.read_byte"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_byte()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.read_bytes"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_bytes()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.read_guid"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_guid()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.read_int16"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_int16()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.read_int32"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_int32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.read_int64"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_int64()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.read_string"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_string()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.read_uint16"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_uint16()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.read_uint32"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_uint32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.read_uint64"><code class="docutils literal notranslate"><span class="pre">BinaryStream.read_uint64()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.write"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.write7bit_int32"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write7bit_int32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.write7bit_int64"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write7bit_int64()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.write7bit_uint32"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write7bit_uint32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.write7bit_uint64"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write7bit_uint64()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.write_boolean"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write_boolean()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.write_buffer"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write_buffer()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.write_byte"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write_byte()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.write_guid"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write_guid()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.write_int16"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write_int16()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.write_int32"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write_int32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.write_int64"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write_int64()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.write_string"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write_string()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.write_uint16"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write_uint16()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.write_uint32"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write_uint32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.binarystream.BinaryStream.write_uint64"><code class="docutils literal notranslate"><span class="pre">BinaryStream.write_uint64()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="gsf.html#module-gsf.encoding7bit">gsf.encoding7bit module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="gsf.html#gsf.encoding7bit.Encoding7Bit"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.encoding7bit.Encoding7Bit.read_int16"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.read_int16()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.encoding7bit.Encoding7Bit.read_int32"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.read_int32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.encoding7bit.Encoding7Bit.read_int64"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.read_int64()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.encoding7bit.Encoding7Bit.read_uint16"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.read_uint16()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.encoding7bit.Encoding7Bit.read_uint32"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.read_uint32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.encoding7bit.Encoding7Bit.read_uint64"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.read_uint64()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.encoding7bit.Encoding7Bit.write_int16"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.write_int16()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.encoding7bit.Encoding7Bit.write_int32"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.write_int32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.encoding7bit.Encoding7Bit.write_int64"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.write_int64()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.encoding7bit.Encoding7Bit.write_uint16"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.write_uint16()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.encoding7bit.Encoding7Bit.write_uint32"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.write_uint32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.encoding7bit.Encoding7Bit.write_uint64"><code class="docutils literal notranslate"><span class="pre">Encoding7Bit.write_uint64()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="gsf.html#module-gsf.endianorder">gsf.endianorder module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="gsf.html#gsf.endianorder.BigEndian"><code class="docutils literal notranslate"><span class="pre">BigEndian</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.BigEndian.static_init"><code class="docutils literal notranslate"><span class="pre">BigEndian.static_init()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.BigEndian.swaporder"><code class="docutils literal notranslate"><span class="pre">BigEndian.swaporder</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.BigEndian.target_byteorder"><code class="docutils literal notranslate"><span class="pre">BigEndian.target_byteorder</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="gsf.html#gsf.endianorder.LittleEndian"><code class="docutils literal notranslate"><span class="pre">LittleEndian</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.LittleEndian.static_init"><code class="docutils literal notranslate"><span class="pre">LittleEndian.static_init()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.LittleEndian.swaporder"><code class="docutils literal notranslate"><span class="pre">LittleEndian.swaporder</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.LittleEndian.target_byteorder"><code class="docutils literal notranslate"><span class="pre">LittleEndian.target_byteorder</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="gsf.html#gsf.endianorder.NativeEndian"><code class="docutils literal notranslate"><span class="pre">NativeEndian</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.NativeEndian.from_float16"><code class="docutils literal notranslate"><span class="pre">NativeEndian.from_float16()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.NativeEndian.from_float32"><code class="docutils literal notranslate"><span class="pre">NativeEndian.from_float32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.NativeEndian.from_float64"><code class="docutils literal notranslate"><span class="pre">NativeEndian.from_float64()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.NativeEndian.from_int16"><code class="docutils literal notranslate"><span class="pre">NativeEndian.from_int16()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.NativeEndian.from_int32"><code class="docutils literal notranslate"><span class="pre">NativeEndian.from_int32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.NativeEndian.from_int64"><code class="docutils literal notranslate"><span class="pre">NativeEndian.from_int64()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.NativeEndian.from_uint16"><code class="docutils literal notranslate"><span class="pre">NativeEndian.from_uint16()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.NativeEndian.from_uint32"><code class="docutils literal notranslate"><span class="pre">NativeEndian.from_uint32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.NativeEndian.from_uint64"><code class="docutils literal notranslate"><span class="pre">NativeEndian.from_uint64()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.NativeEndian.static_init"><code class="docutils literal notranslate"><span class="pre">NativeEndian.static_init()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.NativeEndian.swaporder"><code class="docutils literal notranslate"><span class="pre">NativeEndian.swaporder</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.NativeEndian.target_byteorder"><code class="docutils literal notranslate"><span class="pre">NativeEndian.target_byteorder</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.NativeEndian.to_float16"><code class="docutils literal notranslate"><span class="pre">NativeEndian.to_float16()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.NativeEndian.to_float32"><code class="docutils literal notranslate"><span class="pre">NativeEndian.to_float32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.NativeEndian.to_float64"><code class="docutils literal notranslate"><span class="pre">NativeEndian.to_float64()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.NativeEndian.to_int16"><code class="docutils literal notranslate"><span class="pre">NativeEndian.to_int16()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.NativeEndian.to_int32"><code class="docutils literal notranslate"><span class="pre">NativeEndian.to_int32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.NativeEndian.to_int64"><code class="docutils literal notranslate"><span class="pre">NativeEndian.to_int64()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.NativeEndian.to_uint16"><code class="docutils literal notranslate"><span class="pre">NativeEndian.to_uint16()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.NativeEndian.to_uint32"><code class="docutils literal notranslate"><span class="pre">NativeEndian.to_uint32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.endianorder.NativeEndian.to_uint64"><code class="docutils literal notranslate"><span class="pre">NativeEndian.to_uint64()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="gsf.html#module-gsf.streamencoder">gsf.streamencoder module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder"><code class="docutils literal notranslate"><span class="pre">StreamEncoder</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.default_byteorder"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.default_byteorder</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.read"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.read()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.read7bit_uint32"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.read7bit_uint32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.read7bit_uint64"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.read7bit_uint64()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.read_bool"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.read_bool()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.read_byte"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.read_byte()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.read_int16"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.read_int16()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.read_int32"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.read_int32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.read_int64"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.read_int64()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.read_uint16"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.read_uint16()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.read_uint32"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.read_uint32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.read_uint64"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.read_uint64()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.write"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.write()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.write7bit_uint32"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.write7bit_uint32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.write7bit_uint64"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.write7bit_uint64()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.write_bool"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.write_bool()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.write_byte"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.write_byte()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.write_int16"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.write_int16()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.write_int32"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.write_int32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.write_int64"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.write_int64()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.write_uint16"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.write_uint16()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.write_uint32"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.write_uint32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.streamencoder.StreamEncoder.write_uint64"><code class="docutils literal notranslate"><span class="pre">StreamEncoder.write_uint64()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="gsf.html#module-gsf">Module contents</a><ul>
<li class="toctree-l3"><a class="reference internal" href="gsf.html#gsf.ByteSize"><code class="docutils literal notranslate"><span class="pre">ByteSize</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.ByteSize.FLOAT16"><code class="docutils literal notranslate"><span class="pre">ByteSize.FLOAT16</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.ByteSize.FLOAT32"><code class="docutils literal notranslate"><span class="pre">ByteSize.FLOAT32</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.ByteSize.FLOAT64"><code class="docutils literal notranslate"><span class="pre">ByteSize.FLOAT64</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.ByteSize.INT16"><code class="docutils literal notranslate"><span class="pre">ByteSize.INT16</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.ByteSize.INT32"><code class="docutils literal notranslate"><span class="pre">ByteSize.INT32</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.ByteSize.INT64"><code class="docutils literal notranslate"><span class="pre">ByteSize.INT64</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.ByteSize.INT8"><code class="docutils literal notranslate"><span class="pre">ByteSize.INT8</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.ByteSize.UINT16"><code class="docutils literal notranslate"><span class="pre">ByteSize.UINT16</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.ByteSize.UINT32"><code class="docutils literal notranslate"><span class="pre">ByteSize.UINT32</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.ByteSize.UINT64"><code class="docutils literal notranslate"><span class="pre">ByteSize.UINT64</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.ByteSize.UINT8"><code class="docutils literal notranslate"><span class="pre">ByteSize.UINT8</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="gsf.html#gsf.Convert"><code class="docutils literal notranslate"><span class="pre">Convert</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Convert.from_str"><code class="docutils literal notranslate"><span class="pre">Convert.from_str()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="gsf.html#gsf.Empty"><code class="docutils literal notranslate"><span class="pre">Empty</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Empty.DATETIME"><code class="docutils literal notranslate"><span class="pre">Empty.DATETIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Empty.DECIMAL"><code class="docutils literal notranslate"><span class="pre">Empty.DECIMAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Empty.DOUBLE"><code class="docutils literal notranslate"><span class="pre">Empty.DOUBLE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Empty.GUID"><code class="docutils literal notranslate"><span class="pre">Empty.GUID</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Empty.INT16"><code class="docutils literal notranslate"><span class="pre">Empty.INT16</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Empty.INT32"><code class="docutils literal notranslate"><span class="pre">Empty.INT32</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Empty.INT64"><code class="docutils literal notranslate"><span class="pre">Empty.INT64</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Empty.INT8"><code class="docutils literal notranslate"><span class="pre">Empty.INT8</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Empty.SINGLE"><code class="docutils literal notranslate"><span class="pre">Empty.SINGLE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Empty.STRING"><code class="docutils literal notranslate"><span class="pre">Empty.STRING</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Empty.TICKS"><code class="docutils literal notranslate"><span class="pre">Empty.TICKS</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Empty.UINT16"><code class="docutils literal notranslate"><span class="pre">Empty.UINT16</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Empty.UINT32"><code class="docutils literal notranslate"><span class="pre">Empty.UINT32</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Empty.UINT64"><code class="docutils literal notranslate"><span class="pre">Empty.UINT64</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Empty.UINT8"><code class="docutils literal notranslate"><span class="pre">Empty.UINT8</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="gsf.html#gsf.Limits"><code class="docutils literal notranslate"><span class="pre">Limits</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Limits.MAXBYTE"><code class="docutils literal notranslate"><span class="pre">Limits.MAXBYTE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Limits.MAXINT16"><code class="docutils literal notranslate"><span class="pre">Limits.MAXINT16</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Limits.MAXINT32"><code class="docutils literal notranslate"><span class="pre">Limits.MAXINT32</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Limits.MAXINT64"><code class="docutils literal notranslate"><span class="pre">Limits.MAXINT64</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Limits.MAXTICKS"><code class="docutils literal notranslate"><span class="pre">Limits.MAXTICKS</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Limits.MAXUINT16"><code class="docutils literal notranslate"><span class="pre">Limits.MAXUINT16</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Limits.MAXUINT32"><code class="docutils literal notranslate"><span class="pre">Limits.MAXUINT32</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Limits.MAXUINT64"><code class="docutils literal notranslate"><span class="pre">Limits.MAXUINT64</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Limits.MININT16"><code class="docutils literal notranslate"><span class="pre">Limits.MININT16</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Limits.MININT32"><code class="docutils literal notranslate"><span class="pre">Limits.MININT32</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Limits.MININT64"><code class="docutils literal notranslate"><span class="pre">Limits.MININT64</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="gsf.html#gsf.Validate"><code class="docutils literal notranslate"><span class="pre">Validate</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="gsf.html#gsf.Validate.parameters"><code class="docutils literal notranslate"><span class="pre">Validate.parameters()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="gsf.html#gsf.normalize_enumname"><code class="docutils literal notranslate"><span class="pre">normalize_enumname()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="gsf.html#gsf.override"><code class="docutils literal notranslate"><span class="pre">override()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="gsf.html#gsf.static_init"><code class="docutils literal notranslate"><span class="pre">static_init()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="gsf.html#gsf.virtual"><code class="docutils literal notranslate"><span class="pre">virtual()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="sttp.html">sttp package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="sttp.html#subpackages">Subpackages</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html">sttp.data package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#module-sttp.data.callbackerrorlistener">sttp.data.callbackerrorlistener module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#module-sttp.data.columnexpression">sttp.data.columnexpression module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#module-sttp.data.constants">sttp.data.constants module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#module-sttp.data.datacolumn">sttp.data.datacolumn module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#module-sttp.data.datarow">sttp.data.datarow module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#module-sttp.data.dataset">sttp.data.dataset module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#module-sttp.data.datatable">sttp.data.datatable module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#module-sttp.data.datatype">sttp.data.datatype module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#module-sttp.data.errors">sttp.data.errors module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#module-sttp.data.expression">sttp.data.expression module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#module-sttp.data.expressiontree">sttp.data.expressiontree module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#module-sttp.data.filterexpressionparser">sttp.data.filterexpressionparser module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#module-sttp.data.functionexpression">sttp.data.functionexpression module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#module-sttp.data.inlistexpression">sttp.data.inlistexpression module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#module-sttp.data.operatorexpression">sttp.data.operatorexpression module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#module-sttp.data.orderbyterm">sttp.data.orderbyterm module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#module-sttp.data.tableidfields">sttp.data.tableidfields module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#module-sttp.data.unaryexpression">sttp.data.unaryexpression module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#module-sttp.data.valueexpression">sttp.data.valueexpression module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#module-sttp.data">Module contents</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.metadata.html">sttp.metadata package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#subpackages">Subpackages</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#module-sttp.metadata.cache">sttp.metadata.cache module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#module-sttp.metadata">Module contents</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.html">sttp.transport package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#subpackages">Subpackages</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#module-sttp.transport.bufferblock">sttp.transport.bufferblock module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#module-sttp.transport.compactmeasurement">sttp.transport.compactmeasurement module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#module-sttp.transport.constants">sttp.transport.constants module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#module-sttp.transport.datasubscriber">sttp.transport.datasubscriber module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#module-sttp.transport.measurement">sttp.transport.measurement module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#module-sttp.transport.signalindexcache">sttp.transport.signalindexcache module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#module-sttp.transport.signalkind">sttp.transport.signalkind module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#module-sttp.transport.subscriberconnector">sttp.transport.subscriberconnector module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#module-sttp.transport.subscriptioninfo">sttp.transport.subscriptioninfo module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#module-sttp.transport">Module contents</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.html#submodules">Submodules</a></li>
<li class="toctree-l2"><a class="reference internal" href="sttp.html#module-sttp.config">sttp.config module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.html#sttp.config.Config"><code class="docutils literal notranslate"><span class="pre">Config</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.DEFAULT_AUTORECONNECT"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_AUTORECONNECT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.DEFAULT_AUTOREQUESTMETADATA"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_AUTOREQUESTMETADATA</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.DEFAULT_AUTOSUBSCRIBE"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_AUTOSUBSCRIBE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.DEFAULT_COMPRESS_METADATA"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_COMPRESS_METADATA</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.DEFAULT_COMPRESS_PAYLOADDATA"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_COMPRESS_PAYLOADDATA</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.DEFAULT_COMPRESS_SIGNALINDEXCACHE"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_COMPRESS_SIGNALINDEXCACHE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.DEFAULT_MAXRETRIES"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_MAXRETRIES</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.DEFAULT_MAXRETRYINTERVAL"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_MAXRETRYINTERVAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.DEFAULT_METADATAFILTERS"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_METADATAFILTERS</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.DEFAULT_RETRYINTERVAL"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_RETRYINTERVAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.DEFAULT_SOCKET_TIMEOUT"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_SOCKET_TIMEOUT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.DEFAULT_VERSION"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_VERSION</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.autoreconnect"><code class="docutils literal notranslate"><span class="pre">Config.autoreconnect</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.autorequestmetadata"><code class="docutils literal notranslate"><span class="pre">Config.autorequestmetadata</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.autosubscribe"><code class="docutils literal notranslate"><span class="pre">Config.autosubscribe</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.compress_metadata"><code class="docutils literal notranslate"><span class="pre">Config.compress_metadata</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.compress_payloaddata"><code class="docutils literal notranslate"><span class="pre">Config.compress_payloaddata</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.compress_signalindexcache"><code class="docutils literal notranslate"><span class="pre">Config.compress_signalindexcache</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.maxretries"><code class="docutils literal notranslate"><span class="pre">Config.maxretries</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.maxretryinterval"><code class="docutils literal notranslate"><span class="pre">Config.maxretryinterval</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.metadatafilters"><code class="docutils literal notranslate"><span class="pre">Config.metadatafilters</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.retryinterval"><code class="docutils literal notranslate"><span class="pre">Config.retryinterval</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.socket_timeout"><code class="docutils literal notranslate"><span class="pre">Config.socket_timeout</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.config.Config.version"><code class="docutils literal notranslate"><span class="pre">Config.version</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.html#module-sttp.reader">sttp.reader module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.html#sttp.reader.MeasurementReader"><code class="docutils literal notranslate"><span class="pre">MeasurementReader</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.reader.MeasurementReader.dispose"><code class="docutils literal notranslate"><span class="pre">MeasurementReader.dispose()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.reader.MeasurementReader.next_measurement"><code class="docutils literal notranslate"><span class="pre">MeasurementReader.next_measurement()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.html#module-sttp.settings">sttp.settings module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.html#sttp.settings.Settings"><code class="docutils literal notranslate"><span class="pre">Settings</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.DEFAULT_CONSTRAINTPARAMETERS"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_CONSTRAINTPARAMETERS</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.DEFAULT_ENABLE_TIME_REASONABILITY_CHECK"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_ENABLE_TIME_REASONABILITY_CHECK</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.DEFAULT_EXTRA_CONNECTIONSTRING_PARAMETERS"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_EXTRA_CONNECTIONSTRING_PARAMETERS</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.DEFAULT_INCLUDETIME"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_INCLUDETIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.DEFAULT_LAGTIME"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_LAGTIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.DEFAULT_LEADTIME"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_LEADTIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.DEFAULT_PROCESSINGINTERVAL"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_PROCESSINGINTERVAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.DEFAULT_PUBLISHINTERVAL"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_PUBLISHINTERVAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.DEFAULT_REQUEST_NANVALUEFILTER"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_REQUEST_NANVALUEFILTER</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.DEFAULT_STARTTIME"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_STARTTIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.DEFAULT_STOPTIME"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_STOPTIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.DEFAULT_THROTTLED"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_THROTTLED</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.DEFAULT_UDPINTERFACE"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_UDPINTERFACE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.DEFAULT_UDPPORT"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_UDPPORT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.DEFAULT_USE_LOCALCLOCK_AS_REALTIME"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_USE_LOCALCLOCK_AS_REALTIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.DEFAULT_USE_MILLISECONDRESOLUTION"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_USE_MILLISECONDRESOLUTION</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.constraintparameters"><code class="docutils literal notranslate"><span class="pre">Settings.constraintparameters</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.enabletimereasonabilitycheck"><code class="docutils literal notranslate"><span class="pre">Settings.enabletimereasonabilitycheck</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.extra_connectionstring_parameters"><code class="docutils literal notranslate"><span class="pre">Settings.extra_connectionstring_parameters</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.includetime"><code class="docutils literal notranslate"><span class="pre">Settings.includetime</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.lagtime"><code class="docutils literal notranslate"><span class="pre">Settings.lagtime</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.leadtime"><code class="docutils literal notranslate"><span class="pre">Settings.leadtime</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.processinginterval"><code class="docutils literal notranslate"><span class="pre">Settings.processinginterval</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.publishinterval"><code class="docutils literal notranslate"><span class="pre">Settings.publishinterval</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.request_nanvaluefilter"><code class="docutils literal notranslate"><span class="pre">Settings.request_nanvaluefilter</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.starttime"><code class="docutils literal notranslate"><span class="pre">Settings.starttime</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.stoptime"><code class="docutils literal notranslate"><span class="pre">Settings.stoptime</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.throttled"><code class="docutils literal notranslate"><span class="pre">Settings.throttled</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.udpinterface"><code class="docutils literal notranslate"><span class="pre">Settings.udpinterface</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.udpport"><code class="docutils literal notranslate"><span class="pre">Settings.udpport</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.use_millisecondresolution"><code class="docutils literal notranslate"><span class="pre">Settings.use_millisecondresolution</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.settings.Settings.uselocalclockasrealtime"><code class="docutils literal notranslate"><span class="pre">Settings.uselocalclockasrealtime</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.html#module-sttp.subscriber">sttp.subscriber module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber"><code class="docutils literal notranslate"><span class="pre">Subscriber</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.activesignalindexcache"><code class="docutils literal notranslate"><span class="pre">Subscriber.activesignalindexcache</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.adjustedvalue"><code class="docutils literal notranslate"><span class="pre">Subscriber.adjustedvalue()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.connect"><code class="docutils literal notranslate"><span class="pre">Subscriber.connect()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.connected"><code class="docutils literal notranslate"><span class="pre">Subscriber.connected</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.default_connectionestablished_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.default_connectionestablished_receiver()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.default_connectionterminated_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.default_connectionterminated_receiver()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.default_errormessage_logger"><code class="docutils literal notranslate"><span class="pre">Subscriber.default_errormessage_logger()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.default_statusmessage_logger"><code class="docutils literal notranslate"><span class="pre">Subscriber.default_statusmessage_logger()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.disconnect"><code class="docutils literal notranslate"><span class="pre">Subscriber.disconnect()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.dispose"><code class="docutils literal notranslate"><span class="pre">Subscriber.dispose()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.errormessage"><code class="docutils literal notranslate"><span class="pre">Subscriber.errormessage()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.measurement_metadata"><code class="docutils literal notranslate"><span class="pre">Subscriber.measurement_metadata()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.metadatacache"><code class="docutils literal notranslate"><span class="pre">Subscriber.metadatacache</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.read_measurements"><code class="docutils literal notranslate"><span class="pre">Subscriber.read_measurements()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.request_metadata"><code class="docutils literal notranslate"><span class="pre">Subscriber.request_metadata()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.set_configurationchanged_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_configurationchanged_receiver()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.set_connectionestablished_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_connectionestablished_receiver()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.set_connectionterminated_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_connectionterminated_receiver()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.set_data_starttime_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_data_starttime_receiver()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.set_errormessage_logger"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_errormessage_logger()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.set_historicalreadcomplete_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_historicalreadcomplete_receiver()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.set_metadatanotification_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_metadatanotification_receiver()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.set_newbufferblock_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_newbufferblock_receiver()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.set_newmeasurements_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_newmeasurements_receiver()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.set_notification_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_notification_receiver()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.set_statusmessage_logger"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_statusmessage_logger()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.set_subscriptionupdated_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_subscriptionupdated_receiver()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.statusmessage"><code class="docutils literal notranslate"><span class="pre">Subscriber.statusmessage()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.subscribe"><code class="docutils literal notranslate"><span class="pre">Subscriber.subscribe()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.subscribed"><code class="docutils literal notranslate"><span class="pre">Subscriber.subscribed</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.subscriberid"><code class="docutils literal notranslate"><span class="pre">Subscriber.subscriberid</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.total_commandchannel_bytesreceived"><code class="docutils literal notranslate"><span class="pre">Subscriber.total_commandchannel_bytesreceived</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.total_datachannel_bytesreceived"><code class="docutils literal notranslate"><span class="pre">Subscriber.total_datachannel_bytesreceived</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.total_measurementsreceived"><code class="docutils literal notranslate"><span class="pre">Subscriber.total_measurementsreceived</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.subscriber.Subscriber.unsubscribe"><code class="docutils literal notranslate"><span class="pre">Subscriber.unsubscribe()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.html#module-sttp.ticks">sttp.ticks module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks"><code class="docutils literal notranslate"><span class="pre">Ticks</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks.LEAPSECOND_DIRECTION"><code class="docutils literal notranslate"><span class="pre">Ticks.LEAPSECOND_DIRECTION</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks.LEAPSECOND_FLAG"><code class="docutils literal notranslate"><span class="pre">Ticks.LEAPSECOND_FLAG</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks.PERDAY"><code class="docutils literal notranslate"><span class="pre">Ticks.PERDAY</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks.PERHOUR"><code class="docutils literal notranslate"><span class="pre">Ticks.PERHOUR</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks.PERMICROSECOND"><code class="docutils literal notranslate"><span class="pre">Ticks.PERMICROSECOND</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks.PERMILLISECOND"><code class="docutils literal notranslate"><span class="pre">Ticks.PERMILLISECOND</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks.PERMINUTE"><code class="docutils literal notranslate"><span class="pre">Ticks.PERMINUTE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks.PERSECOND"><code class="docutils literal notranslate"><span class="pre">Ticks.PERSECOND</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks.UNIXBASEOFFSET"><code class="docutils literal notranslate"><span class="pre">Ticks.UNIXBASEOFFSET</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks.VALUEMASK"><code class="docutils literal notranslate"><span class="pre">Ticks.VALUEMASK</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks.from_datetime"><code class="docutils literal notranslate"><span class="pre">Ticks.from_datetime()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks.from_timedelta"><code class="docutils literal notranslate"><span class="pre">Ticks.from_timedelta()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks.is_leapsecond"><code class="docutils literal notranslate"><span class="pre">Ticks.is_leapsecond()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks.is_negative_leapsecond"><code class="docutils literal notranslate"><span class="pre">Ticks.is_negative_leapsecond()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks.now"><code class="docutils literal notranslate"><span class="pre">Ticks.now()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks.set_leapsecond"><code class="docutils literal notranslate"><span class="pre">Ticks.set_leapsecond()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks.set_negative_leapsecond"><code class="docutils literal notranslate"><span class="pre">Ticks.set_negative_leapsecond()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks.timestampvalue"><code class="docutils literal notranslate"><span class="pre">Ticks.timestampvalue()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks.to_datetime"><code class="docutils literal notranslate"><span class="pre">Ticks.to_datetime()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks.to_shortstring"><code class="docutils literal notranslate"><span class="pre">Ticks.to_shortstring()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks.to_string"><code class="docutils literal notranslate"><span class="pre">Ticks.to_string()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.ticks.Ticks.utcnow"><code class="docutils literal notranslate"><span class="pre">Ticks.utcnow()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.html#module-sttp.version">sttp.version module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.html#sttp.version.Version"><code class="docutils literal notranslate"><span class="pre">Version</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.version.Version.STTP_SOURCE"><code class="docutils literal notranslate"><span class="pre">Version.STTP_SOURCE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.version.Version.STTP_UPDATEDON"><code class="docutils literal notranslate"><span class="pre">Version.STTP_UPDATEDON</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.html#sttp.version.Version.STTP_VERSION"><code class="docutils literal notranslate"><span class="pre">Version.STTP_VERSION</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.html#module-sttp">Module contents</a></li>
</ul>
</li>
</ul>
</div>
</section>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
  
 
<hr />
<p>
    <a href="https://github.com/sttp/pyapi">GitHub Home</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://pypi.org/project/sttpapi/" target="_blank">PyPi Package: sttpapi</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://sttp.github.io/documentation/" target="_blank">General STTP Documentation</a>
</p>
<br /><br />
Copyright © 2022, <a href="https://gridprotectionalliance.org/" target="_blank">Grid&nbsp;Protection&nbsp;Alliance</a>
<a href="https://github.com/sttp/pyapi">
    <img align="right" style="margin-top: -50px"
        src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/LockPython_64High.png">
</a>


</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>