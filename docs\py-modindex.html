

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Python Module Index &mdash; sttp/pyapi 0.6.4 documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=92fd9be5" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=8209df7b" />

  
    <link rel="shortcut icon" href="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/favicon.ico"/>
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=bbec6902"></script>
      <script src="_static/documentation_options.js?v=b1f64a84"></script>
      <script src="_static/doctools.js?v=92e14aea"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
 


</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            sttp/pyapi
              <img src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/sttp.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <!-- Local TOC -->
              <div class="local-toc"></div>
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">sttp/pyapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Python Module Index</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             

   <h1>Python Module Index</h1>

   <div class="modindex-jumpbox">
   <a href="#cap-g"><strong>g</strong></a> | 
   <a href="#cap-s"><strong>s</strong></a>
   </div>

   <table class="indextable modindextable">
     <tr class="pcap"><td></td><td>&#160;</td><td></td></tr>
     <tr class="cap" id="cap-g"><td></td><td>
       <strong>g</strong></td><td></td></tr>
     <tr>
       <td><img src="_static/minus.png" class="toggler"
              id="toggle-1" style="display: none" alt="-" /></td>
       <td>
       <a href="gsf.html#module-gsf"><code class="xref">gsf</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-1">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="gsf.html#module-gsf.binarystream"><code class="xref">gsf.binarystream</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-1">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="gsf.html#module-gsf.encoding7bit"><code class="xref">gsf.encoding7bit</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-1">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="gsf.html#module-gsf.endianorder"><code class="xref">gsf.endianorder</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-1">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="gsf.html#module-gsf.streamencoder"><code class="xref">gsf.streamencoder</code></a></td><td>
       <em></em></td></tr>
     <tr class="pcap"><td></td><td>&#160;</td><td></td></tr>
     <tr class="cap" id="cap-s"><td></td><td>
       <strong>s</strong></td><td></td></tr>
     <tr>
       <td><img src="_static/minus.png" class="toggler"
              id="toggle-2" style="display: none" alt="-" /></td>
       <td>
       <a href="sttp.html#module-sttp"><code class="xref">sttp</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.html#module-sttp.config"><code class="xref">sttp.config</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.data.html#module-sttp.data"><code class="xref">sttp.data</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.data.html#module-sttp.data.callbackerrorlistener"><code class="xref">sttp.data.callbackerrorlistener</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.data.html#module-sttp.data.columnexpression"><code class="xref">sttp.data.columnexpression</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.data.html#module-sttp.data.constants"><code class="xref">sttp.data.constants</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.data.html#module-sttp.data.datacolumn"><code class="xref">sttp.data.datacolumn</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.data.html#module-sttp.data.datarow"><code class="xref">sttp.data.datarow</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.data.html#module-sttp.data.dataset"><code class="xref">sttp.data.dataset</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.data.html#module-sttp.data.datatable"><code class="xref">sttp.data.datatable</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.data.html#module-sttp.data.datatype"><code class="xref">sttp.data.datatype</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.data.html#module-sttp.data.errors"><code class="xref">sttp.data.errors</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.data.html#module-sttp.data.expression"><code class="xref">sttp.data.expression</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.data.html#module-sttp.data.expressiontree"><code class="xref">sttp.data.expressiontree</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.data.html#module-sttp.data.filterexpressionparser"><code class="xref">sttp.data.filterexpressionparser</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.data.html#module-sttp.data.functionexpression"><code class="xref">sttp.data.functionexpression</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.data.html#module-sttp.data.inlistexpression"><code class="xref">sttp.data.inlistexpression</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.data.html#module-sttp.data.operatorexpression"><code class="xref">sttp.data.operatorexpression</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.data.html#module-sttp.data.orderbyterm"><code class="xref">sttp.data.orderbyterm</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.data.html#module-sttp.data.tableidfields"><code class="xref">sttp.data.tableidfields</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.data.html#module-sttp.data.unaryexpression"><code class="xref">sttp.data.unaryexpression</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.data.html#module-sttp.data.valueexpression"><code class="xref">sttp.data.valueexpression</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.metadata.html#module-sttp.metadata"><code class="xref">sttp.metadata</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.metadata.html#module-sttp.metadata.cache"><code class="xref">sttp.metadata.cache</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.metadata.record.html#module-sttp.metadata.record"><code class="xref">sttp.metadata.record</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.metadata.record.html#module-sttp.metadata.record.device"><code class="xref">sttp.metadata.record.device</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.metadata.record.html#module-sttp.metadata.record.measurement"><code class="xref">sttp.metadata.record.measurement</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.metadata.record.html#module-sttp.metadata.record.phasor"><code class="xref">sttp.metadata.record.phasor</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.html#module-sttp.reader"><code class="xref">sttp.reader</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.html#module-sttp.settings"><code class="xref">sttp.settings</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.html#module-sttp.subscriber"><code class="xref">sttp.subscriber</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.html#module-sttp.ticks"><code class="xref">sttp.ticks</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.transport.html#module-sttp.transport"><code class="xref">sttp.transport</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.transport.html#module-sttp.transport.bufferblock"><code class="xref">sttp.transport.bufferblock</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.transport.html#module-sttp.transport.compactmeasurement"><code class="xref">sttp.transport.compactmeasurement</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.transport.html#module-sttp.transport.constants"><code class="xref">sttp.transport.constants</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.transport.html#module-sttp.transport.datasubscriber"><code class="xref">sttp.transport.datasubscriber</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.transport.html#module-sttp.transport.measurement"><code class="xref">sttp.transport.measurement</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.transport.html#module-sttp.transport.signalindexcache"><code class="xref">sttp.transport.signalindexcache</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.transport.html#module-sttp.transport.signalkind"><code class="xref">sttp.transport.signalkind</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.transport.html#module-sttp.transport.subscriberconnector"><code class="xref">sttp.transport.subscriberconnector</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.transport.html#module-sttp.transport.subscriptioninfo"><code class="xref">sttp.transport.subscriptioninfo</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.transport.tssc.html#module-sttp.transport.tssc"><code class="xref">sttp.transport.tssc</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.transport.tssc.html#module-sttp.transport.tssc.decoder"><code class="xref">sttp.transport.tssc.decoder</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.transport.tssc.html#module-sttp.transport.tssc.pointmetadata"><code class="xref">sttp.transport.tssc.pointmetadata</code></a></td><td>
       <em></em></td></tr>
     <tr class="cg-2">
       <td></td>
       <td>&#160;&#160;&#160;
       <a href="sttp.html#module-sttp.version"><code class="xref">sttp.version</code></a></td><td>
       <em></em></td></tr>
   </table>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
  
 
<hr />
<p>
    <a href="https://github.com/sttp/pyapi">GitHub Home</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://pypi.org/project/sttpapi/" target="_blank">PyPi Package: sttpapi</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://sttp.github.io/documentation/" target="_blank">General STTP Documentation</a>
</p>
<br /><br />
Copyright © 2022, <a href="https://gridprotectionalliance.org/" target="_blank">Grid&nbsp;Protection&nbsp;Alliance</a>
<a href="https://github.com/sttp/pyapi">
    <img align="right" style="margin-top: -50px"
        src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/LockPython_64High.png">
</a>


</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>