Search.setIndex({"alltitles": {"Indices and tables": [[1, "indices-and-tables"]], "Module contents": [[0, "module-gsf"], [3, "module-sttp"], [4, "module-sttp.data"], [5, "module-sttp.metadata"], [6, "module-sttp.metadata.record"], [7, "module-sttp.transport"], [8, "module-sttp.transport.tssc"]], "Python STTP API documentation": [[1, null]], "Submodules": [[0, "submodules"], [3, "submodules"], [4, "submodules"], [5, "submodules"], [6, "submodules"], [7, "submodules"], [8, "submodules"]], "Subpackages": [[3, "subpackages"], [5, "subpackages"], [7, "subpackages"]], "gsf package": [[0, null]], "gsf.binarystream module": [[0, "module-gsf.binarystream"]], "gsf.encoding7bit module": [[0, "module-gsf.encoding7bit"]], "gsf.endianorder module": [[0, "module-gsf.endianorder"]], "gsf.streamencoder module": [[0, "module-gsf.streamencoder"]], "src": [[2, null]], "sttp package": [[3, null]], "sttp.config module": [[3, "module-sttp.config"]], "sttp.data package": [[4, null]], "sttp.data.callbackerrorlistener module": [[4, "module-sttp.data.callbackerrorlistener"]], "sttp.data.columnexpression module": [[4, "module-sttp.data.columnexpression"]], "sttp.data.constants module": [[4, "module-sttp.data.constants"]], "sttp.data.datacolumn module": [[4, "module-sttp.data.datacolumn"]], "sttp.data.datarow module": [[4, "module-sttp.data.datarow"]], "sttp.data.dataset module": [[4, "module-sttp.data.dataset"]], "sttp.data.datatable module": [[4, "module-sttp.data.datatable"]], "sttp.data.datatype module": [[4, "module-sttp.data.datatype"]], "sttp.data.errors module": [[4, "module-sttp.data.errors"]], "sttp.data.expression module": [[4, "module-sttp.data.expression"]], "sttp.data.expressiontree module": [[4, "module-sttp.data.expressiontree"]], "sttp.data.filterexpressionparser module": [[4, "module-sttp.data.filterexpressionparser"]], "sttp.data.functionexpression module": [[4, "module-sttp.data.functionexpression"]], "sttp.data.inlistexpression module": [[4, "module-sttp.data.inlistexpression"]], "sttp.data.operatorexpression module": [[4, "module-sttp.data.operatorexpression"]], "sttp.data.orderbyterm module": [[4, "module-sttp.data.orderbyterm"]], "sttp.data.tableidfields module": [[4, "module-sttp.data.tableidfields"]], "sttp.data.unaryexpression module": [[4, "module-sttp.data.unaryexpression"]], "sttp.data.valueexpression module": [[4, "module-sttp.data.valueexpression"]], "sttp.metadata package": [[5, null]], "sttp.metadata.cache module": [[5, "module-sttp.metadata.cache"]], "sttp.metadata.record package": [[6, null]], "sttp.metadata.record.device module": [[6, "module-sttp.metadata.record.device"]], "sttp.metadata.record.measurement module": [[6, "module-sttp.metadata.record.measurement"]], "sttp.metadata.record.phasor module": [[6, "module-sttp.metadata.record.phasor"]], "sttp.reader module": [[3, "module-sttp.reader"]], "sttp.settings module": [[3, "module-sttp.settings"]], "sttp.subscriber module": [[3, "module-sttp.subscriber"]], "sttp.ticks module": [[3, "module-sttp.ticks"]], "sttp.transport package": [[7, null]], "sttp.transport.bufferblock module": [[7, "module-sttp.transport.bufferblock"]], "sttp.transport.compactmeasurement module": [[7, "module-sttp.transport.compactmeasurement"]], "sttp.transport.constants module": [[7, "module-sttp.transport.constants"]], "sttp.transport.datasubscriber module": [[7, "module-sttp.transport.datasubscriber"]], "sttp.transport.measurement module": [[7, "module-sttp.transport.measurement"]], "sttp.transport.signalindexcache module": [[7, "module-sttp.transport.signalindexcache"]], "sttp.transport.signalkind module": [[7, "module-sttp.transport.signalkind"]], "sttp.transport.subscriberconnector module": [[7, "module-sttp.transport.subscriberconnector"]], "sttp.transport.subscriptioninfo module": [[7, "module-sttp.transport.subscriptioninfo"]], "sttp.transport.tssc package": [[8, null]], "sttp.transport.tssc.decoder module": [[8, "module-sttp.transport.tssc.decoder"]], "sttp.transport.tssc.pointmetadata module": [[8, "module-sttp.transport.tssc.pointmetadata"]], "sttp.version module": [[3, "module-sttp.version"]]}, "docnames": ["gsf", "index", "modules", "sttp", "sttp.data", "sttp.metadata", "sttp.metadata.record", "sttp.transport", "sttp.transport.tssc"], "envversion": {"sphinx": 64, "sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.viewcode": 1}, "filenames": ["gsf.rst", "index.rst", "modules.rst", "sttp.rst", "sttp.data.rst", "sttp.metadata.rst", "sttp.metadata.record.rst", "sttp.transport.rst", "sttp.transport.tssc.rst"], "indexentries": {"abs (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.ABS", false]], "accessid (sttp.metadata.record.device.devicerecord property)": [[6, "sttp.metadata.record.device.DeviceRecord.accessid", false]], "acronym (sttp.metadata.record.device.devicerecord property)": [[6, "sttp.metadata.record.device.DeviceRecord.acronym", false]], "acronym() (sttp.transport.signalkind.signalkindenum static method)": [[7, "sttp.transport.signalkind.SignalKindEnum.acronym", false]], "activesignalindexcache (sttp.subscriber.subscriber property)": [[3, "sttp.subscriber.Subscriber.activesignalindexcache", false]], "activesignalindexcache (sttp.transport.datasubscriber.datasubscriber property)": [[7, "sttp.transport.datasubscriber.DataSubscriber.activesignalindexcache", false]], "add (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.ADD", false]], "add_column() (sttp.data.datatable.datatable method)": [[4, "sttp.data.datatable.DataTable.add_column", false]], "add_measurement() (sttp.metadata.cache.metadatacache method)": [[5, "sttp.metadata.cache.MetadataCache.add_measurement", false]], "add_row() (sttp.data.datatable.datatable method)": [[4, "sttp.data.datatable.DataTable.add_row", false]], "add_table() (sttp.data.dataset.dataset method)": [[4, "sttp.data.dataset.DataSet.add_table", false]], "adder (sttp.metadata.record.measurement.measurementrecord property)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.adder", false]], "adjustedvalue() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.adjustedvalue", false]], "adjustedvalue() (sttp.transport.datasubscriber.datasubscriber method)": [[7, "sttp.transport.datasubscriber.DataSubscriber.adjustedvalue", false]], "alarm (sttp.transport.signalkind.signalkind attribute)": [[7, "sttp.transport.signalkind.SignalKind.ALARM", false]], "alarmhigh (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.ALARMHIGH", false]], "alarmlow (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.ALARMLOW", false]], "alog (sttp.metadata.record.measurement.signaltype attribute)": [[6, "sttp.metadata.record.measurement.SignalType.ALOG", false]], "alrm (sttp.metadata.record.measurement.signaltype attribute)": [[6, "sttp.metadata.record.measurement.SignalType.ALRM", false]], "analog (sttp.transport.signalkind.signalkind attribute)": [[7, "sttp.transport.signalkind.SignalKind.ANALOG", false]], "and (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.AND", false]], "angle (sttp.metadata.record.phasor.compositephasormeasurement attribute)": [[6, "sttp.metadata.record.phasor.CompositePhasorMeasurement.ANGLE", false]], "angle (sttp.transport.signalkind.signalkind attribute)": [[7, "sttp.transport.signalkind.SignalKind.ANGLE", false]], "angle_measurement (sttp.metadata.record.phasor.phasorrecord property)": [[6, "sttp.metadata.record.phasor.PhasorRecord.angle_measurement", false]], "applyto_bool() (sttp.data.unaryexpression.unaryexpression method)": [[4, "sttp.data.unaryexpression.UnaryExpression.applyto_bool", false]], "applyto_decimal() (sttp.data.unaryexpression.unaryexpression method)": [[4, "sttp.data.unaryexpression.UnaryExpression.applyto_decimal", false]], "applyto_double() (sttp.data.unaryexpression.unaryexpression method)": [[4, "sttp.data.unaryexpression.UnaryExpression.applyto_double", false]], "applyto_int32() (sttp.data.unaryexpression.unaryexpression method)": [[4, "sttp.data.unaryexpression.UnaryExpression.applyto_int32", false]], "applyto_int64() (sttp.data.unaryexpression.unaryexpression method)": [[4, "sttp.data.unaryexpression.UnaryExpression.applyto_int64", false]], "arguments (sttp.data.functionexpression.functionexpression property)": [[4, "sttp.data.functionexpression.FunctionExpression.arguments", false]], "arguments (sttp.data.inlistexpression.inlistexpression property)": [[4, "sttp.data.inlistexpression.InListExpression.arguments", false]], "ascending (sttp.data.orderbyterm.orderbyterm attribute)": [[4, "sttp.data.orderbyterm.OrderByTerm.ascending", false]], "autoreconnect (sttp.config.config attribute)": [[3, "sttp.config.Config.autoreconnect", false]], "autoreconnect (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.AUTORECONNECT", false]], "autoreconnect (sttp.transport.subscriberconnector.subscriberconnector attribute)": [[7, "sttp.transport.subscriberconnector.SubscriberConnector.autoreconnect", false]], "autoreconnect_callback (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.autoreconnect_callback", false]], "autorequestmetadata (sttp.config.config attribute)": [[3, "sttp.config.Config.autorequestmetadata", false]], "autorequestmetadata (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.AUTOREQUESTMETADATA", false]], "autosubscribe (sttp.config.config attribute)": [[3, "sttp.config.Config.autosubscribe", false]], "autosubscribe (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.AUTOSUBSCRIBE", false]], "baddata (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.BADDATA", false]], "badtime (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.BADTIME", false]], "basekv (sttp.metadata.record.phasor.phasorrecord property)": [[6, "sttp.metadata.record.phasor.PhasorRecord.basekv", false]], "basetimeoffset (sttp.transport.compactmeasurement.compactstateflags attribute)": [[7, "sttp.transport.compactmeasurement.CompactStateFlags.BASETIMEOFFSET", false]], "bigendian (class in gsf.endianorder)": [[0, "gsf.endianorder.BigEndian", false]], "binarystream (class in gsf.binarystream)": [[0, "gsf.binarystream.BinaryStream", false]], "bitshiftleft (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.BITSHIFTLEFT", false]], "bitshiftright (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.BITSHIFTRIGHT", false]], "bitwiseand (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.BITWISEAND", false]], "bitwiseor (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.BITWISEOR", false]], "bitwisexor (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.BITWISEXOR", false]], "boolean (sttp.data.constants.expressionvaluetype attribute)": [[4, "sttp.data.constants.ExpressionValueType.BOOLEAN", false]], "boolean (sttp.data.datatype.datatype attribute)": [[4, "sttp.data.datatype.DataType.BOOLEAN", false]], "booleanvalue() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.booleanvalue", false]], "booleanvalue() (sttp.data.valueexpression.valueexpression method)": [[4, "sttp.data.valueexpression.ValueExpression.booleanvalue", false]], "booleanvalue_byname() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.booleanvalue_byname", false]], "buffer (sttp.transport.bufferblock.bufferblock property)": [[7, "sttp.transport.bufferblock.BufferBlock.buffer", false]], "bufferblock (class in sttp.transport.bufferblock)": [[7, "sttp.transport.bufferblock.BufferBlock", false]], "bufferblock (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.BUFFERBLOCK", false]], "bytesize (class in gsf)": [[0, "gsf.ByteSize", false]], "cacheindex (sttp.transport.constants.datapacketflags attribute)": [[7, "sttp.transport.constants.DataPacketFlags.CACHEINDEX", false]], "calc (sttp.metadata.record.measurement.signaltype attribute)": [[6, "sttp.metadata.record.measurement.SignalType.CALC", false]], "calculatedvalue (sttp.transport.compactmeasurement.compactstateflags attribute)": [[7, "sttp.transport.compactmeasurement.CompactStateFlags.CALCULATEDVALUE", false]], "calculatedvalue (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.CALCULATEDVALUE", false]], "calculation (sttp.transport.signalkind.signalkind attribute)": [[7, "sttp.transport.signalkind.SignalKind.CALCULATION", false]], "calculationerror (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.CALCULATIONERROR", false]], "calculationwarning (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.CALCULATIONWARNING", false]], "callbackerrorlistener (class in sttp.data.callbackerrorlistener)": [[4, "sttp.data.callbackerrorlistener.CallbackErrorListener", false]], "cancel() (sttp.transport.subscriberconnector.subscriberconnector method)": [[7, "sttp.transport.subscriberconnector.SubscriberConnector.cancel", false]], "canceled (sttp.transport.constants.connectstatus attribute)": [[7, "sttp.transport.constants.ConnectStatus.CANCELED", false]], "ceiling (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.CEILING", false]], "cipherindex (sttp.transport.constants.datapacketflags attribute)": [[7, "sttp.transport.constants.DataPacketFlags.CIPHERINDEX", false]], "clear_columns() (sttp.data.datatable.datatable method)": [[4, "sttp.data.datatable.DataTable.clear_columns", false]], "clear_rows() (sttp.data.datatable.datatable method)": [[4, "sttp.data.datatable.DataTable.clear_rows", false]], "clear_tables() (sttp.data.dataset.dataset method)": [[4, "sttp.data.dataset.DataSet.clear_tables", false]], "clone_column() (sttp.data.datatable.datatable method)": [[4, "sttp.data.datatable.DataTable.clone_column", false]], "clone_row() (sttp.data.datatable.datatable method)": [[4, "sttp.data.datatable.DataTable.clone_row", false]], "coalesce (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.COALESCE", false]], "codewords (class in sttp.transport.tssc.pointmetadata)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords", false]], "column (sttp.data.constants.expressiontype attribute)": [[4, "sttp.data.constants.ExpressionType.COLUMN", false]], "column (sttp.data.orderbyterm.orderbyterm attribute)": [[4, "sttp.data.orderbyterm.OrderByTerm.column", false]], "column() (sttp.data.datatable.datatable method)": [[4, "sttp.data.datatable.DataTable.column", false]], "column_byname() (sttp.data.datatable.datatable method)": [[4, "sttp.data.datatable.DataTable.column_byname", false]], "columncount (sttp.data.datatable.datatable property)": [[4, "sttp.data.datatable.DataTable.columncount", false]], "columnexpression (class in sttp.data.columnexpression)": [[4, "sttp.data.columnexpression.ColumnExpression", false]], "columnindex() (sttp.data.datatable.datatable method)": [[4, "sttp.data.datatable.DataTable.columnindex", false]], "columnvalue_as_string() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.columnvalue_as_string", false]], "compact (sttp.transport.constants.datapacketflags attribute)": [[7, "sttp.transport.constants.DataPacketFlags.COMPACT", false]], "compactmeasurement (class in sttp.transport.compactmeasurement)": [[7, "sttp.transport.compactmeasurement.CompactMeasurement", false]], "compactstateflags (class in sttp.transport.compactmeasurement)": [[7, "sttp.transport.compactmeasurement.CompactStateFlags", false]], "companyacronym (sttp.metadata.record.device.devicerecord property)": [[6, "sttp.metadata.record.device.DeviceRecord.companyacronym", false]], "compare_datarowcolumns() (sttp.data.datarow.datarow static method)": [[4, "sttp.data.datarow.DataRow.compare_datarowcolumns", false]], "comparisonalarm (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.COMPARISONALARM", false]], "compositephasormeasurement (class in sttp.metadata.record.phasor)": [[6, "sttp.metadata.record.phasor.CompositePhasorMeasurement", false]], "compress_metadata (sttp.config.config attribute)": [[3, "sttp.config.Config.compress_metadata", false]], "compress_metadata (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.COMPRESS_METADATA", false]], "compress_metadata (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.compress_metadata", false]], "compress_payloaddata (sttp.config.config attribute)": [[3, "sttp.config.Config.compress_payloaddata", false]], "compress_payloaddata (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.COMPRESS_PAYLOADDATA", false]], "compress_payloaddata (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.compress_payloaddata", false]], "compress_signalindexcache (sttp.config.config attribute)": [[3, "sttp.config.Config.compress_signalindexcache", false]], "compress_signalindexcache (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.COMPRESS_SIGNALINDEXCACHE", false]], "compress_signalindexcache (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.compress_signalindexcache", false]], "compressed (sttp.transport.constants.datapacketflags attribute)": [[7, "sttp.transport.constants.DataPacketFlags.COMPRESSED", false]], "compressionmodes (class in sttp.transport.constants)": [[7, "sttp.transport.constants.CompressionModes", false]], "compressmetadata (sttp.transport.constants.operationalmodes attribute)": [[7, "sttp.transport.constants.OperationalModes.COMPRESSMETADATA", false]], "compresspayloaddata (sttp.transport.constants.operationalmodes attribute)": [[7, "sttp.transport.constants.OperationalModes.COMPRESSPAYLOADDATA", false]], "compresssignalindexcache (sttp.transport.constants.operationalmodes attribute)": [[7, "sttp.transport.constants.OperationalModes.COMPRESSSIGNALINDEXCACHE", false]], "computed (sttp.data.datacolumn.datacolumn property)": [[4, "sttp.data.datacolumn.DataColumn.computed", false]], "config (class in sttp.config)": [[3, "sttp.config.Config", false]], "configurationchanged (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.CONFIGURATIONCHANGED", false]], "configurationchanged_callback (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.configurationchanged_callback", false]], "confirmbufferblock (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.CONFIRMBUFFERBLOCK", false]], "confirmnotification (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.CONFIRMNOTIFICATION", false]], "confirmupdatebasetimes (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.CONFIRMUPDATEBASETIMES", false]], "confirmupdatecipherkeys (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.CONFIRMUPDATECIPHERKEYS", false]], "confirmupdatesignalindexcache (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.CONFIRMUPDATESIGNALINDEXCACHE", false]], "connect (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.CONNECT", false]], "connect() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.connect", false]], "connect() (sttp.transport.datasubscriber.datasubscriber method)": [[7, "sttp.transport.datasubscriber.DataSubscriber.connect", false]], "connect() (sttp.transport.subscriberconnector.subscriberconnector method)": [[7, "sttp.transport.subscriberconnector.SubscriberConnector.connect", false]], "connected (sttp.subscriber.subscriber property)": [[3, "sttp.subscriber.Subscriber.connected", false]], "connected (sttp.transport.datasubscriber.datasubscriber property)": [[7, "sttp.transport.datasubscriber.DataSubscriber.connected", false]], "connectionterminated_callback (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.connectionterminated_callback", false]], "connector (sttp.transport.datasubscriber.datasubscriber property)": [[7, "sttp.transport.datasubscriber.DataSubscriber.connector", false]], "connectstatus (class in sttp.transport.constants)": [[7, "sttp.transport.constants.ConnectStatus", false]], "constraintparameters (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.constraintparameters", false]], "constraintparameters (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.CONSTRAINTPARAMETERS", false]], "constraintparameters (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.constraintparameters", false]], "contains (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.CONTAINS", false]], "contains() (sttp.transport.signalindexcache.signalindexcache method)": [[7, "sttp.transport.signalindexcache.SignalIndexCache.contains", false]], "convert (class in gsf)": [[0, "gsf.Convert", false]], "convert (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.CONVERT", false]], "convert() (sttp.data.valueexpression.valueexpression method)": [[4, "sttp.data.valueexpression.ValueExpression.convert", false]], "count (sttp.transport.signalindexcache.signalindexcache property)": [[7, "sttp.transport.signalindexcache.SignalIndexCache.count", false]], "create_column() (sttp.data.datatable.datatable method)": [[4, "sttp.data.datatable.DataTable.create_column", false]], "create_row() (sttp.data.datatable.datatable method)": [[4, "sttp.data.datatable.DataTable.create_row", false]], "create_table() (sttp.data.dataset.dataset method)": [[4, "sttp.data.dataset.DataSet.create_table", false]], "data_starttime_callback (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.data_starttime_callback", false]], "datachannel_interface (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.DATACHANNEL_INTERFACE", false]], "datachannel_interface (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.datachannel_interface", false]], "datachannel_localport (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.DATACHANNEL_LOCALPORT", false]], "datachannel_localport (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.datachannel_localport", false]], "datacolumn (class in sttp.data.datacolumn)": [[4, "sttp.data.datacolumn.DataColumn", false]], "datacolumn (sttp.data.columnexpression.columnexpression property)": [[4, "sttp.data.columnexpression.ColumnExpression.datacolumn", false]], "datapacket (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.DATAPACKET", false]], "datapacketflags (class in sttp.transport.constants)": [[7, "sttp.transport.constants.DataPacketFlags", false]], "dataquality (sttp.transport.compactmeasurement.compactstateflags attribute)": [[7, "sttp.transport.compactmeasurement.CompactStateFlags.DATAQUALITY", false]], "datarange (sttp.transport.compactmeasurement.compactstateflags attribute)": [[7, "sttp.transport.compactmeasurement.CompactStateFlags.DATARANGE", false]], "datarow (class in sttp.data.datarow)": [[4, "sttp.data.datarow.DataRow", false]], "dataset (class in sttp.data.dataset)": [[4, "sttp.data.dataset.DataSet", false]], "dataset (sttp.data.filterexpressionparser.filterexpressionparser attribute)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.dataset", false]], "datastarttime (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.DATASTARTTIME", false]], "datasubscriber (class in sttp.transport.datasubscriber)": [[7, "sttp.transport.datasubscriber.DataSubscriber", false]], "datatable (class in sttp.data.datatable)": [[4, "sttp.data.datatable.DataTable", false]], "datatype (class in sttp.data.datatype)": [[4, "sttp.data.datatype.DataType", false]], "datatype (sttp.data.datacolumn.datacolumn property)": [[4, "sttp.data.datacolumn.DataColumn.datatype", false]], "dateadd (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.DATEADD", false]], "datediff (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.DATEDIFF", false]], "datepart (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.DATEPART", false]], "datetime (gsf.empty attribute)": [[0, "gsf.Empty.DATETIME", false]], "datetime (sttp.data.constants.expressionvaluetype attribute)": [[4, "sttp.data.constants.ExpressionValueType.DATETIME", false]], "datetime (sttp.data.datatype.datatype attribute)": [[4, "sttp.data.datatype.DataType.DATETIME", false]], "datetime (sttp.transport.measurement.measurement property)": [[7, "sttp.transport.measurement.Measurement.datetime", false]], "datetimevalue() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.datetimevalue", false]], "datetimevalue() (sttp.data.valueexpression.valueexpression method)": [[4, "sttp.data.valueexpression.ValueExpression.datetimevalue", false]], "datetimevalue_byname() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.datetimevalue_byname", false]], "day (sttp.data.constants.timeinterval attribute)": [[4, "sttp.data.constants.TimeInterval.DAY", false]], "dayofyear (sttp.data.constants.timeinterval attribute)": [[4, "sttp.data.constants.TimeInterval.DAYOFYEAR", false]], "decimal (gsf.empty attribute)": [[0, "gsf.Empty.DECIMAL", false]], "decimal (sttp.data.constants.expressionvaluetype attribute)": [[4, "sttp.data.constants.ExpressionValueType.DECIMAL", false]], "decimal (sttp.data.datatype.datatype attribute)": [[4, "sttp.data.datatype.DataType.DECIMAL", false]], "decimalvalue() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.decimalvalue", false]], "decimalvalue() (sttp.data.valueexpression.valueexpression method)": [[4, "sttp.data.valueexpression.ValueExpression.decimalvalue", false]], "decimalvalue_byname() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.decimalvalue_byname", false]], "decode() (sttp.transport.compactmeasurement.compactmeasurement method)": [[7, "sttp.transport.compactmeasurement.CompactMeasurement.decode", false]], "decode() (sttp.transport.signalindexcache.signalindexcache method)": [[7, "sttp.transport.signalindexcache.SignalIndexCache.decode", false]], "decoder (class in sttp.transport.tssc.decoder)": [[8, "sttp.transport.tssc.decoder.Decoder", false]], "decodestr() (sttp.transport.datasubscriber.datasubscriber method)": [[7, "sttp.transport.datasubscriber.DataSubscriber.decodestr", false]], "default_adder (sttp.metadata.record.measurement.measurementrecord attribute)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_ADDER", false]], "default_autoreconnect (sttp.config.config attribute)": [[3, "sttp.config.Config.DEFAULT_AUTORECONNECT", false]], "default_autoreconnect (sttp.transport.subscriberconnector.subscriberconnector attribute)": [[7, "sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_AUTORECONNECT", false]], "default_autorequestmetadata (sttp.config.config attribute)": [[3, "sttp.config.Config.DEFAULT_AUTOREQUESTMETADATA", false]], "default_autosubscribe (sttp.config.config attribute)": [[3, "sttp.config.Config.DEFAULT_AUTOSUBSCRIBE", false]], "default_basekv (sttp.metadata.record.phasor.phasorrecord attribute)": [[6, "sttp.metadata.record.phasor.PhasorRecord.DEFAULT_BASEKV", false]], "default_buffer (sttp.transport.bufferblock.bufferblock attribute)": [[7, "sttp.transport.bufferblock.BufferBlock.DEFAULT_BUFFER", false]], "default_byteorder (gsf.streamencoder.streamencoder property)": [[0, "gsf.streamencoder.StreamEncoder.default_byteorder", false]], "default_companyname (sttp.metadata.record.device.devicerecord attribute)": [[6, "sttp.metadata.record.device.DeviceRecord.DEFAULT_COMPANYNAME", false]], "default_compress_metadata (sttp.config.config attribute)": [[3, "sttp.config.Config.DEFAULT_COMPRESS_METADATA", false]], "default_compress_metadata (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.DEFAULT_COMPRESS_METADATA", false]], "default_compress_payloaddata (sttp.config.config attribute)": [[3, "sttp.config.Config.DEFAULT_COMPRESS_PAYLOADDATA", false]], "default_compress_payloaddata (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.DEFAULT_COMPRESS_PAYLOADDATA", false]], "default_compress_signalindexcache (sttp.config.config attribute)": [[3, "sttp.config.Config.DEFAULT_COMPRESS_SIGNALINDEXCACHE", false]], "default_compress_signalindexcache (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.DEFAULT_COMPRESS_SIGNALINDEXCACHE", false]], "default_connectionestablished_receiver() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.default_connectionestablished_receiver", false]], "default_connectionterminated_receiver() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.default_connectionterminated_receiver", false]], "default_constraintparameters (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.DEFAULT_CONSTRAINTPARAMETERS", false]], "default_constraintparameters (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_CONSTRAINTPARAMETERS", false]], "default_datachannel_interface (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_DATACHANNEL_INTERFACE", false]], "default_datachannel_localport (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_DATACHANNEL_LOCALPORT", false]], "default_datatype() (in module sttp.data.datatype)": [[4, "sttp.data.datatype.default_datatype", false]], "default_description (sttp.metadata.record.measurement.measurementrecord attribute)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_DESCRIPTION", false]], "default_deviceacronym (sttp.metadata.record.measurement.measurementrecord attribute)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_DEVICEACRONYM", false]], "default_enable_time_reasonability_check (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.DEFAULT_ENABLE_TIME_REASONABILITY_CHECK", false]], "default_enable_time_reasonability_check (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_ENABLE_TIME_REASONABILITY_CHECK", false]], "default_errormessage_callback() (sttp.transport.subscriberconnector.subscriberconnector method)": [[7, "sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_ERRORMESSAGE_CALLBACK", false]], "default_errormessage_logger() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.default_errormessage_logger", false]], "default_extra_connectionstring_parameters (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.DEFAULT_EXTRA_CONNECTIONSTRING_PARAMETERS", false]], "default_extra_connectionstring_parameters (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_EXTRA_CONNECTIONSTRING_PARAMETERS", false]], "default_filterexpression (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_FILTEREXPRESSION", false]], "default_flags (sttp.transport.measurement.measurement attribute)": [[7, "sttp.transport.measurement.Measurement.DEFAULT_FLAGS", false]], "default_framespersecond (sttp.metadata.record.device.devicerecord attribute)": [[6, "sttp.metadata.record.device.DeviceRecord.DEFAULT_FRAMESPERSECOND", false]], "default_hostname (sttp.transport.subscriberconnector.subscriberconnector attribute)": [[7, "sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_HOSTNAME", false]], "default_id (sttp.metadata.record.measurement.measurementrecord attribute)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_ID", false]], "default_includetime (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.DEFAULT_INCLUDETIME", false]], "default_includetime (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_INCLUDETIME", false]], "default_lagtime (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.DEFAULT_LAGTIME", false]], "default_lagtime (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_LAGTIME", false]], "default_latitude (sttp.metadata.record.device.devicerecord attribute)": [[6, "sttp.metadata.record.device.DeviceRecord.DEFAULT_LATITUDE", false]], "default_leadtime (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.DEFAULT_LEADTIME", false]], "default_leadtime (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_LEADTIME", false]], "default_longitude (sttp.metadata.record.device.devicerecord attribute)": [[6, "sttp.metadata.record.device.DeviceRecord.DEFAULT_LONGITUDE", false]], "default_maxretries (sttp.config.config attribute)": [[3, "sttp.config.Config.DEFAULT_MAXRETRIES", false]], "default_maxretries (sttp.transport.subscriberconnector.subscriberconnector attribute)": [[7, "sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_MAXRETRIES", false]], "default_maxretryinterval (sttp.config.config attribute)": [[3, "sttp.config.Config.DEFAULT_MAXRETRYINTERVAL", false]], "default_maxretryinterval (sttp.transport.subscriberconnector.subscriberconnector attribute)": [[7, "sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_MAXRETRYINTERVAL", false]], "default_metadatafilters (sttp.config.config attribute)": [[3, "sttp.config.Config.DEFAULT_METADATAFILTERS", false]], "default_multiplier (sttp.metadata.record.measurement.measurementrecord attribute)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_MULTIPLIER", false]], "default_name (sttp.data.dataset.dataset attribute)": [[4, "sttp.data.dataset.DataSet.DEFAULT_NAME", false]], "default_parentacronym (sttp.metadata.record.device.devicerecord attribute)": [[6, "sttp.metadata.record.device.DeviceRecord.DEFAULT_PARENTACRONYM", false]], "default_phase (sttp.metadata.record.phasor.phasorrecord attribute)": [[6, "sttp.metadata.record.phasor.PhasorRecord.DEFAULT_PHASE", false]], "default_pointtag (sttp.metadata.record.measurement.measurementrecord attribute)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_POINTTAG", false]], "default_port (sttp.transport.subscriberconnector.subscriberconnector attribute)": [[7, "sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_PORT", false]], "default_processinginterval (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.DEFAULT_PROCESSINGINTERVAL", false]], "default_processinginterval (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_PROCESSINGINTERVAL", false]], "default_protocolname (sttp.metadata.record.device.devicerecord attribute)": [[6, "sttp.metadata.record.device.DeviceRecord.DEFAULT_PROTOCOLNAME", false]], "default_publishinterval (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.DEFAULT_PUBLISHINTERVAL", false]], "default_publishinterval (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_PUBLISHINTERVAL", false]], "default_reconnect_callback() (sttp.transport.subscriberconnector.subscriberconnector method)": [[7, "sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_RECONNECT_CALLBACK", false]], "default_request_nanvaluefilter (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.DEFAULT_REQUEST_NANVALUEFILTER", false]], "default_request_nanvaluefilter (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_REQUEST_NANVALUEFILTER", false]], "default_retryinterval (sttp.config.config attribute)": [[3, "sttp.config.Config.DEFAULT_RETRYINTERVAL", false]], "default_retryinterval (sttp.transport.subscriberconnector.subscriberconnector attribute)": [[7, "sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_RETRYINTERVAL", false]], "default_signalid (sttp.metadata.record.measurement.measurementrecord attribute)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_SIGNALID", false]], "default_signalid (sttp.transport.bufferblock.bufferblock attribute)": [[7, "sttp.transport.bufferblock.BufferBlock.DEFAULT_SIGNALID", false]], "default_signalid (sttp.transport.measurement.measurement attribute)": [[7, "sttp.transport.measurement.Measurement.DEFAULT_SIGNALID", false]], "default_signalreference (sttp.metadata.record.measurement.measurementrecord attribute)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_SIGNALREFERENCE", false]], "default_signaltypename (sttp.metadata.record.measurement.measurementrecord attribute)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_SIGNALTYPENAME", false]], "default_socket_timeout (sttp.config.config attribute)": [[3, "sttp.config.Config.DEFAULT_SOCKET_TIMEOUT", false]], "default_socket_timeout (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.DEFAULT_SOCKET_TIMEOUT", false]], "default_source (sttp.metadata.record.measurement.measurementrecord attribute)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_SOURCE", false]], "default_starttime (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.DEFAULT_STARTTIME", false]], "default_starttime (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_STARTTIME", false]], "default_statusmessage_logger() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.default_statusmessage_logger", false]], "default_stoptime (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.DEFAULT_STOPTIME", false]], "default_stoptime (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_STOPTIME", false]], "default_sttp_sourceinfo (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.DEFAULT_STTP_SOURCEINFO", false]], "default_sttp_updatedoninfo (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.DEFAULT_STTP_UPDATEDONINFO", false]], "default_sttp_versioninfo (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.DEFAULT_STTP_VERSIONINFO", false]], "default_tableidfields (in module sttp.data.tableidfields)": [[4, "sttp.data.tableidfields.DEFAULT_TABLEIDFIELDS", false]], "default_throttled (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.DEFAULT_THROTTLED", false]], "default_throttled (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_THROTTLED", false]], "default_timestamp (sttp.transport.measurement.measurement attribute)": [[7, "sttp.transport.measurement.Measurement.DEFAULT_TIMESTAMP", false]], "default_type (sttp.metadata.record.phasor.phasorrecord attribute)": [[6, "sttp.metadata.record.phasor.PhasorRecord.DEFAULT_TYPE", false]], "default_udpdatachannel (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_UDPDATACHANNEL", false]], "default_udpinterface (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.DEFAULT_UDPINTERFACE", false]], "default_udpport (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.DEFAULT_UDPPORT", false]], "default_updatedon (sttp.metadata.record.device.devicerecord attribute)": [[6, "sttp.metadata.record.device.DeviceRecord.DEFAULT_UPDATEDON", false]], "default_updatedon (sttp.metadata.record.measurement.measurementrecord attribute)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_UPDATEDON", false]], "default_updatedon (sttp.metadata.record.phasor.phasorrecord attribute)": [[6, "sttp.metadata.record.phasor.PhasorRecord.DEFAULT_UPDATEDON", false]], "default_use_localclock_as_realtime (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.DEFAULT_USE_LOCALCLOCK_AS_REALTIME", false]], "default_use_localclock_as_realtime (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_USE_LOCALCLOCK_AS_REALTIME", false]], "default_use_millisecondresolution (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.DEFAULT_USE_MILLISECONDRESOLUTION", false]], "default_use_millisecondresolution (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_USE_MILLISECONDRESOLUTION", false]], "default_value (sttp.transport.measurement.measurement attribute)": [[7, "sttp.transport.measurement.Measurement.DEFAULT_VALUE", false]], "default_vendoracronym (sttp.metadata.record.device.devicerecord attribute)": [[6, "sttp.metadata.record.device.DeviceRecord.DEFAULT_VENDORACRONYM", false]], "default_vendordevicename (sttp.metadata.record.device.devicerecord attribute)": [[6, "sttp.metadata.record.device.DeviceRecord.DEFAULT_VENDORDEVICENAME", false]], "default_version (sttp.config.config attribute)": [[3, "sttp.config.Config.DEFAULT_VERSION", false]], "default_version (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.DEFAULT_VERSION", false]], "defaults (class in sttp.transport.constants)": [[7, "sttp.transport.constants.Defaults", false]], "defineoperationalmodes (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.DEFINEOPERATIONALMODES", false]], "derive_arithmetic_operationvaluetype() (in module sttp.data.constants)": [[4, "sttp.data.constants.derive_arithmetic_operationvaluetype", false]], "derive_arithmetic_operationvaluetype_fromboolean() (in module sttp.data.constants)": [[4, "sttp.data.constants.derive_arithmetic_operationvaluetype_fromboolean", false]], "derive_arithmetic_operationvaluetype_fromdecimal() (in module sttp.data.constants)": [[4, "sttp.data.constants.derive_arithmetic_operationvaluetype_fromdecimal", false]], "derive_arithmetic_operationvaluetype_fromdouble() (in module sttp.data.constants)": [[4, "sttp.data.constants.derive_arithmetic_operationvaluetype_fromdouble", false]], "derive_arithmetic_operationvaluetype_fromint32() (in module sttp.data.constants)": [[4, "sttp.data.constants.derive_arithmetic_operationvaluetype_fromint32", false]], "derive_arithmetic_operationvaluetype_fromint64() (in module sttp.data.constants)": [[4, "sttp.data.constants.derive_arithmetic_operationvaluetype_fromint64", false]], "derive_boolean_operationvaluetype() (in module sttp.data.constants)": [[4, "sttp.data.constants.derive_boolean_operationvaluetype", false]], "derive_comparison_operationvaluetype() (in module sttp.data.constants)": [[4, "sttp.data.constants.derive_comparison_operationvaluetype", false]], "derive_comparison_operationvaluetype_fromboolean() (in module sttp.data.constants)": [[4, "sttp.data.constants.derive_comparison_operationvaluetype_fromboolean", false]], "derive_comparison_operationvaluetype_fromdatetime() (in module sttp.data.constants)": [[4, "sttp.data.constants.derive_comparison_operationvaluetype_fromdatetime", false]], "derive_comparison_operationvaluetype_fromdecimal() (in module sttp.data.constants)": [[4, "sttp.data.constants.derive_comparison_operationvaluetype_fromdecimal", false]], "derive_comparison_operationvaluetype_fromdouble() (in module sttp.data.constants)": [[4, "sttp.data.constants.derive_comparison_operationvaluetype_fromdouble", false]], "derive_comparison_operationvaluetype_fromguid() (in module sttp.data.constants)": [[4, "sttp.data.constants.derive_comparison_operationvaluetype_fromguid", false]], "derive_comparison_operationvaluetype_fromint32() (in module sttp.data.constants)": [[4, "sttp.data.constants.derive_comparison_operationvaluetype_fromint32", false]], "derive_comparison_operationvaluetype_fromint64() (in module sttp.data.constants)": [[4, "sttp.data.constants.derive_comparison_operationvaluetype_fromint64", false]], "derive_integer_operationvaluetype() (in module sttp.data.constants)": [[4, "sttp.data.constants.derive_integer_operationvaluetype", false]], "derive_integer_operationvaluetype_fromboolean() (in module sttp.data.constants)": [[4, "sttp.data.constants.derive_integer_operationvaluetype_fromboolean", false]], "derive_integer_operationvaluetype_fromint32() (in module sttp.data.constants)": [[4, "sttp.data.constants.derive_integer_operationvaluetype_fromint32", false]], "derive_integer_operationvaluetype_fromint64() (in module sttp.data.constants)": [[4, "sttp.data.constants.derive_integer_operationvaluetype_fromint64", false]], "derive_operationvaluetype() (in module sttp.data.constants)": [[4, "sttp.data.constants.derive_operationvaluetype", false]], "description (sttp.metadata.record.measurement.measurementrecord property)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.description", false]], "device (sttp.metadata.record.measurement.measurementrecord attribute)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.device", false]], "device (sttp.metadata.record.phasor.phasorrecord attribute)": [[6, "sttp.metadata.record.phasor.PhasorRecord.device", false]], "device_records (sttp.metadata.cache.metadatacache attribute)": [[5, "sttp.metadata.cache.MetadataCache.device_records", false]], "deviceacronym (sttp.metadata.record.measurement.measurementrecord property)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.deviceacronym", false]], "deviceacronym (sttp.metadata.record.phasor.phasorrecord property)": [[6, "sttp.metadata.record.phasor.PhasorRecord.deviceacronym", false]], "deviceacronym_device_map (sttp.metadata.cache.metadatacache attribute)": [[5, "sttp.metadata.cache.MetadataCache.deviceacronym_device_map", false]], "deviceid (sttp.metadata.record.device.devicerecord property)": [[6, "sttp.metadata.record.device.DeviceRecord.deviceid", false]], "deviceid_device_map (sttp.metadata.cache.metadatacache attribute)": [[5, "sttp.metadata.cache.MetadataCache.deviceid_device_map", false]], "devicerecord (class in sttp.metadata.record.device)": [[6, "sttp.metadata.record.device.DeviceRecord", false]], "dfdt (sttp.metadata.record.measurement.signaltype attribute)": [[6, "sttp.metadata.record.measurement.SignalType.DFDT", false]], "dfdt (sttp.transport.signalkind.signalkind attribute)": [[7, "sttp.transport.signalkind.SignalKind.DFDT", false]], "digi (sttp.metadata.record.measurement.signaltype attribute)": [[6, "sttp.metadata.record.measurement.SignalType.DIGI", false]], "digital (sttp.transport.signalkind.signalkind attribute)": [[7, "sttp.transport.signalkind.SignalKind.DIGITAL", false]], "discardedvalue (sttp.transport.compactmeasurement.compactstateflags attribute)": [[7, "sttp.transport.compactmeasurement.CompactStateFlags.DISCARDEDVALUE", false]], "discardedvalue (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.DISCARDEDVALUE", false]], "disconnect() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.disconnect", false]], "disconnect() (sttp.transport.datasubscriber.datasubscriber method)": [[7, "sttp.transport.datasubscriber.DataSubscriber.disconnect", false]], "dispose() (sttp.reader.measurementreader method)": [[3, "sttp.reader.MeasurementReader.dispose", false]], "dispose() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.dispose", false]], "dispose() (sttp.transport.datasubscriber.datasubscriber method)": [[7, "sttp.transport.datasubscriber.DataSubscriber.dispose", false]], "dispose() (sttp.transport.subscriberconnector.subscriberconnector method)": [[7, "sttp.transport.subscriberconnector.SubscriberConnector.dispose", false]], "disposing (sttp.transport.datasubscriber.datasubscriber property)": [[7, "sttp.transport.datasubscriber.DataSubscriber.disposing", false]], "divide (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.DIVIDE", false]], "double (gsf.empty attribute)": [[0, "gsf.Empty.DOUBLE", false]], "double (sttp.data.constants.expressionvaluetype attribute)": [[4, "sttp.data.constants.ExpressionValueType.DOUBLE", false]], "double (sttp.data.datatype.datatype attribute)": [[4, "sttp.data.datatype.DataType.DOUBLE", false]], "doublevalue() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.doublevalue", false]], "doublevalue() (sttp.data.valueexpression.valueexpression method)": [[4, "sttp.data.valueexpression.ValueExpression.doublevalue", false]], "doublevalue_byname() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.doublevalue_byname", false]], "downsampled (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.DOWNSAMPLED", false]], "empty (class in gsf)": [[0, "gsf.Empty", false]], "emptystringvalue (in module sttp.data.valueexpression)": [[4, "sttp.data.valueexpression.EMPTYSTRINGVALUE", false]], "enable_time_reasonability_check (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.ENABLE_TIME_REASONABILITY_CHECK", false]], "enabletimereasonabilitycheck (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.enabletimereasonabilitycheck", false]], "enabletimereasonabilitycheck (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.enabletimereasonabilitycheck", false]], "encodestr() (sttp.transport.datasubscriber.datasubscriber method)": [[7, "sttp.transport.datasubscriber.DataSubscriber.encodestr", false]], "encoding7bit (class in gsf.encoding7bit)": [[0, "gsf.encoding7bit.Encoding7Bit", false]], "encodingmask (sttp.transport.constants.operationalmodes attribute)": [[7, "sttp.transport.constants.OperationalModes.ENCODINGMASK", false]], "endofstream (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.ENDOFSTREAM", false]], "endswith (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.ENDSWITH", false]], "enterexpression() (sttp.data.filterexpressionparser.filterexpressionparser method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.enterExpression", false]], "enterfilterexpressionstatement() (sttp.data.filterexpressionparser.filterexpressionparser method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.enterFilterExpressionStatement", false]], "enterfilterstatement() (sttp.data.filterexpressionparser.filterexpressionparser method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.enterFilterStatement", false]], "equal (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.EQUAL", false]], "equalexactmatch (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.EQUALEXACTMATCH", false]], "errormessage() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.errormessage", false]], "errormessage_callback (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.errormessage_callback", false]], "errormessage_callback (sttp.transport.subscriberconnector.subscriberconnector attribute)": [[7, "sttp.transport.subscriberconnector.SubscriberConnector.errormessage_callback", false]], "evaluate() (sttp.data.expressiontree.expressiontree method)": [[4, "sttp.data.expressiontree.ExpressionTree.evaluate", false]], "evaluate() (sttp.data.filterexpressionparser.filterexpressionparser method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.evaluate", false]], "evaluate_datarowexpression() (sttp.data.filterexpressionparser.filterexpressionparser static method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.evaluate_datarowexpression", false]], "evaluate_expression() (sttp.data.filterexpressionparser.filterexpressionparser static method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.evaluate_expression", false]], "evaluateerror": [[4, "sttp.data.errors.EvaluateError", false]], "exactmatch (sttp.data.inlistexpression.inlistexpression property)": [[4, "sttp.data.inlistexpression.InListExpression.exactmatch", false]], "exitcolumnname() (sttp.data.filterexpressionparser.filterexpressionparser method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.exitColumnName", false]], "exitexpression() (sttp.data.filterexpressionparser.filterexpressionparser method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.exitExpression", false]], "exitfunctionexpression() (sttp.data.filterexpressionparser.filterexpressionparser method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.exitFunctionExpression", false]], "exitidentifierstatement() (sttp.data.filterexpressionparser.filterexpressionparser method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.exitIdentifierStatement", false]], "exitliteralvalue() (sttp.data.filterexpressionparser.filterexpressionparser method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.exitLiteralValue", false]], "exitpredicateexpression() (sttp.data.filterexpressionparser.filterexpressionparser method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.exitPredicateExpression", false]], "exitvalueexpression() (sttp.data.filterexpressionparser.filterexpressionparser method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.exitValueExpression", false]], "expression (class in sttp.data.expression)": [[4, "sttp.data.expression.Expression", false]], "expression (sttp.data.datacolumn.datacolumn property)": [[4, "sttp.data.datacolumn.DataColumn.expression", false]], "expressionfunctiontype (class in sttp.data.constants)": [[4, "sttp.data.constants.ExpressionFunctionType", false]], "expressionoperatortype (class in sttp.data.constants)": [[4, "sttp.data.constants.ExpressionOperatorType", false]], "expressiontree (class in sttp.data.expressiontree)": [[4, "sttp.data.expressiontree.ExpressionTree", false]], "expressiontrees (sttp.data.filterexpressionparser.filterexpressionparser property)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.expressiontrees", false]], "expressiontype (class in sttp.data.constants)": [[4, "sttp.data.constants.ExpressionType", false]], "expressiontype (sttp.data.columnexpression.columnexpression property)": [[4, "sttp.data.columnexpression.ColumnExpression.expressiontype", false]], "expressiontype (sttp.data.expression.expression property)": [[4, "sttp.data.expression.Expression.expressiontype", false]], "expressiontype (sttp.data.functionexpression.functionexpression property)": [[4, "sttp.data.functionexpression.FunctionExpression.expressiontype", false]], "expressiontype (sttp.data.inlistexpression.inlistexpression property)": [[4, "sttp.data.inlistexpression.InListExpression.expressiontype", false]], "expressiontype (sttp.data.operatorexpression.operatorexpression property)": [[4, "sttp.data.operatorexpression.OperatorExpression.expressiontype", false]], "expressiontype (sttp.data.unaryexpression.unaryexpression property)": [[4, "sttp.data.unaryexpression.UnaryExpression.expressiontype", false]], "expressiontype (sttp.data.valueexpression.valueexpression property)": [[4, "sttp.data.valueexpression.ValueExpression.expressiontype", false]], "expressionunarytype (class in sttp.data.constants)": [[4, "sttp.data.constants.ExpressionUnaryType", false]], "expressionvaluetype (class in sttp.data.constants)": [[4, "sttp.data.constants.ExpressionValueType", false]], "expressionvaluetypelen (in module sttp.data.constants)": [[4, "sttp.data.constants.EXPRESSIONVALUETYPELEN", false]], "ext_xmlschemadata_namespace (in module sttp.data.dataset)": [[4, "sttp.data.dataset.EXT_XMLSCHEMADATA_NAMESPACE", false]], "extactmatch (sttp.data.orderbyterm.orderbyterm attribute)": [[4, "sttp.data.orderbyterm.OrderByTerm.extactmatch", false]], "extra_connectionstring_parameters (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.extra_connectionstring_parameters", false]], "extra_connectionstring_parameters (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.EXTRA_CONNECTIONSTRING_PARAMETERS", false]], "extra_connectionstring_parameters (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.extra_connectionstring_parameters", false]], "failed (sttp.transport.constants.connectstatus attribute)": [[7, "sttp.transport.constants.ConnectStatus.FAILED", false]], "failed (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.FAILED", false]], "falsevalue (in module sttp.data.valueexpression)": [[4, "sttp.data.valueexpression.FALSEVALUE", false]], "filtered_rows (sttp.data.filterexpressionparser.filterexpressionparser property)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.filtered_rows", false]], "filtered_rowset (sttp.data.filterexpressionparser.filterexpressionparser property)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.filtered_rowset", false]], "filtered_signalids (sttp.data.filterexpressionparser.filterexpressionparser property)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.filtered_signalids", false]], "filtered_signalidset (sttp.data.filterexpressionparser.filterexpressionparser property)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.filtered_signalidset", false]], "filterexpression (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.FILTEREXPRESSION", false]], "filterexpression (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.filterexpression", false]], "filterexpression_statementcount (sttp.data.filterexpressionparser.filterexpressionparser property)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.filterexpression_statementcount", false]], "filterexpressionparser (class in sttp.data.filterexpressionparser)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser", false]], "find_device_acronym() (sttp.metadata.cache.metadatacache method)": [[5, "sttp.metadata.cache.MetadataCache.find_device_acronym", false]], "find_device_id() (sttp.metadata.cache.metadatacache method)": [[5, "sttp.metadata.cache.MetadataCache.find_device_id", false]], "find_devices() (sttp.metadata.cache.metadatacache method)": [[5, "sttp.metadata.cache.MetadataCache.find_devices", false]], "find_measurement_id() (sttp.metadata.cache.metadatacache method)": [[5, "sttp.metadata.cache.MetadataCache.find_measurement_id", false]], "find_measurement_pointtag() (sttp.metadata.cache.metadatacache method)": [[5, "sttp.metadata.cache.MetadataCache.find_measurement_pointtag", false]], "find_measurement_signalid() (sttp.metadata.cache.metadatacache method)": [[5, "sttp.metadata.cache.MetadataCache.find_measurement_signalid", false]], "find_measurement_signalreference() (sttp.metadata.cache.metadatacache method)": [[5, "sttp.metadata.cache.MetadataCache.find_measurement_signalreference", false]], "find_measurements() (sttp.metadata.cache.metadatacache method)": [[5, "sttp.metadata.cache.MetadataCache.find_measurements", false]], "find_measurements_signaltype() (sttp.metadata.cache.metadatacache method)": [[5, "sttp.metadata.cache.MetadataCache.find_measurements_signaltype", false]], "find_measurements_signaltypename() (sttp.metadata.cache.metadatacache method)": [[5, "sttp.metadata.cache.MetadataCache.find_measurements_signaltypename", false]], "flag (sttp.metadata.record.measurement.signaltype attribute)": [[6, "sttp.metadata.record.measurement.SignalType.FLAG", false]], "flags (sttp.transport.measurement.measurement attribute)": [[7, "sttp.transport.measurement.Measurement.flags", false]], "flatlinealarm (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.FLATLINEALARM", false]], "float16 (gsf.bytesize attribute)": [[0, "gsf.ByteSize.FLOAT16", false]], "float32 (gsf.bytesize attribute)": [[0, "gsf.ByteSize.FLOAT32", false]], "float64 (gsf.bytesize attribute)": [[0, "gsf.ByteSize.FLOAT64", false]], "floor (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.FLOOR", false]], "flush() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.flush", false]], "framespersecond (sttp.metadata.record.device.devicerecord property)": [[6, "sttp.metadata.record.device.DeviceRecord.framespersecond", false]], "freq (sttp.metadata.record.measurement.signaltype attribute)": [[6, "sttp.metadata.record.measurement.SignalType.FREQ", false]], "frequency (sttp.transport.signalkind.signalkind attribute)": [[7, "sttp.transport.signalkind.SignalKind.FREQUENCY", false]], "from_dataset() (sttp.data.filterexpressionparser.filterexpressionparser static method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.from_dataset", false]], "from_datetime() (sttp.ticks.ticks static method)": [[3, "sttp.ticks.Ticks.from_datetime", false]], "from_float16() (gsf.endianorder.nativeendian class method)": [[0, "gsf.endianorder.NativeEndian.from_float16", false]], "from_float32() (gsf.endianorder.nativeendian class method)": [[0, "gsf.endianorder.NativeEndian.from_float32", false]], "from_float64() (gsf.endianorder.nativeendian class method)": [[0, "gsf.endianorder.NativeEndian.from_float64", false]], "from_int16() (gsf.endianorder.nativeendian class method)": [[0, "gsf.endianorder.NativeEndian.from_int16", false]], "from_int32() (gsf.endianorder.nativeendian class method)": [[0, "gsf.endianorder.NativeEndian.from_int32", false]], "from_int64() (gsf.endianorder.nativeendian class method)": [[0, "gsf.endianorder.NativeEndian.from_int64", false]], "from_str() (gsf.convert static method)": [[0, "gsf.Convert.from_str", false]], "from_timedelta() (sttp.ticks.ticks static method)": [[3, "sttp.ticks.Ticks.from_timedelta", false]], "from_uint16() (gsf.endianorder.nativeendian class method)": [[0, "gsf.endianorder.NativeEndian.from_uint16", false]], "from_uint32() (gsf.endianorder.nativeendian class method)": [[0, "gsf.endianorder.NativeEndian.from_uint32", false]], "from_uint64() (gsf.endianorder.nativeendian class method)": [[0, "gsf.endianorder.NativeEndian.from_uint64", false]], "from_xml() (sttp.data.dataset.dataset static method)": [[4, "sttp.data.dataset.DataSet.from_xml", false]], "function (sttp.data.constants.expressiontype attribute)": [[4, "sttp.data.constants.ExpressionType.FUNCTION", false]], "functionexpression (class in sttp.data.functionexpression)": [[4, "sttp.data.functionexpression.FunctionExpression", false]], "functiontype (sttp.data.functionexpression.functionexpression property)": [[4, "sttp.data.functionexpression.FunctionExpression.functiontype", false]], "futuretimealarm (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.FUTURETIMEALARM", false]], "generate_expressiontree() (sttp.data.filterexpressionparser.filterexpressionparser static method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.generate_expressiontree", false]], "generate_expressiontrees() (sttp.data.filterexpressionparser.filterexpressionparser static method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.generate_expressiontrees", false]], "generate_expressiontrees_fromtable() (sttp.data.filterexpressionparser.filterexpressionparser static method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.generate_expressiontrees_fromtable", false]], "get_binarylength() (sttp.transport.compactmeasurement.compactmeasurement method)": [[7, "sttp.transport.compactmeasurement.CompactMeasurement.get_binarylength", false]], "get_compact_stateflags() (sttp.transport.compactmeasurement.compactmeasurement method)": [[7, "sttp.transport.compactmeasurement.CompactMeasurement.get_compact_stateflags", false]], "get_timestamp_c2() (sttp.transport.compactmeasurement.compactmeasurement method)": [[7, "sttp.transport.compactmeasurement.CompactMeasurement.get_timestamp_c2", false]], "get_timestamp_c4() (sttp.transport.compactmeasurement.compactmeasurement method)": [[7, "sttp.transport.compactmeasurement.CompactMeasurement.get_timestamp_c4", false]], "getprimarymetadataschema (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.GETPRIMARYMETADATASCHEMA", false]], "getsignalselectionschema (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.GETSIGNALSELECTIONSCHEMA", false]], "greaterthan (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.GREATERTHAN", false]], "greaterthanorequal (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.GREATERTHANOREQUAL", false]], "gsf": [[0, "module-gsf", false]], "gsf.binarystream": [[0, "module-gsf.binarystream", false]], "gsf.encoding7bit": [[0, "module-gsf.encoding7bit", false]], "gsf.endianorder": [[0, "module-gsf.endianorder", false]], "gsf.streamencoder": [[0, "module-gsf.streamencoder", false]], "guid (gsf.empty attribute)": [[0, "gsf.Empty.GUID", false]], "guid (sttp.data.constants.expressionvaluetype attribute)": [[4, "sttp.data.constants.ExpressionValueType.GUID", false]], "guid (sttp.data.datatype.datatype attribute)": [[4, "sttp.data.datatype.DataType.GUID", false]], "guidvalue() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.guidvalue", false]], "guidvalue() (sttp.data.valueexpression.valueexpression method)": [[4, "sttp.data.valueexpression.ValueExpression.guidvalue", false]], "guidvalue_byname() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.guidvalue_byname", false]], "gzip (sttp.transport.constants.compressionmodes attribute)": [[7, "sttp.transport.constants.CompressionModes.GZIP", false]], "has_notkeyword (sttp.data.inlistexpression.inlistexpression property)": [[4, "sttp.data.inlistexpression.InListExpression.has_notkeyword", false]], "hostname (sttp.transport.subscriberconnector.subscriberconnector attribute)": [[7, "sttp.transport.subscriberconnector.SubscriberConnector.hostname", false]], "hour (sttp.data.constants.timeinterval attribute)": [[4, "sttp.data.constants.TimeInterval.HOUR", false]], "id (sttp.metadata.record.measurement.measurementrecord property)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.id", false]], "id (sttp.metadata.record.phasor.phasorrecord property)": [[6, "sttp.metadata.record.phasor.PhasorRecord.id", false]], "id() (sttp.transport.signalindexcache.signalindexcache method)": [[7, "sttp.transport.signalindexcache.SignalIndexCache.id", false]], "id_measurement_map (sttp.metadata.cache.metadatacache attribute)": [[5, "sttp.metadata.cache.MetadataCache.id_measurement_map", false]], "iif (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.IIF", false]], "implementationspecificextensionmask (sttp.transport.constants.operationalmodes attribute)": [[7, "sttp.transport.constants.OperationalModes.IMPLEMENTATIONSPECIFICEXTENSIONMASK", false]], "includetime (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.includetime", false]], "includetime (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.INCLUDETIME", false]], "includetime (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.includetime", false]], "index (sttp.data.datacolumn.datacolumn property)": [[4, "sttp.data.datacolumn.DataColumn.index", false]], "indexof (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.INDEXOF", false]], "inlist (sttp.data.constants.expressiontype attribute)": [[4, "sttp.data.constants.ExpressionType.INLIST", false]], "inlistexpression (class in sttp.data.inlistexpression)": [[4, "sttp.data.inlistexpression.InListExpression", false]], "int16 (gsf.bytesize attribute)": [[0, "gsf.ByteSize.INT16", false]], "int16 (gsf.empty attribute)": [[0, "gsf.Empty.INT16", false]], "int16 (sttp.data.datatype.datatype attribute)": [[4, "sttp.data.datatype.DataType.INT16", false]], "int16value() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.int16value", false]], "int16value_byname() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.int16value_byname", false]], "int32 (gsf.bytesize attribute)": [[0, "gsf.ByteSize.INT32", false]], "int32 (gsf.empty attribute)": [[0, "gsf.Empty.INT32", false]], "int32 (sttp.data.constants.expressionvaluetype attribute)": [[4, "sttp.data.constants.ExpressionValueType.INT32", false]], "int32 (sttp.data.datatype.datatype attribute)": [[4, "sttp.data.datatype.DataType.INT32", false]], "int32value() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.int32value", false]], "int32value() (sttp.data.valueexpression.valueexpression method)": [[4, "sttp.data.valueexpression.ValueExpression.int32value", false]], "int32value_byname() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.int32value_byname", false]], "int64 (gsf.bytesize attribute)": [[0, "gsf.ByteSize.INT64", false]], "int64 (gsf.empty attribute)": [[0, "gsf.Empty.INT64", false]], "int64 (sttp.data.constants.expressionvaluetype attribute)": [[4, "sttp.data.constants.ExpressionValueType.INT64", false]], "int64 (sttp.data.datatype.datatype attribute)": [[4, "sttp.data.datatype.DataType.INT64", false]], "int64value() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.int64value", false]], "int64value() (sttp.data.valueexpression.valueexpression method)": [[4, "sttp.data.valueexpression.ValueExpression.int64value", false]], "int64value_byname() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.int64value_byname", false]], "int8 (gsf.bytesize attribute)": [[0, "gsf.ByteSize.INT8", false]], "int8 (gsf.empty attribute)": [[0, "gsf.Empty.INT8", false]], "int8 (sttp.data.datatype.datatype attribute)": [[4, "sttp.data.datatype.DataType.INT8", false]], "int8value() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.int8value", false]], "int8value_byname() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.int8value_byname", false]], "integervalue() (sttp.data.valueexpression.valueexpression method)": [[4, "sttp.data.valueexpression.ValueExpression.integervalue", false]], "io_buffersize (gsf.binarystream.binarystream attribute)": [[0, "gsf.binarystream.BinaryStream.IO_BUFFERSIZE", false]], "ipha (sttp.metadata.record.measurement.signaltype attribute)": [[6, "sttp.metadata.record.measurement.SignalType.IPHA", false]], "iphm (sttp.metadata.record.measurement.signaltype attribute)": [[6, "sttp.metadata.record.measurement.SignalType.IPHM", false]], "is_integertype() (in module sttp.data.constants)": [[4, "sttp.data.constants.is_integertype", false]], "is_leapsecond() (sttp.ticks.ticks static method)": [[3, "sttp.ticks.Ticks.is_leapsecond", false]], "is_negative_leapsecond() (sttp.ticks.ticks static method)": [[3, "sttp.ticks.Ticks.is_negative_leapsecond", false]], "is_null() (sttp.data.valueexpression.valueexpression method)": [[4, "sttp.data.valueexpression.ValueExpression.is_null", false]], "is_numerictype() (in module sttp.data.constants)": [[4, "sttp.data.constants.is_numerictype", false]], "isdate (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.ISDATE", false]], "isguid (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.ISGUID", false]], "isinteger (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.ISINTEGER", false]], "isnotnull (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.ISNOTNULL", false]], "isnull (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.ISNULL", false]], "isnull (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.ISNULL", false]], "isnumeric (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.ISNUMERIC", false]], "label (sttp.metadata.record.phasor.phasorrecord property)": [[6, "sttp.metadata.record.phasor.PhasorRecord.label", false]], "lagtime (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.lagtime", false]], "lagtime (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.LAGTIME", false]], "lagtime (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.lagtime", false]], "lastindexof (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.LASTINDEXOF", false]], "latetimealarm (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.LATETIMEALARM", false]], "latitude (sttp.metadata.record.device.devicerecord property)": [[6, "sttp.metadata.record.device.DeviceRecord.latitude", false]], "leadtime (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.leadtime", false]], "leadtime (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.LEADTIME", false]], "leadtime (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.leadtime", false]], "leapsecond_direction (sttp.ticks.ticks attribute)": [[3, "sttp.ticks.Ticks.LEAPSECOND_DIRECTION", false]], "leapsecond_flag (sttp.ticks.ticks attribute)": [[3, "sttp.ticks.Ticks.LEAPSECOND_FLAG", false]], "leftvalue (sttp.data.operatorexpression.operatorexpression property)": [[4, "sttp.data.operatorexpression.OperatorExpression.leftvalue", false]], "len (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.LEN", false]], "lessthan (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.LESSTHAN", false]], "lessthanorequal (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.LESSTHANOREQUAL", false]], "like (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.LIKE", false]], "likeexactmatch (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.LIKEEXACTMATCH", false]], "limits (class in gsf)": [[0, "gsf.Limits", false]], "littleendian (class in gsf.endianorder)": [[0, "gsf.endianorder.LittleEndian", false]], "longitude (sttp.metadata.record.device.devicerecord property)": [[6, "sttp.metadata.record.device.DeviceRecord.longitude", false]], "lookup_metadata() (sttp.transport.datasubscriber.datasubscriber method)": [[7, "sttp.transport.datasubscriber.DataSubscriber.lookup_metadata", false]], "lower (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.LOWER", false]], "magnitude (sttp.metadata.record.phasor.compositephasormeasurement attribute)": [[6, "sttp.metadata.record.phasor.CompositePhasorMeasurement.MAGNITUDE", false]], "magnitude (sttp.transport.signalkind.signalkind attribute)": [[7, "sttp.transport.signalkind.SignalKind.MAGNITUDE", false]], "magnitude_measurement (sttp.metadata.record.phasor.phasorrecord property)": [[6, "sttp.metadata.record.phasor.PhasorRecord.magnitude_measurement", false]], "maxbyte (gsf.limits attribute)": [[0, "gsf.Limits.MAXBYTE", false]], "maxint16 (gsf.limits attribute)": [[0, "gsf.Limits.MAXINT16", false]], "maxint32 (gsf.limits attribute)": [[0, "gsf.Limits.MAXINT32", false]], "maxint64 (gsf.limits attribute)": [[0, "gsf.Limits.MAXINT64", false]], "maxof (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.MAXOF", false]], "maxretries (sttp.config.config attribute)": [[3, "sttp.config.Config.maxretries", false]], "maxretries (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.MAXRETRIES", false]], "maxretries (sttp.transport.subscriberconnector.subscriberconnector attribute)": [[7, "sttp.transport.subscriberconnector.SubscriberConnector.maxretries", false]], "maxretryinterval (sttp.config.config attribute)": [[3, "sttp.config.Config.maxretryinterval", false]], "maxretryinterval (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.MAXRETRYINTERVAL", false]], "maxretryinterval (sttp.transport.subscriberconnector.subscriberconnector attribute)": [[7, "sttp.transport.subscriberconnector.SubscriberConnector.maxretryinterval", false]], "maxticks (gsf.limits attribute)": [[0, "gsf.Limits.MAXTICKS", false]], "maxuint16 (gsf.limits attribute)": [[0, "gsf.Limits.MAXUINT16", false]], "maxuint32 (gsf.limits attribute)": [[0, "gsf.Limits.MAXUINT32", false]], "maxuint64 (gsf.limits attribute)": [[0, "gsf.Limits.MAXUINT64", false]], "measurement (class in sttp.transport.measurement)": [[7, "sttp.transport.measurement.Measurement", false]], "measurement_metadata() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.measurement_metadata", false]], "measurement_records (sttp.metadata.cache.metadatacache attribute)": [[5, "sttp.metadata.cache.MetadataCache.measurement_records", false]], "measurementerror (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.MEASUREMENTERROR", false]], "measurementkey_fieldname (sttp.data.tableidfields.tableidfields attribute)": [[4, "sttp.data.tableidfields.TableIDFields.measurementkey_fieldname", false]], "measurementreader (class in sttp.reader)": [[3, "sttp.reader.MeasurementReader", false]], "measurementrecord (class in sttp.metadata.record.measurement)": [[6, "sttp.metadata.record.measurement.MeasurementRecord", false]], "measurements (sttp.metadata.record.device.devicerecord attribute)": [[6, "sttp.metadata.record.device.DeviceRecord.measurements", false]], "measurements (sttp.metadata.record.phasor.phasorrecord attribute)": [[6, "sttp.metadata.record.phasor.PhasorRecord.measurements", false]], "metadatacache (class in sttp.metadata.cache)": [[5, "sttp.metadata.cache.MetadataCache", false]], "metadatacache (sttp.subscriber.subscriber property)": [[3, "sttp.subscriber.Subscriber.metadatacache", false]], "metadatacache (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.metadatacache", false]], "metadatafilters (sttp.config.config attribute)": [[3, "sttp.config.Config.metadatafilters", false]], "metadatafilters (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.METADATAFILTERS", false]], "metadatareceived_callback (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.metadatareceived_callback", false]], "metadatarefresh (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.METADATAREFRESH", false]], "millisecond (sttp.data.constants.timeinterval attribute)": [[4, "sttp.data.constants.TimeInterval.MILLISECOND", false]], "minint16 (gsf.limits attribute)": [[0, "gsf.Limits.MININT16", false]], "minint32 (gsf.limits attribute)": [[0, "gsf.Limits.MININT32", false]], "minint64 (gsf.limits attribute)": [[0, "gsf.Limits.MININT64", false]], "minof (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.MINOF", false]], "minus (sttp.data.constants.expressionunarytype attribute)": [[4, "sttp.data.constants.ExpressionUnaryType.MINUS", false]], "minute (sttp.data.constants.timeinterval attribute)": [[4, "sttp.data.constants.TimeInterval.MINUTE", false]], "module": [[0, "module-gsf", false], [0, "module-gsf.binarystream", false], [0, "module-gsf.encoding7bit", false], [0, "module-gsf.endianorder", false], [0, "module-gsf.streamencoder", false], [3, "module-sttp", false], [3, "module-sttp.config", false], [3, "module-sttp.reader", false], [3, "module-sttp.settings", false], [3, "module-sttp.subscriber", false], [3, "module-sttp.ticks", false], [3, "module-sttp.version", false], [4, "module-sttp.data", false], [4, "module-sttp.data.callbackerrorlistener", false], [4, "module-sttp.data.columnexpression", false], [4, "module-sttp.data.constants", false], [4, "module-sttp.data.datacolumn", false], [4, "module-sttp.data.datarow", false], [4, "module-sttp.data.dataset", false], [4, "module-sttp.data.datatable", false], [4, "module-sttp.data.datatype", false], [4, "module-sttp.data.errors", false], [4, "module-sttp.data.expression", false], [4, "module-sttp.data.expressiontree", false], [4, "module-sttp.data.filterexpressionparser", false], [4, "module-sttp.data.functionexpression", false], [4, "module-sttp.data.inlistexpression", false], [4, "module-sttp.data.operatorexpression", false], [4, "module-sttp.data.orderbyterm", false], [4, "module-sttp.data.tableidfields", false], [4, "module-sttp.data.unaryexpression", false], [4, "module-sttp.data.valueexpression", false], [5, "module-sttp.metadata", false], [5, "module-sttp.metadata.cache", false], [6, "module-sttp.metadata.record", false], [6, "module-sttp.metadata.record.device", false], [6, "module-sttp.metadata.record.measurement", false], [6, "module-sttp.metadata.record.phasor", false], [7, "module-sttp.transport", false], [7, "module-sttp.transport.bufferblock", false], [7, "module-sttp.transport.compactmeasurement", false], [7, "module-sttp.transport.constants", false], [7, "module-sttp.transport.datasubscriber", false], [7, "module-sttp.transport.measurement", false], [7, "module-sttp.transport.signalindexcache", false], [7, "module-sttp.transport.signalkind", false], [7, "module-sttp.transport.subscriberconnector", false], [7, "module-sttp.transport.subscriptioninfo", false], [8, "module-sttp.transport.tssc", false], [8, "module-sttp.transport.tssc.decoder", false], [8, "module-sttp.transport.tssc.pointmetadata", false]], "modulus (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.MODULUS", false]], "month (sttp.data.constants.timeinterval attribute)": [[4, "sttp.data.constants.TimeInterval.MONTH", false]], "multiplier (sttp.metadata.record.measurement.measurementrecord property)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.multiplier", false]], "multiply (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.MULTIPLY", false]], "name (sttp.data.datacolumn.datacolumn property)": [[4, "sttp.data.datacolumn.DataColumn.name", false]], "name (sttp.data.dataset.dataset attribute)": [[4, "sttp.data.dataset.DataSet.name", false]], "name (sttp.data.datatable.datatable property)": [[4, "sttp.data.datatable.DataTable.name", false]], "name (sttp.metadata.record.device.devicerecord property)": [[6, "sttp.metadata.record.device.DeviceRecord.name", false]], "nativeendian (class in gsf.endianorder)": [[0, "gsf.endianorder.NativeEndian", false]], "newbufferblocks_callback (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.newbufferblocks_callback", false]], "newmeasurements_callback (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.newmeasurements_callback", false]], "next_measurement() (sttp.reader.measurementreader method)": [[3, "sttp.reader.MeasurementReader.next_measurement", false]], "nodeid (sttp.metadata.record.device.devicerecord property)": [[6, "sttp.metadata.record.device.DeviceRecord.nodeid", false]], "noflags (sttp.transport.constants.compressionmodes attribute)": [[7, "sttp.transport.constants.CompressionModes.NOFLAGS", false]], "noflags (sttp.transport.constants.datapacketflags attribute)": [[7, "sttp.transport.constants.DataPacketFlags.NOFLAGS", false]], "noflags (sttp.transport.constants.operationalmodes attribute)": [[7, "sttp.transport.constants.OperationalModes.NOFLAGS", false]], "noop (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.NOOP", false]], "normal (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.NORMAL", false]], "normalize_enumname() (in module gsf)": [[0, "gsf.normalize_enumname", false]], "not (sttp.data.constants.expressionunarytype attribute)": [[4, "sttp.data.constants.ExpressionUnaryType.NOT", false]], "notequal (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.NOTEQUAL", false]], "notequalexactmatch (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.NOTEQUALEXACTMATCH", false]], "notificationreceived_callback (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.notificationreceived_callback", false]], "notify (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.NOTIFY", false]], "notlike (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.NOTLIKE", false]], "notlikeexactmatch (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.NOTLIKEEXACTMATCH", false]], "now (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.NOW", false]], "now() (sttp.ticks.ticks static method)": [[3, "sttp.ticks.Ticks.now", false]], "nthindexof (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.NTHINDEXOF", false]], "nullboolvalue (in module sttp.data.valueexpression)": [[4, "sttp.data.valueexpression.NULLBOOLVALUE", false]], "nulldatetimevalue (in module sttp.data.valueexpression)": [[4, "sttp.data.valueexpression.NULLDATETIMEVALUE", false]], "nullint32value (in module sttp.data.valueexpression)": [[4, "sttp.data.valueexpression.NULLINT32VALUE", false]], "nullstringvalue (in module sttp.data.valueexpression)": [[4, "sttp.data.valueexpression.NULLSTRINGVALUE", false]], "nullvalue (in module sttp.data.valueexpression)": [[4, "sttp.data.valueexpression.NULLVALUE", false]], "nullvalue() (sttp.data.valueexpression.valueexpression static method)": [[4, "sttp.data.valueexpression.ValueExpression.nullvalue", false]], "off (sttp.transport.constants.securitymode attribute)": [[7, "sttp.transport.constants.SecurityMode.OFF", false]], "operationalencoding (class in sttp.transport.constants)": [[7, "sttp.transport.constants.OperationalEncoding", false]], "operationalmodes (class in sttp.transport.constants)": [[7, "sttp.transport.constants.OperationalModes", false]], "operator (sttp.data.constants.expressiontype attribute)": [[4, "sttp.data.constants.ExpressionType.OPERATOR", false]], "operatorexpression (class in sttp.data.operatorexpression)": [[4, "sttp.data.operatorexpression.OperatorExpression", false]], "operatortype (sttp.data.operatorexpression.operatorexpression property)": [[4, "sttp.data.operatorexpression.OperatorExpression.operatortype", false]], "or (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.OR", false]], "orderbyterm (class in sttp.data.orderbyterm)": [[4, "sttp.data.orderbyterm.OrderByTerm", false]], "orderbyterms (sttp.data.expressiontree.expressiontree attribute)": [[4, "sttp.data.expressiontree.ExpressionTree.orderbyterms", false]], "overrangeerror (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.OVERRANGEERROR", false]], "override() (in module gsf)": [[0, "gsf.override", false]], "parameters() (gsf.validate static method)": [[0, "gsf.Validate.parameters", false]], "parent (sttp.data.datacolumn.datacolumn property)": [[4, "sttp.data.datacolumn.DataColumn.parent", false]], "parent (sttp.data.datarow.datarow property)": [[4, "sttp.data.datarow.DataRow.parent", false]], "parent (sttp.data.datatable.datatable property)": [[4, "sttp.data.datatable.DataTable.parent", false]], "parentacronym (sttp.metadata.record.device.devicerecord property)": [[6, "sttp.metadata.record.device.DeviceRecord.parentacronym", false]], "parse() (sttp.data.constants.timeinterval class method)": [[4, "sttp.data.constants.TimeInterval.parse", false]], "parse() (sttp.metadata.record.measurement.signaltype class method)": [[6, "sttp.metadata.record.measurement.SignalType.parse", false]], "parse_acronym() (sttp.transport.signalkind.signalkindenum static method)": [[7, "sttp.transport.signalkind.SignalKindEnum.parse_acronym", false]], "parse_xml() (sttp.data.dataset.dataset method)": [[4, "sttp.data.dataset.DataSet.parse_xml", false]], "parse_xmldoc() (sttp.data.dataset.dataset method)": [[4, "sttp.data.dataset.DataSet.parse_xmldoc", false]], "parse_xsddatatype() (in module sttp.data.datatype)": [[4, "sttp.data.datatype.parse_xsddatatype", false]], "parsingexception_callback (sttp.data.callbackerrorlistener.callbackerrorlistener attribute)": [[4, "sttp.data.callbackerrorlistener.CallbackErrorListener.parsingexception_callback", false]], "perday (sttp.ticks.ticks attribute)": [[3, "sttp.ticks.Ticks.PERDAY", false]], "perhour (sttp.ticks.ticks attribute)": [[3, "sttp.ticks.Ticks.PERHOUR", false]], "permicrosecond (sttp.ticks.ticks attribute)": [[3, "sttp.ticks.Ticks.PERMICROSECOND", false]], "permillisecond (sttp.ticks.ticks attribute)": [[3, "sttp.ticks.Ticks.PERMILLISECOND", false]], "perminute (sttp.ticks.ticks attribute)": [[3, "sttp.ticks.Ticks.PERMINUTE", false]], "persecond (sttp.ticks.ticks attribute)": [[3, "sttp.ticks.Ticks.PERSECOND", false]], "phase (sttp.metadata.record.phasor.phasorrecord property)": [[6, "sttp.metadata.record.phasor.PhasorRecord.phase", false]], "phasor (sttp.metadata.record.measurement.measurementrecord attribute)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.phasor", false]], "phasorrecord (class in sttp.metadata.record.phasor)": [[6, "sttp.metadata.record.phasor.PhasorRecord", false]], "phasorrecords (sttp.metadata.cache.metadatacache attribute)": [[5, "sttp.metadata.cache.MetadataCache.phasorRecords", false]], "phasors (sttp.metadata.record.device.devicerecord attribute)": [[6, "sttp.metadata.record.device.DeviceRecord.phasors", false]], "plus (sttp.data.constants.expressionunarytype attribute)": [[4, "sttp.data.constants.ExpressionUnaryType.PLUS", false]], "pointidxor12 (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.POINTIDXOR12", false]], "pointidxor16 (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.POINTIDXOR16", false]], "pointidxor20 (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.POINTIDXOR20", false]], "pointidxor24 (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.POINTIDXOR24", false]], "pointidxor32 (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.POINTIDXOR32", false]], "pointidxor4 (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.POINTIDXOR4", false]], "pointidxor8 (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.POINTIDXOR8", false]], "pointmetadata (class in sttp.transport.tssc.pointmetadata)": [[8, "sttp.transport.tssc.pointmetadata.PointMetadata", false]], "pointtag (sttp.metadata.record.measurement.measurementrecord property)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.pointtag", false]], "pointtag_fieldname (sttp.data.tableidfields.tableidfields attribute)": [[4, "sttp.data.tableidfields.TableIDFields.pointtag_fieldname", false]], "pointtag_measurement_map (sttp.metadata.cache.metadatacache attribute)": [[5, "sttp.metadata.cache.MetadataCache.pointtag_measurement_map", false]], "port (sttp.transport.subscriberconnector.subscriberconnector attribute)": [[7, "sttp.transport.subscriberconnector.SubscriberConnector.port", false]], "power (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.POWER", false]], "primary_tablename (sttp.data.filterexpressionparser.filterexpressionparser attribute)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.primary_tablename", false]], "processingcomplete (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.PROCESSINGCOMPLETE", false]], "processingcomplete_callback (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.processingcomplete_callback", false]], "processinginterval (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.processinginterval", false]], "processinginterval (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.PROCESSINGINTERVAL", false]], "processinginterval (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.processinginterval", false]], "protocolname (sttp.metadata.record.device.devicerecord property)": [[6, "sttp.metadata.record.device.DeviceRecord.protocolname", false]], "publishinterval (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.publishinterval", false]], "publishinterval (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.PUBLISHINTERVAL", false]], "publishinterval (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.publishinterval", false]], "qual (sttp.metadata.record.measurement.signaltype attribute)": [[6, "sttp.metadata.record.measurement.SignalType.QUAL", false]], "quality (sttp.transport.signalkind.signalkind attribute)": [[7, "sttp.transport.signalkind.SignalKind.QUALITY", false]], "read() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.read", false]], "read() (gsf.streamencoder.streamencoder method)": [[0, "gsf.streamencoder.StreamEncoder.read", false]], "read7bit_int32() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.read7bit_int32", false]], "read7bit_int64() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.read7bit_int64", false]], "read7bit_uint32() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.read7bit_uint32", false]], "read7bit_uint32() (gsf.streamencoder.streamencoder method)": [[0, "gsf.streamencoder.StreamEncoder.read7bit_uint32", false]], "read7bit_uint64() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.read7bit_uint64", false]], "read7bit_uint64() (gsf.streamencoder.streamencoder method)": [[0, "gsf.streamencoder.StreamEncoder.read7bit_uint64", false]], "read_all() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.read_all", false]], "read_bool() (gsf.streamencoder.streamencoder method)": [[0, "gsf.streamencoder.StreamEncoder.read_bool", false]], "read_boolean() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.read_boolean", false]], "read_buffer() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.read_buffer", false]], "read_byte() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.read_byte", false]], "read_byte() (gsf.streamencoder.streamencoder method)": [[0, "gsf.streamencoder.StreamEncoder.read_byte", false]], "read_bytes() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.read_bytes", false]], "read_code() (sttp.transport.tssc.pointmetadata.pointmetadata method)": [[8, "sttp.transport.tssc.pointmetadata.PointMetadata.read_code", false]], "read_guid() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.read_guid", false]], "read_int16() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.read_int16", false]], "read_int16() (gsf.encoding7bit.encoding7bit static method)": [[0, "gsf.encoding7bit.Encoding7Bit.read_int16", false]], "read_int16() (gsf.streamencoder.streamencoder method)": [[0, "gsf.streamencoder.StreamEncoder.read_int16", false]], "read_int32() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.read_int32", false]], "read_int32() (gsf.encoding7bit.encoding7bit static method)": [[0, "gsf.encoding7bit.Encoding7Bit.read_int32", false]], "read_int32() (gsf.streamencoder.streamencoder method)": [[0, "gsf.streamencoder.StreamEncoder.read_int32", false]], "read_int64() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.read_int64", false]], "read_int64() (gsf.encoding7bit.encoding7bit static method)": [[0, "gsf.encoding7bit.Encoding7Bit.read_int64", false]], "read_int64() (gsf.streamencoder.streamencoder method)": [[0, "gsf.streamencoder.StreamEncoder.read_int64", false]], "read_measurements() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.read_measurements", false]], "read_string() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.read_string", false]], "read_uint16() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.read_uint16", false]], "read_uint16() (gsf.encoding7bit.encoding7bit static method)": [[0, "gsf.encoding7bit.Encoding7Bit.read_uint16", false]], "read_uint16() (gsf.streamencoder.streamencoder method)": [[0, "gsf.streamencoder.StreamEncoder.read_uint16", false]], "read_uint32() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.read_uint32", false]], "read_uint32() (gsf.encoding7bit.encoding7bit static method)": [[0, "gsf.encoding7bit.Encoding7Bit.read_uint32", false]], "read_uint32() (gsf.streamencoder.streamencoder method)": [[0, "gsf.streamencoder.StreamEncoder.read_uint32", false]], "read_uint64() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.read_uint64", false]], "read_uint64() (gsf.encoding7bit.encoding7bit static method)": [[0, "gsf.encoding7bit.Encoding7Bit.read_uint64", false]], "read_uint64() (gsf.streamencoder.streamencoder method)": [[0, "gsf.streamencoder.StreamEncoder.read_uint64", false]], "receivedasbad (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.RECEIVEDASBAD", false]], "receiveexternalmetadata (sttp.transport.constants.operationalmodes attribute)": [[7, "sttp.transport.constants.OperationalModes.RECEIVEEXTERNALMETADATA", false]], "receiveinternalmetadata (sttp.transport.constants.operationalmodes attribute)": [[7, "sttp.transport.constants.OperationalModes.RECEIVEINTERNALMETADATA", false]], "reconnect_callback (sttp.transport.subscriberconnector.subscriberconnector attribute)": [[7, "sttp.transport.subscriberconnector.SubscriberConnector.reconnect_callback", false]], "record() (sttp.transport.signalindexcache.signalindexcache method)": [[7, "sttp.transport.signalindexcache.SignalIndexCache.record", false]], "regexmatch (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.REGEXMATCH", false]], "regexval (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.REGEXVAL", false]], "remove_table() (sttp.data.dataset.dataset method)": [[4, "sttp.data.dataset.DataSet.remove_table", false]], "replace (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.REPLACE", false]], "request_metadata() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.request_metadata", false]], "request_nanvaluefilter (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.request_nanvaluefilter", false]], "request_nanvaluefilter (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.REQUEST_NANVALUEFILTER", false]], "request_nanvaluefilter (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.request_nanvaluefilter", false]], "reservedqualityflag (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.RESERVEDQUALITYFLAG", false]], "reservedtimeflag (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.RESERVEDTIMEFLAG", false]], "reset_connection() (sttp.transport.subscriberconnector.subscriberconnector method)": [[7, "sttp.transport.subscriberconnector.SubscriberConnector.reset_connection", false]], "retryinterval (sttp.config.config attribute)": [[3, "sttp.config.Config.retryinterval", false]], "retryinterval (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.RETRYINTERVAL", false]], "retryinterval (sttp.transport.subscriberconnector.subscriberconnector attribute)": [[7, "sttp.transport.subscriberconnector.SubscriberConnector.retryinterval", false]], "reverse (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.REVERSE", false]], "rightvalue (sttp.data.operatorexpression.operatorexpression property)": [[4, "sttp.data.operatorexpression.OperatorExpression.rightvalue", false]], "rocalarm (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.ROCALARM", false]], "root (sttp.data.expressiontree.expressiontree attribute)": [[4, "sttp.data.expressiontree.ExpressionTree.root", false]], "rotatecipherkeys (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.ROTATECIPHERKEYS", false]], "round (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.ROUND", false]], "row() (sttp.data.datatable.datatable method)": [[4, "sttp.data.datatable.DataTable.row", false]], "rowcount (sttp.data.datatable.datatable property)": [[4, "sttp.data.datatable.DataTable.rowcount", false]], "rowswhere() (sttp.data.datatable.datatable method)": [[4, "sttp.data.datatable.DataTable.rowswhere", false]], "rowvalue_as_string() (sttp.data.datatable.datatable method)": [[4, "sttp.data.datatable.DataTable.rowvalue_as_string", false]], "rowvalue_as_string_byname() (sttp.data.datatable.datatable method)": [[4, "sttp.data.datatable.DataTable.rowvalue_as_string_byname", false]], "runtimeid (sttp.transport.compactmeasurement.compactmeasurement property)": [[7, "sttp.transport.compactmeasurement.CompactMeasurement.runtimeid", false]], "second (sttp.data.constants.timeinterval attribute)": [[4, "sttp.data.constants.TimeInterval.SECOND", false]], "securitymode (class in sttp.transport.constants)": [[7, "sttp.transport.constants.SecurityMode", false]], "select() (sttp.data.datatable.datatable method)": [[4, "sttp.data.datatable.DataTable.select", false]], "select() (sttp.data.expressiontree.expressiontree method)": [[4, "sttp.data.expressiontree.ExpressionTree.select", false]], "select_datarows() (sttp.data.filterexpressionparser.filterexpressionparser static method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.select_datarows", false]], "select_datarows_fromtable() (sttp.data.filterexpressionparser.filterexpressionparser static method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.select_datarows_fromtable", false]], "select_datarowset() (sttp.data.filterexpressionparser.filterexpressionparser static method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.select_datarowset", false]], "select_datarowset_fromtable() (sttp.data.filterexpressionparser.filterexpressionparser static method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.select_datarowset_fromtable", false]], "select_signalidset() (sttp.data.filterexpressionparser.filterexpressionparser static method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.select_signalidset", false]], "select_signalidset_fromtable() (sttp.data.filterexpressionparser.filterexpressionparser static method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.select_signalidset_fromtable", false]], "selectwhere() (sttp.data.expressiontree.expressiontree method)": [[4, "sttp.data.expressiontree.ExpressionTree.selectwhere", false]], "send_servercommand() (sttp.transport.datasubscriber.datasubscriber method)": [[7, "sttp.transport.datasubscriber.DataSubscriber.send_servercommand", false]], "send_servercommand_withmessage() (sttp.transport.datasubscriber.datasubscriber method)": [[7, "sttp.transport.datasubscriber.DataSubscriber.send_servercommand_withmessage", false]], "sequencenumber (sttp.transport.tssc.decoder.decoder attribute)": [[8, "sttp.transport.tssc.decoder.Decoder.sequencenumber", false]], "servercommand (class in sttp.transport.constants)": [[7, "sttp.transport.constants.ServerCommand", false]], "serverresponse (class in sttp.transport.constants)": [[7, "sttp.transport.constants.ServerResponse", false]], "set_buffer() (sttp.transport.tssc.decoder.decoder method)": [[8, "sttp.transport.tssc.decoder.Decoder.set_buffer", false]], "set_compact_stateflags() (sttp.transport.compactmeasurement.compactmeasurement method)": [[7, "sttp.transport.compactmeasurement.CompactMeasurement.set_compact_stateflags", false]], "set_configurationchanged_receiver() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.set_configurationchanged_receiver", false]], "set_connectionestablished_receiver() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.set_connectionestablished_receiver", false]], "set_connectionterminated_receiver() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.set_connectionterminated_receiver", false]], "set_data_starttime_receiver() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.set_data_starttime_receiver", false]], "set_errormessage_logger() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.set_errormessage_logger", false]], "set_historicalreadcomplete_receiver() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.set_historicalreadcomplete_receiver", false]], "set_leapsecond() (sttp.ticks.ticks static method)": [[3, "sttp.ticks.Ticks.set_leapsecond", false]], "set_metadatanotification_receiver() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.set_metadatanotification_receiver", false]], "set_negative_leapsecond() (sttp.ticks.ticks static method)": [[3, "sttp.ticks.Ticks.set_negative_leapsecond", false]], "set_newbufferblock_receiver() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.set_newbufferblock_receiver", false]], "set_newmeasurements_receiver() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.set_newmeasurements_receiver", false]], "set_notification_receiver() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.set_notification_receiver", false]], "set_parsingexception_callback() (sttp.data.filterexpressionparser.filterexpressionparser method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.set_parsingexception_callback", false]], "set_statusmessage_logger() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.set_statusmessage_logger", false]], "set_subscriptionupdated_receiver() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.set_subscriptionupdated_receiver", false]], "set_value() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.set_value", false]], "set_value_byname() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.set_value_byname", false]], "settings (class in sttp.settings)": [[3, "sttp.settings.Settings", false]], "signalid (sttp.metadata.record.measurement.measurementrecord property)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.signalid", false]], "signalid (sttp.transport.bufferblock.bufferblock attribute)": [[7, "sttp.transport.bufferblock.BufferBlock.signalid", false]], "signalid (sttp.transport.measurement.measurement attribute)": [[7, "sttp.transport.measurement.Measurement.signalid", false]], "signalid() (sttp.transport.signalindexcache.signalindexcache method)": [[7, "sttp.transport.signalindexcache.SignalIndexCache.signalid", false]], "signalid_fieldname (sttp.data.tableidfields.tableidfields attribute)": [[4, "sttp.data.tableidfields.TableIDFields.signalid_fieldname", false]], "signalid_measurement_map (sttp.metadata.cache.metadatacache attribute)": [[5, "sttp.metadata.cache.MetadataCache.signalid_measurement_map", false]], "signalids (sttp.transport.signalindexcache.signalindexcache property)": [[7, "sttp.transport.signalindexcache.SignalIndexCache.signalids", false]], "signalindex() (sttp.transport.signalindexcache.signalindexcache method)": [[7, "sttp.transport.signalindexcache.SignalIndexCache.signalindex", false]], "signalindexcache (class in sttp.transport.signalindexcache)": [[7, "sttp.transport.signalindexcache.SignalIndexCache", false]], "signalkind (class in sttp.transport.signalkind)": [[7, "sttp.transport.signalkind.SignalKind", false]], "signalkindenum (class in sttp.transport.signalkind)": [[7, "sttp.transport.signalkind.SignalKindEnum", false]], "signalref_measurement_map (sttp.metadata.cache.metadatacache attribute)": [[5, "sttp.metadata.cache.MetadataCache.signalref_measurement_map", false]], "signalreference (sttp.metadata.record.measurement.measurementrecord property)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.signalreference", false]], "signaltype (class in sttp.metadata.record.measurement)": [[6, "sttp.metadata.record.measurement.SignalType", false]], "signaltype (sttp.metadata.record.measurement.measurementrecord property)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.signaltype", false]], "signaltype() (sttp.transport.signalkind.signalkindenum static method)": [[7, "sttp.transport.signalkind.SignalKindEnum.signaltype", false]], "signaltypename (sttp.metadata.record.measurement.measurementrecord property)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.signaltypename", false]], "single (gsf.empty attribute)": [[0, "gsf.Empty.SINGLE", false]], "single (sttp.data.datatype.datatype attribute)": [[4, "sttp.data.datatype.DataType.SINGLE", false]], "singlevalue() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.singlevalue", false]], "singlevalue_byname() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.singlevalue_byname", false]], "socket_timeout (sttp.config.config attribute)": [[3, "sttp.config.Config.socket_timeout", false]], "socket_timeout (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.SOCKET_TIMEOUT", false]], "socket_timeout (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.socket_timeout", false]], "source (sttp.metadata.record.measurement.measurementrecord property)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.source", false]], "source() (sttp.transport.signalindexcache.signalindexcache method)": [[7, "sttp.transport.signalindexcache.SignalIndexCache.source", false]], "sourceindex (sttp.metadata.record.phasor.phasorrecord property)": [[6, "sttp.metadata.record.phasor.PhasorRecord.sourceindex", false]], "split (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.SPLIT", false]], "sqrt (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.SQRT", false]], "startswith (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.STARTSWITH", false]], "starttime (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.starttime", false]], "starttime (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.STARTTIME", false]], "starttime (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.starttime", false]], "stat (sttp.metadata.record.measurement.signaltype attribute)": [[6, "sttp.metadata.record.measurement.SignalType.STAT", false]], "stateflags (class in sttp.transport.constants)": [[7, "sttp.transport.constants.StateFlags", false]], "stateflags2 (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.STATEFLAGS2", false]], "stateflags7bit32 (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.STATEFLAGS7BIT32", false]], "static_init() (gsf.endianorder.bigendian class method)": [[0, "gsf.endianorder.BigEndian.static_init", false]], "static_init() (gsf.endianorder.littleendian class method)": [[0, "gsf.endianorder.LittleEndian.static_init", false]], "static_init() (gsf.endianorder.nativeendian class method)": [[0, "gsf.endianorder.NativeEndian.static_init", false]], "static_init() (in module gsf)": [[0, "gsf.static_init", false]], "statistic (sttp.transport.signalkind.signalkind attribute)": [[7, "sttp.transport.signalkind.SignalKind.STATISTIC", false]], "status (sttp.transport.signalkind.signalkind attribute)": [[7, "sttp.transport.signalkind.SignalKind.STATUS", false]], "statusmessage() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.statusmessage", false]], "statusmessage_callback (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.statusmessage_callback", false]], "stoptime (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.stoptime", false]], "stoptime (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.STOPTIME", false]], "stoptime (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.stoptime", false]], "strcmp (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.STRCMP", false]], "strcount (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.STRCOUNT", false]], "streamencoder (class in gsf.streamencoder)": [[0, "gsf.streamencoder.StreamEncoder", false]], "string (gsf.empty attribute)": [[0, "gsf.Empty.STRING", false]], "string (sttp.data.constants.expressionvaluetype attribute)": [[4, "sttp.data.constants.ExpressionValueType.STRING", false]], "string (sttp.data.datatype.datatype attribute)": [[4, "sttp.data.datatype.DataType.STRING", false]], "stringvalue() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.stringvalue", false]], "stringvalue() (sttp.data.valueexpression.valueexpression method)": [[4, "sttp.data.valueexpression.ValueExpression.stringvalue", false]], "stringvalue_byname() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.stringvalue_byname", false]], "sttp": [[3, "module-sttp", false]], "sttp.config": [[3, "module-sttp.config", false]], "sttp.data": [[4, "module-sttp.data", false]], "sttp.data.callbackerrorlistener": [[4, "module-sttp.data.callbackerrorlistener", false]], "sttp.data.columnexpression": [[4, "module-sttp.data.columnexpression", false]], "sttp.data.constants": [[4, "module-sttp.data.constants", false]], "sttp.data.datacolumn": [[4, "module-sttp.data.datacolumn", false]], "sttp.data.datarow": [[4, "module-sttp.data.datarow", false]], "sttp.data.dataset": [[4, "module-sttp.data.dataset", false]], "sttp.data.datatable": [[4, "module-sttp.data.datatable", false]], "sttp.data.datatype": [[4, "module-sttp.data.datatype", false]], "sttp.data.errors": [[4, "module-sttp.data.errors", false]], "sttp.data.expression": [[4, "module-sttp.data.expression", false]], "sttp.data.expressiontree": [[4, "module-sttp.data.expressiontree", false]], "sttp.data.filterexpressionparser": [[4, "module-sttp.data.filterexpressionparser", false]], "sttp.data.functionexpression": [[4, "module-sttp.data.functionexpression", false]], "sttp.data.inlistexpression": [[4, "module-sttp.data.inlistexpression", false]], "sttp.data.operatorexpression": [[4, "module-sttp.data.operatorexpression", false]], "sttp.data.orderbyterm": [[4, "module-sttp.data.orderbyterm", false]], "sttp.data.tableidfields": [[4, "module-sttp.data.tableidfields", false]], "sttp.data.unaryexpression": [[4, "module-sttp.data.unaryexpression", false]], "sttp.data.valueexpression": [[4, "module-sttp.data.valueexpression", false]], "sttp.metadata": [[5, "module-sttp.metadata", false]], "sttp.metadata.cache": [[5, "module-sttp.metadata.cache", false]], "sttp.metadata.record": [[6, "module-sttp.metadata.record", false]], "sttp.metadata.record.device": [[6, "module-sttp.metadata.record.device", false]], "sttp.metadata.record.measurement": [[6, "module-sttp.metadata.record.measurement", false]], "sttp.metadata.record.phasor": [[6, "module-sttp.metadata.record.phasor", false]], "sttp.reader": [[3, "module-sttp.reader", false]], "sttp.settings": [[3, "module-sttp.settings", false]], "sttp.subscriber": [[3, "module-sttp.subscriber", false]], "sttp.ticks": [[3, "module-sttp.ticks", false]], "sttp.transport": [[7, "module-sttp.transport", false]], "sttp.transport.bufferblock": [[7, "module-sttp.transport.bufferblock", false]], "sttp.transport.compactmeasurement": [[7, "module-sttp.transport.compactmeasurement", false]], "sttp.transport.constants": [[7, "module-sttp.transport.constants", false]], "sttp.transport.datasubscriber": [[7, "module-sttp.transport.datasubscriber", false]], "sttp.transport.measurement": [[7, "module-sttp.transport.measurement", false]], "sttp.transport.signalindexcache": [[7, "module-sttp.transport.signalindexcache", false]], "sttp.transport.signalkind": [[7, "module-sttp.transport.signalkind", false]], "sttp.transport.subscriberconnector": [[7, "module-sttp.transport.subscriberconnector", false]], "sttp.transport.subscriptioninfo": [[7, "module-sttp.transport.subscriptioninfo", false]], "sttp.transport.tssc": [[8, "module-sttp.transport.tssc", false]], "sttp.transport.tssc.decoder": [[8, "module-sttp.transport.tssc.decoder", false]], "sttp.transport.tssc.pointmetadata": [[8, "module-sttp.transport.tssc.pointmetadata", false]], "sttp.version": [[3, "module-sttp.version", false]], "sttp_source (sttp.version.version attribute)": [[3, "sttp.version.Version.STTP_SOURCE", false]], "sttp_sourceinfo (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.sttp_sourceinfo", false]], "sttp_updatedon (sttp.version.version attribute)": [[3, "sttp.version.Version.STTP_UPDATEDON", false]], "sttp_updatedoninfo (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.sttp_updatedoninfo", false]], "sttp_version (sttp.version.version attribute)": [[3, "sttp.version.Version.STTP_VERSION", false]], "sttp_versioninfo (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.sttp_versioninfo", false]], "subscribe (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.SUBSCRIBE", false]], "subscribe() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.subscribe", false]], "subscribe() (sttp.transport.datasubscriber.datasubscriber method)": [[7, "sttp.transport.datasubscriber.DataSubscriber.subscribe", false]], "subscribed (sttp.subscriber.subscriber property)": [[3, "sttp.subscriber.Subscriber.subscribed", false]], "subscribed (sttp.transport.datasubscriber.datasubscriber property)": [[7, "sttp.transport.datasubscriber.DataSubscriber.subscribed", false]], "subscriber (class in sttp.subscriber)": [[3, "sttp.subscriber.Subscriber", false]], "subscriberconnector (class in sttp.transport.subscriberconnector)": [[7, "sttp.transport.subscriberconnector.SubscriberConnector", false]], "subscriberid (sttp.subscriber.subscriber property)": [[3, "sttp.subscriber.Subscriber.subscriberid", false]], "subscriberid (sttp.transport.datasubscriber.datasubscriber property)": [[7, "sttp.transport.datasubscriber.DataSubscriber.subscriberid", false]], "subscription (sttp.transport.datasubscriber.datasubscriber property)": [[7, "sttp.transport.datasubscriber.DataSubscriber.subscription", false]], "subscriptioninfo (class in sttp.transport.subscriptioninfo)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo", false]], "subscriptionupdated_callback (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.subscriptionupdated_callback", false]], "substr (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.SUBSTR", false]], "subtract (sttp.data.constants.expressionoperatortype attribute)": [[4, "sttp.data.constants.ExpressionOperatorType.SUBTRACT", false]], "succeeded (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.SUCCEEDED", false]], "success (sttp.transport.constants.connectstatus attribute)": [[7, "sttp.transport.constants.ConnectStatus.SUCCESS", false]], "suspectdata (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.SUSPECTDATA", false]], "suspecttime (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.SUSPECTTIME", false]], "swaporder (gsf.endianorder.bigendian attribute)": [[0, "gsf.endianorder.BigEndian.swaporder", false]], "swaporder (gsf.endianorder.littleendian attribute)": [[0, "gsf.endianorder.LittleEndian.swaporder", false]], "swaporder (gsf.endianorder.nativeendian attribute)": [[0, "gsf.endianorder.NativeEndian.swaporder", false]], "syntaxerror() (sttp.data.callbackerrorlistener.callbackerrorlistener method)": [[4, "sttp.data.callbackerrorlistener.CallbackErrorListener.syntaxError", false]], "systemerror (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.SYSTEMERROR", false]], "systemissue (sttp.transport.compactmeasurement.compactstateflags attribute)": [[7, "sttp.transport.compactmeasurement.CompactStateFlags.SYSTEMISSUE", false]], "systemwarning (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.SYSTEMWARNING", false]], "table() (sttp.data.dataset.dataset method)": [[4, "sttp.data.dataset.DataSet.table", false]], "table() (sttp.data.filterexpressionparser.filterexpressionparser method)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.table", false]], "tablecount (sttp.data.dataset.dataset property)": [[4, "sttp.data.dataset.DataSet.tablecount", false]], "tableidfields (class in sttp.data.tableidfields)": [[4, "sttp.data.tableidfields.TableIDFields", false]], "tableidfields_map (sttp.data.filterexpressionparser.filterexpressionparser attribute)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.tableidfields_map", false]], "tablename (sttp.data.expressiontree.expressiontree attribute)": [[4, "sttp.data.expressiontree.ExpressionTree.tablename", false]], "tablenames() (sttp.data.dataset.dataset method)": [[4, "sttp.data.dataset.DataSet.tablenames", false]], "tables() (sttp.data.dataset.dataset method)": [[4, "sttp.data.dataset.DataSet.tables", false]], "target_byteorder (gsf.endianorder.bigendian attribute)": [[0, "gsf.endianorder.BigEndian.target_byteorder", false]], "target_byteorder (gsf.endianorder.littleendian attribute)": [[0, "gsf.endianorder.LittleEndian.target_byteorder", false]], "target_byteorder (gsf.endianorder.nativeendian attribute)": [[0, "gsf.endianorder.NativeEndian.target_byteorder", false]], "throttled (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.throttled", false]], "throttled (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.THROTTLED", false]], "throttled (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.throttled", false]], "ticks (class in sttp.ticks)": [[3, "sttp.ticks.Ticks", false]], "ticks (gsf.empty attribute)": [[0, "gsf.Empty.TICKS", false]], "timedelta1forward (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.TIMEDELTA1FORWARD", false]], "timedelta1reverse (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.TIMEDELTA1REVERSE", false]], "timedelta2forward (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.TIMEDELTA2FORWARD", false]], "timedelta2reverse (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.TIMEDELTA2REVERSE", false]], "timedelta3forward (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.TIMEDELTA3FORWARD", false]], "timedelta3reverse (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.TIMEDELTA3REVERSE", false]], "timedelta4forward (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.TIMEDELTA4FORWARD", false]], "timedelta4reverse (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.TIMEDELTA4REVERSE", false]], "timeindex (sttp.transport.compactmeasurement.compactstateflags attribute)": [[7, "sttp.transport.compactmeasurement.CompactStateFlags.TIMEINDEX", false]], "timeinterval (class in sttp.data.constants)": [[4, "sttp.data.constants.TimeInterval", false]], "timequality (sttp.transport.compactmeasurement.compactstateflags attribute)": [[7, "sttp.transport.compactmeasurement.CompactStateFlags.TIMEQUALITY", false]], "timestamp (sttp.transport.measurement.measurement attribute)": [[7, "sttp.transport.measurement.Measurement.timestamp", false]], "timestamp2 (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.TIMESTAMP2", false]], "timestampvalue (sttp.transport.measurement.measurement property)": [[7, "sttp.transport.measurement.Measurement.timestampvalue", false]], "timestampvalue() (sttp.ticks.ticks static method)": [[3, "sttp.ticks.Ticks.timestampvalue", false]], "timexor7bit (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.TIMEXOR7BIT", false]], "tls (sttp.transport.constants.securitymode attribute)": [[7, "sttp.transport.constants.SecurityMode.TLS", false]], "to_datetime() (sttp.ticks.ticks static method)": [[3, "sttp.ticks.Ticks.to_datetime", false]], "to_float16() (gsf.endianorder.nativeendian class method)": [[0, "gsf.endianorder.NativeEndian.to_float16", false]], "to_float32() (gsf.endianorder.nativeendian class method)": [[0, "gsf.endianorder.NativeEndian.to_float32", false]], "to_float64() (gsf.endianorder.nativeendian class method)": [[0, "gsf.endianorder.NativeEndian.to_float64", false]], "to_int16() (gsf.endianorder.nativeendian class method)": [[0, "gsf.endianorder.NativeEndian.to_int16", false]], "to_int32() (gsf.endianorder.nativeendian class method)": [[0, "gsf.endianorder.NativeEndian.to_int32", false]], "to_int64() (gsf.endianorder.nativeendian class method)": [[0, "gsf.endianorder.NativeEndian.to_int64", false]], "to_shortstring() (sttp.ticks.ticks static method)": [[3, "sttp.ticks.Ticks.to_shortstring", false]], "to_string() (sttp.ticks.ticks static method)": [[3, "sttp.ticks.Ticks.to_string", false]], "to_uint16() (gsf.endianorder.nativeendian class method)": [[0, "gsf.endianorder.NativeEndian.to_uint16", false]], "to_uint32() (gsf.endianorder.nativeendian class method)": [[0, "gsf.endianorder.NativeEndian.to_uint32", false]], "to_uint64() (gsf.endianorder.nativeendian class method)": [[0, "gsf.endianorder.NativeEndian.to_uint64", false]], "toplimit (sttp.data.expressiontree.expressiontree attribute)": [[4, "sttp.data.expressiontree.ExpressionTree.toplimit", false]], "total_commandchannel_bytesreceived (sttp.subscriber.subscriber property)": [[3, "sttp.subscriber.Subscriber.total_commandchannel_bytesreceived", false]], "total_commandchannel_bytesreceived (sttp.transport.datasubscriber.datasubscriber property)": [[7, "sttp.transport.datasubscriber.DataSubscriber.total_commandchannel_bytesreceived", false]], "total_datachannel_bytesreceived (sttp.subscriber.subscriber property)": [[3, "sttp.subscriber.Subscriber.total_datachannel_bytesreceived", false]], "total_datachannel_bytesreceived (sttp.transport.datasubscriber.datasubscriber property)": [[7, "sttp.transport.datasubscriber.DataSubscriber.total_datachannel_bytesreceived", false]], "total_measurementsreceived (sttp.subscriber.subscriber property)": [[3, "sttp.subscriber.Subscriber.total_measurementsreceived", false]], "total_measurementsreceived (sttp.transport.datasubscriber.datasubscriber property)": [[7, "sttp.transport.datasubscriber.DataSubscriber.total_measurementsreceived", false]], "track_filteredrows (sttp.data.filterexpressionparser.filterexpressionparser attribute)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.track_filteredrows", false]], "track_filteredsignalids (sttp.data.filterexpressionparser.filterexpressionparser attribute)": [[4, "sttp.data.filterexpressionparser.FilterExpressionParser.track_filteredsignalids", false]], "trim (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.TRIM", false]], "trimleft (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.TRIMLEFT", false]], "trimright (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.TRIMRIGHT", false]], "truevalue (in module sttp.data.valueexpression)": [[4, "sttp.data.valueexpression.TRUEVALUE", false]], "try_get_measurement() (sttp.transport.tssc.decoder.decoder method)": [[8, "sttp.transport.tssc.decoder.Decoder.try_get_measurement", false]], "tssc (sttp.transport.constants.compressionmodes attribute)": [[7, "sttp.transport.constants.CompressionModes.TSSC", false]], "type (sttp.metadata.record.phasor.phasorrecord property)": [[6, "sttp.metadata.record.phasor.PhasorRecord.type", false]], "udpdatachannel (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.UDPDATACHANNEL", false]], "udpdatachannel (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.udpdatachannel", false]], "udpinterface (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.udpinterface", false]], "udpport (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.udpport", false]], "uint16 (gsf.bytesize attribute)": [[0, "gsf.ByteSize.UINT16", false]], "uint16 (gsf.empty attribute)": [[0, "gsf.Empty.UINT16", false]], "uint16 (sttp.data.datatype.datatype attribute)": [[4, "sttp.data.datatype.DataType.UINT16", false]], "uint16value() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.uint16value", false]], "uint16value_byname() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.uint16value_byname", false]], "uint32 (gsf.bytesize attribute)": [[0, "gsf.ByteSize.UINT32", false]], "uint32 (gsf.empty attribute)": [[0, "gsf.Empty.UINT32", false]], "uint32 (sttp.data.datatype.datatype attribute)": [[4, "sttp.data.datatype.DataType.UINT32", false]], "uint32value() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.uint32value", false]], "uint32value_byname() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.uint32value_byname", false]], "uint64 (gsf.bytesize attribute)": [[0, "gsf.ByteSize.UINT64", false]], "uint64 (gsf.empty attribute)": [[0, "gsf.Empty.UINT64", false]], "uint64 (sttp.data.datatype.datatype attribute)": [[4, "sttp.data.datatype.DataType.UINT64", false]], "uint64value() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.uint64value", false]], "uint64value_byname() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.uint64value_byname", false]], "uint8 (gsf.bytesize attribute)": [[0, "gsf.ByteSize.UINT8", false]], "uint8 (gsf.empty attribute)": [[0, "gsf.Empty.UINT8", false]], "uint8 (sttp.data.datatype.datatype attribute)": [[4, "sttp.data.datatype.DataType.UINT8", false]], "uint8value() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.uint8value", false]], "uint8value_byname() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.uint8value_byname", false]], "unary (sttp.data.constants.expressiontype attribute)": [[4, "sttp.data.constants.ExpressionType.UNARY", false]], "unaryexpression (class in sttp.data.unaryexpression)": [[4, "sttp.data.unaryexpression.UnaryExpression", false]], "unarytype (sttp.data.unaryexpression.unaryexpression property)": [[4, "sttp.data.unaryexpression.UnaryExpression.unarytype", false]], "undefined (sttp.data.constants.expressionvaluetype attribute)": [[4, "sttp.data.constants.ExpressionValueType.UNDEFINED", false]], "underrangeerror (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.UNDERRANGEERROR", false]], "unixbaseoffset (sttp.ticks.ticks attribute)": [[3, "sttp.ticks.Ticks.UNIXBASEOFFSET", false]], "unkn (sttp.metadata.record.measurement.signaltype attribute)": [[6, "sttp.metadata.record.measurement.SignalType.UNKN", false]], "unknown (sttp.transport.signalkind.signalkind attribute)": [[7, "sttp.transport.signalkind.SignalKind.UNKNOWN", false]], "unsubscribe (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.UNSUBSCRIBE", false]], "unsubscribe() (sttp.subscriber.subscriber method)": [[3, "sttp.subscriber.Subscriber.unsubscribe", false]], "unsubscribe() (sttp.transport.datasubscriber.datasubscriber method)": [[7, "sttp.transport.datasubscriber.DataSubscriber.unsubscribe", false]], "updatebasetimes (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.UPDATEBASETIMES", false]], "updatecipherkeys (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.UPDATECIPHERKEYS", false]], "updatedon (sttp.metadata.record.device.devicerecord property)": [[6, "sttp.metadata.record.device.DeviceRecord.updatedon", false]], "updatedon (sttp.metadata.record.measurement.measurementrecord property)": [[6, "sttp.metadata.record.measurement.MeasurementRecord.updatedon", false]], "updatedon (sttp.metadata.record.phasor.phasorrecord property)": [[6, "sttp.metadata.record.phasor.PhasorRecord.updatedon", false]], "updateprocessinginterval (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.UPDATEPROCESSINGINTERVAL", false]], "updatesignalindexcache (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.UPDATESIGNALINDEXCACHE", false]], "upper (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.UPPER", false]], "upsampled (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.UPSAMPLED", false]], "use_localclock_as_realtime (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.USE_LOCALCLOCK_AS_REALTIME", false]], "use_millisecondresolution (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.use_millisecondresolution", false]], "use_millisecondresolution (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.USE_MILLISECONDRESOLUTION", false]], "use_millisecondresolution (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.use_millisecondresolution", false]], "uselocalclockasrealtime (sttp.settings.settings attribute)": [[3, "sttp.settings.Settings.uselocalclockasrealtime", false]], "uselocalclockasrealtime (sttp.transport.subscriptioninfo.subscriptioninfo attribute)": [[7, "sttp.transport.subscriptioninfo.SubscriptionInfo.uselocalclockasrealtime", false]], "usercommand00 (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.USERCOMMAND00", false]], "usercommand01 (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.USERCOMMAND01", false]], "usercommand02 (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.USERCOMMAND02", false]], "usercommand03 (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.USERCOMMAND03", false]], "usercommand04 (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.USERCOMMAND04", false]], "usercommand05 (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.USERCOMMAND05", false]], "usercommand06 (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.USERCOMMAND06", false]], "usercommand07 (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.USERCOMMAND07", false]], "usercommand08 (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.USERCOMMAND08", false]], "usercommand09 (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.USERCOMMAND09", false]], "usercommand10 (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.USERCOMMAND10", false]], "usercommand11 (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.USERCOMMAND11", false]], "usercommand12 (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.USERCOMMAND12", false]], "usercommand13 (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.USERCOMMAND13", false]], "usercommand14 (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.USERCOMMAND14", false]], "usercommand15 (sttp.transport.constants.servercommand attribute)": [[7, "sttp.transport.constants.ServerCommand.USERCOMMAND15", false]], "userdefinedflag1 (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.USERDEFINEDFLAG1", false]], "userdefinedflag2 (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.USERDEFINEDFLAG2", false]], "userdefinedflag3 (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.USERDEFINEDFLAG3", false]], "userdefinedflag4 (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.USERDEFINEDFLAG4", false]], "userdefinedflag5 (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.USERDEFINEDFLAG5", false]], "userresponse00 (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.USERRESPONSE00", false]], "userresponse01 (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.USERRESPONSE01", false]], "userresponse02 (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.USERRESPONSE02", false]], "userresponse03 (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.USERRESPONSE03", false]], "userresponse04 (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.USERRESPONSE04", false]], "userresponse05 (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.USERRESPONSE05", false]], "userresponse06 (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.USERRESPONSE06", false]], "userresponse07 (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.USERRESPONSE07", false]], "userresponse08 (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.USERRESPONSE08", false]], "userresponse09 (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.USERRESPONSE09", false]], "userresponse10 (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.USERRESPONSE10", false]], "userresponse11 (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.USERRESPONSE11", false]], "userresponse12 (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.USERRESPONSE12", false]], "userresponse13 (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.USERRESPONSE13", false]], "userresponse14 (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.USERRESPONSE14", false]], "userresponse15 (sttp.transport.constants.serverresponse attribute)": [[7, "sttp.transport.constants.ServerResponse.USERRESPONSE15", false]], "utcnow (sttp.data.constants.expressionfunctiontype attribute)": [[4, "sttp.data.constants.ExpressionFunctionType.UTCNOW", false]], "utcnow() (sttp.ticks.ticks static method)": [[3, "sttp.ticks.Ticks.utcnow", false]], "utf16be (sttp.transport.constants.operationalencoding attribute)": [[7, "sttp.transport.constants.OperationalEncoding.UTF16BE", false]], "utf16le (sttp.transport.constants.operationalencoding attribute)": [[7, "sttp.transport.constants.OperationalEncoding.UTF16LE", false]], "utf8 (sttp.transport.constants.operationalencoding attribute)": [[7, "sttp.transport.constants.OperationalEncoding.UTF8", false]], "validate (class in gsf)": [[0, "gsf.Validate", false]], "value (sttp.data.constants.expressiontype attribute)": [[4, "sttp.data.constants.ExpressionType.VALUE", false]], "value (sttp.data.inlistexpression.inlistexpression property)": [[4, "sttp.data.inlistexpression.InListExpression.value", false]], "value (sttp.data.unaryexpression.unaryexpression property)": [[4, "sttp.data.unaryexpression.UnaryExpression.value", false]], "value (sttp.data.valueexpression.valueexpression property)": [[4, "sttp.data.valueexpression.ValueExpression.value", false]], "value (sttp.transport.measurement.measurement attribute)": [[7, "sttp.transport.measurement.Measurement.value", false]], "value() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.value", false]], "value1 (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.VALUE1", false]], "value2 (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.VALUE2", false]], "value3 (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.VALUE3", false]], "value_as_string() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.value_as_string", false]], "value_as_string_byname() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.value_as_string_byname", false]], "value_buffersize (gsf.binarystream.binarystream attribute)": [[0, "gsf.binarystream.BinaryStream.VALUE_BUFFERSIZE", false]], "value_byname() (sttp.data.datarow.datarow method)": [[4, "sttp.data.datarow.DataRow.value_byname", false]], "valueexpression (class in sttp.data.valueexpression)": [[4, "sttp.data.valueexpression.ValueExpression", false]], "valuemask (sttp.ticks.ticks attribute)": [[3, "sttp.ticks.Ticks.VALUEMASK", false]], "valuetype (sttp.data.valueexpression.valueexpression property)": [[4, "sttp.data.valueexpression.ValueExpression.valuetype", false]], "valuexor12 (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.VALUEXOR12", false]], "valuexor16 (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.VALUEXOR16", false]], "valuexor20 (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.VALUEXOR20", false]], "valuexor24 (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.VALUEXOR24", false]], "valuexor28 (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.VALUEXOR28", false]], "valuexor32 (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.VALUEXOR32", false]], "valuexor4 (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.VALUEXOR4", false]], "valuexor8 (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.VALUEXOR8", false]], "valuezero (sttp.transport.tssc.pointmetadata.codewords attribute)": [[8, "sttp.transport.tssc.pointmetadata.CodeWords.VALUEZERO", false]], "vendoracronym (sttp.metadata.record.device.devicerecord property)": [[6, "sttp.metadata.record.device.DeviceRecord.vendoracronym", false]], "vendordevicename (sttp.metadata.record.device.devicerecord property)": [[6, "sttp.metadata.record.device.DeviceRecord.vendordevicename", false]], "version (class in sttp.version)": [[3, "sttp.version.Version", false]], "version (sttp.config.config attribute)": [[3, "sttp.config.Config.version", false]], "version (sttp.transport.constants.defaults attribute)": [[7, "sttp.transport.constants.Defaults.VERSION", false]], "version (sttp.transport.datasubscriber.datasubscriber attribute)": [[7, "sttp.transport.datasubscriber.DataSubscriber.version", false]], "versionmask (sttp.transport.constants.operationalmodes attribute)": [[7, "sttp.transport.constants.OperationalModes.VERSIONMASK", false]], "virtual() (in module gsf)": [[0, "gsf.virtual", false]], "vpha (sttp.metadata.record.measurement.signaltype attribute)": [[6, "sttp.metadata.record.measurement.SignalType.VPHA", false]], "vphm (sttp.metadata.record.measurement.signaltype attribute)": [[6, "sttp.metadata.record.measurement.SignalType.VPHM", false]], "warninghigh (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.WARNINGHIGH", false]], "warninglow (sttp.transport.constants.stateflags attribute)": [[7, "sttp.transport.constants.StateFlags.WARNINGLOW", false]], "week (sttp.data.constants.timeinterval attribute)": [[4, "sttp.data.constants.TimeInterval.WEEK", false]], "weekday (sttp.data.constants.timeinterval attribute)": [[4, "sttp.data.constants.TimeInterval.WEEKDAY", false]], "write() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.write", false]], "write() (gsf.streamencoder.streamencoder method)": [[0, "gsf.streamencoder.StreamEncoder.write", false]], "write7bit_int32() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.write7bit_int32", false]], "write7bit_int64() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.write7bit_int64", false]], "write7bit_uint32() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.write7bit_uint32", false]], "write7bit_uint32() (gsf.streamencoder.streamencoder method)": [[0, "gsf.streamencoder.StreamEncoder.write7bit_uint32", false]], "write7bit_uint64() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.write7bit_uint64", false]], "write7bit_uint64() (gsf.streamencoder.streamencoder method)": [[0, "gsf.streamencoder.StreamEncoder.write7bit_uint64", false]], "write_bool() (gsf.streamencoder.streamencoder method)": [[0, "gsf.streamencoder.StreamEncoder.write_bool", false]], "write_boolean() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.write_boolean", false]], "write_buffer() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.write_buffer", false]], "write_byte() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.write_byte", false]], "write_byte() (gsf.streamencoder.streamencoder method)": [[0, "gsf.streamencoder.StreamEncoder.write_byte", false]], "write_code() (sttp.transport.tssc.pointmetadata.pointmetadata method)": [[8, "sttp.transport.tssc.pointmetadata.PointMetadata.write_code", false]], "write_guid() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.write_guid", false]], "write_int16() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.write_int16", false]], "write_int16() (gsf.encoding7bit.encoding7bit static method)": [[0, "gsf.encoding7bit.Encoding7Bit.write_int16", false]], "write_int16() (gsf.streamencoder.streamencoder method)": [[0, "gsf.streamencoder.StreamEncoder.write_int16", false]], "write_int32() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.write_int32", false]], "write_int32() (gsf.encoding7bit.encoding7bit static method)": [[0, "gsf.encoding7bit.Encoding7Bit.write_int32", false]], "write_int32() (gsf.streamencoder.streamencoder method)": [[0, "gsf.streamencoder.StreamEncoder.write_int32", false]], "write_int64() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.write_int64", false]], "write_int64() (gsf.encoding7bit.encoding7bit static method)": [[0, "gsf.encoding7bit.Encoding7Bit.write_int64", false]], "write_int64() (gsf.streamencoder.streamencoder method)": [[0, "gsf.streamencoder.StreamEncoder.write_int64", false]], "write_string() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.write_string", false]], "write_uint16() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.write_uint16", false]], "write_uint16() (gsf.encoding7bit.encoding7bit static method)": [[0, "gsf.encoding7bit.Encoding7Bit.write_uint16", false]], "write_uint16() (gsf.streamencoder.streamencoder method)": [[0, "gsf.streamencoder.StreamEncoder.write_uint16", false]], "write_uint32() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.write_uint32", false]], "write_uint32() (gsf.encoding7bit.encoding7bit static method)": [[0, "gsf.encoding7bit.Encoding7Bit.write_uint32", false]], "write_uint32() (gsf.streamencoder.streamencoder method)": [[0, "gsf.streamencoder.StreamEncoder.write_uint32", false]], "write_uint64() (gsf.binarystream.binarystream method)": [[0, "gsf.binarystream.BinaryStream.write_uint64", false]], "write_uint64() (gsf.encoding7bit.encoding7bit static method)": [[0, "gsf.encoding7bit.Encoding7Bit.write_uint64", false]], "write_uint64() (gsf.streamencoder.streamencoder method)": [[0, "gsf.streamencoder.StreamEncoder.write_uint64", false]], "xmlschema_namespace (in module sttp.data.dataset)": [[4, "sttp.data.dataset.XMLSCHEMA_NAMESPACE", false]], "xsdformat() (in module sttp.data.dataset)": [[4, "sttp.data.dataset.xsdformat", false]], "year (sttp.data.constants.timeinterval attribute)": [[4, "sttp.data.constants.TimeInterval.YEAR", false]]}, "objects": {"": [[0, 0, 0, "-", "gsf"], [3, 0, 0, "-", "sttp"]], "gsf": [[0, 1, 1, "", "ByteSize"], [0, 1, 1, "", "Convert"], [0, 1, 1, "", "Empty"], [0, 1, 1, "", "Limits"], [0, 1, 1, "", "Validate"], [0, 0, 0, "-", "binarystream"], [0, 0, 0, "-", "encoding7bit"], [0, 0, 0, "-", "endianorder"], [0, 4, 1, "", "normalize_enumname"], [0, 4, 1, "", "override"], [0, 4, 1, "", "static_init"], [0, 0, 0, "-", "streamencoder"], [0, 4, 1, "", "virtual"]], "gsf.ByteSize": [[0, 2, 1, "", "FLOAT16"], [0, 2, 1, "", "FLOAT32"], [0, 2, 1, "", "FLOAT64"], [0, 2, 1, "", "INT16"], [0, 2, 1, "", "INT32"], [0, 2, 1, "", "INT64"], [0, 2, 1, "", "INT8"], [0, 2, 1, "", "UINT16"], [0, 2, 1, "", "UINT32"], [0, 2, 1, "", "UINT64"], [0, 2, 1, "", "UINT8"]], "gsf.Convert": [[0, 3, 1, "", "from_str"]], "gsf.Empty": [[0, 2, 1, "", "DATETIME"], [0, 2, 1, "", "DECIMAL"], [0, 2, 1, "", "DOUBLE"], [0, 2, 1, "", "GUID"], [0, 2, 1, "", "INT16"], [0, 2, 1, "", "INT32"], [0, 2, 1, "", "INT64"], [0, 2, 1, "", "INT8"], [0, 2, 1, "", "SINGLE"], [0, 2, 1, "", "STRING"], [0, 2, 1, "", "TICKS"], [0, 2, 1, "", "UINT16"], [0, 2, 1, "", "UINT32"], [0, 2, 1, "", "UINT64"], [0, 2, 1, "", "UINT8"]], "gsf.Limits": [[0, 2, 1, "", "MAXBYTE"], [0, 2, 1, "", "MAXINT16"], [0, 2, 1, "", "MAXINT32"], [0, 2, 1, "", "MAXINT64"], [0, 2, 1, "", "MAXTICKS"], [0, 2, 1, "", "MAXUINT16"], [0, 2, 1, "", "MAXUINT32"], [0, 2, 1, "", "MAXUINT64"], [0, 2, 1, "", "MININT16"], [0, 2, 1, "", "MININT32"], [0, 2, 1, "", "MININT64"]], "gsf.Validate": [[0, 3, 1, "", "parameters"]], "gsf.binarystream": [[0, 1, 1, "", "BinaryStream"]], "gsf.binarystream.BinaryStream": [[0, 2, 1, "", "IO_BUFFERSIZE"], [0, 2, 1, "", "VALUE_BUFFERSIZE"], [0, 3, 1, "", "flush"], [0, 3, 1, "", "read"], [0, 3, 1, "", "read7bit_int32"], [0, 3, 1, "", "read7bit_int64"], [0, 3, 1, "", "read7bit_uint32"], [0, 3, 1, "", "read7bit_uint64"], [0, 3, 1, "", "read_all"], [0, 3, 1, "", "read_boolean"], [0, 3, 1, "", "read_buffer"], [0, 3, 1, "", "read_byte"], [0, 3, 1, "", "read_bytes"], [0, 3, 1, "", "read_guid"], [0, 3, 1, "", "read_int16"], [0, 3, 1, "", "read_int32"], [0, 3, 1, "", "read_int64"], [0, 3, 1, "", "read_string"], [0, 3, 1, "", "read_uint16"], [0, 3, 1, "", "read_uint32"], [0, 3, 1, "", "read_uint64"], [0, 3, 1, "", "write"], [0, 3, 1, "", "write7bit_int32"], [0, 3, 1, "", "write7bit_int64"], [0, 3, 1, "", "write7bit_uint32"], [0, 3, 1, "", "write7bit_uint64"], [0, 3, 1, "", "write_boolean"], [0, 3, 1, "", "write_buffer"], [0, 3, 1, "", "write_byte"], [0, 3, 1, "", "write_guid"], [0, 3, 1, "", "write_int16"], [0, 3, 1, "", "write_int32"], [0, 3, 1, "", "write_int64"], [0, 3, 1, "", "write_string"], [0, 3, 1, "", "write_uint16"], [0, 3, 1, "", "write_uint32"], [0, 3, 1, "", "write_uint64"]], "gsf.encoding7bit": [[0, 1, 1, "", "Encoding7Bit"]], "gsf.encoding7bit.Encoding7Bit": [[0, 3, 1, "", "read_int16"], [0, 3, 1, "", "read_int32"], [0, 3, 1, "", "read_int64"], [0, 3, 1, "", "read_uint16"], [0, 3, 1, "", "read_uint32"], [0, 3, 1, "", "read_uint64"], [0, 3, 1, "", "write_int16"], [0, 3, 1, "", "write_int32"], [0, 3, 1, "", "write_int64"], [0, 3, 1, "", "write_uint16"], [0, 3, 1, "", "write_uint32"], [0, 3, 1, "", "write_uint64"]], "gsf.endianorder": [[0, 1, 1, "", "BigEndian"], [0, 1, 1, "", "LittleEndian"], [0, 1, 1, "", "NativeEndian"]], "gsf.endianorder.BigEndian": [[0, 3, 1, "", "static_init"], [0, 2, 1, "", "swaporder"], [0, 2, 1, "", "target_byteorder"]], "gsf.endianorder.LittleEndian": [[0, 3, 1, "", "static_init"], [0, 2, 1, "", "swaporder"], [0, 2, 1, "", "target_byteorder"]], "gsf.endianorder.NativeEndian": [[0, 3, 1, "", "from_float16"], [0, 3, 1, "", "from_float32"], [0, 3, 1, "", "from_float64"], [0, 3, 1, "", "from_int16"], [0, 3, 1, "", "from_int32"], [0, 3, 1, "", "from_int64"], [0, 3, 1, "", "from_uint16"], [0, 3, 1, "", "from_uint32"], [0, 3, 1, "", "from_uint64"], [0, 3, 1, "", "static_init"], [0, 2, 1, "", "swaporder"], [0, 2, 1, "", "target_byteorder"], [0, 3, 1, "", "to_float16"], [0, 3, 1, "", "to_float32"], [0, 3, 1, "", "to_float64"], [0, 3, 1, "", "to_int16"], [0, 3, 1, "", "to_int32"], [0, 3, 1, "", "to_int64"], [0, 3, 1, "", "to_uint16"], [0, 3, 1, "", "to_uint32"], [0, 3, 1, "", "to_uint64"]], "gsf.streamencoder": [[0, 1, 1, "", "StreamEncoder"]], "gsf.streamencoder.StreamEncoder": [[0, 5, 1, "", "default_byteorder"], [0, 3, 1, "", "read"], [0, 3, 1, "", "read7bit_uint32"], [0, 3, 1, "", "read7bit_uint64"], [0, 3, 1, "", "read_bool"], [0, 3, 1, "", "read_byte"], [0, 3, 1, "", "read_int16"], [0, 3, 1, "", "read_int32"], [0, 3, 1, "", "read_int64"], [0, 3, 1, "", "read_uint16"], [0, 3, 1, "", "read_uint32"], [0, 3, 1, "", "read_uint64"], [0, 3, 1, "", "write"], [0, 3, 1, "", "write7bit_uint32"], [0, 3, 1, "", "write7bit_uint64"], [0, 3, 1, "", "write_bool"], [0, 3, 1, "", "write_byte"], [0, 3, 1, "", "write_int16"], [0, 3, 1, "", "write_int32"], [0, 3, 1, "", "write_int64"], [0, 3, 1, "", "write_uint16"], [0, 3, 1, "", "write_uint32"], [0, 3, 1, "", "write_uint64"]], "sttp": [[3, 0, 0, "-", "config"], [4, 0, 0, "-", "data"], [5, 0, 0, "-", "metadata"], [3, 0, 0, "-", "reader"], [3, 0, 0, "-", "settings"], [3, 0, 0, "-", "subscriber"], [3, 0, 0, "-", "ticks"], [7, 0, 0, "-", "transport"], [3, 0, 0, "-", "version"]], "sttp.config": [[3, 1, 1, "", "Config"]], "sttp.config.Config": [[3, 2, 1, "", "DEFAULT_AUTORECONNECT"], [3, 2, 1, "", "DEFAULT_AUTOREQUESTMETADATA"], [3, 2, 1, "", "DEFAULT_AUTOSUBSCRIBE"], [3, 2, 1, "", "DEFAULT_COMPRESS_METADATA"], [3, 2, 1, "", "DEFAULT_COMPRESS_PAYLOADDATA"], [3, 2, 1, "", "DEFAULT_COMPRESS_SIGNALINDEXCACHE"], [3, 2, 1, "", "DEFAULT_MAXRETRIES"], [3, 2, 1, "", "DEFAULT_MAXRETRYINTERVAL"], [3, 2, 1, "", "DEFAULT_METADATAFILTERS"], [3, 2, 1, "", "DEFAULT_RETRYINTERVAL"], [3, 2, 1, "", "DEFAULT_SOCKET_TIMEOUT"], [3, 2, 1, "", "DEFAULT_VERSION"], [3, 2, 1, "", "autoreconnect"], [3, 2, 1, "", "autorequestmetadata"], [3, 2, 1, "", "autosubscribe"], [3, 2, 1, "", "compress_metadata"], [3, 2, 1, "", "compress_payloaddata"], [3, 2, 1, "", "compress_signalindexcache"], [3, 2, 1, "", "maxretries"], [3, 2, 1, "", "maxretryinterval"], [3, 2, 1, "", "metadatafilters"], [3, 2, 1, "", "retryinterval"], [3, 2, 1, "", "socket_timeout"], [3, 2, 1, "", "version"]], "sttp.data": [[4, 0, 0, "-", "callbackerrorlistener"], [4, 0, 0, "-", "columnexpression"], [4, 0, 0, "-", "constants"], [4, 0, 0, "-", "datacolumn"], [4, 0, 0, "-", "datarow"], [4, 0, 0, "-", "dataset"], [4, 0, 0, "-", "datatable"], [4, 0, 0, "-", "datatype"], [4, 0, 0, "-", "errors"], [4, 0, 0, "-", "expression"], [4, 0, 0, "-", "expressiontree"], [4, 0, 0, "-", "filterexpressionparser"], [4, 0, 0, "-", "functionexpression"], [4, 0, 0, "-", "inlistexpression"], [4, 0, 0, "-", "operatorexpression"], [4, 0, 0, "-", "orderbyterm"], [4, 0, 0, "-", "tableidfields"], [4, 0, 0, "-", "unaryexpression"], [4, 0, 0, "-", "valueexpression"]], "sttp.data.callbackerrorlistener": [[4, 1, 1, "", "CallbackErrorListener"]], "sttp.data.callbackerrorlistener.CallbackErrorListener": [[4, 2, 1, "", "parsingexception_callback"], [4, 3, 1, "", "syntaxError"]], "sttp.data.columnexpression": [[4, 1, 1, "", "ColumnExpression"]], "sttp.data.columnexpression.ColumnExpression": [[4, 5, 1, "", "datacolumn"], [4, 5, 1, "", "expressiontype"]], "sttp.data.constants": [[4, 6, 1, "", "EXPRESSIONVALUETYPELEN"], [4, 1, 1, "", "ExpressionFunctionType"], [4, 1, 1, "", "ExpressionOperatorType"], [4, 1, 1, "", "ExpressionType"], [4, 1, 1, "", "ExpressionUnaryType"], [4, 1, 1, "", "ExpressionValueType"], [4, 1, 1, "", "TimeInterval"], [4, 4, 1, "", "derive_arithmetic_operationvaluetype"], [4, 4, 1, "", "derive_arithmetic_operationvaluetype_fromboolean"], [4, 4, 1, "", "derive_arithmetic_operationvaluetype_fromdecimal"], [4, 4, 1, "", "derive_arithmetic_operationvaluetype_fromdouble"], [4, 4, 1, "", "derive_arithmetic_operationvaluetype_fromint32"], [4, 4, 1, "", "derive_arithmetic_operationvaluetype_fromint64"], [4, 4, 1, "", "derive_boolean_operationvaluetype"], [4, 4, 1, "", "derive_comparison_operationvaluetype"], [4, 4, 1, "", "derive_comparison_operationvaluetype_fromboolean"], [4, 4, 1, "", "derive_comparison_operationvaluetype_fromdatetime"], [4, 4, 1, "", "derive_comparison_operationvaluetype_fromdecimal"], [4, 4, 1, "", "derive_comparison_operationvaluetype_fromdouble"], [4, 4, 1, "", "derive_comparison_operationvaluetype_fromguid"], [4, 4, 1, "", "derive_comparison_operationvaluetype_fromint32"], [4, 4, 1, "", "derive_comparison_operationvaluetype_fromint64"], [4, 4, 1, "", "derive_integer_operationvaluetype"], [4, 4, 1, "", "derive_integer_operationvaluetype_fromboolean"], [4, 4, 1, "", "derive_integer_operationvaluetype_fromint32"], [4, 4, 1, "", "derive_integer_operationvaluetype_fromint64"], [4, 4, 1, "", "derive_operationvaluetype"], [4, 4, 1, "", "is_integertype"], [4, 4, 1, "", "is_numerictype"]], "sttp.data.constants.ExpressionFunctionType": [[4, 2, 1, "", "ABS"], [4, 2, 1, "", "CEILING"], [4, 2, 1, "", "COALESCE"], [4, 2, 1, "", "CONTAINS"], [4, 2, 1, "", "CONVERT"], [4, 2, 1, "", "DATEADD"], [4, 2, 1, "", "DATEDIFF"], [4, 2, 1, "", "DATEPART"], [4, 2, 1, "", "ENDSWITH"], [4, 2, 1, "", "FLOOR"], [4, 2, 1, "", "IIF"], [4, 2, 1, "", "INDEXOF"], [4, 2, 1, "", "ISDATE"], [4, 2, 1, "", "ISGUID"], [4, 2, 1, "", "ISINTEGER"], [4, 2, 1, "", "ISNULL"], [4, 2, 1, "", "ISNUMERIC"], [4, 2, 1, "", "LASTINDEXOF"], [4, 2, 1, "", "LEN"], [4, 2, 1, "", "LOWER"], [4, 2, 1, "", "MAXOF"], [4, 2, 1, "", "MINOF"], [4, 2, 1, "", "NOW"], [4, 2, 1, "", "NTHINDEXOF"], [4, 2, 1, "", "POWER"], [4, 2, 1, "", "REGEXMATCH"], [4, 2, 1, "", "REGEXVAL"], [4, 2, 1, "", "REPLACE"], [4, 2, 1, "", "REVERSE"], [4, 2, 1, "", "ROUND"], [4, 2, 1, "", "SPLIT"], [4, 2, 1, "", "SQRT"], [4, 2, 1, "", "STARTSWITH"], [4, 2, 1, "", "STRCMP"], [4, 2, 1, "", "STRCOUNT"], [4, 2, 1, "", "SUBSTR"], [4, 2, 1, "", "TRIM"], [4, 2, 1, "", "TRIMLEFT"], [4, 2, 1, "", "TRIMRIGHT"], [4, 2, 1, "", "UPPER"], [4, 2, 1, "", "UTCNOW"]], "sttp.data.constants.ExpressionOperatorType": [[4, 2, 1, "", "ADD"], [4, 2, 1, "", "AND"], [4, 2, 1, "", "BITSHIFTLEFT"], [4, 2, 1, "", "BITSHIFTRIGHT"], [4, 2, 1, "", "BITWISEAND"], [4, 2, 1, "", "BITWISEOR"], [4, 2, 1, "", "BITWISEXOR"], [4, 2, 1, "", "DIVIDE"], [4, 2, 1, "", "EQUAL"], [4, 2, 1, "", "EQUALEXACTMATCH"], [4, 2, 1, "", "GREATERTHAN"], [4, 2, 1, "", "GREATERTHANOREQUAL"], [4, 2, 1, "", "ISNOTNULL"], [4, 2, 1, "", "ISNULL"], [4, 2, 1, "", "LESSTHAN"], [4, 2, 1, "", "LESSTHANOREQUAL"], [4, 2, 1, "", "LIKE"], [4, 2, 1, "", "LIKEEXACTMATCH"], [4, 2, 1, "", "MODULUS"], [4, 2, 1, "", "MULTIPLY"], [4, 2, 1, "", "NOTEQUAL"], [4, 2, 1, "", "NOTEQUALEXACTMATCH"], [4, 2, 1, "", "NOTLIKE"], [4, 2, 1, "", "NOTLIKEEXACTMATCH"], [4, 2, 1, "", "OR"], [4, 2, 1, "", "SUBTRACT"]], "sttp.data.constants.ExpressionType": [[4, 2, 1, "", "COLUMN"], [4, 2, 1, "", "FUNCTION"], [4, 2, 1, "", "INLIST"], [4, 2, 1, "", "OPERATOR"], [4, 2, 1, "", "UNARY"], [4, 2, 1, "", "VALUE"]], "sttp.data.constants.ExpressionUnaryType": [[4, 2, 1, "", "MINUS"], [4, 2, 1, "", "NOT"], [4, 2, 1, "", "PLUS"]], "sttp.data.constants.ExpressionValueType": [[4, 2, 1, "", "BOOLEAN"], [4, 2, 1, "", "DATETIME"], [4, 2, 1, "", "DECIMAL"], [4, 2, 1, "", "DOUBLE"], [4, 2, 1, "", "GUID"], [4, 2, 1, "", "INT32"], [4, 2, 1, "", "INT64"], [4, 2, 1, "", "STRING"], [4, 2, 1, "", "UNDEFINED"]], "sttp.data.constants.TimeInterval": [[4, 2, 1, "", "DAY"], [4, 2, 1, "", "DAYOFYEAR"], [4, 2, 1, "", "HOUR"], [4, 2, 1, "", "MILLISECOND"], [4, 2, 1, "", "MINUTE"], [4, 2, 1, "", "MONTH"], [4, 2, 1, "", "SECOND"], [4, 2, 1, "", "WEEK"], [4, 2, 1, "", "WEEKDAY"], [4, 2, 1, "", "YEAR"], [4, 3, 1, "", "parse"]], "sttp.data.datacolumn": [[4, 1, 1, "", "DataColumn"]], "sttp.data.datacolumn.DataColumn": [[4, 5, 1, "", "computed"], [4, 5, 1, "", "datatype"], [4, 5, 1, "", "expression"], [4, 5, 1, "", "index"], [4, 5, 1, "", "name"], [4, 5, 1, "", "parent"]], "sttp.data.datarow": [[4, 1, 1, "", "DataRow"]], "sttp.data.datarow.DataRow": [[4, 3, 1, "", "booleanvalue"], [4, 3, 1, "", "booleanvalue_byname"], [4, 3, 1, "", "columnvalue_as_string"], [4, 3, 1, "", "compare_datarowcolumns"], [4, 3, 1, "", "datetimevalue"], [4, 3, 1, "", "datetimevalue_byname"], [4, 3, 1, "", "decimalvalue"], [4, 3, 1, "", "decimalvalue_byname"], [4, 3, 1, "", "doublevalue"], [4, 3, 1, "", "doublevalue_byname"], [4, 3, 1, "", "guidvalue"], [4, 3, 1, "", "guidvalue_byname"], [4, 3, 1, "", "int16value"], [4, 3, 1, "", "int16value_byname"], [4, 3, 1, "", "int32value"], [4, 3, 1, "", "int32value_byname"], [4, 3, 1, "", "int64value"], [4, 3, 1, "", "int64value_byname"], [4, 3, 1, "", "int8value"], [4, 3, 1, "", "int8value_byname"], [4, 5, 1, "", "parent"], [4, 3, 1, "", "set_value"], [4, 3, 1, "", "set_value_byname"], [4, 3, 1, "", "singlevalue"], [4, 3, 1, "", "singlevalue_byname"], [4, 3, 1, "", "stringvalue"], [4, 3, 1, "", "stringvalue_byname"], [4, 3, 1, "", "uint16value"], [4, 3, 1, "", "uint16value_byname"], [4, 3, 1, "", "uint32value"], [4, 3, 1, "", "uint32value_byname"], [4, 3, 1, "", "uint64value"], [4, 3, 1, "", "uint64value_byname"], [4, 3, 1, "", "uint8value"], [4, 3, 1, "", "uint8value_byname"], [4, 3, 1, "", "value"], [4, 3, 1, "", "value_as_string"], [4, 3, 1, "", "value_as_string_byname"], [4, 3, 1, "", "value_byname"]], "sttp.data.dataset": [[4, 1, 1, "", "DataSet"], [4, 6, 1, "", "EXT_XMLSCHEMADATA_NAMESPACE"], [4, 6, 1, "", "XMLSCHEMA_NAMESPACE"], [4, 4, 1, "", "xsdformat"]], "sttp.data.dataset.DataSet": [[4, 2, 1, "", "DEFAULT_NAME"], [4, 3, 1, "", "add_table"], [4, 3, 1, "", "clear_tables"], [4, 3, 1, "", "create_table"], [4, 3, 1, "", "from_xml"], [4, 2, 1, "", "name"], [4, 3, 1, "", "parse_xml"], [4, 3, 1, "", "parse_xmldoc"], [4, 3, 1, "", "remove_table"], [4, 3, 1, "", "table"], [4, 5, 1, "", "tablecount"], [4, 3, 1, "", "tablenames"], [4, 3, 1, "", "tables"]], "sttp.data.datatable": [[4, 1, 1, "", "DataTable"]], "sttp.data.datatable.DataTable": [[4, 3, 1, "", "add_column"], [4, 3, 1, "", "add_row"], [4, 3, 1, "", "clear_columns"], [4, 3, 1, "", "clear_rows"], [4, 3, 1, "", "clone_column"], [4, 3, 1, "", "clone_row"], [4, 3, 1, "", "column"], [4, 3, 1, "", "column_byname"], [4, 5, 1, "", "columncount"], [4, 3, 1, "", "columnindex"], [4, 3, 1, "", "create_column"], [4, 3, 1, "", "create_row"], [4, 5, 1, "", "name"], [4, 5, 1, "", "parent"], [4, 3, 1, "", "row"], [4, 5, 1, "", "rowcount"], [4, 3, 1, "", "rowswhere"], [4, 3, 1, "", "rowvalue_as_string"], [4, 3, 1, "", "rowvalue_as_string_byname"], [4, 3, 1, "", "select"]], "sttp.data.datatype": [[4, 1, 1, "", "DataType"], [4, 4, 1, "", "default_datatype"], [4, 4, 1, "", "parse_xsddatatype"]], "sttp.data.datatype.DataType": [[4, 2, 1, "", "BOOLEAN"], [4, 2, 1, "", "DATETIME"], [4, 2, 1, "", "DECIMAL"], [4, 2, 1, "", "DOUBLE"], [4, 2, 1, "", "GUID"], [4, 2, 1, "", "INT16"], [4, 2, 1, "", "INT32"], [4, 2, 1, "", "INT64"], [4, 2, 1, "", "INT8"], [4, 2, 1, "", "SINGLE"], [4, 2, 1, "", "STRING"], [4, 2, 1, "", "UINT16"], [4, 2, 1, "", "UINT32"], [4, 2, 1, "", "UINT64"], [4, 2, 1, "", "UINT8"]], "sttp.data.errors": [[4, 7, 1, "", "EvaluateError"]], "sttp.data.expression": [[4, 1, 1, "", "Expression"]], "sttp.data.expression.Expression": [[4, 5, 1, "", "expressiontype"]], "sttp.data.expressiontree": [[4, 1, 1, "", "ExpressionTree"]], "sttp.data.expressiontree.ExpressionTree": [[4, 3, 1, "", "evaluate"], [4, 2, 1, "", "orderbyterms"], [4, 2, 1, "", "root"], [4, 3, 1, "", "select"], [4, 3, 1, "", "selectwhere"], [4, 2, 1, "", "tablename"], [4, 2, 1, "", "toplimit"]], "sttp.data.filterexpressionparser": [[4, 1, 1, "", "FilterExpressionParser"]], "sttp.data.filterexpressionparser.FilterExpressionParser": [[4, 2, 1, "", "dataset"], [4, 3, 1, "", "enterExpression"], [4, 3, 1, "", "enterFilterExpressionStatement"], [4, 3, 1, "", "enterFilterStatement"], [4, 3, 1, "", "evaluate"], [4, 3, 1, "", "evaluate_datarowexpression"], [4, 3, 1, "", "evaluate_expression"], [4, 3, 1, "", "exitColumnName"], [4, 3, 1, "", "exitExpression"], [4, 3, 1, "", "exitFunctionExpression"], [4, 3, 1, "", "exitIdentifierStatement"], [4, 3, 1, "", "exitLiteralValue"], [4, 3, 1, "", "exitPredicateExpression"], [4, 3, 1, "", "exitValueExpression"], [4, 5, 1, "", "expressiontrees"], [4, 5, 1, "", "filtered_rows"], [4, 5, 1, "", "filtered_rowset"], [4, 5, 1, "", "filtered_signalids"], [4, 5, 1, "", "filtered_signalidset"], [4, 5, 1, "", "filterexpression_statementcount"], [4, 3, 1, "", "from_dataset"], [4, 3, 1, "", "generate_expressiontree"], [4, 3, 1, "", "generate_expressiontrees"], [4, 3, 1, "", "generate_expressiontrees_fromtable"], [4, 2, 1, "", "primary_tablename"], [4, 3, 1, "", "select_datarows"], [4, 3, 1, "", "select_datarows_fromtable"], [4, 3, 1, "", "select_datarowset"], [4, 3, 1, "", "select_datarowset_fromtable"], [4, 3, 1, "", "select_signalidset"], [4, 3, 1, "", "select_signalidset_fromtable"], [4, 3, 1, "", "set_parsingexception_callback"], [4, 3, 1, "", "table"], [4, 2, 1, "", "tableidfields_map"], [4, 2, 1, "", "track_filteredrows"], [4, 2, 1, "", "track_filteredsignalids"]], "sttp.data.functionexpression": [[4, 1, 1, "", "FunctionExpression"]], "sttp.data.functionexpression.FunctionExpression": [[4, 5, 1, "", "arguments"], [4, 5, 1, "", "expressiontype"], [4, 5, 1, "", "functiontype"]], "sttp.data.inlistexpression": [[4, 1, 1, "", "InListExpression"]], "sttp.data.inlistexpression.InListExpression": [[4, 5, 1, "", "arguments"], [4, 5, 1, "", "exactmatch"], [4, 5, 1, "", "expressiontype"], [4, 5, 1, "", "has_notkeyword"], [4, 5, 1, "", "value"]], "sttp.data.operatorexpression": [[4, 1, 1, "", "OperatorExpression"]], "sttp.data.operatorexpression.OperatorExpression": [[4, 5, 1, "", "expressiontype"], [4, 5, 1, "", "leftvalue"], [4, 5, 1, "", "operatortype"], [4, 5, 1, "", "rightvalue"]], "sttp.data.orderbyterm": [[4, 1, 1, "", "OrderByTerm"]], "sttp.data.orderbyterm.OrderByTerm": [[4, 2, 1, "", "ascending"], [4, 2, 1, "", "column"], [4, 2, 1, "", "extactmatch"]], "sttp.data.tableidfields": [[4, 6, 1, "", "DEFAULT_TABLEIDFIELDS"], [4, 1, 1, "", "TableIDFields"]], "sttp.data.tableidfields.TableIDFields": [[4, 2, 1, "", "measurementkey_fieldname"], [4, 2, 1, "", "pointtag_fieldname"], [4, 2, 1, "", "signalid_fieldname"]], "sttp.data.unaryexpression": [[4, 1, 1, "", "UnaryExpression"]], "sttp.data.unaryexpression.UnaryExpression": [[4, 3, 1, "", "applyto_bool"], [4, 3, 1, "", "applyto_decimal"], [4, 3, 1, "", "applyto_double"], [4, 3, 1, "", "applyto_int32"], [4, 3, 1, "", "applyto_int64"], [4, 5, 1, "", "expressiontype"], [4, 5, 1, "", "unarytype"], [4, 5, 1, "", "value"]], "sttp.data.valueexpression": [[4, 6, 1, "", "EMPTYSTRINGVALUE"], [4, 6, 1, "", "FALSEVALUE"], [4, 6, 1, "", "NULLBOOLVALUE"], [4, 6, 1, "", "NULLDATETIMEVALUE"], [4, 6, 1, "", "NULLINT32VALUE"], [4, 6, 1, "", "NULLSTRINGVALUE"], [4, 6, 1, "", "NULLVALUE"], [4, 6, 1, "", "TRUEVALUE"], [4, 1, 1, "", "ValueExpression"]], "sttp.data.valueexpression.ValueExpression": [[4, 3, 1, "", "booleanvalue"], [4, 3, 1, "", "convert"], [4, 3, 1, "", "datetimevalue"], [4, 3, 1, "", "decimalvalue"], [4, 3, 1, "", "doublevalue"], [4, 5, 1, "", "expressiontype"], [4, 3, 1, "", "guidvalue"], [4, 3, 1, "", "int32value"], [4, 3, 1, "", "int64value"], [4, 3, 1, "", "integervalue"], [4, 3, 1, "", "is_null"], [4, 3, 1, "", "nullvalue"], [4, 3, 1, "", "stringvalue"], [4, 5, 1, "", "value"], [4, 5, 1, "", "valuetype"]], "sttp.metadata": [[5, 0, 0, "-", "cache"], [6, 0, 0, "-", "record"]], "sttp.metadata.cache": [[5, 1, 1, "", "MetadataCache"]], "sttp.metadata.cache.MetadataCache": [[5, 3, 1, "", "add_measurement"], [5, 2, 1, "", "device_records"], [5, 2, 1, "", "deviceacronym_device_map"], [5, 2, 1, "", "deviceid_device_map"], [5, 3, 1, "", "find_device_acronym"], [5, 3, 1, "", "find_device_id"], [5, 3, 1, "", "find_devices"], [5, 3, 1, "", "find_measurement_id"], [5, 3, 1, "", "find_measurement_pointtag"], [5, 3, 1, "", "find_measurement_signalid"], [5, 3, 1, "", "find_measurement_signalreference"], [5, 3, 1, "", "find_measurements"], [5, 3, 1, "", "find_measurements_signaltype"], [5, 3, 1, "", "find_measurements_signaltypename"], [5, 2, 1, "", "id_measurement_map"], [5, 2, 1, "", "measurement_records"], [5, 2, 1, "", "phasorRecords"], [5, 2, 1, "", "pointtag_measurement_map"], [5, 2, 1, "", "signalid_measurement_map"], [5, 2, 1, "", "signalref_measurement_map"]], "sttp.metadata.record": [[6, 0, 0, "-", "device"], [6, 0, 0, "-", "measurement"], [6, 0, 0, "-", "phasor"]], "sttp.metadata.record.device": [[6, 1, 1, "", "DeviceRecord"]], "sttp.metadata.record.device.DeviceRecord": [[6, 2, 1, "", "DEFAULT_COMPANYNAME"], [6, 2, 1, "", "DEFAULT_FRAMESPERSECOND"], [6, 2, 1, "", "DEFAULT_LATITUDE"], [6, 2, 1, "", "DEFAULT_LONGITUDE"], [6, 2, 1, "", "DEFAULT_PARENTACRONYM"], [6, 2, 1, "", "DEFAULT_PROTOCOLNAME"], [6, 2, 1, "", "DEFAULT_UPDATEDON"], [6, 2, 1, "", "DEFAULT_VENDORACRONYM"], [6, 2, 1, "", "DEFAULT_VENDORDEVICENAME"], [6, 5, 1, "", "accessid"], [6, 5, 1, "", "acronym"], [6, 5, 1, "", "companyacronym"], [6, 5, 1, "", "deviceid"], [6, 5, 1, "", "framespersecond"], [6, 5, 1, "", "latitude"], [6, 5, 1, "", "longitude"], [6, 2, 1, "", "measurements"], [6, 5, 1, "", "name"], [6, 5, 1, "", "nodeid"], [6, 5, 1, "", "parentacronym"], [6, 2, 1, "", "phasors"], [6, 5, 1, "", "protocolname"], [6, 5, 1, "", "updatedon"], [6, 5, 1, "", "vendoracronym"], [6, 5, 1, "", "vendordevicename"]], "sttp.metadata.record.measurement": [[6, 1, 1, "", "MeasurementRecord"], [6, 1, 1, "", "SignalType"]], "sttp.metadata.record.measurement.MeasurementRecord": [[6, 2, 1, "", "DEFAULT_ADDER"], [6, 2, 1, "", "DEFAULT_DESCRIPTION"], [6, 2, 1, "", "DEFAULT_DEVICEACRONYM"], [6, 2, 1, "", "DEFAULT_ID"], [6, 2, 1, "", "DEFAULT_MULTIPLIER"], [6, 2, 1, "", "DEFAULT_POINTTAG"], [6, 2, 1, "", "DEFAULT_SIGNALID"], [6, 2, 1, "", "DEFAULT_SIGNALREFERENCE"], [6, 2, 1, "", "DEFAULT_SIGNALTYPENAME"], [6, 2, 1, "", "DEFAULT_SOURCE"], [6, 2, 1, "", "DEFAULT_UPDATEDON"], [6, 5, 1, "", "adder"], [6, 5, 1, "", "description"], [6, 2, 1, "", "device"], [6, 5, 1, "", "deviceacronym"], [6, 5, 1, "", "id"], [6, 5, 1, "", "multiplier"], [6, 2, 1, "", "phasor"], [6, 5, 1, "", "pointtag"], [6, 5, 1, "", "signalid"], [6, 5, 1, "", "signalreference"], [6, 5, 1, "", "signaltype"], [6, 5, 1, "", "signaltypename"], [6, 5, 1, "", "source"], [6, 5, 1, "", "updatedon"]], "sttp.metadata.record.measurement.SignalType": [[6, 2, 1, "", "ALOG"], [6, 2, 1, "", "ALRM"], [6, 2, 1, "", "CALC"], [6, 2, 1, "", "DFDT"], [6, 2, 1, "", "DIGI"], [6, 2, 1, "", "FLAG"], [6, 2, 1, "", "FREQ"], [6, 2, 1, "", "IPHA"], [6, 2, 1, "", "IPHM"], [6, 2, 1, "", "QUAL"], [6, 2, 1, "", "STAT"], [6, 2, 1, "", "UNKN"], [6, 2, 1, "", "VPHA"], [6, 2, 1, "", "VPHM"], [6, 3, 1, "", "parse"]], "sttp.metadata.record.phasor": [[6, 1, 1, "", "CompositePhasorMeasurement"], [6, 1, 1, "", "PhasorRecord"]], "sttp.metadata.record.phasor.CompositePhasorMeasurement": [[6, 2, 1, "", "ANGLE"], [6, 2, 1, "", "MAGNITUDE"]], "sttp.metadata.record.phasor.PhasorRecord": [[6, 2, 1, "", "DEFAULT_BASEKV"], [6, 2, 1, "", "DEFAULT_PHASE"], [6, 2, 1, "", "DEFAULT_TYPE"], [6, 2, 1, "", "DEFAULT_UPDATEDON"], [6, 5, 1, "", "angle_measurement"], [6, 5, 1, "", "basekv"], [6, 2, 1, "", "device"], [6, 5, 1, "", "deviceacronym"], [6, 5, 1, "", "id"], [6, 5, 1, "", "label"], [6, 5, 1, "", "magnitude_measurement"], [6, 2, 1, "", "measurements"], [6, 5, 1, "", "phase"], [6, 5, 1, "", "sourceindex"], [6, 5, 1, "", "type"], [6, 5, 1, "", "updatedon"]], "sttp.reader": [[3, 1, 1, "", "MeasurementReader"]], "sttp.reader.MeasurementReader": [[3, 3, 1, "", "dispose"], [3, 3, 1, "", "next_measurement"]], "sttp.settings": [[3, 1, 1, "", "Settings"]], "sttp.settings.Settings": [[3, 2, 1, "", "DEFAULT_CONSTRAINTPARAMETERS"], [3, 2, 1, "", "DEFAULT_ENABLE_TIME_REASONABILITY_CHECK"], [3, 2, 1, "", "DEFAULT_EXTRA_CONNECTIONSTRING_PARAMETERS"], [3, 2, 1, "", "DEFAULT_INCLUDETIME"], [3, 2, 1, "", "DEFAULT_LAGTIME"], [3, 2, 1, "", "DEFAULT_LEADTIME"], [3, 2, 1, "", "DEFAULT_PROCESSINGINTERVAL"], [3, 2, 1, "", "DEFAULT_PUBLISHINTERVAL"], [3, 2, 1, "", "DEFAULT_REQUEST_NANVALUEFILTER"], [3, 2, 1, "", "DEFAULT_STARTTIME"], [3, 2, 1, "", "DEFAULT_STOPTIME"], [3, 2, 1, "", "DEFAULT_THROTTLED"], [3, 2, 1, "", "DEFAULT_UDPINTERFACE"], [3, 2, 1, "", "DEFAULT_UDPPORT"], [3, 2, 1, "", "DEFAULT_USE_LOCALCLOCK_AS_REALTIME"], [3, 2, 1, "", "DEFAULT_USE_MILLISECONDRESOLUTION"], [3, 2, 1, "", "constraintparameters"], [3, 2, 1, "", "enabletimereasonabilitycheck"], [3, 2, 1, "", "extra_connectionstring_parameters"], [3, 2, 1, "", "includetime"], [3, 2, 1, "", "lagtime"], [3, 2, 1, "", "leadtime"], [3, 2, 1, "", "processinginterval"], [3, 2, 1, "", "publishinterval"], [3, 2, 1, "", "request_nanvaluefilter"], [3, 2, 1, "", "starttime"], [3, 2, 1, "", "stoptime"], [3, 2, 1, "", "throttled"], [3, 2, 1, "", "udpinterface"], [3, 2, 1, "", "udpport"], [3, 2, 1, "", "use_millisecondresolution"], [3, 2, 1, "", "uselocalclockasrealtime"]], "sttp.subscriber": [[3, 1, 1, "", "Subscriber"]], "sttp.subscriber.Subscriber": [[3, 5, 1, "", "activesignalindexcache"], [3, 3, 1, "", "adjustedvalue"], [3, 3, 1, "", "connect"], [3, 5, 1, "", "connected"], [3, 3, 1, "", "default_connectionestablished_receiver"], [3, 3, 1, "", "default_connectionterminated_receiver"], [3, 3, 1, "", "default_errormessage_logger"], [3, 3, 1, "", "default_statusmessage_logger"], [3, 3, 1, "", "disconnect"], [3, 3, 1, "", "dispose"], [3, 3, 1, "", "errormessage"], [3, 3, 1, "", "measurement_metadata"], [3, 5, 1, "", "metadatacache"], [3, 3, 1, "", "read_measurements"], [3, 3, 1, "", "request_metadata"], [3, 3, 1, "", "set_configurationchanged_receiver"], [3, 3, 1, "", "set_connectionestablished_receiver"], [3, 3, 1, "", "set_connectionterminated_receiver"], [3, 3, 1, "", "set_data_starttime_receiver"], [3, 3, 1, "", "set_errormessage_logger"], [3, 3, 1, "", "set_historicalreadcomplete_receiver"], [3, 3, 1, "", "set_metadatanotification_receiver"], [3, 3, 1, "", "set_newbufferblock_receiver"], [3, 3, 1, "", "set_newmeasurements_receiver"], [3, 3, 1, "", "set_notification_receiver"], [3, 3, 1, "", "set_statusmessage_logger"], [3, 3, 1, "", "set_subscriptionupdated_receiver"], [3, 3, 1, "", "statusmessage"], [3, 3, 1, "", "subscribe"], [3, 5, 1, "", "subscribed"], [3, 5, 1, "", "subscriberid"], [3, 5, 1, "", "total_commandchannel_bytesreceived"], [3, 5, 1, "", "total_datachannel_bytesreceived"], [3, 5, 1, "", "total_measurementsreceived"], [3, 3, 1, "", "unsubscribe"]], "sttp.ticks": [[3, 1, 1, "", "Ticks"]], "sttp.ticks.Ticks": [[3, 2, 1, "", "LEAPSECOND_DIRECTION"], [3, 2, 1, "", "LEAPSECOND_FLAG"], [3, 2, 1, "", "PERDAY"], [3, 2, 1, "", "PERHOUR"], [3, 2, 1, "", "PERMICROSECOND"], [3, 2, 1, "", "PERMILLISECOND"], [3, 2, 1, "", "PERMINUTE"], [3, 2, 1, "", "PERSECOND"], [3, 2, 1, "", "UNIXBASEOFFSET"], [3, 2, 1, "", "VALUEMASK"], [3, 3, 1, "", "from_datetime"], [3, 3, 1, "", "from_timedelta"], [3, 3, 1, "", "is_leapsecond"], [3, 3, 1, "", "is_negative_leapsecond"], [3, 3, 1, "", "now"], [3, 3, 1, "", "set_leapsecond"], [3, 3, 1, "", "set_negative_leapsecond"], [3, 3, 1, "", "timestampvalue"], [3, 3, 1, "", "to_datetime"], [3, 3, 1, "", "to_shortstring"], [3, 3, 1, "", "to_string"], [3, 3, 1, "", "utcnow"]], "sttp.transport": [[7, 0, 0, "-", "bufferblock"], [7, 0, 0, "-", "compactmeasurement"], [7, 0, 0, "-", "constants"], [7, 0, 0, "-", "datasubscriber"], [7, 0, 0, "-", "measurement"], [7, 0, 0, "-", "signalindexcache"], [7, 0, 0, "-", "signalkind"], [7, 0, 0, "-", "subscriberconnector"], [7, 0, 0, "-", "subscriptioninfo"], [8, 0, 0, "-", "tssc"]], "sttp.transport.bufferblock": [[7, 1, 1, "", "BufferBlock"]], "sttp.transport.bufferblock.BufferBlock": [[7, 2, 1, "", "DEFAULT_BUFFER"], [7, 2, 1, "", "DEFAULT_SIGNALID"], [7, 5, 1, "", "buffer"], [7, 2, 1, "", "signalid"]], "sttp.transport.compactmeasurement": [[7, 1, 1, "", "CompactMeasurement"], [7, 1, 1, "", "CompactStateFlags"]], "sttp.transport.compactmeasurement.CompactMeasurement": [[7, 3, 1, "", "decode"], [7, 3, 1, "", "get_binarylength"], [7, 3, 1, "", "get_compact_stateflags"], [7, 3, 1, "", "get_timestamp_c2"], [7, 3, 1, "", "get_timestamp_c4"], [7, 5, 1, "", "runtimeid"], [7, 3, 1, "", "set_compact_stateflags"]], "sttp.transport.compactmeasurement.CompactStateFlags": [[7, 2, 1, "", "BASETIMEOFFSET"], [7, 2, 1, "", "CALCULATEDVALUE"], [7, 2, 1, "", "DATAQUALITY"], [7, 2, 1, "", "DATARANGE"], [7, 2, 1, "", "DISCARDEDVALUE"], [7, 2, 1, "", "SYSTEMISSUE"], [7, 2, 1, "", "TIMEINDEX"], [7, 2, 1, "", "TIMEQUALITY"]], "sttp.transport.constants": [[7, 1, 1, "", "CompressionModes"], [7, 1, 1, "", "ConnectStatus"], [7, 1, 1, "", "DataPacketFlags"], [7, 1, 1, "", "Defaults"], [7, 1, 1, "", "OperationalEncoding"], [7, 1, 1, "", "OperationalModes"], [7, 1, 1, "", "SecurityMode"], [7, 1, 1, "", "ServerCommand"], [7, 1, 1, "", "ServerResponse"], [7, 1, 1, "", "StateFlags"]], "sttp.transport.constants.CompressionModes": [[7, 2, 1, "", "GZIP"], [7, 2, 1, "", "NOFLAGS"], [7, 2, 1, "", "TSSC"]], "sttp.transport.constants.ConnectStatus": [[7, 2, 1, "", "CANCELED"], [7, 2, 1, "", "FAILED"], [7, 2, 1, "", "SUCCESS"]], "sttp.transport.constants.DataPacketFlags": [[7, 2, 1, "", "CACHEINDEX"], [7, 2, 1, "", "CIPHERINDEX"], [7, 2, 1, "", "COMPACT"], [7, 2, 1, "", "COMPRESSED"], [7, 2, 1, "", "NOFLAGS"]], "sttp.transport.constants.Defaults": [[7, 2, 1, "", "AUTORECONNECT"], [7, 2, 1, "", "AUTOREQUESTMETADATA"], [7, 2, 1, "", "AUTOSUBSCRIBE"], [7, 2, 1, "", "COMPRESS_METADATA"], [7, 2, 1, "", "COMPRESS_PAYLOADDATA"], [7, 2, 1, "", "COMPRESS_SIGNALINDEXCACHE"], [7, 2, 1, "", "CONSTRAINTPARAMETERS"], [7, 2, 1, "", "DATACHANNEL_INTERFACE"], [7, 2, 1, "", "DATACHANNEL_LOCALPORT"], [7, 2, 1, "", "ENABLE_TIME_REASONABILITY_CHECK"], [7, 2, 1, "", "EXTRA_CONNECTIONSTRING_PARAMETERS"], [7, 2, 1, "", "FILTEREXPRESSION"], [7, 2, 1, "", "INCLUDETIME"], [7, 2, 1, "", "LAGTIME"], [7, 2, 1, "", "LEADTIME"], [7, 2, 1, "", "MAXRETRIES"], [7, 2, 1, "", "MAXRETRYINTERVAL"], [7, 2, 1, "", "METADATAFILTERS"], [7, 2, 1, "", "PROCESSINGINTERVAL"], [7, 2, 1, "", "PUBLISHINTERVAL"], [7, 2, 1, "", "REQUEST_NANVALUEFILTER"], [7, 2, 1, "", "RETRYINTERVAL"], [7, 2, 1, "", "SOCKET_TIMEOUT"], [7, 2, 1, "", "STARTTIME"], [7, 2, 1, "", "STOPTIME"], [7, 2, 1, "", "THROTTLED"], [7, 2, 1, "", "UDPDATACHANNEL"], [7, 2, 1, "", "USE_LOCALCLOCK_AS_REALTIME"], [7, 2, 1, "", "USE_MILLISECONDRESOLUTION"], [7, 2, 1, "", "VERSION"]], "sttp.transport.constants.OperationalEncoding": [[7, 2, 1, "", "UTF16BE"], [7, 2, 1, "", "UTF16LE"], [7, 2, 1, "", "UTF8"]], "sttp.transport.constants.OperationalModes": [[7, 2, 1, "", "COMPRESSMETADATA"], [7, 2, 1, "", "COMPRESSPAYLOADDATA"], [7, 2, 1, "", "COMPRESSSIGNALINDEXCACHE"], [7, 2, 1, "", "ENCODINGMASK"], [7, 2, 1, "", "IMPLEMENTATIONSPECIFICEXTENSIONMASK"], [7, 2, 1, "", "NOFLAGS"], [7, 2, 1, "", "RECEIVEEXTERNALMETADATA"], [7, 2, 1, "", "RECEIVEINTERNALMETADATA"], [7, 2, 1, "", "VERSIONMASK"]], "sttp.transport.constants.SecurityMode": [[7, 2, 1, "", "OFF"], [7, 2, 1, "", "TLS"]], "sttp.transport.constants.ServerCommand": [[7, 2, 1, "", "CONFIRMBUFFERBLOCK"], [7, 2, 1, "", "CONFIRMNOTIFICATION"], [7, 2, 1, "", "CONFIRMUPDATEBASETIMES"], [7, 2, 1, "", "CONFIRMUPDATECIPHERKEYS"], [7, 2, 1, "", "CONFIRMUPDATESIGNALINDEXCACHE"], [7, 2, 1, "", "CONNECT"], [7, 2, 1, "", "DEFINEOPERATIONALMODES"], [7, 2, 1, "", "GETPRIMARYMETADATASCHEMA"], [7, 2, 1, "", "GETSIGNALSELECTIONSCHEMA"], [7, 2, 1, "", "METADATAREFRESH"], [7, 2, 1, "", "ROTATECIPHERKEYS"], [7, 2, 1, "", "SUBSCRIBE"], [7, 2, 1, "", "UNSUBSCRIBE"], [7, 2, 1, "", "UPDATEPROCESSINGINTERVAL"], [7, 2, 1, "", "USERCOMMAND00"], [7, 2, 1, "", "USERCOMMAND01"], [7, 2, 1, "", "USERCOMMAND02"], [7, 2, 1, "", "USERCOMMAND03"], [7, 2, 1, "", "USERCOMMAND04"], [7, 2, 1, "", "USERCOMMAND05"], [7, 2, 1, "", "USERCOMMAND06"], [7, 2, 1, "", "USERCOMMAND07"], [7, 2, 1, "", "USERCOMMAND08"], [7, 2, 1, "", "USERCOMMAND09"], [7, 2, 1, "", "USERCOMMAND10"], [7, 2, 1, "", "USERCOMMAND11"], [7, 2, 1, "", "USERCOMMAND12"], [7, 2, 1, "", "USERCOMMAND13"], [7, 2, 1, "", "USERCOMMAND14"], [7, 2, 1, "", "USERCOMMAND15"]], "sttp.transport.constants.ServerResponse": [[7, 2, 1, "", "BUFFERBLOCK"], [7, 2, 1, "", "CONFIGURATIONCHANGED"], [7, 2, 1, "", "DATAPACKET"], [7, 2, 1, "", "DATASTARTTIME"], [7, 2, 1, "", "FAILED"], [7, 2, 1, "", "NOOP"], [7, 2, 1, "", "NOTIFY"], [7, 2, 1, "", "PROCESSINGCOMPLETE"], [7, 2, 1, "", "SUCCEEDED"], [7, 2, 1, "", "UPDATEBASETIMES"], [7, 2, 1, "", "UPDATECIPHERKEYS"], [7, 2, 1, "", "UPDATESIGNALINDEXCACHE"], [7, 2, 1, "", "USERRESPONSE00"], [7, 2, 1, "", "USERRESPONSE01"], [7, 2, 1, "", "USERRESPONSE02"], [7, 2, 1, "", "USERRESPONSE03"], [7, 2, 1, "", "USERRESPONSE04"], [7, 2, 1, "", "USERRESPONSE05"], [7, 2, 1, "", "USERRESPONSE06"], [7, 2, 1, "", "USERRESPONSE07"], [7, 2, 1, "", "USERRESPONSE08"], [7, 2, 1, "", "USERRESPONSE09"], [7, 2, 1, "", "USERRESPONSE10"], [7, 2, 1, "", "USERRESPONSE11"], [7, 2, 1, "", "USERRESPONSE12"], [7, 2, 1, "", "USERRESPONSE13"], [7, 2, 1, "", "USERRESPONSE14"], [7, 2, 1, "", "USERRESPONSE15"]], "sttp.transport.constants.StateFlags": [[7, 2, 1, "", "ALARMHIGH"], [7, 2, 1, "", "ALARMLOW"], [7, 2, 1, "", "BADDATA"], [7, 2, 1, "", "BADTIME"], [7, 2, 1, "", "CALCULATEDVALUE"], [7, 2, 1, "", "CALCULATIONERROR"], [7, 2, 1, "", "CALCULATIONWARNING"], [7, 2, 1, "", "COMPARISONALARM"], [7, 2, 1, "", "DISCARDEDVALUE"], [7, 2, 1, "", "DOWNSAMPLED"], [7, 2, 1, "", "FLATLINEALARM"], [7, 2, 1, "", "FUTURETIMEALARM"], [7, 2, 1, "", "LATETIMEALARM"], [7, 2, 1, "", "MEASUREMENTERROR"], [7, 2, 1, "", "NORMAL"], [7, 2, 1, "", "OVERRANGEERROR"], [7, 2, 1, "", "RECEIVEDASBAD"], [7, 2, 1, "", "RESERVEDQUALITYFLAG"], [7, 2, 1, "", "RESERVEDTIMEFLAG"], [7, 2, 1, "", "ROCALARM"], [7, 2, 1, "", "SUSPECTDATA"], [7, 2, 1, "", "SUSPECTTIME"], [7, 2, 1, "", "SYSTEMERROR"], [7, 2, 1, "", "SYSTEMWARNING"], [7, 2, 1, "", "UNDERRANGEERROR"], [7, 2, 1, "", "UPSAMPLED"], [7, 2, 1, "", "USERDEFINEDFLAG1"], [7, 2, 1, "", "USERDEFINEDFLAG2"], [7, 2, 1, "", "USERDEFINEDFLAG3"], [7, 2, 1, "", "USERDEFINEDFLAG4"], [7, 2, 1, "", "USERDEFINEDFLAG5"], [7, 2, 1, "", "WARNINGHIGH"], [7, 2, 1, "", "WARNINGLOW"]], "sttp.transport.datasubscriber": [[7, 1, 1, "", "DataSubscriber"]], "sttp.transport.datasubscriber.DataSubscriber": [[7, 2, 1, "", "DEFAULT_COMPRESS_METADATA"], [7, 2, 1, "", "DEFAULT_COMPRESS_PAYLOADDATA"], [7, 2, 1, "", "DEFAULT_COMPRESS_SIGNALINDEXCACHE"], [7, 2, 1, "", "DEFAULT_SOCKET_TIMEOUT"], [7, 2, 1, "", "DEFAULT_STTP_SOURCEINFO"], [7, 2, 1, "", "DEFAULT_STTP_UPDATEDONINFO"], [7, 2, 1, "", "DEFAULT_STTP_VERSIONINFO"], [7, 2, 1, "", "DEFAULT_VERSION"], [7, 5, 1, "", "activesignalindexcache"], [7, 3, 1, "", "adjustedvalue"], [7, 2, 1, "", "autoreconnect_callback"], [7, 2, 1, "", "compress_metadata"], [7, 2, 1, "", "compress_payloaddata"], [7, 2, 1, "", "compress_signalindexcache"], [7, 2, 1, "", "configurationchanged_callback"], [7, 3, 1, "", "connect"], [7, 5, 1, "", "connected"], [7, 2, 1, "", "connectionterminated_callback"], [7, 5, 1, "", "connector"], [7, 2, 1, "", "data_starttime_callback"], [7, 3, 1, "", "decodestr"], [7, 3, 1, "", "disconnect"], [7, 3, 1, "", "dispose"], [7, 5, 1, "", "disposing"], [7, 3, 1, "", "encodestr"], [7, 2, 1, "", "errormessage_callback"], [7, 3, 1, "", "lookup_metadata"], [7, 2, 1, "", "metadatacache"], [7, 2, 1, "", "metadatareceived_callback"], [7, 2, 1, "", "newbufferblocks_callback"], [7, 2, 1, "", "newmeasurements_callback"], [7, 2, 1, "", "notificationreceived_callback"], [7, 2, 1, "", "processingcomplete_callback"], [7, 3, 1, "", "send_servercommand"], [7, 3, 1, "", "send_servercommand_withmessage"], [7, 2, 1, "", "socket_timeout"], [7, 2, 1, "", "statusmessage_callback"], [7, 2, 1, "", "sttp_sourceinfo"], [7, 2, 1, "", "sttp_updatedoninfo"], [7, 2, 1, "", "sttp_versioninfo"], [7, 3, 1, "", "subscribe"], [7, 5, 1, "", "subscribed"], [7, 5, 1, "", "subscriberid"], [7, 5, 1, "", "subscription"], [7, 2, 1, "", "subscriptionupdated_callback"], [7, 5, 1, "", "total_commandchannel_bytesreceived"], [7, 5, 1, "", "total_datachannel_bytesreceived"], [7, 5, 1, "", "total_measurementsreceived"], [7, 3, 1, "", "unsubscribe"], [7, 2, 1, "", "version"]], "sttp.transport.measurement": [[7, 1, 1, "", "Measurement"]], "sttp.transport.measurement.Measurement": [[7, 2, 1, "", "DEFAULT_FLAGS"], [7, 2, 1, "", "DEFAULT_SIGNALID"], [7, 2, 1, "", "DEFAULT_TIMESTAMP"], [7, 2, 1, "", "DEFAULT_VALUE"], [7, 5, 1, "", "datetime"], [7, 2, 1, "", "flags"], [7, 2, 1, "", "signalid"], [7, 2, 1, "", "timestamp"], [7, 5, 1, "", "timestampvalue"], [7, 2, 1, "", "value"]], "sttp.transport.signalindexcache": [[7, 1, 1, "", "SignalIndexCache"]], "sttp.transport.signalindexcache.SignalIndexCache": [[7, 3, 1, "", "contains"], [7, 5, 1, "", "count"], [7, 3, 1, "", "decode"], [7, 3, 1, "", "id"], [7, 3, 1, "", "record"], [7, 3, 1, "", "signalid"], [7, 5, 1, "", "signalids"], [7, 3, 1, "", "signalindex"], [7, 3, 1, "", "source"]], "sttp.transport.signalkind": [[7, 1, 1, "", "SignalKind"], [7, 1, 1, "", "SignalKindEnum"]], "sttp.transport.signalkind.SignalKind": [[7, 2, 1, "", "ALARM"], [7, 2, 1, "", "ANALOG"], [7, 2, 1, "", "ANGLE"], [7, 2, 1, "", "CALCULATION"], [7, 2, 1, "", "DFDT"], [7, 2, 1, "", "DIGITAL"], [7, 2, 1, "", "FREQUENCY"], [7, 2, 1, "", "MAGNITUDE"], [7, 2, 1, "", "QUALITY"], [7, 2, 1, "", "STATISTIC"], [7, 2, 1, "", "STATUS"], [7, 2, 1, "", "UNKNOWN"]], "sttp.transport.signalkind.SignalKindEnum": [[7, 3, 1, "", "acronym"], [7, 3, 1, "", "parse_acronym"], [7, 3, 1, "", "signaltype"]], "sttp.transport.subscriberconnector": [[7, 1, 1, "", "SubscriberConnector"]], "sttp.transport.subscriberconnector.SubscriberConnector": [[7, 2, 1, "", "DEFAULT_AUTORECONNECT"], [7, 3, 1, "", "DEFAULT_ERRORMESSAGE_CALLBACK"], [7, 2, 1, "", "DEFAULT_HOSTNAME"], [7, 2, 1, "", "DEFAULT_MAXRETRIES"], [7, 2, 1, "", "DEFAULT_MAXRETRYINTERVAL"], [7, 2, 1, "", "DEFAULT_PORT"], [7, 3, 1, "", "DEFAULT_RECONNECT_CALLBACK"], [7, 2, 1, "", "DEFAULT_RETRYINTERVAL"], [7, 2, 1, "", "autoreconnect"], [7, 3, 1, "", "cancel"], [7, 3, 1, "", "connect"], [7, 3, 1, "", "dispose"], [7, 2, 1, "", "errormessage_callback"], [7, 2, 1, "", "hostname"], [7, 2, 1, "", "maxretries"], [7, 2, 1, "", "maxretryinterval"], [7, 2, 1, "", "port"], [7, 2, 1, "", "reconnect_callback"], [7, 3, 1, "", "reset_connection"], [7, 2, 1, "", "retryinterval"]], "sttp.transport.subscriptioninfo": [[7, 1, 1, "", "SubscriptionInfo"]], "sttp.transport.subscriptioninfo.SubscriptionInfo": [[7, 2, 1, "", "DEFAULT_CONSTRAINTPARAMETERS"], [7, 2, 1, "", "DEFAULT_DATACHANNEL_INTERFACE"], [7, 2, 1, "", "DEFAULT_DATACHANNEL_LOCALPORT"], [7, 2, 1, "", "DEFAULT_ENABLE_TIME_REASONABILITY_CHECK"], [7, 2, 1, "", "DEFAULT_EXTRA_CONNECTIONSTRING_PARAMETERS"], [7, 2, 1, "", "DEFAULT_FILTEREXPRESSION"], [7, 2, 1, "", "DEFAULT_INCLUDETIME"], [7, 2, 1, "", "DEFAULT_LAGTIME"], [7, 2, 1, "", "DEFAULT_LEADTIME"], [7, 2, 1, "", "DEFAULT_PROCESSINGINTERVAL"], [7, 2, 1, "", "DEFAULT_PUBLISHINTERVAL"], [7, 2, 1, "", "DEFAULT_REQUEST_NANVALUEFILTER"], [7, 2, 1, "", "DEFAULT_STARTTIME"], [7, 2, 1, "", "DEFAULT_STOPTIME"], [7, 2, 1, "", "DEFAULT_THROTTLED"], [7, 2, 1, "", "DEFAULT_UDPDATACHANNEL"], [7, 2, 1, "", "DEFAULT_USE_LOCALCLOCK_AS_REALTIME"], [7, 2, 1, "", "DEFAULT_USE_MILLISECONDRESOLUTION"], [7, 2, 1, "", "constraintparameters"], [7, 2, 1, "", "datachannel_interface"], [7, 2, 1, "", "datachannel_localport"], [7, 2, 1, "", "enabletimereasonabilitycheck"], [7, 2, 1, "", "extra_connectionstring_parameters"], [7, 2, 1, "", "filterexpression"], [7, 2, 1, "", "includetime"], [7, 2, 1, "", "lagtime"], [7, 2, 1, "", "leadtime"], [7, 2, 1, "", "processinginterval"], [7, 2, 1, "", "publishinterval"], [7, 2, 1, "", "request_nanvaluefilter"], [7, 2, 1, "", "starttime"], [7, 2, 1, "", "stoptime"], [7, 2, 1, "", "throttled"], [7, 2, 1, "", "udpdatachannel"], [7, 2, 1, "", "use_millisecondresolution"], [7, 2, 1, "", "uselocalclockasrealtime"]], "sttp.transport.tssc": [[8, 0, 0, "-", "decoder"], [8, 0, 0, "-", "pointmetadata"]], "sttp.transport.tssc.decoder": [[8, 1, 1, "", "Decoder"]], "sttp.transport.tssc.decoder.Decoder": [[8, 2, 1, "", "sequencenumber"], [8, 3, 1, "", "set_buffer"], [8, 3, 1, "", "try_get_measurement"]], "sttp.transport.tssc.pointmetadata": [[8, 1, 1, "", "CodeWords"], [8, 1, 1, "", "PointMetadata"]], "sttp.transport.tssc.pointmetadata.CodeWords": [[8, 2, 1, "", "ENDOFSTREAM"], [8, 2, 1, "", "POINTIDXOR12"], [8, 2, 1, "", "POINTIDXOR16"], [8, 2, 1, "", "POINTIDXOR20"], [8, 2, 1, "", "POINTIDXOR24"], [8, 2, 1, "", "POINTIDXOR32"], [8, 2, 1, "", "POINTIDXOR4"], [8, 2, 1, "", "POINTIDXOR8"], [8, 2, 1, "", "STATEFLAGS2"], [8, 2, 1, "", "STATEFLAGS7BIT32"], [8, 2, 1, "", "TIMEDELTA1FORWARD"], [8, 2, 1, "", "TIMEDELTA1REVERSE"], [8, 2, 1, "", "TIMEDELTA2FORWARD"], [8, 2, 1, "", "TIMEDELTA2REVERSE"], [8, 2, 1, "", "TIMEDELTA3FORWARD"], [8, 2, 1, "", "TIMEDELTA3REVERSE"], [8, 2, 1, "", "TIMEDELTA4FORWARD"], [8, 2, 1, "", "TIMEDELTA4REVERSE"], [8, 2, 1, "", "TIMESTAMP2"], [8, 2, 1, "", "TIMEXOR7BIT"], [8, 2, 1, "", "VALUE1"], [8, 2, 1, "", "VALUE2"], [8, 2, 1, "", "VALUE3"], [8, 2, 1, "", "VALUEXOR12"], [8, 2, 1, "", "VALUEXOR16"], [8, 2, 1, "", "VALUEXOR20"], [8, 2, 1, "", "VALUEXOR24"], [8, 2, 1, "", "VALUEXOR28"], [8, 2, 1, "", "VALUEXOR32"], [8, 2, 1, "", "VALUEXOR4"], [8, 2, 1, "", "VALUEXOR8"], [8, 2, 1, "", "VALUEZERO"]], "sttp.transport.tssc.pointmetadata.PointMetadata": [[8, 3, 1, "", "read_code"], [8, 3, 1, "", "write_code"]], "sttp.version": [[3, 1, 1, "", "Version"]], "sttp.version.Version": [[3, 2, 1, "", "STTP_SOURCE"], [3, 2, 1, "", "STTP_UPDATEDON"], [3, 2, 1, "", "STTP_VERSION"]]}, "objnames": {"0": ["py", "module", "Python module"], "1": ["py", "class", "Python class"], "2": ["py", "attribute", "Python attribute"], "3": ["py", "method", "Python method"], "4": ["py", "function", "Python function"], "5": ["py", "property", "Python property"], "6": ["py", "data", "Python data"], "7": ["py", "exception", "Python exception"]}, "objtypes": {"0": "py:module", "1": "py:class", "2": "py:attribute", "3": "py:method", "4": "py:function", "5": "py:property", "6": "py:data", "7": "py:exception"}, "terms": {"": [3, 7], "0": [0, 3, 4, 6, 7, 8], "00": 3, "000": 3, "0000": [0, 6, 7], "00000000": [0, 6, 7], "000000000000": [0, 6, 7], "0001": 3, "01": 3, "02": 3, "04": 3, "05": 3, "0dbc4ffef1e8": [3, 7], "0x3fffffffffffffff": 3, "0x4000000000000000": 3, "0x8000000000000000": 3, "1": [0, 3, 4, 6, 7, 8], "10": [3, 4, 6, 7, 8], "100": 3, "10000": 3, "10000000": 3, "1024": 7, "1048576": 7, "1073741824": 7, "11": [4, 6, 7, 8], "11ff913e7877": [3, 7], "12": [3, 4, 6, 7, 8], "128": 7, "129": 7, "13": [4, 6, 7, 8], "130": 7, "131": 7, "131072": 7, "132": 7, "133": 7, "134": 7, "134217728": 7, "135": 7, "136": 7, "137": 7, "138": 7, "14": [4, 8], "1420": 0, "15": [3, 4, 7, 8], "16": [0, 3, 4, 7, 8], "16384": 7, "16711680": 7, "16777216": 7, "17": [4, 8], "18": [4, 8], "18446744073709551615": 0, "19": [4, 8], "1970": 3, "2": [0, 3, 4, 6, 7, 8], "20": [3, 4, 7, 8], "2001": 4, "2006": 3, "2024": [3, 7], "2048": 7, "208": 7, "209": 7, "2097152": 7, "21": [4, 8], "210": 7, "211": 7, "212": 7, "213": 7, "214": 7, "2147483647": 0, "2147483648": [0, 7], "215": 7, "216": 7, "217": 7, "218": 7, "219": 7, "22": [4, 8], "220": 7, "221": 7, "222": 7, "223": 7, "224": 7, "225": 7, "226": 7, "227": 7, "228": 7, "229": 7, "23": [4, 8], "230": 7, "231": 7, "232": 7, "233": 7, "234": 7, "235": 7, "236": 7, "237": 7, "238": 7, "239": 7, "24": [4, 8], "25": [4, 8], "255": [0, 7], "256": 7, "26": [4, 8], "262144": 7, "268435456": 7, "27": [4, 8], "28": [4, 8], "29": [4, 8], "3": [4, 6, 7, 8], "30": [3, 4, 6, 7, 8], "31": [4, 8], "3155378975999999999": 0, "32": [0, 4, 7], "32767": 0, "32768": [0, 7], "33": 4, "33554432": 7, "34": 4, "35": 4, "35bd": [3, 7], "36": 4, "36000000000": 3, "366": 4, "37": 4, "38": 4, "38a47b0": [3, 7], "39": 4, "4": [0, 3, 4, 6, 7, 8], "40": 4, "4096": 7, "4143": [3, 7], "4194304": 7, "4294967295": 0, "4611686018427387903": 3, "4611686018427387904": 3, "4e5b": [3, 7], "5": [3, 4, 6, 7, 8], "500": 6, "512": 7, "524288": 7, "53": 4, "536870912": 7, "58": 3, "59": [3, 4], "6": [3, 4, 6, 7, 8], "60": 3, "600000000": 3, "62": [3, 7], "621355968000000000": 3, "63": 3, "63rd": 3, "64": [0, 3, 4, 7], "64th": 3, "65535": 0, "65536": 7, "67108864": 7, "7": [0, 4, 6, 7, 8], "768": 7, "8": [0, 4, 6, 7, 8], "8192": 7, "8388608": 7, "864000000000": 3, "9": [4, 6, 7, 8], "9223372036854775807": 0, "9223372036854775808": [0, 3], "92c9": [3, 7], "999": [3, 4], "999999999": 3, "9a0a": [3, 7], "A": [3, 6, 7], "AND": [3, 4, 7], "As": 7, "BY": 4, "For": [0, 4], "If": [3, 4, 6, 7], "In": 4, "NOT": [3, 4], "Not": 4, "OR": [3, 4], "One": 4, "The": [0, 3, 4, 6, 7, 8], "There": 3, "These": 4, "Will": 0, "With": [3, 7], "ab": [3, 4], "absolut": 4, "abstract": 0, "access": [3, 4, 6, 7], "accessid": [5, 6], "accomplish": 4, "accord": 7, "acronym": [3, 5, 6, 7], "across": 5, "activ": [3, 7], "activemeasur": [3, 4, 7], "activesignalindexcach": [2, 3, 7], "actual": 3, "add": [3, 4], "add_column": [3, 4], "add_measur": [3, 5], "add_row": [3, 4], "add_tabl": [3, 4], "adder": [3, 5, 6, 7], "addit": 6, "addition": 7, "address": 3, "adjust": [3, 6, 7], "adjustedvalu": [2, 3, 7], "advanc": 0, "affect": 7, "after": 3, "against": [3, 7], "agre": 7, "alarm": [3, 7], "alarmhigh": [3, 7], "alarmlow": [3, 7], "algorithm": [7, 8], "aliv": 7, "all": [0, 3, 4, 7], "allow": [3, 6, 7], "alog": [5, 6], "along": 7, "alpha": 6, "alreadi": 3, "alrm": [5, 6], "also": 4, "alwai": 7, "an": [0, 3, 4, 7], "analog": [3, 7], "ancillari": 6, "angl": [3, 5, 6, 7], "angle_measur": [5, 6], "ani": [3, 4, 6, 7], "antlr": 4, "api": [3, 7], "appli": [3, 4, 7], "applic": [3, 6, 7], "applylimit": 4, "applysort": 4, "applyto_bool": [3, 4], "applyto_decim": [3, 4], "applyto_doubl": [3, 4], "applyto_int32": [3, 4], "applyto_int64": [3, 4], "ar": [3, 4, 5, 7], "arbitrari": 4, "argument": [3, 4], "around": 0, "arrai": 0, "arriv": 3, "asc": 4, "ascend": [3, 4], "assign": [3, 4, 7, 8], "associ": [4, 6, 7], "atom": 7, "attempt": [4, 7], "auto": 7, "automat": [3, 4, 7], "autoreconnect": [2, 3, 7], "autoreconnect_callback": [3, 7], "autorequestmetadata": [2, 3, 7], "autosubscrib": [2, 3, 7], "avail": [0, 3, 6], "b": 6, "back": [3, 7], "backward": 7, "bad": 7, "baddata": [3, 7], "badtim": [3, 7], "base": [0, 3, 4, 5, 6, 7, 8], "basekv": [5, 6], "basetimeoffset": [3, 7], "basic": [0, 3, 7], "been": [0, 3, 4, 7], "befor": [3, 4], "begin": 4, "being": [3, 4, 7], "between": [4, 7], "beyond": 3, "big": [0, 7], "bigendian": [0, 2], "binari": [4, 7], "binarystream": 2, "bind": 3, "bit": [0, 3, 4, 7], "bitshiftleft": [3, 4], "bitshiftright": [3, 4], "bitwis": 4, "bitwiseand": [3, 4], "bitwiseor": [3, 4], "bitwisexor": [3, 4], "block": [3, 7], "bool": [0, 3, 4, 7, 8], "boolean": [3, 4, 7], "booleanvalu": [3, 4], "booleanvalue_bynam": [3, 4], "both": [3, 4], "boundari": [4, 6, 7], "br": 4, "buffer": [0, 3, 4, 7, 8], "bufferblock": [2, 3], "bus1": [3, 7], "byte": [0, 3, 4, 7, 8], "bytearrai": [0, 7], "byteord": 0, "bytes": [0, 2], "c": 6, "cach": [2, 3, 4, 6, 7], "cacheindex": [3, 7], "calc": [5, 6], "calcul": [3, 7], "calculatedvalu": [3, 7], "calculationerror": [3, 7], "calculationwarn": [3, 7], "calendar": 3, "call": [0, 4, 7], "callabl": [0, 3, 4, 7, 8], "callback": [3, 4], "callbackerrorlisten": [2, 3], "can": [0, 3, 4, 6, 7], "cancel": [3, 7], "cannot": [3, 4], "case": [3, 4, 7], "ceil": [3, 4], "chang": [3, 7], "channel": [3, 7], "charact": [4, 7], "check": [3, 6, 7], "cipher": 7, "cipherindex": [3, 7], "cl": 0, "class": [0, 3, 4, 5, 6, 7, 8], "classmethod": [0, 4, 6], "claus": 4, "cleanli": [3, 7], "clear": [4, 7], "clear_column": [3, 4], "clear_row": [3, 4], "clear_tabl": [3, 4], "client": [3, 7], "clock": [3, 7], "clone_column": [3, 4], "clone_row": [3, 4], "coalesc": [3, 4], "code": [6, 7, 8], "codeword": [7, 8], "collect": [4, 5], "colon": [3, 4], "column": [3, 4], "column_bynam": [3, 4], "columncount": [3, 4], "columnexpress": [2, 3], "columnindex": [3, 4], "columnnam": 4, "columnnamecontext": 4, "columnvalue_as_str": [3, 4], "com": 4, "combin": 4, "comma": 4, "command": [3, 7], "commandcod": 7, "common": [3, 4, 6], "commun": 7, "compact": [3, 7], "compactmeasur": [2, 3], "compactstateflag": [3, 7], "compani": [3, 6, 7], "companyacronym": [5, 6], "compar": 4, "compare_datarowcolumn": [3, 4], "comparis": 4, "comparison": 7, "comparisonalarm": [3, 7], "compat": 7, "complet": [3, 7], "compositephasormeasur": [5, 6], "compress": [3, 7, 8], "compress_metadata": [2, 3, 7], "compress_payloaddata": [2, 3, 7], "compress_signalindexcach": [2, 3, 7], "compressionmod": [3, 7], "compressmetadata": [3, 7], "compresspayloaddata": [3, 7], "compresssignalindexcach": [3, 7], "comput": [3, 4], "config": 2, "configur": [3, 7], "configurationchang": [3, 7], "configurationchanged_callback": [3, 7], "confirm": 7, "confirmbufferblock": [3, 7], "confirmnotif": [3, 7], "confirmupdatebasetim": [3, 7], "confirmupdatecipherkei": [3, 7], "confirmupdatesignalindexcach": [3, 7], "connect": [2, 3, 7], "connectionterminated_callback": [3, 7], "connector": [3, 7], "connectstatu": [3, 7], "consid": [3, 7], "consist": 4, "constant": [2, 3], "constraint": [3, 7], "constraintparamet": [2, 3, 7], "construct": 0, "contain": [3, 4, 7], "content": 2, "context": [6, 7], "continu": 0, "control": [3, 7], "convers": 0, "convert": [0, 2, 3, 4], "coordin": 7, "copi": 4, "could": [4, 7], "count": [0, 3, 7], "creat": [4, 7], "create_column": [3, 4], "create_row": [3, 4], "create_t": [3, 4], "criteria": 4, "ctx": 4, "current": [3, 4, 6, 7], "custom": [3, 7], "cycl": 3, "d": 7, "dai": [3, 4], "data": [2, 3, 6, 7, 8], "data_starttime_callback": [3, 7], "databas": 4, "datachannel_interfac": [3, 7], "datachannel_localport": [3, 7], "datacolumn": [2, 3], "datapacket": [3, 7], "datapacketflag": [3, 7], "datapublish": 7, "dataqu": [3, 7], "datarang": [3, 7], "datarow": [2, 3], "dataset": [2, 3, 5], "datastarttim": [3, 7], "datasubscrib": [2, 3, 6], "datat": [2, 3], "datatyp": [2, 3], "date": 4, "dateadd": [3, 4], "datediff": [3, 4], "datepart": [3, 4], "datetim": [0, 2, 3, 4, 6, 7], "datetimevalu": [3, 4], "datetimevalue_bynam": [3, 4], "dattim": 3, "dayofyear": [3, 4], "decim": [0, 2, 3, 4, 6], "decimalvalu": [3, 4], "decimalvalue_bynam": [3, 4], "decod": [0, 3, 7], "decodestr": [3, 7], "default": [3, 4, 6, 7], "default_add": [5, 6], "default_autoreconnect": [2, 3, 7], "default_autorequestmetadata": [2, 3], "default_autosubscrib": [2, 3], "default_basekv": [5, 6], "default_buff": [3, 7], "default_byteord": [0, 2], "default_companynam": [5, 6], "default_compress_metadata": [2, 3, 7], "default_compress_payloaddata": [2, 3, 7], "default_compress_signalindexcach": [2, 3, 7], "default_connectionestablished_receiv": [2, 3], "default_connectionterminated_receiv": [2, 3], "default_constraintparamet": [2, 3, 7], "default_datachannel_interfac": [3, 7], "default_datachannel_localport": [3, 7], "default_datatyp": [3, 4], "default_descript": [5, 6], "default_deviceacronym": [5, 6], "default_enable_time_reasonability_check": [2, 3, 7], "default_errormessage_callback": [3, 7], "default_errormessage_logg": [2, 3], "default_extra_connectionstring_paramet": [2, 3, 7], "default_filterexpress": [3, 7], "default_flag": [3, 7], "default_framespersecond": [5, 6], "default_hostnam": [3, 7], "default_id": [5, 6], "default_includetim": [2, 3, 7], "default_lagtim": [2, 3, 7], "default_latitud": [5, 6], "default_leadtim": [2, 3, 7], "default_longitud": [5, 6], "default_maxretri": [2, 3, 7], "default_maxretryinterv": [2, 3, 7], "default_metadatafilt": [2, 3], "default_multipli": [5, 6], "default_nam": [3, 4], "default_parentacronym": [5, 6], "default_phas": [5, 6], "default_pointtag": [5, 6], "default_port": [3, 7], "default_processinginterv": [2, 3, 7], "default_protocolnam": [5, 6], "default_publishinterv": [2, 3, 7], "default_reconnect_callback": [3, 7], "default_request_nanvaluefilt": [2, 3, 7], "default_retryinterv": [2, 3, 7], "default_signalid": [3, 5, 6, 7], "default_signalrefer": [5, 6], "default_signaltypenam": [5, 6], "default_socket_timeout": [2, 3, 7], "default_sourc": [5, 6], "default_starttim": [2, 3, 7], "default_statusmessage_logg": [2, 3], "default_stoptim": [2, 3, 7], "default_sttp_sourceinfo": [3, 7], "default_sttp_updatedoninfo": [3, 7], "default_sttp_versioninfo": [3, 7], "default_tableidfield": [3, 4], "default_throttl": [2, 3, 7], "default_timestamp": [3, 7], "default_typ": [5, 6], "default_udpdatachannel": [3, 7], "default_udpinterfac": [2, 3], "default_udpport": [2, 3], "default_updatedon": [5, 6], "default_use_localclock_as_realtim": [2, 3, 7], "default_use_millisecondresolut": [2, 3, 7], "default_valu": [3, 7], "default_vendoracronym": [5, 6], "default_vendordevicenam": [5, 6], "default_vers": [2, 3, 7], "defaultvalu": 4, "defin": [0, 3, 4, 5, 6, 7], "defineoperationalmod": [3, 7], "definit": 4, "delai": [3, 7], "delet": [3, 4], "delimit": 4, "deliv": 3, "deliveri": 7, "delta": 7, "denot": 3, "deploy": 6, "deriv": 4, "derive_arithmetic_operationvaluetyp": [3, 4], "derive_arithmetic_operationvaluetype_fromboolean": [3, 4], "derive_arithmetic_operationvaluetype_fromdecim": [3, 4], "derive_arithmetic_operationvaluetype_fromdoubl": [3, 4], "derive_arithmetic_operationvaluetype_fromint32": [3, 4], "derive_arithmetic_operationvaluetype_fromint64": [3, 4], "derive_boolean_operationvaluetyp": [3, 4], "derive_comparison_operationvaluetyp": [3, 4], "derive_comparison_operationvaluetype_fromboolean": [3, 4], "derive_comparison_operationvaluetype_fromdatetim": [3, 4], "derive_comparison_operationvaluetype_fromdecim": [3, 4], "derive_comparison_operationvaluetype_fromdoubl": [3, 4], "derive_comparison_operationvaluetype_fromguid": [3, 4], "derive_comparison_operationvaluetype_fromint32": [3, 4], "derive_comparison_operationvaluetype_fromint64": [3, 4], "derive_integer_operationvaluetyp": [3, 4], "derive_integer_operationvaluetype_fromboolean": [3, 4], "derive_integer_operationvaluetype_fromint32": [3, 4], "derive_integer_operationvaluetype_fromint64": [3, 4], "derive_operationvaluetyp": [3, 4], "desc": 4, "descript": [5, 6], "deseri": 3, "design": 3, "desir": [3, 7], "detail": 4, "determin": [3, 4, 7], "deviat": [3, 7], "devic": [3, 5, 7], "device_record": [3, 5], "deviceacronym": [5, 6], "deviceacronym_device_map": [3, 5], "deviceid": [5, 6], "deviceid_device_map": [3, 5], "devicerecord": [5, 6], "df": 7, "dfdt": [3, 5, 6, 7], "dict": [4, 5], "did": 4, "digi": [5, 6], "digit": [3, 7], "direct": [3, 4], "directli": [3, 4, 7], "discard": 7, "discardedvalu": [3, 7], "disconnect": [2, 3, 7], "dispar": 5, "dispos": [2, 3, 7], "divid": [3, 4], "dn": 7, "do": [3, 4], "document": [0, 4], "doe": [3, 4, 7], "dom_gplain": [3, 7], "doubl": [0, 2, 3, 4], "doublevalu": [3, 4], "doublevalue_bynam": [3, 4], "down": [3, 7], "downsampl": [3, 7], "drop": 3, "dt": [3, 7], "dtype": 0, "duplic": 4, "dure": [3, 4, 7], "e": [0, 3, 4, 6, 7], "e4bbfe6a": [3, 7], "each": [3, 4, 7], "effect": 3, "either": 4, "elaps": 3, "element": 4, "ellipsi": [3, 4, 5, 6, 7], "empti": [0, 2, 3, 4, 7], "emptystringvalu": [3, 4], "enabl": [3, 4, 7], "enable_time_reasonability_check": [3, 7], "enabletimereasonabilitycheck": [2, 3, 7], "encod": [0, 7, 8], "encodestr": [3, 7], "encoding7bit": 2, "encodingmask": [3, 7], "encount": 4, "encrypt": 7, "end": [3, 4, 7], "endian": [0, 7], "endianord": 2, "endofstream": [7, 8], "endswith": [3, 4], "enterexpress": [3, 4], "enterfilterexpressionstat": [3, 4], "enterfilterstat": [3, 4], "entir": 0, "enum": 0, "enumer": [4, 6, 7], "epoch": 3, "equal": [3, 4], "equalexactmatch": [3, 4], "error": [2, 3, 7], "errorlisten": 4, "errormessag": [2, 3], "errormessage_callback": [3, 7], "establish": [0, 3, 7], "etc": 6, "evalu": [3, 4], "evaluate_datarowexpress": [3, 4], "evaluate_express": [3, 4], "evaluateerror": [3, 4], "even": [3, 7], "exact": 4, "exactmatch": [3, 4], "exampl": [3, 7], "except": [3, 4, 7, 8], "exchang": 7, "exclud": [3, 7], "execut": [0, 3], "exhaust": 6, "exist": [3, 4, 7], "exit": [3, 7], "exitcolumnnam": [3, 4], "exitexpress": [3, 4], "exitfunctionexpress": [3, 4], "exitidentifierstat": [3, 4], "exitliteralvalu": [3, 4], "exitpredicateexpress": [3, 4], "exitvalueexpress": [3, 4], "expect": [0, 4], "exponenti": [3, 7], "express": [2, 3, 7], "expressioncontext": 4, "expressionfunctiontyp": [3, 4], "expressionoperatortyp": [3, 4], "expressiontre": [2, 3], "expressiontyp": [3, 4], "expressionunarytyp": [3, 4], "expressionvaluetyp": [3, 4], "expressionvaluetypelen": [3, 4], "expresss": 4, "ext_xmlschemadata_namespac": [3, 4], "extactmatch": [3, 4], "extdatatyp": 4, "extend": [4, 7], "extens": 7, "extern": 7, "extra": [3, 7], "extra_connectionstring_paramet": [2, 3, 7], "f10b": [3, 7], "fail": [3, 4, 7], "failur": 7, "fals": [0, 3, 4, 7], "falsevalu": [3, 4], "fast": [3, 7], "feedback": 3, "field": 4, "filter": [3, 4, 7], "filtered_row": [3, 4], "filtered_rowset": [3, 4], "filtered_signalid": [3, 4], "filtered_signalidset": [3, 4], "filterexpress": [3, 4, 7], "filterexpression_statementcount": [3, 4], "filterexpressionpars": [2, 3], "filterexpressionstatementcontext": 4, "filterexpressionsyntaxlisten": 4, "filterstatementcontext": 4, "final": 7, "find": 7, "find_devic": [3, 5], "find_device_acronym": [3, 5], "find_device_id": [3, 5], "find_measur": [3, 5], "find_measurement_id": [3, 5], "find_measurement_pointtag": [3, 5], "find_measurement_signalid": [3, 5], "find_measurement_signalrefer": [3, 5], "find_measurements_signaltyp": [3, 5], "find_measurements_signaltypenam": [3, 5], "first": [3, 4, 7], "five": 0, "flag": [3, 4, 5, 6, 7], "flat": 7, "flatlinealarm": [3, 7], "float": [3, 7], "float16": [0, 2], "float32": [0, 2, 4, 8], "float64": [0, 2, 3, 4, 6, 7], "floor": [3, 4], "flush": [0, 2], "follow": 7, "form": 6, "format": [3, 4, 7], "found": [3, 4, 7], "four": 7, "frame": 6, "framespersecond": [5, 6], "free": 6, "freq": [3, 5, 6, 7], "frequenc": [3, 7], "from": [0, 3, 4, 6, 7], "from_dataset": [3, 4], "from_datetim": [2, 3], "from_float16": [0, 2], "from_float32": [0, 2], "from_float64": [0, 2], "from_int16": [0, 2], "from_int32": [0, 2], "from_int64": [0, 2], "from_str": [0, 2], "from_timedelta": [2, 3], "from_uint16": [0, 2], "from_uint32": [0, 2], "from_uint64": [0, 2], "from_xml": [3, 4], "full": 3, "function": [0, 3, 4, 7], "functionexpress": [2, 3], "functionexpressioncontext": 4, "functiontyp": [3, 4], "futur": [3, 7], "futuretimealarm": [3, 7], "g": [0, 3, 4, 6, 7], "generate_expressiontre": [3, 4], "generate_expressiontrees_fromt": [3, 4], "get": [3, 4, 6, 7], "get_binarylength": [3, 7], "get_compact_stateflag": [3, 7], "get_timestamp_c2": [3, 7], "get_timestamp_c4": [3, 7], "getprimarymetadataschema": [3, 7], "getsignalselectionschema": [3, 7], "github": 4, "given": [4, 5, 6, 7], "global": 7, "gpa": [3, 7], "greater": [4, 7], "greaterthan": [3, 4], "greaterthanorequ": [3, 4], "gregorian": 3, "gsf": 2, "guid": [0, 2, 3, 4, 6, 7], "guidvalu": [3, 4], "guidvalue_bynam": [3, 4], "gzip": [3, 7], "ha": [0, 3, 4, 7], "handl": [0, 3, 7], "handler": 3, "has_notkeyword": [3, 4], "have": [0, 3, 4, 7], "helper": 7, "high": 7, "histor": [3, 7], "hostnam": [3, 7], "hour": [3, 4], "how": 7, "http": 4, "human": 7, "hundr": 3, "i": [0, 3, 4, 6, 7, 8], "id": [3, 4, 5, 6, 7], "id_measurement_map": [3, 5], "identif": [3, 4, 7], "identifi": [3, 6, 7], "identifierstatementcontext": 4, "ignor": 4, "iif": [3, 4], "imag": 7, "immedi": 3, "implement": [3, 4, 7], "implementationspecificextensionmask": [3, 7], "includ": [3, 4, 7], "includetim": [2, 3, 7], "incom": 3, "index": [1, 3, 4, 6, 7], "indexof": [3, 4], "indic": [3, 4, 7], "individu": 4, "infinit": [3, 7], "inform": [3, 4, 6, 7], "initi": [0, 3, 7], "inlist": [3, 4], "inlistexpress": [2, 3], "input": [0, 7], "insensit": 4, "instanc": [3, 4, 6, 7], "instancenam": 5, "instantan": 7, "int": [0, 3, 4, 6, 7], "int16": [0, 2, 3, 4], "int16valu": [3, 4], "int16value_bynam": [3, 4], "int32": [0, 2, 3, 4, 7, 8], "int32valu": [3, 4], "int32value_bynam": [3, 4], "int64": [0, 2, 3, 4, 7, 8], "int64valu": [3, 4], "int64value_bynam": [3, 4], "int8": [0, 2, 3, 4, 7, 8], "int8valu": [3, 4], "int8value_bynam": [3, 4], "integ": [0, 3, 4, 6, 7], "integervalu": [3, 4], "intend": 3, "intenum": [4, 6, 7], "interfac": [3, 7], "intern": [3, 4, 7], "interv": [3, 4, 7], "intflag": 7, "io": 4, "io_buffers": [0, 2], "ip": 7, "ipha": [5, 6], "iphm": [5, 6], "is_integertyp": [3, 4], "is_leapsecond": [2, 3], "is_negative_leapsecond": [2, 3], "is_nul": [3, 4], "is_numerictyp": [3, 4], "isdat": [3, 4], "isguid": [3, 4], "isinteg": [3, 4], "isnotnul": [3, 4], "isnul": [3, 4], "isnumer": [3, 4], "its": [3, 4, 6, 7], "januari": 3, "just": 3, "k": 6, "keep": 7, "kei": [3, 4, 5, 6, 7], "keyword": 4, "kind": 7, "kv": 6, "label": [5, 6], "lag": 7, "lagtim": [2, 3, 7], "languag": 4, "larger": 7, "largest": 4, "last": [3, 4, 6, 7], "lastindexof": [3, 4], "latch": 7, "late": 7, "latest": [3, 7], "latetimealarm": [3, 7], "latitud": [5, 6], "layer": 7, "lead": 7, "leadtim": [2, 3, 7], "leap": [3, 7], "leapsecond_direct": [2, 3], "leapsecond_flag": [2, 3], "left": 4, "leftrow": 4, "leftvalu": [3, 4], "leftvaluetyp": 4, "len": [3, 4], "length": [0, 4, 7], "less": 4, "lessthan": [3, 4], "lessthanorequ": [3, 4], "level": [6, 7], "librari": [3, 7], "like": [3, 4, 7], "likeexactmatch": [3, 4], "limit": [0, 2, 4], "line": [4, 7], "linear": [3, 6, 7], "list": [3, 4, 5, 6, 7], "listen": [4, 7], "literalvaluecontext": 4, "littl": [0, 7], "littleendian": [0, 2], "load": 4, "local": [3, 4, 7], "log": [3, 7], "logger": 3, "longer": [3, 7], "longitud": [5, 6], "lookup": [4, 7], "lookup_metadata": [3, 7], "low": 7, "lower": [3, 4], "magnitud": [3, 5, 6, 7], "magnitude_measur": [5, 6], "mai": [3, 6, 7], "maintain": 3, "make": 3, "manag": [0, 4], "manual": [3, 7], "map": [3, 4, 5, 7], "mark": [0, 3], "mask": 7, "match": 4, "maxbyt": [0, 2], "maximum": [3, 4, 7], "maxint16": [0, 2], "maxint32": [0, 2], "maxint64": [0, 2], "maxof": [3, 4], "maxretri": [2, 3, 7], "maxretryinterv": [2, 3, 7], "maxtick": [0, 2], "maxuint16": [0, 2], "maxuint32": [0, 2], "maxuint64": [0, 2], "mean": [3, 7], "measur": [2, 3, 4, 5, 8], "measurement_metadata": [2, 3], "measurement_record": [3, 5], "measurementerror": [3, 7], "measurementkey_fieldnam": [3, 4], "measurementread": [2, 3], "measurementrecord": [3, 5, 6, 7], "measurmentread": 3, "memori": 4, "messag": [3, 4, 7], "meta": [3, 7], "metadata": [2, 3, 4, 7], "metadatacach": [2, 3, 5, 7], "metadatafilt": [2, 3, 7], "metadatareceived_callback": [3, 7], "metadatarefresh": [3, 7], "method": [0, 3], "microsecond": 3, "microsoft": 4, "midnight": 3, "million": 3, "millionth": 3, "millisecond": [3, 4, 7], "minimum": 4, "minint16": [0, 2], "minint32": [0, 2], "minint64": [0, 2], "minof": [3, 4], "minu": [3, 4], "minut": [3, 4], "miss": 3, "mode": 7, "modifi": 6, "modul": [1, 2], "modulu": [3, 4], "month": [3, 4], "more": 4, "msdata": 4, "msg": 4, "multipl": [4, 6], "multipli": [3, 4, 5, 6, 7], "must": 3, "name": [3, 4, 5, 6, 7], "namespac": [3, 4], "nan": [3, 7], "nanosecond": 3, "nativ": 0, "nativeendian": [0, 2], "natur": 4, "nearest": 3, "need": [3, 4, 7], "neg": [3, 4], "network": 7, "new": [3, 4, 7], "newbufferblocks_callback": [3, 7], "newmeasurements_callback": [3, 7], "next": 7, "next_measur": [2, 3], "nil": 4, "nine": 0, "node": 6, "nodeid": [5, 6], "noflag": [3, 7], "nomin": 6, "non": [3, 4, 7], "none": [0, 3, 4, 5, 6, 7, 8], "noop": [3, 7], "normal": [3, 7], "normalize_enumnam": [0, 2], "note": [0, 3, 4, 7], "notequ": [3, 4], "notequalexactmatch": [3, 4], "notif": [3, 7], "notifi": [3, 7], "notificationreceived_callback": [3, 7], "notion": 0, "notlik": [3, 4], "notlikeexactmatch": [3, 4], "now": [2, 3, 4, 7], "np": [0, 3, 6, 7, 8], "nth": 4, "nthindexof": [3, 4], "null": 4, "nullboolvalu": [3, 4], "nulldatetimevalu": [3, 4], "nullint32valu": [3, 4], "nullstringvalu": [3, 4], "nullvalu": [3, 4], "number": [3, 4, 6, 7], "numer": [4, 6], "numpi": 4, "o": 0, "object": [0, 3, 4, 5, 6, 7, 8], "obsolet": 7, "occur": 3, "occurr": 4, "odd": 7, "off": [3, 7], "offendingsymbol": 4, "offset": [0, 7], "one": [0, 3, 4, 6, 7], "onli": [3, 4, 7], "oper": [0, 3, 4, 7], "operationalencod": [3, 7], "operationalmod": [3, 7], "operationtyp": 4, "operatorexpress": [2, 3], "operatortyp": [3, 4], "option": 7, "order": [0, 4, 6], "orderbyterm": [2, 3], "org": 4, "origin": [4, 6, 7], "other": 4, "otherwis": [3, 4, 6], "out": 4, "output": [0, 3], "outsid": 7, "over": [3, 7], "overrangeerror": [3, 7], "overrid": [0, 2], "packag": 2, "packet": 7, "page": 1, "paramet": [0, 2, 3, 4, 7], "parent": [3, 4, 6], "parentacronym": [5, 6], "pars": [3, 4, 5, 6, 7], "parse_acronym": [3, 7], "parse_xml": [3, 4], "parse_xmldoc": [3, 4], "parse_xsddatatyp": [3, 4], "parser": 4, "parsingexception_callback": [3, 4], "part": [4, 7], "parti": 7, "partial": 7, "past": [3, 7], "patten": 4, "payload": [3, 7], "per": [4, 6], "perdai": [2, 3], "perform": [3, 4, 7], "perhour": [2, 3], "period": 7, "permicrosecond": [2, 3], "permillisecond": [2, 3], "perminut": [2, 3], "persecond": [2, 3], "phase": [5, 6, 7], "phasor": [3, 5, 7], "phasorrecord": [3, 5, 6], "phasortyp": 7, "ping": 7, "place": 4, "playback": [3, 7], "plu": [3, 4], "point": [4, 5, 6], "pointidxor12": [7, 8], "pointidxor16": [7, 8], "pointidxor20": [7, 8], "pointidxor24": [7, 8], "pointidxor32": [7, 8], "pointidxor4": [7, 8], "pointidxor8": [7, 8], "pointmetadata": [3, 7], "pointtag": [4, 5, 6], "pointtag_fieldnam": [3, 4], "pointtag_measurement_map": [3, 5], "port": [3, 7], "portion": 3, "posit": [0, 3, 4], "possibl": [3, 4, 7], "post": 4, "power": [3, 4], "ppa": [3, 7], "pre": 7, "predic": 4, "predicateexpressioncontext": 4, "prefix": [4, 7], "prematur": 0, "preserv": 4, "primari": [4, 7], "primary_t": 4, "primary_tablenam": [3, 4], "primaryt": 4, "process": [3, 7], "processingcomplet": [3, 7], "processingcomplete_callback": [3, 7], "processinginterv": [2, 3, 7], "produc": 4, "properti": [0, 3, 4, 6, 7], "protocol": [3, 6, 7], "protocolnam": [5, 6], "provid": [0, 4, 7], "public": [3, 7], "publish": [3, 5, 7], "publishinterv": [2, 3, 7], "purpos": 0, "python": [0, 3, 4, 7], "qual": [5, 6], "qualiti": [3, 7], "qualnam": [4, 6, 7], "quiet": 7, "rais": 4, "rang": [4, 7], "rate": [6, 7], "raw": 7, "reach": [3, 7], "read": [0, 2, 3, 4], "read7bit_int32": [0, 2], "read7bit_int64": [0, 2], "read7bit_uint32": [0, 2], "read7bit_uint64": [0, 2], "read_al": [0, 2], "read_bool": [0, 2], "read_boolean": [0, 2], "read_buff": [0, 2], "read_byt": [0, 2], "read_cod": [7, 8], "read_guid": [0, 2], "read_int16": [0, 2], "read_int32": [0, 2], "read_int64": [0, 2], "read_measur": [2, 3], "read_str": [0, 2], "read_uint16": [0, 2], "read_uint32": [0, 2], "read_uint64": [0, 2], "readabl": 7, "readbit": 8, "readbits5": 8, "reader": [0, 2], "real": [3, 7], "reason": [3, 4, 7], "reattempt": [3, 7], "receipt": [3, 7], "receiv": [3, 4, 7], "receivedasbad": [3, 7], "receiveexternalmetadata": [3, 7], "receiveinternalmetadata": [3, 7], "recept": [3, 7], "recogn": 4, "recommend": 3, "reconnect": 7, "reconnect_callback": [3, 7], "record": [3, 4, 5, 7], "reduc": [3, 4], "reestablish": 7, "refer": [4, 5, 6, 7], "referenc": 4, "refresh": 7, "refus": 7, "regexmatch": [3, 4], "regexv": [3, 4], "regist": 4, "registri": [6, 7], "regular": 4, "relat": [3, 4, 7], "releas": 3, "remain": [3, 7], "remaind": 4, "remov": [4, 7], "remove_t": [3, 4], "replac": [3, 4], "report": [4, 6, 7], "repres": [3, 4, 5, 6, 7], "represent": 3, "request": [3, 7], "request_metadata": [2, 3], "request_nanvaluefilt": [2, 3, 7], "requestnanvaluefilt": [3, 7], "requir": 7, "reserv": 7, "reservedqualityflag": [3, 7], "reservedtimeflag": [3, 7], "reset": 7, "reset_connect": [3, 7], "resolut": [3, 7], "respect": [4, 6], "respons": [3, 7], "restrict": [3, 7], "result": [4, 7], "retri": [3, 7], "retryinterv": [2, 3, 7], "return": [0, 4, 6, 7], "revers": [3, 4, 7], "right": 4, "rightrow": 4, "rightvalu": [3, 4], "rightvaluetyp": 4, "rocalarm": [3, 7], "root": [3, 4], "rotatecipherkei": [3, 7], "round": [3, 4], "row": [3, 4], "rowcount": [3, 4], "rowindex": 4, "rowswher": [3, 4], "rowvalue_as_str": [3, 4], "rowvalue_as_string_bynam": [3, 4], "run": 7, "runtim": 7, "runtimeid": [3, 7], "safe": 7, "sampl": [3, 7], "schema": [4, 7], "search": 1, "searchval": 5, "second": [3, 4, 6, 7], "secur": 7, "securitymod": [3, 7], "see": [3, 4], "select": [3, 4, 7], "select_datarow": [3, 4], "select_datarows_fromt": [3, 4], "select_datarowset": [3, 4], "select_datarowset_fromt": [3, 4], "select_signalidset": [3, 4], "select_signalidset_fromt": [3, 4], "selectwher": [3, 4], "self": [0, 4], "semi": [3, 4], "send": [3, 7], "send_servercommand": [3, 7], "send_servercommand_withmessag": [3, 7], "sensit": 4, "sent": 7, "separ": [3, 4], "sequenc": [0, 7, 8], "sequencenumb": [7, 8], "seri": [7, 8], "serial": [3, 7], "server": 7, "servercommand": [3, 7], "serverrespons": [3, 7], "session": 7, "set": [2, 4, 6, 7], "set_buff": [7, 8], "set_compact_stateflag": [3, 7], "set_configurationchanged_receiv": [2, 3], "set_connectionestablished_receiv": [2, 3], "set_connectionterminated_receiv": [2, 3], "set_data_starttime_receiv": [2, 3], "set_errormessage_logg": [2, 3], "set_historicalreadcomplete_receiv": [2, 3], "set_leapsecond": [2, 3], "set_metadatanotification_receiv": [2, 3], "set_negative_leapsecond": [2, 3], "set_newbufferblock_receiv": [2, 3], "set_newmeasurements_receiv": [2, 3], "set_notification_receiv": [2, 3], "set_parsingexception_callback": [3, 4], "set_statusmessage_logg": [2, 3], "set_subscriptionupdated_receiv": [2, 3], "set_valu": [3, 4], "set_value_bynam": [3, 4], "shift": 4, "should": [3, 4, 7], "show": 3, "shut": [3, 7], "sign": 0, "signal": [3, 4, 5, 6, 7], "signalid": [3, 4, 5, 6, 7], "signalid_fieldnam": [3, 4], "signalid_measurement_map": [3, 5], "signalindex": [3, 7], "signalindexcach": [2, 3], "signalkind": [2, 3], "signalkindenum": [3, 7], "signalref_measurement_map": [3, 5], "signalrefer": [5, 6], "signaltyp": [3, 5, 6, 7], "signaltypenam": [5, 6], "similarli": 4, "simpl": [4, 7], "simpli": [0, 3, 7], "simplifi": 3, "sinc": [3, 7], "singl": [0, 2, 3, 4], "singlevalu": [3, 4], "singlevalue_bynam": [3, 4], "smallest": 4, "so": 3, "socket": [0, 3, 7], "socket_timeout": [2, 3, 7], "solicit": 7, "some": [6, 7], "soon": 7, "sort": 4, "sortord": 4, "sourc": [0, 3, 4, 5, 6, 7, 8], "source_buff": 0, "sourceindex": [5, 6], "special": [7, 8], "specif": 7, "specifi": [0, 3, 4, 7], "speed": [3, 7], "split": [3, 4], "sql": 4, "sqrt": [3, 4], "squar": 4, "standard": [3, 7], "start": [3, 4, 6, 7], "startindex": 0, "startswith": [3, 4], "starttim": [2, 3, 7], "stat": [3, 5, 6, 7], "state": 7, "stateflag": [3, 7], "stateflags2": [7, 8], "stateflags7bit32": [7, 8], "statement": 4, "static": [0, 3, 4, 7], "static_init": [0, 2], "statist": [3, 7], "statu": [3, 7], "statusmessag": [2, 3], "statusmessage_callback": [3, 7], "stderr": 3, "stdio": 3, "still": 4, "stop": [3, 7], "stoptim": [2, 3, 7], "str": [0, 3, 4, 5, 6, 7], "strcmp": [3, 4], "strcount": [3, 4], "stream": [0, 3, 7], "stream_read": 0, "stream_writ": 0, "streamencod": 2, "string": [0, 2, 3, 4, 6, 7], "stringvalu": [3, 4], "stringvalue_bynam": [3, 4], "structur": 4, "sttp": 2, "sttp_sourc": [2, 3], "sttp_sourceinfo": [3, 7], "sttp_updatedon": [2, 3], "sttp_updatedoninfo": [3, 7], "sttp_version": [2, 3], "sttp_versioninfo": [3, 7], "sub": [3, 7], "submodul": 2, "subpackag": 2, "subscrib": [2, 7], "subscriberconnector": [2, 3], "subscriberid": [2, 3, 7], "subscript": [3, 7], "subscriptioninfo": [2, 3], "subscriptionupdated_callback": [3, 7], "substr": [3, 4], "subtract": [3, 4], "succeed": [3, 7], "success": [3, 4, 7], "suffix": 4, "support": [3, 7], "suppress_console_erroroutput": 4, "suspect": 7, "suspectdata": [3, 7], "suspecttim": [3, 7], "swapord": [0, 2], "synchron": [3, 7, 8], "syntax": 4, "syntaxerror": [3, 4], "system": [5, 7], "systemerror": [3, 7], "systemissu": [3, 7], "systemwarn": [3, 7], "tabl": [3, 4], "tablecount": [3, 4], "tableidfield": [2, 3], "tableidfields_map": [3, 4], "tablenam": [3, 4], "tag": [3, 4, 5, 6, 7], "take": 3, "taken": 7, "target": [3, 4, 7], "target_buff": 0, "target_byteord": [0, 2], "target_typevalu": 4, "target_valuetyp": 4, "tcp": [3, 7], "td": 3, "tempor": [3, 7], "ten": 3, "termin": [3, 7], "test": 7, "text": 7, "than": 4, "thi": [0, 3, 4, 6, 7], "those": 7, "thread": 3, "threshold": 7, "throttl": [2, 3, 7], "tick": [0, 2, 7], "time": [3, 4, 7, 8], "timedelta": 3, "timedelta1forward": [7, 8], "timedelta1revers": [7, 8], "timedelta2forward": [7, 8], "timedelta2revers": [7, 8], "timedelta3forward": [7, 8], "timedelta3revers": [7, 8], "timedelta4forward": [7, 8], "timedelta4revers": [7, 8], "timeindex": [3, 7], "timeinterv": [3, 4], "timeout": [3, 7], "timequ": [3, 7], "timer": [3, 7], "timespec": 3, "timestamp": [3, 7], "timestamp2": [7, 8], "timestampvalu": [2, 3, 7], "timexor7bit": [7, 8], "timezon": [0, 6], "titl": [3, 7], "tl": [3, 7], "to_datetim": [2, 3], "to_float16": [0, 2], "to_float32": [0, 2], "to_float64": [0, 2], "to_int16": [0, 2], "to_int32": [0, 2], "to_int64": [0, 2], "to_shortstr": [2, 3], "to_str": [2, 3], "to_uint16": [0, 2], "to_uint32": [0, 2], "to_uint64": [0, 2], "toler": [3, 7], "took": 7, "top": 4, "toplimit": [3, 4], "total": [3, 4, 7], "total_commandchannel_bytesreceiv": [2, 3, 7], "total_datachannel_bytesreceiv": [2, 3, 7], "total_measurementsreceiv": [2, 3, 7], "tr": 4, "track": 4, "track_filteredrow": [3, 4], "track_filteredsignalid": [3, 4], "transfer": [3, 7], "transit": 7, "transmiss": 7, "transport": [2, 3], "travers": 4, "tree": 4, "trim": [3, 4], "trimleft": [3, 4], "trimright": [3, 4], "true": [0, 3, 4, 7], "truevalu": [3, 4], "try_get_measur": [7, 8], "tssc": [3, 7], "tupl": [3, 4, 7, 8], "tva_shelbi": [3, 7], "two": [0, 4, 6, 7], "type": [0, 4, 5, 6, 7], "typic": [5, 7], "tzinfo": [0, 6], "udp": [3, 7], "udpdatachannel": [3, 7], "udpinterfac": [2, 3], "udpport": [2, 3], "uint16": [0, 2, 3, 4, 7], "uint16valu": [3, 4], "uint16value_bynam": [3, 4], "uint32": [0, 2, 3, 4, 7, 8], "uint32valu": [3, 4], "uint32value_bynam": [3, 4], "uint64": [0, 2, 3, 4, 5, 6, 7], "uint64valu": [3, 4], "uint64value_bynam": [3, 4], "uint8": [0, 2, 3, 4], "uint8valu": [3, 4], "uint8value_bynam": [3, 4], "unari": [3, 4], "unaryexpress": [2, 3], "unarytyp": [3, 4], "undefin": [3, 4], "under": 7, "underrangeerror": [3, 7], "undetermin": 7, "unencrypt": 7, "unicod": 7, "uniqu": [4, 5, 6, 7], "unit": [4, 7], "unix": 3, "unixbaseoffset": [2, 3], "unkn": [5, 6], "unknown": [3, 7], "unreason": 7, "unsign": 0, "unsolicit": 7, "unsubscrib": [2, 3, 7], "unsupport": 7, "until": [0, 3], "up": [3, 7], "updat": [3, 6, 7], "updatebasetim": [3, 7], "updatecipherkei": [3, 7], "updatedon": [5, 6], "updateprocessinginterv": [3, 7], "updatesignalindexcach": [3, 7], "upon": [3, 7], "upper": [3, 4], "upsampl": [3, 7], "urn": 4, "us": [0, 3, 4, 7, 8], "use_localclock_as_realtim": [3, 7], "use_millisecondresolut": [2, 3, 7], "uselocalclockasrealtim": [2, 3, 7], "usemillisecondresolut": [3, 7], "user": [4, 7], "usercommand00": [3, 7], "usercommand01": [3, 7], "usercommand02": [3, 7], "usercommand03": [3, 7], "usercommand04": [3, 7], "usercommand05": [3, 7], "usercommand06": [3, 7], "usercommand07": [3, 7], "usercommand08": [3, 7], "usercommand09": [3, 7], "usercommand10": [3, 7], "usercommand11": [3, 7], "usercommand12": [3, 7], "usercommand13": [3, 7], "usercommand14": [3, 7], "usercommand15": [3, 7], "userdefinedflag1": [3, 7], "userdefinedflag2": [3, 7], "userdefinedflag3": [3, 7], "userdefinedflag4": [3, 7], "userdefinedflag5": [3, 7], "userresponse00": [3, 7], "userresponse01": [3, 7], "userresponse02": [3, 7], "userresponse03": [3, 7], "userresponse04": [3, 7], "userresponse05": [3, 7], "userresponse06": [3, 7], "userresponse07": [3, 7], "userresponse08": [3, 7], "userresponse09": [3, 7], "userresponse10": [3, 7], "userresponse11": [3, 7], "userresponse12": [3, 7], "userresponse13": [3, 7], "userresponse14": [3, 7], "userresponse15": [3, 7], "utc": [0, 3, 4, 6], "utcnow": [2, 3, 4], "utf": 7, "utf16b": [3, 7], "utf16l": [3, 7], "utf8": [3, 7], "uuid": [0, 3, 4, 5, 6, 7], "v": [6, 7], "valid": [0, 2, 4], "valu": [0, 3, 4, 6, 7], "value1": [7, 8], "value2": [7, 8], "value3": [7, 8], "value_as_str": [3, 4], "value_as_string_bynam": [3, 4], "value_buffers": [0, 2], "value_bynam": [3, 4], "valueexpress": [2, 3], "valueexpressioncontext": 4, "valuemask": [2, 3], "valuetyp": [3, 4], "valuexor12": [7, 8], "valuexor16": [7, 8], "valuexor20": [7, 8], "valuexor24": [7, 8], "valuexor28": [7, 8], "valuexor32": [7, 8], "valuexor4": [7, 8], "valuexor8": [7, 8], "valuezero": [7, 8], "variabl": 7, "vendor": 6, "vendoracronym": [5, 6], "vendordevicenam": [5, 6], "verifi": 7, "version": [2, 7], "versionmask": [3, 7], "vh": [3, 7], "via": [3, 4, 7], "view": [3, 7], "virtual": [0, 2], "voltag": [6, 7], "vpha": [5, 6], "vphm": [5, 6], "w3": 4, "w3c": 4, "wa": [3, 4, 6, 7], "wait": 3, "want": 7, "warn": 7, "warninghigh": [3, 7], "warninglow": [3, 7], "week": [3, 4], "weekdai": [3, 4], "when": [0, 3, 4, 6, 7], "where": [3, 4, 7], "whether": [3, 4, 7], "which": [3, 7], "while": [3, 4, 7], "whole": 7, "width": 7, "wire": [3, 7], "within": 4, "work": [4, 8], "would": [3, 4, 7], "wrap": 0, "write": [0, 2, 3], "write7bit_int32": [0, 2], "write7bit_int64": [0, 2], "write7bit_uint32": [0, 2], "write7bit_uint64": [0, 2], "write_bool": [0, 2], "write_boolean": [0, 2], "write_buff": [0, 2], "write_byt": [0, 2], "write_cod": [7, 8], "write_guid": [0, 2], "write_int16": [0, 2], "write_int32": [0, 2], "write_int64": [0, 2], "write_str": [0, 2], "write_uint16": [0, 2], "write_uint32": [0, 2], "write_uint64": [0, 2], "writebit": 8, "writer": 0, "www": 4, "xml": [3, 4], "xmlschema": 4, "xmlschema_namespac": [3, 4], "xor": 4, "xsd": 4, "xsdformat": [3, 4], "xsdtypenam": 4, "year": [3, 4], "yield": 4, "zero": [3, 4, 6, 7]}, "titles": ["gsf package", "Python STTP API documentation", "src", "sttp package", "sttp.data package", "sttp.metadata package", "sttp.metadata.record package", "sttp.transport package", "sttp.transport.tssc package"], "titleterms": {"api": 1, "binarystream": 0, "bufferblock": 7, "cach": 5, "callbackerrorlisten": 4, "columnexpress": 4, "compactmeasur": 7, "config": 3, "constant": [4, 7], "content": [0, 3, 4, 5, 6, 7, 8], "data": 4, "datacolumn": 4, "datarow": 4, "dataset": 4, "datasubscrib": 7, "datat": 4, "datatyp": 4, "decod": 8, "devic": 6, "document": 1, "encoding7bit": 0, "endianord": 0, "error": 4, "express": 4, "expressiontre": 4, "filterexpressionpars": 4, "functionexpress": 4, "gsf": 0, "indic": 1, "inlistexpress": 4, "measur": [6, 7], "metadata": [5, 6], "modul": [0, 3, 4, 5, 6, 7, 8], "operatorexpress": 4, "orderbyterm": 4, "packag": [0, 3, 4, 5, 6, 7, 8], "phasor": 6, "pointmetadata": 8, "python": 1, "reader": 3, "record": 6, "set": 3, "signalindexcach": 7, "signalkind": 7, "src": 2, "streamencod": 0, "sttp": [1, 3, 4, 5, 6, 7, 8], "submodul": [0, 3, 4, 5, 6, 7, 8], "subpackag": [3, 5, 7], "subscrib": 3, "subscriberconnector": 7, "subscriptioninfo": 7, "tabl": 1, "tableidfield": 4, "tick": 3, "transport": [7, 8], "tssc": 8, "unaryexpress": 4, "valueexpress": 4, "version": 3}})