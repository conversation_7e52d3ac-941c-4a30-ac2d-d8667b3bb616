

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>sttp.data package &mdash; sttp/pyapi 0.6.4 documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=92fd9be5" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=8209df7b" />

  
    <link rel="shortcut icon" href="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/favicon.ico"/>
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=bbec6902"></script>
      <script src="_static/documentation_options.js?v=b1f64a84"></script>
      <script src="_static/doctools.js?v=92e14aea"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            sttp/pyapi
              <img src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/sttp.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <!-- Local TOC -->
              <div class="local-toc"><ul>
<li><a class="reference internal" href="#">sttp.data package</a><ul>
<li><a class="reference internal" href="#submodules">Submodules</a></li>
<li><a class="reference internal" href="#module-sttp.data.callbackerrorlistener">sttp.data.callbackerrorlistener module</a><ul>
<li><a class="reference internal" href="#sttp.data.callbackerrorlistener.CallbackErrorListener"><code class="docutils literal notranslate"><span class="pre">CallbackErrorListener</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.callbackerrorlistener.CallbackErrorListener.parsingexception_callback"><code class="docutils literal notranslate"><span class="pre">CallbackErrorListener.parsingexception_callback</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.callbackerrorlistener.CallbackErrorListener.syntaxError"><code class="docutils literal notranslate"><span class="pre">CallbackErrorListener.syntaxError()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.data.columnexpression">sttp.data.columnexpression module</a><ul>
<li><a class="reference internal" href="#sttp.data.columnexpression.ColumnExpression"><code class="docutils literal notranslate"><span class="pre">ColumnExpression</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.columnexpression.ColumnExpression.datacolumn"><code class="docutils literal notranslate"><span class="pre">ColumnExpression.datacolumn</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.columnexpression.ColumnExpression.expressiontype"><code class="docutils literal notranslate"><span class="pre">ColumnExpression.expressiontype</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.data.constants">sttp.data.constants module</a><ul>
<li><a class="reference internal" href="#sttp.data.constants.EXPRESSIONVALUETYPELEN"><code class="docutils literal notranslate"><span class="pre">EXPRESSIONVALUETYPELEN</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.ABS"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.ABS</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.CEILING"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.CEILING</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.COALESCE"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.COALESCE</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.CONTAINS"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.CONTAINS</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.CONVERT"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.CONVERT</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.DATEADD"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.DATEADD</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.DATEDIFF"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.DATEDIFF</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.DATEPART"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.DATEPART</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.ENDSWITH"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.ENDSWITH</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.FLOOR"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.FLOOR</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.IIF"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.IIF</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.INDEXOF"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.INDEXOF</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.ISDATE"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.ISDATE</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.ISGUID"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.ISGUID</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.ISINTEGER"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.ISINTEGER</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.ISNULL"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.ISNULL</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.ISNUMERIC"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.ISNUMERIC</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.LASTINDEXOF"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.LASTINDEXOF</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.LEN"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.LEN</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.LOWER"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.LOWER</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.MAXOF"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.MAXOF</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.MINOF"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.MINOF</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.NOW"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.NOW</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.NTHINDEXOF"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.NTHINDEXOF</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.POWER"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.POWER</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.REGEXMATCH"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.REGEXMATCH</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.REGEXVAL"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.REGEXVAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.REPLACE"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.REPLACE</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.REVERSE"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.REVERSE</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.ROUND"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.ROUND</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.SPLIT"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.SPLIT</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.SQRT"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.SQRT</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.STARTSWITH"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.STARTSWITH</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.STRCMP"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.STRCMP</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.STRCOUNT"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.STRCOUNT</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.SUBSTR"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.SUBSTR</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.TRIM"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.TRIM</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.TRIMLEFT"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.TRIMLEFT</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.TRIMRIGHT"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.TRIMRIGHT</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.UPPER"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.UPPER</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType.UTCNOW"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.UTCNOW</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.ADD"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.ADD</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.AND"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.AND</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.BITSHIFTLEFT"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.BITSHIFTLEFT</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.BITSHIFTRIGHT"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.BITSHIFTRIGHT</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.BITWISEAND"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.BITWISEAND</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.BITWISEOR"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.BITWISEOR</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.BITWISEXOR"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.BITWISEXOR</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.DIVIDE"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.DIVIDE</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.EQUAL"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.EQUAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.EQUALEXACTMATCH"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.EQUALEXACTMATCH</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.GREATERTHAN"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.GREATERTHAN</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.GREATERTHANOREQUAL"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.GREATERTHANOREQUAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.ISNOTNULL"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.ISNOTNULL</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.ISNULL"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.ISNULL</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.LESSTHAN"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.LESSTHAN</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.LESSTHANOREQUAL"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.LESSTHANOREQUAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.LIKE"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.LIKE</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.LIKEEXACTMATCH"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.LIKEEXACTMATCH</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.MODULUS"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.MODULUS</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.MULTIPLY"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.MULTIPLY</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.NOTEQUAL"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.NOTEQUAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.NOTEQUALEXACTMATCH"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.NOTEQUALEXACTMATCH</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.NOTLIKE"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.NOTLIKE</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.NOTLIKEEXACTMATCH"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.NOTLIKEEXACTMATCH</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.OR"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.OR</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType.SUBTRACT"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.SUBTRACT</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionType"><code class="docutils literal notranslate"><span class="pre">ExpressionType</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionType.COLUMN"><code class="docutils literal notranslate"><span class="pre">ExpressionType.COLUMN</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionType.FUNCTION"><code class="docutils literal notranslate"><span class="pre">ExpressionType.FUNCTION</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionType.INLIST"><code class="docutils literal notranslate"><span class="pre">ExpressionType.INLIST</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionType.OPERATOR"><code class="docutils literal notranslate"><span class="pre">ExpressionType.OPERATOR</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionType.UNARY"><code class="docutils literal notranslate"><span class="pre">ExpressionType.UNARY</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionType.VALUE"><code class="docutils literal notranslate"><span class="pre">ExpressionType.VALUE</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionUnaryType"><code class="docutils literal notranslate"><span class="pre">ExpressionUnaryType</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionUnaryType.MINUS"><code class="docutils literal notranslate"><span class="pre">ExpressionUnaryType.MINUS</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionUnaryType.NOT"><code class="docutils literal notranslate"><span class="pre">ExpressionUnaryType.NOT</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionUnaryType.PLUS"><code class="docutils literal notranslate"><span class="pre">ExpressionUnaryType.PLUS</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionValueType"><code class="docutils literal notranslate"><span class="pre">ExpressionValueType</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionValueType.BOOLEAN"><code class="docutils literal notranslate"><span class="pre">ExpressionValueType.BOOLEAN</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionValueType.DATETIME"><code class="docutils literal notranslate"><span class="pre">ExpressionValueType.DATETIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionValueType.DECIMAL"><code class="docutils literal notranslate"><span class="pre">ExpressionValueType.DECIMAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionValueType.DOUBLE"><code class="docutils literal notranslate"><span class="pre">ExpressionValueType.DOUBLE</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionValueType.GUID"><code class="docutils literal notranslate"><span class="pre">ExpressionValueType.GUID</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionValueType.INT32"><code class="docutils literal notranslate"><span class="pre">ExpressionValueType.INT32</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionValueType.INT64"><code class="docutils literal notranslate"><span class="pre">ExpressionValueType.INT64</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionValueType.STRING"><code class="docutils literal notranslate"><span class="pre">ExpressionValueType.STRING</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.ExpressionValueType.UNDEFINED"><code class="docutils literal notranslate"><span class="pre">ExpressionValueType.UNDEFINED</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#sttp.data.constants.TimeInterval"><code class="docutils literal notranslate"><span class="pre">TimeInterval</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.constants.TimeInterval.DAY"><code class="docutils literal notranslate"><span class="pre">TimeInterval.DAY</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.TimeInterval.DAYOFYEAR"><code class="docutils literal notranslate"><span class="pre">TimeInterval.DAYOFYEAR</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.TimeInterval.HOUR"><code class="docutils literal notranslate"><span class="pre">TimeInterval.HOUR</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.TimeInterval.MILLISECOND"><code class="docutils literal notranslate"><span class="pre">TimeInterval.MILLISECOND</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.TimeInterval.MINUTE"><code class="docutils literal notranslate"><span class="pre">TimeInterval.MINUTE</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.TimeInterval.MONTH"><code class="docutils literal notranslate"><span class="pre">TimeInterval.MONTH</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.TimeInterval.SECOND"><code class="docutils literal notranslate"><span class="pre">TimeInterval.SECOND</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.TimeInterval.WEEK"><code class="docutils literal notranslate"><span class="pre">TimeInterval.WEEK</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.TimeInterval.WEEKDAY"><code class="docutils literal notranslate"><span class="pre">TimeInterval.WEEKDAY</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.TimeInterval.YEAR"><code class="docutils literal notranslate"><span class="pre">TimeInterval.YEAR</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.TimeInterval.parse"><code class="docutils literal notranslate"><span class="pre">TimeInterval.parse()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#sttp.data.constants.derive_arithmetic_operationvaluetype"><code class="docutils literal notranslate"><span class="pre">derive_arithmetic_operationvaluetype()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.derive_arithmetic_operationvaluetype_fromboolean"><code class="docutils literal notranslate"><span class="pre">derive_arithmetic_operationvaluetype_fromboolean()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.derive_arithmetic_operationvaluetype_fromdecimal"><code class="docutils literal notranslate"><span class="pre">derive_arithmetic_operationvaluetype_fromdecimal()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.derive_arithmetic_operationvaluetype_fromdouble"><code class="docutils literal notranslate"><span class="pre">derive_arithmetic_operationvaluetype_fromdouble()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.derive_arithmetic_operationvaluetype_fromint32"><code class="docutils literal notranslate"><span class="pre">derive_arithmetic_operationvaluetype_fromint32()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.derive_arithmetic_operationvaluetype_fromint64"><code class="docutils literal notranslate"><span class="pre">derive_arithmetic_operationvaluetype_fromint64()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.derive_boolean_operationvaluetype"><code class="docutils literal notranslate"><span class="pre">derive_boolean_operationvaluetype()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.derive_comparison_operationvaluetype"><code class="docutils literal notranslate"><span class="pre">derive_comparison_operationvaluetype()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.derive_comparison_operationvaluetype_fromboolean"><code class="docutils literal notranslate"><span class="pre">derive_comparison_operationvaluetype_fromboolean()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.derive_comparison_operationvaluetype_fromdatetime"><code class="docutils literal notranslate"><span class="pre">derive_comparison_operationvaluetype_fromdatetime()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.derive_comparison_operationvaluetype_fromdecimal"><code class="docutils literal notranslate"><span class="pre">derive_comparison_operationvaluetype_fromdecimal()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.derive_comparison_operationvaluetype_fromdouble"><code class="docutils literal notranslate"><span class="pre">derive_comparison_operationvaluetype_fromdouble()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.derive_comparison_operationvaluetype_fromguid"><code class="docutils literal notranslate"><span class="pre">derive_comparison_operationvaluetype_fromguid()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.derive_comparison_operationvaluetype_fromint32"><code class="docutils literal notranslate"><span class="pre">derive_comparison_operationvaluetype_fromint32()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.derive_comparison_operationvaluetype_fromint64"><code class="docutils literal notranslate"><span class="pre">derive_comparison_operationvaluetype_fromint64()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.derive_integer_operationvaluetype"><code class="docutils literal notranslate"><span class="pre">derive_integer_operationvaluetype()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.derive_integer_operationvaluetype_fromboolean"><code class="docutils literal notranslate"><span class="pre">derive_integer_operationvaluetype_fromboolean()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.derive_integer_operationvaluetype_fromint32"><code class="docutils literal notranslate"><span class="pre">derive_integer_operationvaluetype_fromint32()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.derive_integer_operationvaluetype_fromint64"><code class="docutils literal notranslate"><span class="pre">derive_integer_operationvaluetype_fromint64()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.derive_operationvaluetype"><code class="docutils literal notranslate"><span class="pre">derive_operationvaluetype()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.is_integertype"><code class="docutils literal notranslate"><span class="pre">is_integertype()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.constants.is_numerictype"><code class="docutils literal notranslate"><span class="pre">is_numerictype()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.data.datacolumn">sttp.data.datacolumn module</a><ul>
<li><a class="reference internal" href="#sttp.data.datacolumn.DataColumn"><code class="docutils literal notranslate"><span class="pre">DataColumn</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.datacolumn.DataColumn.computed"><code class="docutils literal notranslate"><span class="pre">DataColumn.computed</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datacolumn.DataColumn.datatype"><code class="docutils literal notranslate"><span class="pre">DataColumn.datatype</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datacolumn.DataColumn.expression"><code class="docutils literal notranslate"><span class="pre">DataColumn.expression</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datacolumn.DataColumn.index"><code class="docutils literal notranslate"><span class="pre">DataColumn.index</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datacolumn.DataColumn.name"><code class="docutils literal notranslate"><span class="pre">DataColumn.name</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datacolumn.DataColumn.parent"><code class="docutils literal notranslate"><span class="pre">DataColumn.parent</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.data.datarow">sttp.data.datarow module</a><ul>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow"><code class="docutils literal notranslate"><span class="pre">DataRow</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.booleanvalue"><code class="docutils literal notranslate"><span class="pre">DataRow.booleanvalue()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.booleanvalue_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.booleanvalue_byname()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.columnvalue_as_string"><code class="docutils literal notranslate"><span class="pre">DataRow.columnvalue_as_string()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.compare_datarowcolumns"><code class="docutils literal notranslate"><span class="pre">DataRow.compare_datarowcolumns()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.datetimevalue"><code class="docutils literal notranslate"><span class="pre">DataRow.datetimevalue()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.datetimevalue_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.datetimevalue_byname()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.decimalvalue"><code class="docutils literal notranslate"><span class="pre">DataRow.decimalvalue()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.decimalvalue_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.decimalvalue_byname()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.doublevalue"><code class="docutils literal notranslate"><span class="pre">DataRow.doublevalue()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.doublevalue_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.doublevalue_byname()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.guidvalue"><code class="docutils literal notranslate"><span class="pre">DataRow.guidvalue()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.guidvalue_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.guidvalue_byname()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.int16value"><code class="docutils literal notranslate"><span class="pre">DataRow.int16value()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.int16value_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.int16value_byname()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.int32value"><code class="docutils literal notranslate"><span class="pre">DataRow.int32value()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.int32value_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.int32value_byname()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.int64value"><code class="docutils literal notranslate"><span class="pre">DataRow.int64value()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.int64value_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.int64value_byname()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.int8value"><code class="docutils literal notranslate"><span class="pre">DataRow.int8value()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.int8value_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.int8value_byname()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.parent"><code class="docutils literal notranslate"><span class="pre">DataRow.parent</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.set_value"><code class="docutils literal notranslate"><span class="pre">DataRow.set_value()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.set_value_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.set_value_byname()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.singlevalue"><code class="docutils literal notranslate"><span class="pre">DataRow.singlevalue()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.singlevalue_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.singlevalue_byname()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.stringvalue"><code class="docutils literal notranslate"><span class="pre">DataRow.stringvalue()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.stringvalue_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.stringvalue_byname()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.uint16value"><code class="docutils literal notranslate"><span class="pre">DataRow.uint16value()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.uint16value_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.uint16value_byname()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.uint32value"><code class="docutils literal notranslate"><span class="pre">DataRow.uint32value()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.uint32value_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.uint32value_byname()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.uint64value"><code class="docutils literal notranslate"><span class="pre">DataRow.uint64value()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.uint64value_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.uint64value_byname()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.uint8value"><code class="docutils literal notranslate"><span class="pre">DataRow.uint8value()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.uint8value_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.uint8value_byname()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.value"><code class="docutils literal notranslate"><span class="pre">DataRow.value()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.value_as_string"><code class="docutils literal notranslate"><span class="pre">DataRow.value_as_string()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.value_as_string_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.value_as_string_byname()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datarow.DataRow.value_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.value_byname()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.data.dataset">sttp.data.dataset module</a><ul>
<li><a class="reference internal" href="#sttp.data.dataset.DataSet"><code class="docutils literal notranslate"><span class="pre">DataSet</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.dataset.DataSet.DEFAULT_NAME"><code class="docutils literal notranslate"><span class="pre">DataSet.DEFAULT_NAME</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.dataset.DataSet.add_table"><code class="docutils literal notranslate"><span class="pre">DataSet.add_table()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.dataset.DataSet.clear_tables"><code class="docutils literal notranslate"><span class="pre">DataSet.clear_tables()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.dataset.DataSet.create_table"><code class="docutils literal notranslate"><span class="pre">DataSet.create_table()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.dataset.DataSet.from_xml"><code class="docutils literal notranslate"><span class="pre">DataSet.from_xml()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.dataset.DataSet.name"><code class="docutils literal notranslate"><span class="pre">DataSet.name</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.dataset.DataSet.parse_xml"><code class="docutils literal notranslate"><span class="pre">DataSet.parse_xml()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.dataset.DataSet.parse_xmldoc"><code class="docutils literal notranslate"><span class="pre">DataSet.parse_xmldoc()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.dataset.DataSet.remove_table"><code class="docutils literal notranslate"><span class="pre">DataSet.remove_table()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.dataset.DataSet.table"><code class="docutils literal notranslate"><span class="pre">DataSet.table()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.dataset.DataSet.tablecount"><code class="docutils literal notranslate"><span class="pre">DataSet.tablecount</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.dataset.DataSet.tablenames"><code class="docutils literal notranslate"><span class="pre">DataSet.tablenames()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.dataset.DataSet.tables"><code class="docutils literal notranslate"><span class="pre">DataSet.tables()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#sttp.data.dataset.EXT_XMLSCHEMADATA_NAMESPACE"><code class="docutils literal notranslate"><span class="pre">EXT_XMLSCHEMADATA_NAMESPACE</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.dataset.XMLSCHEMA_NAMESPACE"><code class="docutils literal notranslate"><span class="pre">XMLSCHEMA_NAMESPACE</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.dataset.xsdformat"><code class="docutils literal notranslate"><span class="pre">xsdformat()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.data.datatable">sttp.data.datatable module</a><ul>
<li><a class="reference internal" href="#sttp.data.datatable.DataTable"><code class="docutils literal notranslate"><span class="pre">DataTable</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.datatable.DataTable.add_column"><code class="docutils literal notranslate"><span class="pre">DataTable.add_column()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatable.DataTable.add_row"><code class="docutils literal notranslate"><span class="pre">DataTable.add_row()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatable.DataTable.clear_columns"><code class="docutils literal notranslate"><span class="pre">DataTable.clear_columns()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatable.DataTable.clear_rows"><code class="docutils literal notranslate"><span class="pre">DataTable.clear_rows()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatable.DataTable.clone_column"><code class="docutils literal notranslate"><span class="pre">DataTable.clone_column()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatable.DataTable.clone_row"><code class="docutils literal notranslate"><span class="pre">DataTable.clone_row()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatable.DataTable.column"><code class="docutils literal notranslate"><span class="pre">DataTable.column()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatable.DataTable.column_byname"><code class="docutils literal notranslate"><span class="pre">DataTable.column_byname()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatable.DataTable.columncount"><code class="docutils literal notranslate"><span class="pre">DataTable.columncount</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatable.DataTable.columnindex"><code class="docutils literal notranslate"><span class="pre">DataTable.columnindex()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatable.DataTable.create_column"><code class="docutils literal notranslate"><span class="pre">DataTable.create_column()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatable.DataTable.create_row"><code class="docutils literal notranslate"><span class="pre">DataTable.create_row()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatable.DataTable.name"><code class="docutils literal notranslate"><span class="pre">DataTable.name</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatable.DataTable.parent"><code class="docutils literal notranslate"><span class="pre">DataTable.parent</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatable.DataTable.row"><code class="docutils literal notranslate"><span class="pre">DataTable.row()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatable.DataTable.rowcount"><code class="docutils literal notranslate"><span class="pre">DataTable.rowcount</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatable.DataTable.rowswhere"><code class="docutils literal notranslate"><span class="pre">DataTable.rowswhere()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatable.DataTable.rowvalue_as_string"><code class="docutils literal notranslate"><span class="pre">DataTable.rowvalue_as_string()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatable.DataTable.rowvalue_as_string_byname"><code class="docutils literal notranslate"><span class="pre">DataTable.rowvalue_as_string_byname()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatable.DataTable.select"><code class="docutils literal notranslate"><span class="pre">DataTable.select()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.data.datatype">sttp.data.datatype module</a><ul>
<li><a class="reference internal" href="#sttp.data.datatype.DataType"><code class="docutils literal notranslate"><span class="pre">DataType</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.datatype.DataType.BOOLEAN"><code class="docutils literal notranslate"><span class="pre">DataType.BOOLEAN</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatype.DataType.DATETIME"><code class="docutils literal notranslate"><span class="pre">DataType.DATETIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatype.DataType.DECIMAL"><code class="docutils literal notranslate"><span class="pre">DataType.DECIMAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatype.DataType.DOUBLE"><code class="docutils literal notranslate"><span class="pre">DataType.DOUBLE</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatype.DataType.GUID"><code class="docutils literal notranslate"><span class="pre">DataType.GUID</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatype.DataType.INT16"><code class="docutils literal notranslate"><span class="pre">DataType.INT16</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatype.DataType.INT32"><code class="docutils literal notranslate"><span class="pre">DataType.INT32</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatype.DataType.INT64"><code class="docutils literal notranslate"><span class="pre">DataType.INT64</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatype.DataType.INT8"><code class="docutils literal notranslate"><span class="pre">DataType.INT8</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatype.DataType.SINGLE"><code class="docutils literal notranslate"><span class="pre">DataType.SINGLE</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatype.DataType.STRING"><code class="docutils literal notranslate"><span class="pre">DataType.STRING</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatype.DataType.UINT16"><code class="docutils literal notranslate"><span class="pre">DataType.UINT16</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatype.DataType.UINT32"><code class="docutils literal notranslate"><span class="pre">DataType.UINT32</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatype.DataType.UINT64"><code class="docutils literal notranslate"><span class="pre">DataType.UINT64</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatype.DataType.UINT8"><code class="docutils literal notranslate"><span class="pre">DataType.UINT8</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#sttp.data.datatype.default_datatype"><code class="docutils literal notranslate"><span class="pre">default_datatype()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.datatype.parse_xsddatatype"><code class="docutils literal notranslate"><span class="pre">parse_xsddatatype()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.data.errors">sttp.data.errors module</a><ul>
<li><a class="reference internal" href="#sttp.data.errors.EvaluateError"><code class="docutils literal notranslate"><span class="pre">EvaluateError</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.data.expression">sttp.data.expression module</a><ul>
<li><a class="reference internal" href="#sttp.data.expression.Expression"><code class="docutils literal notranslate"><span class="pre">Expression</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.expression.Expression.expressiontype"><code class="docutils literal notranslate"><span class="pre">Expression.expressiontype</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.data.expressiontree">sttp.data.expressiontree module</a><ul>
<li><a class="reference internal" href="#sttp.data.expressiontree.ExpressionTree"><code class="docutils literal notranslate"><span class="pre">ExpressionTree</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.expressiontree.ExpressionTree.evaluate"><code class="docutils literal notranslate"><span class="pre">ExpressionTree.evaluate()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.expressiontree.ExpressionTree.orderbyterms"><code class="docutils literal notranslate"><span class="pre">ExpressionTree.orderbyterms</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.expressiontree.ExpressionTree.root"><code class="docutils literal notranslate"><span class="pre">ExpressionTree.root</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.expressiontree.ExpressionTree.select"><code class="docutils literal notranslate"><span class="pre">ExpressionTree.select()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.expressiontree.ExpressionTree.selectwhere"><code class="docutils literal notranslate"><span class="pre">ExpressionTree.selectwhere()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.expressiontree.ExpressionTree.tablename"><code class="docutils literal notranslate"><span class="pre">ExpressionTree.tablename</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.expressiontree.ExpressionTree.toplimit"><code class="docutils literal notranslate"><span class="pre">ExpressionTree.toplimit</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.data.filterexpressionparser">sttp.data.filterexpressionparser module</a><ul>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.dataset"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.dataset</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.enterExpression"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.enterExpression()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.enterFilterExpressionStatement"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.enterFilterExpressionStatement()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.enterFilterStatement"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.enterFilterStatement()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.evaluate"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.evaluate()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.evaluate_datarowexpression"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.evaluate_datarowexpression()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.evaluate_expression"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.evaluate_expression()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.exitColumnName"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.exitColumnName()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.exitExpression"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.exitExpression()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.exitFunctionExpression"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.exitFunctionExpression()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.exitIdentifierStatement"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.exitIdentifierStatement()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.exitLiteralValue"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.exitLiteralValue()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.exitPredicateExpression"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.exitPredicateExpression()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.exitValueExpression"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.exitValueExpression()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.expressiontrees"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.expressiontrees</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.filtered_rows"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.filtered_rows</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.filtered_rowset"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.filtered_rowset</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.filtered_signalids"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.filtered_signalids</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.filtered_signalidset"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.filtered_signalidset</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.filterexpression_statementcount"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.filterexpression_statementcount</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.from_dataset"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.from_dataset()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.generate_expressiontree"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.generate_expressiontree()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.generate_expressiontrees"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.generate_expressiontrees()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.generate_expressiontrees_fromtable"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.generate_expressiontrees_fromtable()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.primary_tablename"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.primary_tablename</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.select_datarows"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.select_datarows()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.select_datarows_fromtable"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.select_datarows_fromtable()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.select_datarowset"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.select_datarowset()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.select_datarowset_fromtable"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.select_datarowset_fromtable()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.select_signalidset"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.select_signalidset()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.select_signalidset_fromtable"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.select_signalidset_fromtable()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.set_parsingexception_callback"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.set_parsingexception_callback()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.table"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.table()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.tableidfields_map"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.tableidfields_map</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.track_filteredrows"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.track_filteredrows</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser.track_filteredsignalids"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.track_filteredsignalids</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.data.functionexpression">sttp.data.functionexpression module</a><ul>
<li><a class="reference internal" href="#sttp.data.functionexpression.FunctionExpression"><code class="docutils literal notranslate"><span class="pre">FunctionExpression</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.functionexpression.FunctionExpression.arguments"><code class="docutils literal notranslate"><span class="pre">FunctionExpression.arguments</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.functionexpression.FunctionExpression.expressiontype"><code class="docutils literal notranslate"><span class="pre">FunctionExpression.expressiontype</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.functionexpression.FunctionExpression.functiontype"><code class="docutils literal notranslate"><span class="pre">FunctionExpression.functiontype</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.data.inlistexpression">sttp.data.inlistexpression module</a><ul>
<li><a class="reference internal" href="#sttp.data.inlistexpression.InListExpression"><code class="docutils literal notranslate"><span class="pre">InListExpression</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.inlistexpression.InListExpression.arguments"><code class="docutils literal notranslate"><span class="pre">InListExpression.arguments</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.inlistexpression.InListExpression.exactmatch"><code class="docutils literal notranslate"><span class="pre">InListExpression.exactmatch</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.inlistexpression.InListExpression.expressiontype"><code class="docutils literal notranslate"><span class="pre">InListExpression.expressiontype</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.inlistexpression.InListExpression.has_notkeyword"><code class="docutils literal notranslate"><span class="pre">InListExpression.has_notkeyword</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.inlistexpression.InListExpression.value"><code class="docutils literal notranslate"><span class="pre">InListExpression.value</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.data.operatorexpression">sttp.data.operatorexpression module</a><ul>
<li><a class="reference internal" href="#sttp.data.operatorexpression.OperatorExpression"><code class="docutils literal notranslate"><span class="pre">OperatorExpression</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.operatorexpression.OperatorExpression.expressiontype"><code class="docutils literal notranslate"><span class="pre">OperatorExpression.expressiontype</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.operatorexpression.OperatorExpression.leftvalue"><code class="docutils literal notranslate"><span class="pre">OperatorExpression.leftvalue</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.operatorexpression.OperatorExpression.operatortype"><code class="docutils literal notranslate"><span class="pre">OperatorExpression.operatortype</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.operatorexpression.OperatorExpression.rightvalue"><code class="docutils literal notranslate"><span class="pre">OperatorExpression.rightvalue</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.data.orderbyterm">sttp.data.orderbyterm module</a><ul>
<li><a class="reference internal" href="#sttp.data.orderbyterm.OrderByTerm"><code class="docutils literal notranslate"><span class="pre">OrderByTerm</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.orderbyterm.OrderByTerm.ascending"><code class="docutils literal notranslate"><span class="pre">OrderByTerm.ascending</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.orderbyterm.OrderByTerm.column"><code class="docutils literal notranslate"><span class="pre">OrderByTerm.column</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.orderbyterm.OrderByTerm.extactmatch"><code class="docutils literal notranslate"><span class="pre">OrderByTerm.extactmatch</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.data.tableidfields">sttp.data.tableidfields module</a><ul>
<li><a class="reference internal" href="#sttp.data.tableidfields.DEFAULT_TABLEIDFIELDS"><code class="docutils literal notranslate"><span class="pre">DEFAULT_TABLEIDFIELDS</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.tableidfields.TableIDFields"><code class="docutils literal notranslate"><span class="pre">TableIDFields</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.tableidfields.TableIDFields.measurementkey_fieldname"><code class="docutils literal notranslate"><span class="pre">TableIDFields.measurementkey_fieldname</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.tableidfields.TableIDFields.pointtag_fieldname"><code class="docutils literal notranslate"><span class="pre">TableIDFields.pointtag_fieldname</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.tableidfields.TableIDFields.signalid_fieldname"><code class="docutils literal notranslate"><span class="pre">TableIDFields.signalid_fieldname</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.data.unaryexpression">sttp.data.unaryexpression module</a><ul>
<li><a class="reference internal" href="#sttp.data.unaryexpression.UnaryExpression"><code class="docutils literal notranslate"><span class="pre">UnaryExpression</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.unaryexpression.UnaryExpression.applyto_bool"><code class="docutils literal notranslate"><span class="pre">UnaryExpression.applyto_bool()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.unaryexpression.UnaryExpression.applyto_decimal"><code class="docutils literal notranslate"><span class="pre">UnaryExpression.applyto_decimal()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.unaryexpression.UnaryExpression.applyto_double"><code class="docutils literal notranslate"><span class="pre">UnaryExpression.applyto_double()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.unaryexpression.UnaryExpression.applyto_int32"><code class="docutils literal notranslate"><span class="pre">UnaryExpression.applyto_int32()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.unaryexpression.UnaryExpression.applyto_int64"><code class="docutils literal notranslate"><span class="pre">UnaryExpression.applyto_int64()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.unaryexpression.UnaryExpression.expressiontype"><code class="docutils literal notranslate"><span class="pre">UnaryExpression.expressiontype</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.unaryexpression.UnaryExpression.unarytype"><code class="docutils literal notranslate"><span class="pre">UnaryExpression.unarytype</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.unaryexpression.UnaryExpression.value"><code class="docutils literal notranslate"><span class="pre">UnaryExpression.value</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.data.valueexpression">sttp.data.valueexpression module</a><ul>
<li><a class="reference internal" href="#sttp.data.valueexpression.EMPTYSTRINGVALUE"><code class="docutils literal notranslate"><span class="pre">EMPTYSTRINGVALUE</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.valueexpression.FALSEVALUE"><code class="docutils literal notranslate"><span class="pre">FALSEVALUE</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.valueexpression.NULLBOOLVALUE"><code class="docutils literal notranslate"><span class="pre">NULLBOOLVALUE</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.valueexpression.NULLDATETIMEVALUE"><code class="docutils literal notranslate"><span class="pre">NULLDATETIMEVALUE</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.valueexpression.NULLINT32VALUE"><code class="docutils literal notranslate"><span class="pre">NULLINT32VALUE</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.valueexpression.NULLSTRINGVALUE"><code class="docutils literal notranslate"><span class="pre">NULLSTRINGVALUE</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.valueexpression.NULLVALUE"><code class="docutils literal notranslate"><span class="pre">NULLVALUE</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.valueexpression.TRUEVALUE"><code class="docutils literal notranslate"><span class="pre">TRUEVALUE</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression"><code class="docutils literal notranslate"><span class="pre">ValueExpression</span></code></a><ul>
<li><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression.booleanvalue"><code class="docutils literal notranslate"><span class="pre">ValueExpression.booleanvalue()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression.convert"><code class="docutils literal notranslate"><span class="pre">ValueExpression.convert()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression.datetimevalue"><code class="docutils literal notranslate"><span class="pre">ValueExpression.datetimevalue()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression.decimalvalue"><code class="docutils literal notranslate"><span class="pre">ValueExpression.decimalvalue()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression.doublevalue"><code class="docutils literal notranslate"><span class="pre">ValueExpression.doublevalue()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression.expressiontype"><code class="docutils literal notranslate"><span class="pre">ValueExpression.expressiontype</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression.guidvalue"><code class="docutils literal notranslate"><span class="pre">ValueExpression.guidvalue()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression.int32value"><code class="docutils literal notranslate"><span class="pre">ValueExpression.int32value()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression.int64value"><code class="docutils literal notranslate"><span class="pre">ValueExpression.int64value()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression.integervalue"><code class="docutils literal notranslate"><span class="pre">ValueExpression.integervalue()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression.is_null"><code class="docutils literal notranslate"><span class="pre">ValueExpression.is_null()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression.nullvalue"><code class="docutils literal notranslate"><span class="pre">ValueExpression.nullvalue()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression.stringvalue"><code class="docutils literal notranslate"><span class="pre">ValueExpression.stringvalue()</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression.value"><code class="docutils literal notranslate"><span class="pre">ValueExpression.value</span></code></a></li>
<li><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression.valuetype"><code class="docutils literal notranslate"><span class="pre">ValueExpression.valuetype</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.data">Module contents</a></li>
</ul>
</li>
</ul>
</div>
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">sttp/pyapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">sttp.data package</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/sttp.data.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="sttp-data-package">
<h1>sttp.data package<a class="headerlink" href="#sttp-data-package" title="Link to this heading"></a></h1>
<section id="submodules">
<h2>Submodules<a class="headerlink" href="#submodules" title="Link to this heading"></a></h2>
</section>
<section id="module-sttp.data.callbackerrorlistener">
<span id="sttp-data-callbackerrorlistener-module"></span><h2>sttp.data.callbackerrorlistener module<a class="headerlink" href="#module-sttp.data.callbackerrorlistener" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.callbackerrorlistener.CallbackErrorListener">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.callbackerrorlistener.</span></span><span class="sig-name descname"><span class="pre">CallbackErrorListener</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">callback</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/callbackerrorlistener.html#CallbackErrorListener"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.callbackerrorlistener.CallbackErrorListener" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">ErrorListener</span></code></p>
<p>Defines a implementation of an ANTLR error listener that reports
any parsing exceptions to a user defined callback.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.callbackerrorlistener.CallbackErrorListener.parsingexception_callback">
<span class="sig-name descname"><span class="pre">parsingexception_callback</span></span><a class="headerlink" href="#sttp.data.callbackerrorlistener.CallbackErrorListener.parsingexception_callback" title="Link to this definition"></a></dt>
<dd><p>Defines a callback for reporting ANTLR parsing exceptions.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.callbackerrorlistener.CallbackErrorListener.syntaxError">
<span class="sig-name descname"><span class="pre">syntaxError</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">recognizer</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">offendingSymbol</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">line</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">column</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">e</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/callbackerrorlistener.html#CallbackErrorListener.syntaxError"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.callbackerrorlistener.CallbackErrorListener.syntaxError" title="Link to this definition"></a></dt>
<dd><p>Called when ANTLR parser encounters a syntax error.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.data.columnexpression">
<span id="sttp-data-columnexpression-module"></span><h2>sttp.data.columnexpression module<a class="headerlink" href="#module-sttp.data.columnexpression" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.columnexpression.ColumnExpression">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.columnexpression.</span></span><span class="sig-name descname"><span class="pre">ColumnExpression</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dataColumn</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datacolumn.DataColumn" title="sttp.data.datacolumn.DataColumn"><span class="pre">DataColumn</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/columnexpression.html#ColumnExpression"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.columnexpression.ColumnExpression" title="Link to this definition"></a></dt>
<dd><p>Bases: <a class="reference internal" href="#sttp.data.expression.Expression" title="sttp.data.expression.Expression"><code class="xref py py-class docutils literal notranslate"><span class="pre">Expression</span></code></a></p>
<p>Represents a <cite>DataColumn</cite> expression from a <cite>DataTable</cite>.</p>
<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.columnexpression.ColumnExpression.datacolumn">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">datacolumn</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.datacolumn.DataColumn" title="sttp.data.datacolumn.DataColumn"><span class="pre">DataColumn</span></a></em><a class="headerlink" href="#sttp.data.columnexpression.ColumnExpression.datacolumn" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>DataColumn</cite> associated with this <cite>ColumnExpression</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.columnexpression.ColumnExpression.expressiontype">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">expressiontype</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.constants.ExpressionType" title="sttp.data.constants.ExpressionType"><span class="pre">ExpressionType</span></a></em><a class="headerlink" href="#sttp.data.columnexpression.ColumnExpression.expressiontype" title="Link to this definition"></a></dt>
<dd><p>Gets the type of this <cite>ColumnExpression</cite>.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.data.constants">
<span id="sttp-data-constants-module"></span><h2>sttp.data.constants module<a class="headerlink" href="#module-sttp.data.constants" title="Link to this heading"></a></h2>
<dl class="py data">
<dt class="sig sig-object py" id="sttp.data.constants.EXPRESSIONVALUETYPELEN">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">EXPRESSIONVALUETYPELEN</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">9</span></em><a class="headerlink" href="#sttp.data.constants.EXPRESSIONVALUETYPELEN" title="Link to this definition"></a></dt>
<dd><p>Defines the number of elements in the <cite>ExpressionValueType</cite> enumeration.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">ExpressionFunctionType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names=&lt;not</span> <span class="pre">given&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start=1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary=None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/constants.html#ExpressionFunctionType"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></p>
<p>Defines an enumeration of possible expression function types.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.ABS">
<span class="sig-name descname"><span class="pre">ABS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.ABS" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the absolute value of the specified numeric expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.CEILING">
<span class="sig-name descname"><span class="pre">CEILING</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.CEILING" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the smallest integer greater than or equal to the specified numeric expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.COALESCE">
<span class="sig-name descname"><span class="pre">COALESCE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.COALESCE" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the first non-null value from the specified expression list.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.CONTAINS">
<span class="sig-name descname"><span class="pre">CONTAINS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">4</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.CONTAINS" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns a boolean value indicating whether the specified string expression contains the specified substring.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.CONVERT">
<span class="sig-name descname"><span class="pre">CONVERT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">3</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.CONVERT" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that converts the specified expression to the specified data type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.DATEADD">
<span class="sig-name descname"><span class="pre">DATEADD</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">5</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.DATEADD" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that adds the specified number of specified <cite>TimeInterval</cite> units to the specified date expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.DATEDIFF">
<span class="sig-name descname"><span class="pre">DATEDIFF</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">6</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.DATEDIFF" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the number of specified <cite>TimeInterval</cite> units between the specified date expressions.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.DATEPART">
<span class="sig-name descname"><span class="pre">DATEPART</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">7</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.DATEPART" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the specified <cite>TimeInterval</cite> unit from the specified date expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.ENDSWITH">
<span class="sig-name descname"><span class="pre">ENDSWITH</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">8</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.ENDSWITH" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns a boolean value indicating whether the specified string expression ends with the specified substring.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.FLOOR">
<span class="sig-name descname"><span class="pre">FLOOR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">9</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.FLOOR" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the largest integer less than or equal to the specified numeric expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.IIF">
<span class="sig-name descname"><span class="pre">IIF</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">10</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.IIF" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the first expression if the specified boolean expression is true, otherwise returns the second expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.INDEXOF">
<span class="sig-name descname"><span class="pre">INDEXOF</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">11</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.INDEXOF" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the zero-based index of the first occurrence of the specified substring in the specified string expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.ISDATE">
<span class="sig-name descname"><span class="pre">ISDATE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">12</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.ISDATE" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns a boolean value indicating whether the specified string expression is a valid date.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.ISGUID">
<span class="sig-name descname"><span class="pre">ISGUID</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">14</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.ISGUID" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns a boolean value indicating whether the specified string expression is a valid GUID.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.ISINTEGER">
<span class="sig-name descname"><span class="pre">ISINTEGER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">13</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.ISINTEGER" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns a boolean value indicating whether the specified string expression is a valid integer.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.ISNULL">
<span class="sig-name descname"><span class="pre">ISNULL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">15</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.ISNULL" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns a boolean value indicating whether the specified expression is null.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.ISNUMERIC">
<span class="sig-name descname"><span class="pre">ISNUMERIC</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">16</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.ISNUMERIC" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns a boolean value indicating whether the specified string expression is a valid numeric value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.LASTINDEXOF">
<span class="sig-name descname"><span class="pre">LASTINDEXOF</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">17</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.LASTINDEXOF" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the zero-based index of the last occurrence of the specified substring in the specified string expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.LEN">
<span class="sig-name descname"><span class="pre">LEN</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">18</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.LEN" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the length of the specified string expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.LOWER">
<span class="sig-name descname"><span class="pre">LOWER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">19</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.LOWER" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the specified string expression converted to lower if self.value ==.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.MAXOF">
<span class="sig-name descname"><span class="pre">MAXOF</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">20</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.MAXOF" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the maximum value of the specified expression list.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.MINOF">
<span class="sig-name descname"><span class="pre">MINOF</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">21</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.MINOF" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the minimum value of the specified expression list.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.NOW">
<span class="sig-name descname"><span class="pre">NOW</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">22</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.NOW" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the current local date and time.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.NTHINDEXOF">
<span class="sig-name descname"><span class="pre">NTHINDEXOF</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">23</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.NTHINDEXOF" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the zero-based index of the nth occurrence of the specified substring in the specified string expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.POWER">
<span class="sig-name descname"><span class="pre">POWER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">24</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.POWER" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the specified numeric expression raised to the specified power.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.REGEXMATCH">
<span class="sig-name descname"><span class="pre">REGEXMATCH</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">25</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.REGEXMATCH" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns a boolean value indicating whether the specified string expression matches the specified regular expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.REGEXVAL">
<span class="sig-name descname"><span class="pre">REGEXVAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">26</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.REGEXVAL" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the first matching value of the specified regular expression in the specified string expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.REPLACE">
<span class="sig-name descname"><span class="pre">REPLACE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">27</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.REPLACE" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the specified string expression with the specified substring replaced with the specified replacement string.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.REVERSE">
<span class="sig-name descname"><span class="pre">REVERSE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">28</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.REVERSE" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the specified string expression with the characters in reverse order.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.ROUND">
<span class="sig-name descname"><span class="pre">ROUND</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">29</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.ROUND" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the specified numeric expression rounded to the specified number of decimal places.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.SPLIT">
<span class="sig-name descname"><span class="pre">SPLIT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">30</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.SPLIT" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the zero-based nth string value from the specified string expression split by the specified delimiter, or null if out of range.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.SQRT">
<span class="sig-name descname"><span class="pre">SQRT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">31</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.SQRT" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the square root of the specified numeric expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.STARTSWITH">
<span class="sig-name descname"><span class="pre">STARTSWITH</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">32</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.STARTSWITH" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns a boolean value indicating whether the specified string expression starts with the specified substring.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.STRCMP">
<span class="sig-name descname"><span class="pre">STRCMP</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">34</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.STRCMP" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns an integer value representing the comparision of the specified left and right strings.
Returned value will be -1 if left is less-than right, 1 if left is greater-than right, or 0 if left equals right.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.STRCOUNT">
<span class="sig-name descname"><span class="pre">STRCOUNT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">33</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.STRCOUNT" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the number of occurrences of the specified substring in the specified string expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.SUBSTR">
<span class="sig-name descname"><span class="pre">SUBSTR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">35</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.SUBSTR" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns a substring of the specified string expression starting at the specified index and with the specified length.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.TRIM">
<span class="sig-name descname"><span class="pre">TRIM</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">36</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.TRIM" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the specified string expression with the specified characters trimmed from the beginning and end.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.TRIMLEFT">
<span class="sig-name descname"><span class="pre">TRIMLEFT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">37</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.TRIMLEFT" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the specified string expression with the specified characters trimmed from the beginning.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.TRIMRIGHT">
<span class="sig-name descname"><span class="pre">TRIMRIGHT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">38</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.TRIMRIGHT" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the specified string expression with the specified characters trimmed from the end.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.UPPER">
<span class="sig-name descname"><span class="pre">UPPER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">39</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.UPPER" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the specified string expression converted to upper if self.value ==.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionFunctionType.UTCNOW">
<span class="sig-name descname"><span class="pre">UTCNOW</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">40</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionFunctionType.UTCNOW" title="Link to this definition"></a></dt>
<dd><p>Defines a function type that returns the current date and time in UTC.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">ExpressionOperatorType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names=&lt;not</span> <span class="pre">given&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start=1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary=None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/constants.html#ExpressionOperatorType"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></p>
<p>Defines an enumeration of possible expression operator types.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.ADD">
<span class="sig-name descname"><span class="pre">ADD</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">3</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.ADD" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>+</cite> that adds the left and right expressions.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.AND">
<span class="sig-name descname"><span class="pre">AND</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">24</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.AND" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>AND</cite> that returns a boolean value indicating whether the left expression and the right expression are both true.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.BITSHIFTLEFT">
<span class="sig-name descname"><span class="pre">BITSHIFTLEFT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">5</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.BITSHIFTLEFT" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>&lt;&lt;</cite> that shifts the left expression left by the number of bits specified by the right expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.BITSHIFTRIGHT">
<span class="sig-name descname"><span class="pre">BITSHIFTRIGHT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">6</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.BITSHIFTRIGHT" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>&gt;&gt;</cite> that shifts the left expression right by the number of bits specified by the right expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.BITWISEAND">
<span class="sig-name descname"><span class="pre">BITWISEAND</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">7</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.BITWISEAND" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>&amp;</cite> that performs a bitwise AND operation on the left and right expressions.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.BITWISEOR">
<span class="sig-name descname"><span class="pre">BITWISEOR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">8</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.BITWISEOR" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>|</cite> that performs a bitwise OR operation on the left and right expressions.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.BITWISEXOR">
<span class="sig-name descname"><span class="pre">BITWISEXOR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">9</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.BITWISEXOR" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>^</cite> that performs a bitwise XOR operation on the left and right expressions.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.DIVIDE">
<span class="sig-name descname"><span class="pre">DIVIDE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.DIVIDE" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>/</cite> that divides the left and right expressions.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.EQUAL">
<span class="sig-name descname"><span class="pre">EQUAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">14</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.EQUAL" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>=</cite> or <cite>==</cite> that returns a boolean value indicating whether the left expression is equal to the right expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.EQUALEXACTMATCH">
<span class="sig-name descname"><span class="pre">EQUALEXACTMATCH</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">15</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.EQUALEXACTMATCH" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>===</cite> that returns a boolean value indicating whether the left expression is equal to the right expression, case-sensitive.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.GREATERTHAN">
<span class="sig-name descname"><span class="pre">GREATERTHAN</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">12</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.GREATERTHAN" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>&gt;</cite> that returns a boolean value indicating whether the left expression is greater than the right expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.GREATERTHANOREQUAL">
<span class="sig-name descname"><span class="pre">GREATERTHANOREQUAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">13</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.GREATERTHANOREQUAL" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>&gt;=</cite> that returns a boolean value indicating whether the left expression is greater than or equal to the right expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.ISNOTNULL">
<span class="sig-name descname"><span class="pre">ISNOTNULL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">19</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.ISNOTNULL" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>IS NOT NULL</cite> that returns a boolean value indicating whether the left expression is not null.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.ISNULL">
<span class="sig-name descname"><span class="pre">ISNULL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">18</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.ISNULL" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>IS NULL</cite> that returns a boolean value indicating whether the left expression is null.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.LESSTHAN">
<span class="sig-name descname"><span class="pre">LESSTHAN</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">10</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.LESSTHAN" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>&lt;</cite> that returns a boolean value indicating whether the left expression is less than the right expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.LESSTHANOREQUAL">
<span class="sig-name descname"><span class="pre">LESSTHANOREQUAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">11</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.LESSTHANOREQUAL" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>&lt;=</cite> that returns a boolean value indicating whether the left expression is less than or equal to the right expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.LIKE">
<span class="sig-name descname"><span class="pre">LIKE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">20</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.LIKE" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>LIKE</cite> that returns a boolean value indicating whether the left expression matches the right expression patten.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.LIKEEXACTMATCH">
<span class="sig-name descname"><span class="pre">LIKEEXACTMATCH</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">21</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.LIKEEXACTMATCH" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>LIKE BINARY</cite> or <cite>LIKE ===</cite> that returns a boolean value indicating whether the left expression matches the right expression patten, case-sensitive.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.MODULUS">
<span class="sig-name descname"><span class="pre">MODULUS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.MODULUS" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>%</cite> that returns the remainder of the left and right expressions.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.MULTIPLY">
<span class="sig-name descname"><span class="pre">MULTIPLY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.MULTIPLY" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>*</cite> that multiplies the left and right expressions.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.NOTEQUAL">
<span class="sig-name descname"><span class="pre">NOTEQUAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">16</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.NOTEQUAL" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>&lt;&gt;</cite> or <cite>!=</cite> that returns a boolean value indicating whether the left expression is not equal to the right expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.NOTEQUALEXACTMATCH">
<span class="sig-name descname"><span class="pre">NOTEQUALEXACTMATCH</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">17</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.NOTEQUALEXACTMATCH" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>!==</cite> that returns a boolean value indicating whether the left expression is not equal to the right expression, case-sensitive.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.NOTLIKE">
<span class="sig-name descname"><span class="pre">NOTLIKE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">22</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.NOTLIKE" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>NOT LIKE</cite> that returns a boolean value indicating whether the left expression does not match the right expression patten.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.NOTLIKEEXACTMATCH">
<span class="sig-name descname"><span class="pre">NOTLIKEEXACTMATCH</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">23</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.NOTLIKEEXACTMATCH" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>NOT LIKE BINARY</cite> or <cite>NOT LIKE ===</cite> that returns a boolean value indicating whether the left expression does not match the right expression patten, case-sensitive.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.OR">
<span class="sig-name descname"><span class="pre">OR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">25</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.OR" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>OR</cite> that returns a boolean value indicating whether the left expression or the right expression is true.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionOperatorType.SUBTRACT">
<span class="sig-name descname"><span class="pre">SUBTRACT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">4</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionOperatorType.SUBTRACT" title="Link to this definition"></a></dt>
<dd><p>Defines an operator type <cite>-</cite> that subtracts the right expression from the left expression.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">ExpressionType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names=&lt;not</span> <span class="pre">given&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start=1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary=None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/constants.html#ExpressionType"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.ExpressionType" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></p>
<p>Defines an enumeration of possible expression types.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionType.COLUMN">
<span class="sig-name descname"><span class="pre">COLUMN</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionType.COLUMN" title="Link to this definition"></a></dt>
<dd><p>Column expression type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionType.FUNCTION">
<span class="sig-name descname"><span class="pre">FUNCTION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">4</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionType.FUNCTION" title="Link to this definition"></a></dt>
<dd><p>Function expression type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionType.INLIST">
<span class="sig-name descname"><span class="pre">INLIST</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">3</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionType.INLIST" title="Link to this definition"></a></dt>
<dd><p>In-list expression type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionType.OPERATOR">
<span class="sig-name descname"><span class="pre">OPERATOR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">5</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionType.OPERATOR" title="Link to this definition"></a></dt>
<dd><p>Operator expression type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionType.UNARY">
<span class="sig-name descname"><span class="pre">UNARY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionType.UNARY" title="Link to this definition"></a></dt>
<dd><p>Unary expression type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionType.VALUE">
<span class="sig-name descname"><span class="pre">VALUE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionType.VALUE" title="Link to this definition"></a></dt>
<dd><p>Value expression type.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionUnaryType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">ExpressionUnaryType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names=&lt;not</span> <span class="pre">given&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start=1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary=None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/constants.html#ExpressionUnaryType"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.ExpressionUnaryType" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></p>
<p>Defines an enumeration of possible unary expression types.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionUnaryType.MINUS">
<span class="sig-name descname"><span class="pre">MINUS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionUnaryType.MINUS" title="Link to this definition"></a></dt>
<dd><p>Negative unary expression type, i.e., <cite>-</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionUnaryType.NOT">
<span class="sig-name descname"><span class="pre">NOT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionUnaryType.NOT" title="Link to this definition"></a></dt>
<dd><p>Not unary expression type, i.e., <cite>~</cite> or <cite>!</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionUnaryType.PLUS">
<span class="sig-name descname"><span class="pre">PLUS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionUnaryType.PLUS" title="Link to this definition"></a></dt>
<dd><p>Positive unary expression type, i.e., <cite>+</cite>.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionValueType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">ExpressionValueType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names=&lt;not</span> <span class="pre">given&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start=1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary=None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/constants.html#ExpressionValueType"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.ExpressionValueType" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></p>
<p>Defines an enumeration of possible expression value data types. These expression value
data types are reduced to a reasonable set of possible types that can be represented in
a filter expression. All data table column values will be mapped to these types.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionValueType.BOOLEAN">
<span class="sig-name descname"><span class="pre">BOOLEAN</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionValueType.BOOLEAN" title="Link to this definition"></a></dt>
<dd><p>Boolean value type for an expression, i.e., <cite>bool</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionValueType.DATETIME">
<span class="sig-name descname"><span class="pre">DATETIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">7</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionValueType.DATETIME" title="Link to this definition"></a></dt>
<dd><p>DateTime value type for an expression, i.e., <cite>datetime.datetime</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionValueType.DECIMAL">
<span class="sig-name descname"><span class="pre">DECIMAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">3</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionValueType.DECIMAL" title="Link to this definition"></a></dt>
<dd><p>Decimal value type for an expression, i.e., <cite>decimal.Decimal</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionValueType.DOUBLE">
<span class="sig-name descname"><span class="pre">DOUBLE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">4</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionValueType.DOUBLE" title="Link to this definition"></a></dt>
<dd><p>Double value type for an expression, i.e., <cite>numpy.float64</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionValueType.GUID">
<span class="sig-name descname"><span class="pre">GUID</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">6</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionValueType.GUID" title="Link to this definition"></a></dt>
<dd><p>GUID value type for an expression, i.e., <cite>uuid.UUID</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionValueType.INT32">
<span class="sig-name descname"><span class="pre">INT32</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionValueType.INT32" title="Link to this definition"></a></dt>
<dd><p>32-bit integer value type for an expression, i.e., <cite>numpy.int32</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionValueType.INT64">
<span class="sig-name descname"><span class="pre">INT64</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionValueType.INT64" title="Link to this definition"></a></dt>
<dd><p>64-bit integer value type for an expression, i.e., <cite>numpy.int64</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionValueType.STRING">
<span class="sig-name descname"><span class="pre">STRING</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">5</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionValueType.STRING" title="Link to this definition"></a></dt>
<dd><p>String value type for an expression, i.e., <cite>str</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.ExpressionValueType.UNDEFINED">
<span class="sig-name descname"><span class="pre">UNDEFINED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">8</span></em><a class="headerlink" href="#sttp.data.constants.ExpressionValueType.UNDEFINED" title="Link to this definition"></a></dt>
<dd><p>Undefined value type for an expression, i.e., <cite>None</cite>.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.constants.TimeInterval">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">TimeInterval</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names=&lt;not</span> <span class="pre">given&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start=1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary=None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/constants.html#TimeInterval"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.TimeInterval" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></p>
<p>Defines enumeration of possible DateTime intervals.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.TimeInterval.DAY">
<span class="sig-name descname"><span class="pre">DAY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">3</span></em><a class="headerlink" href="#sttp.data.constants.TimeInterval.DAY" title="Link to this definition"></a></dt>
<dd><p>Represents the day part (1-31) of a DateTime expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.TimeInterval.DAYOFYEAR">
<span class="sig-name descname"><span class="pre">DAYOFYEAR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2</span></em><a class="headerlink" href="#sttp.data.constants.TimeInterval.DAYOFYEAR" title="Link to this definition"></a></dt>
<dd><p>Represents the day of the year (1-366) of a DateTime expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.TimeInterval.HOUR">
<span class="sig-name descname"><span class="pre">HOUR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">6</span></em><a class="headerlink" href="#sttp.data.constants.TimeInterval.HOUR" title="Link to this definition"></a></dt>
<dd><p>Represents the hour part (0-23) of a DateTime expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.TimeInterval.MILLISECOND">
<span class="sig-name descname"><span class="pre">MILLISECOND</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">9</span></em><a class="headerlink" href="#sttp.data.constants.TimeInterval.MILLISECOND" title="Link to this definition"></a></dt>
<dd><p>Represents the millisecond part (0-999) of a DateTime expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.TimeInterval.MINUTE">
<span class="sig-name descname"><span class="pre">MINUTE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">7</span></em><a class="headerlink" href="#sttp.data.constants.TimeInterval.MINUTE" title="Link to this definition"></a></dt>
<dd><p>Represents the minute part (0-59) of a DateTime expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.TimeInterval.MONTH">
<span class="sig-name descname"><span class="pre">MONTH</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1</span></em><a class="headerlink" href="#sttp.data.constants.TimeInterval.MONTH" title="Link to this definition"></a></dt>
<dd><p>Represents the month part (1-12) of a DateTime expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.TimeInterval.SECOND">
<span class="sig-name descname"><span class="pre">SECOND</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">8</span></em><a class="headerlink" href="#sttp.data.constants.TimeInterval.SECOND" title="Link to this definition"></a></dt>
<dd><p>Represents the second part (0-59) of a DateTime expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.TimeInterval.WEEK">
<span class="sig-name descname"><span class="pre">WEEK</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">4</span></em><a class="headerlink" href="#sttp.data.constants.TimeInterval.WEEK" title="Link to this definition"></a></dt>
<dd><p>Represents the week part (1-53) of a DateTime expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.TimeInterval.WEEKDAY">
<span class="sig-name descname"><span class="pre">WEEKDAY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">5</span></em><a class="headerlink" href="#sttp.data.constants.TimeInterval.WEEKDAY" title="Link to this definition"></a></dt>
<dd><p>Represents the weekday part (0-6) of a DateTime expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.constants.TimeInterval.YEAR">
<span class="sig-name descname"><span class="pre">YEAR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#sttp.data.constants.TimeInterval.YEAR" title="Link to this definition"></a></dt>
<dd><p>Represents the year part of a DateTime expression.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.constants.TimeInterval.parse">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="#sttp.data.constants.TimeInterval" title="sttp.data.constants.TimeInterval"><span class="pre">TimeInterval</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#TimeInterval.parse"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.TimeInterval.parse" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.constants.derive_arithmetic_operationvaluetype">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">derive_arithmetic_operationvaluetype</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">operationtype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType" title="sttp.data.constants.ExpressionOperatorType"><span class="pre">ExpressionOperatorType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">leftvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">rightvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#derive_arithmetic_operationvaluetype"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.derive_arithmetic_operationvaluetype" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.constants.derive_arithmetic_operationvaluetype_fromboolean">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">derive_arithmetic_operationvaluetype_fromboolean</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">operationtype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType" title="sttp.data.constants.ExpressionOperatorType"><span class="pre">ExpressionOperatorType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">rightvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#derive_arithmetic_operationvaluetype_fromboolean"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.derive_arithmetic_operationvaluetype_fromboolean" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.constants.derive_arithmetic_operationvaluetype_fromdecimal">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">derive_arithmetic_operationvaluetype_fromdecimal</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">operationtype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType" title="sttp.data.constants.ExpressionOperatorType"><span class="pre">ExpressionOperatorType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">rightvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#derive_arithmetic_operationvaluetype_fromdecimal"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.derive_arithmetic_operationvaluetype_fromdecimal" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.constants.derive_arithmetic_operationvaluetype_fromdouble">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">derive_arithmetic_operationvaluetype_fromdouble</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">operationtype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType" title="sttp.data.constants.ExpressionOperatorType"><span class="pre">ExpressionOperatorType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">rightvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#derive_arithmetic_operationvaluetype_fromdouble"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.derive_arithmetic_operationvaluetype_fromdouble" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.constants.derive_arithmetic_operationvaluetype_fromint32">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">derive_arithmetic_operationvaluetype_fromint32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">operationtype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType" title="sttp.data.constants.ExpressionOperatorType"><span class="pre">ExpressionOperatorType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">rightvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#derive_arithmetic_operationvaluetype_fromint32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.derive_arithmetic_operationvaluetype_fromint32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.constants.derive_arithmetic_operationvaluetype_fromint64">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">derive_arithmetic_operationvaluetype_fromint64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">operationtype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType" title="sttp.data.constants.ExpressionOperatorType"><span class="pre">ExpressionOperatorType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">rightvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#derive_arithmetic_operationvaluetype_fromint64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.derive_arithmetic_operationvaluetype_fromint64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.constants.derive_boolean_operationvaluetype">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">derive_boolean_operationvaluetype</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">operationtype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType" title="sttp.data.constants.ExpressionOperatorType"><span class="pre">ExpressionOperatorType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">leftvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">rightvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#derive_boolean_operationvaluetype"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.derive_boolean_operationvaluetype" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.constants.derive_comparison_operationvaluetype">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">derive_comparison_operationvaluetype</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">operationtype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType" title="sttp.data.constants.ExpressionOperatorType"><span class="pre">ExpressionOperatorType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">leftvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">rightvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#derive_comparison_operationvaluetype"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.derive_comparison_operationvaluetype" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.constants.derive_comparison_operationvaluetype_fromboolean">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">derive_comparison_operationvaluetype_fromboolean</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">operationtype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType" title="sttp.data.constants.ExpressionOperatorType"><span class="pre">ExpressionOperatorType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">rightvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#derive_comparison_operationvaluetype_fromboolean"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.derive_comparison_operationvaluetype_fromboolean" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.constants.derive_comparison_operationvaluetype_fromdatetime">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">derive_comparison_operationvaluetype_fromdatetime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">operationtype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType" title="sttp.data.constants.ExpressionOperatorType"><span class="pre">ExpressionOperatorType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">rightvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#derive_comparison_operationvaluetype_fromdatetime"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.derive_comparison_operationvaluetype_fromdatetime" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.constants.derive_comparison_operationvaluetype_fromdecimal">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">derive_comparison_operationvaluetype_fromdecimal</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">operationtype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType" title="sttp.data.constants.ExpressionOperatorType"><span class="pre">ExpressionOperatorType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">rightvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#derive_comparison_operationvaluetype_fromdecimal"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.derive_comparison_operationvaluetype_fromdecimal" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.constants.derive_comparison_operationvaluetype_fromdouble">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">derive_comparison_operationvaluetype_fromdouble</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">operationtype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType" title="sttp.data.constants.ExpressionOperatorType"><span class="pre">ExpressionOperatorType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">rightvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#derive_comparison_operationvaluetype_fromdouble"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.derive_comparison_operationvaluetype_fromdouble" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.constants.derive_comparison_operationvaluetype_fromguid">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">derive_comparison_operationvaluetype_fromguid</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">operationtype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType" title="sttp.data.constants.ExpressionOperatorType"><span class="pre">ExpressionOperatorType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">rightvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#derive_comparison_operationvaluetype_fromguid"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.derive_comparison_operationvaluetype_fromguid" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.constants.derive_comparison_operationvaluetype_fromint32">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">derive_comparison_operationvaluetype_fromint32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">operationtype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType" title="sttp.data.constants.ExpressionOperatorType"><span class="pre">ExpressionOperatorType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">rightvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#derive_comparison_operationvaluetype_fromint32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.derive_comparison_operationvaluetype_fromint32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.constants.derive_comparison_operationvaluetype_fromint64">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">derive_comparison_operationvaluetype_fromint64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">operationtype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType" title="sttp.data.constants.ExpressionOperatorType"><span class="pre">ExpressionOperatorType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">rightvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#derive_comparison_operationvaluetype_fromint64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.derive_comparison_operationvaluetype_fromint64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.constants.derive_integer_operationvaluetype">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">derive_integer_operationvaluetype</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">operationtype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType" title="sttp.data.constants.ExpressionOperatorType"><span class="pre">ExpressionOperatorType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">leftvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">rightvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#derive_integer_operationvaluetype"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.derive_integer_operationvaluetype" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.constants.derive_integer_operationvaluetype_fromboolean">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">derive_integer_operationvaluetype_fromboolean</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">operationtype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType" title="sttp.data.constants.ExpressionOperatorType"><span class="pre">ExpressionOperatorType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">rightvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#derive_integer_operationvaluetype_fromboolean"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.derive_integer_operationvaluetype_fromboolean" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.constants.derive_integer_operationvaluetype_fromint32">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">derive_integer_operationvaluetype_fromint32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">operationtype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType" title="sttp.data.constants.ExpressionOperatorType"><span class="pre">ExpressionOperatorType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">rightvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#derive_integer_operationvaluetype_fromint32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.derive_integer_operationvaluetype_fromint32" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.constants.derive_integer_operationvaluetype_fromint64">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">derive_integer_operationvaluetype_fromint64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">operationtype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType" title="sttp.data.constants.ExpressionOperatorType"><span class="pre">ExpressionOperatorType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">rightvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#derive_integer_operationvaluetype_fromint64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.derive_integer_operationvaluetype_fromint64" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.constants.derive_operationvaluetype">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">derive_operationvaluetype</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">operationtype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType" title="sttp.data.constants.ExpressionOperatorType"><span class="pre">ExpressionOperatorType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">leftvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">rightvaluetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#derive_operationvaluetype"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.derive_operationvaluetype" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.constants.is_integertype">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">is_integertype</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">bool</span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#is_integertype"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.is_integertype" title="Link to this definition"></a></dt>
<dd><p>Determines if the specified expression value type is an integer type.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.constants.is_numerictype">
<span class="sig-prename descclassname"><span class="pre">sttp.data.constants.</span></span><span class="sig-name descname"><span class="pre">is_numerictype</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">bool</span></span></span><a class="reference internal" href="_modules/sttp/data/constants.html#is_numerictype"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.constants.is_numerictype" title="Link to this definition"></a></dt>
<dd><p>Determines if the specified expression value type is a numeric type.</p>
</dd></dl>

</section>
<section id="module-sttp.data.datacolumn">
<span id="sttp-data-datacolumn-module"></span><h2>sttp.data.datacolumn module<a class="headerlink" href="#module-sttp.data.datacolumn" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.datacolumn.DataColumn">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.datacolumn.</span></span><span class="sig-name descname"><span class="pre">DataColumn</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">parent</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datatable.DataTable" title="sttp.data.datatable.DataTable"><span class="pre">DataTable</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">datatype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datatype.DataType" title="sttp.data.datatype.DataType"><span class="pre">DataType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">expression</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/datacolumn.html#DataColumn"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datacolumn.DataColumn" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Represents a column, i.e., a field, in a <cite>DataTable</cite> defining a name and a data type.
Data columns can also be computed where its value would be derived from other columns and
functions (<a class="reference external" href="https://sttp.github.io/documentation/filter-expressions/">https://sttp.github.io/documentation/filter-expressions/</a>) defined in an expression.</p>
<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.datacolumn.DataColumn.computed">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">computed</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">bool</span></em><a class="headerlink" href="#sttp.data.datacolumn.DataColumn.computed" title="Link to this definition"></a></dt>
<dd><p>Gets a flag that determines if the <cite>DataColumn</cite> is a computed value,
i.e., has a defined expression.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.datacolumn.DataColumn.datatype">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">datatype</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.datatype.DataType" title="sttp.data.datatype.DataType"><span class="pre">DataType</span></a></em><a class="headerlink" href="#sttp.data.datacolumn.DataColumn.datatype" title="Link to this definition"></a></dt>
<dd><p>Gets the column <cite>DataType</cite> enumeration value of the <cite>DataColumn</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.datacolumn.DataColumn.expression">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">expression</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#sttp.data.datacolumn.DataColumn.expression" title="Link to this definition"></a></dt>
<dd><p>Gets the column expression value of the <cite>DataColumn</cite>, if any.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.datacolumn.DataColumn.index">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">index</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">int</span></em><a class="headerlink" href="#sttp.data.datacolumn.DataColumn.index" title="Link to this definition"></a></dt>
<dd><p>Gets the index of the <cite>DataColumn</cite> within its parent <cite>DataTable</cite> columns collection.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.datacolumn.DataColumn.name">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#sttp.data.datacolumn.DataColumn.name" title="Link to this definition"></a></dt>
<dd><p>Gets the column name of the <cite>DataColumn</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.datacolumn.DataColumn.parent">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">parent</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.datatable.DataTable" title="sttp.data.datatable.DataTable"><span class="pre">DataTable</span></a></em><a class="headerlink" href="#sttp.data.datacolumn.DataColumn.parent" title="Link to this definition"></a></dt>
<dd><p>Gets the parent <cite>DataTable</cite> of the <cite>DataColumn</cite>.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.data.datarow">
<span id="sttp-data-datarow-module"></span><h2>sttp.data.datarow module<a class="headerlink" href="#module-sttp.data.datarow" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.datarow.</span></span><span class="sig-name descname"><span class="pre">DataRow</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">parent</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datatable.DataTable" title="sttp.data.datatable.DataTable"><span class="pre">DataTable</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Represents a row, i.e., a record, in a <cite>DataTable</cite> defining a set of values for each
defined <cite>DataColumn</cite> field in the <cite>DataTable</cite> columns collection.</p>
<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.booleanvalue">
<span class="sig-name descname"><span class="pre">booleanvalue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.booleanvalue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.booleanvalue" title="Link to this definition"></a></dt>
<dd><p>Gets the boolean-based record value at the specified column index.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.BOOLEAN</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.booleanvalue_byname">
<span class="sig-name descname"><span class="pre">booleanvalue_byname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.booleanvalue_byname"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.booleanvalue_byname" title="Link to this definition"></a></dt>
<dd><p>Gets the boolean-based record value for the specified column name.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.BOOLEAN</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.columnvalue_as_string">
<span class="sig-name descname"><span class="pre">columnvalue_as_string</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">column</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datacolumn.DataColumn" title="sttp.data.datacolumn.DataColumn"><span class="pre">DataColumn</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.columnvalue_as_string"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.columnvalue_as_string" title="Link to this definition"></a></dt>
<dd><p>Reads the record value for the specified data column converted
to a string. For any errors, an empty string will be returned.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.compare_datarowcolumns">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">compare_datarowcolumns</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">leftrow</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datarow.DataRow" title="sttp.data.datarow.DataRow"><span class="pre">DataRow</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">rightrow</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datarow.DataRow" title="sttp.data.datarow.DataRow"><span class="pre">DataRow</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">columnindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exactmatch</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">int</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.compare_datarowcolumns"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.compare_datarowcolumns" title="Link to this definition"></a></dt>
<dd><p>Returns an integer comparing two <cite>DataRow</cite> column values for the specified column index.
The result will be 0 if <cite>leftrow`==`rightrow</cite>, -1 if <cite>leftrow</cite> &lt; <cite>rightrow</cite>, and +1 if <cite>leftrow</cite> &gt; <cite>rightrow</cite>.
An error will br returned if column index is out of range of either row, or row types do not match.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.datetimevalue">
<span class="sig-name descname"><span class="pre">datetimevalue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">datetime</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.datetimevalue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.datetimevalue" title="Link to this definition"></a></dt>
<dd><p>Gets the datetime-based record value at the specified column index.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.DATETIME</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.datetimevalue_byname">
<span class="sig-name descname"><span class="pre">datetimevalue_byname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">datetime</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.datetimevalue_byname"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.datetimevalue_byname" title="Link to this definition"></a></dt>
<dd><p>Gets the datetime-based record value for the specified column name.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.DATETIME</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.decimalvalue">
<span class="sig-name descname"><span class="pre">decimalvalue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">Decimal</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.decimalvalue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.decimalvalue" title="Link to this definition"></a></dt>
<dd><p>Gets the decimal-based record value at the specified column index.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.DECIMAL</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.decimalvalue_byname">
<span class="sig-name descname"><span class="pre">decimalvalue_byname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">Decimal</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.decimalvalue_byname"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.decimalvalue_byname" title="Link to this definition"></a></dt>
<dd><p>Gets the decimal-based record value for the specified column name.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.DECIMAL</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.doublevalue">
<span class="sig-name descname"><span class="pre">doublevalue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">float64</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.doublevalue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.doublevalue" title="Link to this definition"></a></dt>
<dd><p>Gets the double-based record value at the specified column index.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.DOUBLE</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.doublevalue_byname">
<span class="sig-name descname"><span class="pre">doublevalue_byname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">float64</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.doublevalue_byname"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.doublevalue_byname" title="Link to this definition"></a></dt>
<dd><p>Gets the double-based record value for the specified column name.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.DOUBLE</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.guidvalue">
<span class="sig-name descname"><span class="pre">guidvalue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">UUID</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.guidvalue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.guidvalue" title="Link to this definition"></a></dt>
<dd><p>Gets the guid-based record value at the specified column index.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.GUID</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.guidvalue_byname">
<span class="sig-name descname"><span class="pre">guidvalue_byname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">UUID</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.guidvalue_byname"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.guidvalue_byname" title="Link to this definition"></a></dt>
<dd><p>Gets the guid-based record value for the specified column name.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.GUID</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.int16value">
<span class="sig-name descname"><span class="pre">int16value</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">int16</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.int16value"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.int16value" title="Link to this definition"></a></dt>
<dd><p>Gets the int16-based record value at the specified column index.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.INT16</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.int16value_byname">
<span class="sig-name descname"><span class="pre">int16value_byname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">int16</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.int16value_byname"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.int16value_byname" title="Link to this definition"></a></dt>
<dd><p>Gets the int16-based record value for the specified column name.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.INT16</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.int32value">
<span class="sig-name descname"><span class="pre">int32value</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">int32</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.int32value"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.int32value" title="Link to this definition"></a></dt>
<dd><p>Gets the int32-based record value at the specified column index.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.INT32</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.int32value_byname">
<span class="sig-name descname"><span class="pre">int32value_byname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">int32</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.int32value_byname"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.int32value_byname" title="Link to this definition"></a></dt>
<dd><p>Gets the int32-based record value for the specified column name.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.INT32</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.int64value">
<span class="sig-name descname"><span class="pre">int64value</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">int64</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.int64value"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.int64value" title="Link to this definition"></a></dt>
<dd><p>Gets the int64-based record value at the specified column index.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.INT64</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.int64value_byname">
<span class="sig-name descname"><span class="pre">int64value_byname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">int64</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.int64value_byname"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.int64value_byname" title="Link to this definition"></a></dt>
<dd><p>Gets the int64-based record value for the specified column name.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.INT64</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.int8value">
<span class="sig-name descname"><span class="pre">int8value</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">int8</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.int8value"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.int8value" title="Link to this definition"></a></dt>
<dd><p>Gets the int8-based record value at the specified column index.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.INT8</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.int8value_byname">
<span class="sig-name descname"><span class="pre">int8value_byname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">int8</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.int8value_byname"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.int8value_byname" title="Link to this definition"></a></dt>
<dd><p>Gets the int8-based record value for the specified column name.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.INT8</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.parent">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">parent</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.datatable.DataTable" title="sttp.data.datatable.DataTable"><span class="pre">DataTable</span></a></em><a class="headerlink" href="#sttp.data.datarow.DataRow.parent" title="Link to this definition"></a></dt>
<dd><p>Gets the parent <cite>DataTable</cite> of the <cite>DataRow</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.set_value">
<span class="sig-name descname"><span class="pre">set_value</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.set_value"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.set_value" title="Link to this definition"></a></dt>
<dd><p>Assigns the record value at the specified column index.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.set_value_byname">
<span class="sig-name descname"><span class="pre">set_value_byname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.set_value_byname"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.set_value_byname" title="Link to this definition"></a></dt>
<dd><p>Assigns the record value for the specified column name.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.singlevalue">
<span class="sig-name descname"><span class="pre">singlevalue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">float32</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.singlevalue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.singlevalue" title="Link to this definition"></a></dt>
<dd><p>Gets the single-based record value at the specified column index.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.SINGLE</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.singlevalue_byname">
<span class="sig-name descname"><span class="pre">singlevalue_byname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">float32</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.singlevalue_byname"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.singlevalue_byname" title="Link to this definition"></a></dt>
<dd><p>Gets the single-based record value for the specified column name.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.SINGLE</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.stringvalue">
<span class="sig-name descname"><span class="pre">stringvalue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.stringvalue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.stringvalue" title="Link to this definition"></a></dt>
<dd><p>Gets the string-based record value at the specified column index.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.STRING</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.stringvalue_byname">
<span class="sig-name descname"><span class="pre">stringvalue_byname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.stringvalue_byname"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.stringvalue_byname" title="Link to this definition"></a></dt>
<dd><p>Gets the string-based record value for the specified column name.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.STRING</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.uint16value">
<span class="sig-name descname"><span class="pre">uint16value</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">uint16</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.uint16value"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.uint16value" title="Link to this definition"></a></dt>
<dd><p>Gets the uint16-based record value at the specified column index.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.UINT16</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.uint16value_byname">
<span class="sig-name descname"><span class="pre">uint16value_byname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">uint16</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.uint16value_byname"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.uint16value_byname" title="Link to this definition"></a></dt>
<dd><p>Gets the uint16-based record value for the specified column name.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.UINT16</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.uint32value">
<span class="sig-name descname"><span class="pre">uint32value</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">uint32</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.uint32value"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.uint32value" title="Link to this definition"></a></dt>
<dd><p>Gets the uint32-based record value at the specified column index.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.UINT32</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.uint32value_byname">
<span class="sig-name descname"><span class="pre">uint32value_byname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">uint32</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.uint32value_byname"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.uint32value_byname" title="Link to this definition"></a></dt>
<dd><p>Gets the uint32-based record value for the specified column name.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.UINT32</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.uint64value">
<span class="sig-name descname"><span class="pre">uint64value</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">uint64</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.uint64value"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.uint64value" title="Link to this definition"></a></dt>
<dd><p>Gets the uint64-based record value at the specified column index.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.UINT64</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.uint64value_byname">
<span class="sig-name descname"><span class="pre">uint64value_byname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">uint64</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.uint64value_byname"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.uint64value_byname" title="Link to this definition"></a></dt>
<dd><p>Gets the uint64-based record value for the specified column name.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.UINT64</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.uint8value">
<span class="sig-name descname"><span class="pre">uint8value</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">uint8</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.uint8value"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.uint8value" title="Link to this definition"></a></dt>
<dd><p>Gets the uint8-based record value at the specified column index.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.UINT8</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.uint8value_byname">
<span class="sig-name descname"><span class="pre">uint8value_byname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">uint8</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.uint8value_byname"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.uint8value_byname" title="Link to this definition"></a></dt>
<dd><p>Gets the uint8-based record value for the specified column name.
Second parameter in tuple return value indicates if original value was None.
An error will be returned if column type is not <cite>DataType.UINT8</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.value">
<span class="sig-name descname"><span class="pre">value</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">object</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.value"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.value" title="Link to this definition"></a></dt>
<dd><p>Reads the record value at the specified column index.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.value_as_string">
<span class="sig-name descname"><span class="pre">value_as_string</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.value_as_string"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.value_as_string" title="Link to this definition"></a></dt>
<dd><p>Reads the record value at the specified columnIndex converted to a string.
For column index out of range or any other errors, an empty string will be returned.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.value_as_string_byname">
<span class="sig-name descname"><span class="pre">value_as_string_byname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.value_as_string_byname"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.value_as_string_byname" title="Link to this definition"></a></dt>
<dd><p>Reads the record value for the specified columnName converted to a string.
For column name not found or any other errors, an empty string will be returned.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datarow.DataRow.value_byname">
<span class="sig-name descname"><span class="pre">value_byname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">object</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datarow.html#DataRow.value_byname"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datarow.DataRow.value_byname" title="Link to this definition"></a></dt>
<dd><p>Reads the record value for the specified column name.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.data.dataset">
<span id="sttp-data-dataset-module"></span><h2>sttp.data.dataset module<a class="headerlink" href="#module-sttp.data.dataset" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.dataset.DataSet">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.dataset.</span></span><span class="sig-name descname"><span class="pre">DataSet</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/dataset.html#DataSet"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.dataset.DataSet" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Represents an in-memory cache of records that is structured similarly to information
defined in a database. The data set object consists of a collection of data table objects.
See <a class="reference external" href="https://sttp.github.io/documentation/data-sets/">https://sttp.github.io/documentation/data-sets/</a> for more information.
Note that this implementation uses a case-insensitive map for <cite>DataTable</cite> name lookups.
Internally, case-insensitive lookups are accomplished using <cite>str.upper()</cite>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.dataset.DataSet.DEFAULT_NAME">
<span class="sig-name descname"><span class="pre">DEFAULT_NAME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'DataSet'</span></em><a class="headerlink" href="#sttp.data.dataset.DataSet.DEFAULT_NAME" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.dataset.DataSet.add_table">
<span class="sig-name descname"><span class="pre">add_table</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">table</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datatable.DataTable" title="sttp.data.datatable.DataTable"><span class="pre">DataTable</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/dataset.html#DataSet.add_table"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.dataset.DataSet.add_table" title="Link to this definition"></a></dt>
<dd><p>Adds the specified table to the <cite>DataSet</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.dataset.DataSet.clear_tables">
<span class="sig-name descname"><span class="pre">clear_tables</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/dataset.html#DataSet.clear_tables"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.dataset.DataSet.clear_tables" title="Link to this definition"></a></dt>
<dd><p>Clears the internal table collection.
Any existing tables will be deleted.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.dataset.DataSet.create_table">
<span class="sig-name descname"><span class="pre">create_table</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="#sttp.data.datatable.DataTable" title="sttp.data.datatable.DataTable"><span class="pre">DataTable</span></a></span></span><a class="reference internal" href="_modules/sttp/data/dataset.html#DataSet.create_table"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.dataset.DataSet.create_table" title="Link to this definition"></a></dt>
<dd><p>Creates a new <cite>DataTable</cite> associated with the <cite>DataSet</cite>.
Use <cite>add_table</cite> to add the new table to the <cite>DataSet</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.dataset.DataSet.from_xml">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">from_xml</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">bytes</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.dataset.DataSet" title="sttp.data.dataset.DataSet"><span class="pre">DataSet</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/dataset.html#DataSet.from_xml"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.dataset.DataSet.from_xml" title="Link to this definition"></a></dt>
<dd><p>Creates a new <cite>DataSet</cite> as read from the XML in the specified buffer.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.dataset.DataSet.name">
<span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#sttp.data.dataset.DataSet.name" title="Link to this definition"></a></dt>
<dd><p>Defines the name of the <cite>DataSet</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.dataset.DataSet.parse_xml">
<span class="sig-name descname"><span class="pre">parse_xml</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">bytes</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></span><a class="reference internal" href="_modules/sttp/data/dataset.html#DataSet.parse_xml"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.dataset.DataSet.parse_xml" title="Link to this definition"></a></dt>
<dd><p>Loads the <cite>DataSet</cite> from the XML in the specified buffer.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.dataset.DataSet.parse_xmldoc">
<span class="sig-name descname"><span class="pre">parse_xmldoc</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">root</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Element</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">namespaces</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Dict</span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">str</span><span class="p"><span class="pre">]</span></span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></span><a class="reference internal" href="_modules/sttp/data/dataset.html#DataSet.parse_xmldoc"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.dataset.DataSet.parse_xmldoc" title="Link to this definition"></a></dt>
<dd><p>Loads the <cite>DataSet</cite> from an existing root XML document element.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.dataset.DataSet.remove_table">
<span class="sig-name descname"><span class="pre">remove_table</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tablename</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">bool</span></span></span><a class="reference internal" href="_modules/sttp/data/dataset.html#DataSet.remove_table"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.dataset.DataSet.remove_table" title="Link to this definition"></a></dt>
<dd><p>Removes the specified table name from the <cite>DataSet</cite>. Returns
True if table was removed; otherwise, False if it did not exist.
Lookup is case-insensitive.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.dataset.DataSet.table">
<span class="sig-name descname"><span class="pre">table</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tablename</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="#sttp.data.datatable.DataTable" title="sttp.data.datatable.DataTable"><span class="pre">DataTable</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></span><a class="reference internal" href="_modules/sttp/data/dataset.html#DataSet.table"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.dataset.DataSet.table" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>DataTable</cite> for the specified table name if it exists;
otherwise, None is returned. Lookup is case-insensitive.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.dataset.DataSet.tablecount">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">tablecount</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">int</span></em><a class="headerlink" href="#sttp.data.dataset.DataSet.tablecount" title="Link to this definition"></a></dt>
<dd><p>Gets the total number of tables defined in the <cite>DataSet</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.dataset.DataSet.tablenames">
<span class="sig-name descname"><span class="pre">tablenames</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">List</span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/dataset.html#DataSet.tablenames"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.dataset.DataSet.tablenames" title="Link to this definition"></a></dt>
<dd><p>Gets the table names defined in the <cite>DataSet</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.dataset.DataSet.tables">
<span class="sig-name descname"><span class="pre">tables</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.datatable.DataTable" title="sttp.data.datatable.DataTable"><span class="pre">DataTable</span></a><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/dataset.html#DataSet.tables"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.dataset.DataSet.tables" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>DataTable</cite> instances defined in the <cite>DataSet</cite>.</p>
</dd></dl>

</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sttp.data.dataset.EXT_XMLSCHEMADATA_NAMESPACE">
<span class="sig-prename descclassname"><span class="pre">sttp.data.dataset.</span></span><span class="sig-name descname"><span class="pre">EXT_XMLSCHEMADATA_NAMESPACE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'urn:schemas-microsoft-com:xml-msdata'</span></em><a class="headerlink" href="#sttp.data.dataset.EXT_XMLSCHEMADATA_NAMESPACE" title="Link to this definition"></a></dt>
<dd><p>Defines extended types for XSD elements, e.g., Guid and expression data types.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sttp.data.dataset.XMLSCHEMA_NAMESPACE">
<span class="sig-prename descclassname"><span class="pre">sttp.data.dataset.</span></span><span class="sig-name descname"><span class="pre">XMLSCHEMA_NAMESPACE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'http://www.w3.org/2001/XMLSchema'</span></em><a class="headerlink" href="#sttp.data.dataset.XMLSCHEMA_NAMESPACE" title="Link to this definition"></a></dt>
<dd><p>Defines schema namespace for the W3C XML Schema Definition Language (XSD) used by STTP metadata tables.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.dataset.xsdformat">
<span class="sig-prename descclassname"><span class="pre">sttp.data.dataset.</span></span><span class="sig-name descname"><span class="pre">xsdformat</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">datetime</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="reference internal" href="_modules/sttp/data/dataset.html#xsdformat"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.dataset.xsdformat" title="Link to this definition"></a></dt>
<dd><p>Converts date/time value to a string in XSD XML schema format.</p>
</dd></dl>

</section>
<section id="module-sttp.data.datatable">
<span id="sttp-data-datatable-module"></span><h2>sttp.data.datatable module<a class="headerlink" href="#module-sttp.data.datatable" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.datatable.DataTable">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.datatable.</span></span><span class="sig-name descname"><span class="pre">DataTable</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">parent</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.dataset.DataSet" title="sttp.data.dataset.DataSet"><span class="pre">DataSet</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/datatable.html#DataTable"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datatable.DataTable" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Represents a collection of <cite>DataColumn</cite> objects where each data column defines a name and a data
type. Data columns can also be computed where its value would be derived from other columns and
functions (<a class="reference external" href="https://sttp.github.io/documentation/filter-expressions/">https://sttp.github.io/documentation/filter-expressions/</a>) defined in an expression.
Note that this implementation uses a case-insensitive map for <cite>DataColumn</cite> name lookups.
Internally, case-insensitive lookups are accomplished using <cite>str.upper()</cite>.</p>
<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datatable.DataTable.add_column">
<span class="sig-name descname"><span class="pre">add_column</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">column</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datacolumn.DataColumn" title="sttp.data.datacolumn.DataColumn"><span class="pre">DataColumn</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/datatable.html#DataTable.add_column"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datatable.DataTable.add_column" title="Link to this definition"></a></dt>
<dd><p>Adds the specified column to the <cite>DataTable</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datatable.DataTable.add_row">
<span class="sig-name descname"><span class="pre">add_row</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">row</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datarow.DataRow" title="sttp.data.datarow.DataRow"><span class="pre">DataRow</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/datatable.html#DataTable.add_row"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datatable.DataTable.add_row" title="Link to this definition"></a></dt>
<dd><p>Adds the specified row to the <cite>DataTable</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datatable.DataTable.clear_columns">
<span class="sig-name descname"><span class="pre">clear_columns</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/datatable.html#DataTable.clear_columns"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datatable.DataTable.clear_columns" title="Link to this definition"></a></dt>
<dd><p>Clears the internal column collections.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datatable.DataTable.clear_rows">
<span class="sig-name descname"><span class="pre">clear_rows</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/datatable.html#DataTable.clear_rows"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datatable.DataTable.clear_rows" title="Link to this definition"></a></dt>
<dd><p>Clears the internal row collection.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datatable.DataTable.clone_column">
<span class="sig-name descname"><span class="pre">clone_column</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">source</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datacolumn.DataColumn" title="sttp.data.datacolumn.DataColumn"><span class="pre">DataColumn</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="#sttp.data.datacolumn.DataColumn" title="sttp.data.datacolumn.DataColumn"><span class="pre">DataColumn</span></a></span></span><a class="reference internal" href="_modules/sttp/data/datatable.html#DataTable.clone_column"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datatable.DataTable.clone_column" title="Link to this definition"></a></dt>
<dd><p>Creates a copy of the specified source <cite>DataColumn</cite> associated with the <cite>DataTable</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datatable.DataTable.clone_row">
<span class="sig-name descname"><span class="pre">clone_row</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">source</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datarow.DataRow" title="sttp.data.datarow.DataRow"><span class="pre">DataRow</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="#sttp.data.datarow.DataRow" title="sttp.data.datarow.DataRow"><span class="pre">DataRow</span></a></span></span><a class="reference internal" href="_modules/sttp/data/datatable.html#DataTable.clone_row"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datatable.DataTable.clone_row" title="Link to this definition"></a></dt>
<dd><p>Creates a copy of the specified source <cite>DataRow</cite> associated with the <cite>DataTable</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datatable.DataTable.column">
<span class="sig-name descname"><span class="pre">column</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="#sttp.data.datacolumn.DataColumn" title="sttp.data.datacolumn.DataColumn"><span class="pre">DataColumn</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></span><a class="reference internal" href="_modules/sttp/data/datatable.html#DataTable.column"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datatable.DataTable.column" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>DataColumn</cite> at the specified column index if the index is in range;
otherwise, None is returned.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datatable.DataTable.column_byname">
<span class="sig-name descname"><span class="pre">column_byname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="#sttp.data.datacolumn.DataColumn" title="sttp.data.datacolumn.DataColumn"><span class="pre">DataColumn</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></span><a class="reference internal" href="_modules/sttp/data/datatable.html#DataTable.column_byname"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datatable.DataTable.column_byname" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>DataColumn</cite> for the specified column name if the name exists;
otherwise, None is returned. Lookup is case-insensitive.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.datatable.DataTable.columncount">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">columncount</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">int</span></em><a class="headerlink" href="#sttp.data.datatable.DataTable.columncount" title="Link to this definition"></a></dt>
<dd><p>Gets the total number columns defined in the <cite>DataTable</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datatable.DataTable.columnindex">
<span class="sig-name descname"><span class="pre">columnindex</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">columnname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/sttp/data/datatable.html#DataTable.columnindex"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datatable.DataTable.columnindex" title="Link to this definition"></a></dt>
<dd><p>Gets the index for the specified column name if the name exists;
otherwise, -1 is returned. Lookup is case-insensitive.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datatable.DataTable.create_column">
<span class="sig-name descname"><span class="pre">create_column</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">datatype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datatype.DataType" title="sttp.data.datatype.DataType"><span class="pre">DataType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">expression</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/datatable.html#DataTable.create_column"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datatable.DataTable.create_column" title="Link to this definition"></a></dt>
<dd><p>Creates a new <cite>DataColumn</cite> associated with the <cite>DataTable</cite>.
Use <cite>add_column</cite> to add the new column to the <cite>DataTable</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datatable.DataTable.create_row">
<span class="sig-name descname"><span class="pre">create_row</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="#sttp.data.datarow.DataRow" title="sttp.data.datarow.DataRow"><span class="pre">DataRow</span></a></span></span><a class="reference internal" href="_modules/sttp/data/datatable.html#DataTable.create_row"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datatable.DataTable.create_row" title="Link to this definition"></a></dt>
<dd><p>Creates a new <cite>DataRow</cite> associated with the <cite>DataTable</cite>.
Use <cite>add_row</cite> to add the new row to the <cite>DataTable</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.datatable.DataTable.name">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#sttp.data.datatable.DataTable.name" title="Link to this definition"></a></dt>
<dd><p>Gets the name of the <cite>DataTable</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.datatable.DataTable.parent">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">parent</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.dataset.DataSet" title="sttp.data.dataset.DataSet"><span class="pre">DataSet</span></a></em><a class="headerlink" href="#sttp.data.datatable.DataTable.parent" title="Link to this definition"></a></dt>
<dd><p>Gets the parent <cite>DataSet</cite> of the <cite>DataTable</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datatable.DataTable.row">
<span class="sig-name descname"><span class="pre">row</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">rowindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="#sttp.data.datarow.DataRow" title="sttp.data.datarow.DataRow"><span class="pre">DataRow</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></span><a class="reference internal" href="_modules/sttp/data/datatable.html#DataTable.row"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datatable.DataTable.row" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>DataRow</cite> at the specified row index if the index is in range;
otherwise, None is returned.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.datatable.DataTable.rowcount">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">rowcount</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">int</span></em><a class="headerlink" href="#sttp.data.datatable.DataTable.rowcount" title="Link to this definition"></a></dt>
<dd><p>Gets the total number of rows defined in the <cite>DataTable</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datatable.DataTable.rowswhere">
<span class="sig-name descname"><span class="pre">rowswhere</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">predicate</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.datarow.DataRow" title="sttp.data.datarow.DataRow"><span class="pre">DataRow</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">limit</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">-1</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.datarow.DataRow" title="sttp.data.datarow.DataRow"><span class="pre">DataRow</span></a><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datatable.html#DataTable.rowswhere"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datatable.DataTable.rowswhere" title="Link to this definition"></a></dt>
<dd><p>Returns the rows matching the predicate expression. Set limit parameter
to -1 for all matching rows.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datatable.DataTable.rowvalue_as_string">
<span class="sig-name descname"><span class="pre">rowvalue_as_string</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">rowindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">columnindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="reference internal" href="_modules/sttp/data/datatable.html#DataTable.rowvalue_as_string"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datatable.DataTable.rowvalue_as_string" title="Link to this definition"></a></dt>
<dd><p>Reads the row record value at the specified column index converted to a string.
For column index out of range or any other errors, an empty string will be returned.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datatable.DataTable.rowvalue_as_string_byname">
<span class="sig-name descname"><span class="pre">rowvalue_as_string_byname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">rowindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">columnname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="reference internal" href="_modules/sttp/data/datatable.html#DataTable.rowvalue_as_string_byname"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datatable.DataTable.rowvalue_as_string_byname" title="Link to this definition"></a></dt>
<dd><p>Reads the row record value for the specified column name converted to a string.
For column name not found or any other errors, an empty string will be returned.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.datatable.DataTable.select">
<span class="sig-name descname"><span class="pre">select</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filterexpression</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sortorder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">limit</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">-1</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.datarow.DataRow" title="sttp.data.datarow.DataRow"><span class="pre">DataRow</span></a><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datatable.html#DataTable.select"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datatable.DataTable.select" title="Link to this definition"></a></dt>
<dd><p>Returns the rows matching the filter expression criteria in the specified sort order.</p>
<p>The <cite>filterexpression</cite> parameter should be in the syntax of a SQL WHERE expression but
should not include the WHERE keyword. The <cite>sortorder</cite> parameter defines field names,
separated by commas, that exist in the <cite>DataTable</cite> used to order the results. Each
field specified in the <cite>sortorder</cite> can have an <cite>ASC</cite> or <cite>DESC</cite> suffix; defaults to
<cite>ASC</cite> when no suffix is provided. When <cite>sortorder</cite> is an empty string, records will
be returned in natural order. Set limit parameter to -1 for all matching rows. When
<cite>filterexpression</cite> is an empty string, all records will be returned; any specified
sort order and limit will still be respected.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.data.datatype">
<span id="sttp-data-datatype-module"></span><h2>sttp.data.datatype module<a class="headerlink" href="#module-sttp.data.datatype" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.datatype.DataType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.datatype.</span></span><span class="sig-name descname"><span class="pre">DataType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names=&lt;not</span> <span class="pre">given&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start=1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary=None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/datatype.html#DataType"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datatype.DataType" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></p>
<p>Enumeration of the possible data types for a <cite>DataColumn</cite>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.datatype.DataType.BOOLEAN">
<span class="sig-name descname"><span class="pre">BOOLEAN</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1</span></em><a class="headerlink" href="#sttp.data.datatype.DataType.BOOLEAN" title="Link to this definition"></a></dt>
<dd><p>Represents a Python <cite>bool</cite> data type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.datatype.DataType.DATETIME">
<span class="sig-name descname"><span class="pre">DATETIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2</span></em><a class="headerlink" href="#sttp.data.datatype.DataType.DATETIME" title="Link to this definition"></a></dt>
<dd><p>Represents a Python <cite>datetime</cite> data type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.datatype.DataType.DECIMAL">
<span class="sig-name descname"><span class="pre">DECIMAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">5</span></em><a class="headerlink" href="#sttp.data.datatype.DataType.DECIMAL" title="Link to this definition"></a></dt>
<dd><p>Represents a Python <cite>decimal.Decimal</cite> data type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.datatype.DataType.DOUBLE">
<span class="sig-name descname"><span class="pre">DOUBLE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">4</span></em><a class="headerlink" href="#sttp.data.datatype.DataType.DOUBLE" title="Link to this definition"></a></dt>
<dd><p>Represents a Python <cite>numpy.float64</cite> data type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.datatype.DataType.GUID">
<span class="sig-name descname"><span class="pre">GUID</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">6</span></em><a class="headerlink" href="#sttp.data.datatype.DataType.GUID" title="Link to this definition"></a></dt>
<dd><p>Represents a Python <cite>uuid.UUID</cite> data type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.datatype.DataType.INT16">
<span class="sig-name descname"><span class="pre">INT16</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">8</span></em><a class="headerlink" href="#sttp.data.datatype.DataType.INT16" title="Link to this definition"></a></dt>
<dd><p>Represents a Python <cite>numpy.int16</cite> data type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.datatype.DataType.INT32">
<span class="sig-name descname"><span class="pre">INT32</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">9</span></em><a class="headerlink" href="#sttp.data.datatype.DataType.INT32" title="Link to this definition"></a></dt>
<dd><p>Represents a Python <cite>numpy.int32</cite> data type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.datatype.DataType.INT64">
<span class="sig-name descname"><span class="pre">INT64</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">10</span></em><a class="headerlink" href="#sttp.data.datatype.DataType.INT64" title="Link to this definition"></a></dt>
<dd><p>Represents a Python <cite>numpy.int64</cite> data type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.datatype.DataType.INT8">
<span class="sig-name descname"><span class="pre">INT8</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">7</span></em><a class="headerlink" href="#sttp.data.datatype.DataType.INT8" title="Link to this definition"></a></dt>
<dd><p>Represents a Python <cite>numpy.int8</cite> data type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.datatype.DataType.SINGLE">
<span class="sig-name descname"><span class="pre">SINGLE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">3</span></em><a class="headerlink" href="#sttp.data.datatype.DataType.SINGLE" title="Link to this definition"></a></dt>
<dd><p>Represents a Python <cite>numpy.float32</cite> data type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.datatype.DataType.STRING">
<span class="sig-name descname"><span class="pre">STRING</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#sttp.data.datatype.DataType.STRING" title="Link to this definition"></a></dt>
<dd><p>Represents a Python <cite>str</cite> data type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.datatype.DataType.UINT16">
<span class="sig-name descname"><span class="pre">UINT16</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">12</span></em><a class="headerlink" href="#sttp.data.datatype.DataType.UINT16" title="Link to this definition"></a></dt>
<dd><p>Represents a Python <cite>numpy.uint16</cite> data type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.datatype.DataType.UINT32">
<span class="sig-name descname"><span class="pre">UINT32</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">13</span></em><a class="headerlink" href="#sttp.data.datatype.DataType.UINT32" title="Link to this definition"></a></dt>
<dd><p>Represents a Python <cite>numpy.uint32</cite> data type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.datatype.DataType.UINT64">
<span class="sig-name descname"><span class="pre">UINT64</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">14</span></em><a class="headerlink" href="#sttp.data.datatype.DataType.UINT64" title="Link to this definition"></a></dt>
<dd><p>Represents a Python <cite>numpy.uint64</cite> data type.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.datatype.DataType.UINT8">
<span class="sig-name descname"><span class="pre">UINT8</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">11</span></em><a class="headerlink" href="#sttp.data.datatype.DataType.UINT8" title="Link to this definition"></a></dt>
<dd><p>Represents a Python <cite>numpy.uint8</cite> data type.</p>
</dd></dl>

</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.datatype.default_datatype">
<span class="sig-prename descclassname"><span class="pre">sttp.data.datatype.</span></span><span class="sig-name descname"><span class="pre">default_datatype</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">datatype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datatype.DataType" title="sttp.data.datatype.DataType"><span class="pre">DataType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">object</span></span></span><a class="reference internal" href="_modules/sttp/data/datatype.html#default_datatype"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datatype.default_datatype" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sttp.data.datatype.parse_xsddatatype">
<span class="sig-prename descclassname"><span class="pre">sttp.data.datatype.</span></span><span class="sig-name descname"><span class="pre">parse_xsddatatype</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">xsdtypename</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">extdatatype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.datatype.DataType" title="sttp.data.datatype.DataType"><span class="pre">DataType</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/datatype.html#parse_xsddatatype"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.datatype.parse_xsddatatype" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>DataType</cite> from the provided XSD data type. Return tuple includes boolean
value that determines if parse was successful. See XML Schema Language Datatypes
for possible xsd type name values: <a class="reference external" href="https://www.w3.org/TR/xmlschema-2/">https://www.w3.org/TR/xmlschema-2/</a></p>
</dd></dl>

</section>
<section id="module-sttp.data.errors">
<span id="sttp-data-errors-module"></span><h2>sttp.data.errors module<a class="headerlink" href="#module-sttp.data.errors" title="Link to this heading"></a></h2>
<dl class="py exception">
<dt class="sig sig-object py" id="sttp.data.errors.EvaluateError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.errors.</span></span><span class="sig-name descname"><span class="pre">EvaluateError</span></span><a class="reference internal" href="_modules/sttp/data/errors.html#EvaluateError"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.errors.EvaluateError" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">Exception</span></code></p>
<p>Raised when the <cite>ExpressionTree</cite> fails during evaluation.</p>
</dd></dl>

</section>
<section id="module-sttp.data.expression">
<span id="sttp-data-expression-module"></span><h2>sttp.data.expression module<a class="headerlink" href="#module-sttp.data.expression" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.expression.Expression">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.expression.</span></span><span class="sig-name descname"><span class="pre">Expression</span></span><a class="reference internal" href="_modules/sttp/data/expression.html#Expression"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.expression.Expression" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Represents any expression type.</p>
<p class="rubric">Notes</p>
<p>This is the base class for all expression types.</p>
<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.expression.Expression.expressiontype">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">expressiontype</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.constants.ExpressionType" title="sttp.data.constants.ExpressionType"><span class="pre">ExpressionType</span></a></em><a class="headerlink" href="#sttp.data.expression.Expression.expressiontype" title="Link to this definition"></a></dt>
<dd><p>Gets the type of the expression.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.data.expressiontree">
<span id="sttp-data-expressiontree-module"></span><h2>sttp.data.expressiontree module<a class="headerlink" href="#module-sttp.data.expressiontree" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.expressiontree.ExpressionTree">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.expressiontree.</span></span><span class="sig-name descname"><span class="pre">ExpressionTree</span></span><a class="reference internal" href="_modules/sttp/data/expressiontree.html#ExpressionTree"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.expressiontree.ExpressionTree" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Represents a tree of expressions for evaluation.</p>
<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.expressiontree.ExpressionTree.evaluate">
<span class="sig-name descname"><span class="pre">evaluate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">row</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datarow.DataRow" title="sttp.data.datarow.DataRow"><span class="pre">DataRow</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression" title="sttp.data.valueexpression.ValueExpression"><span class="pre">ValueExpression</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/expressiontree.html#ExpressionTree.evaluate"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.expressiontree.ExpressionTree.evaluate" title="Link to this definition"></a></dt>
<dd><p>Traverses the the <cite>ExpressionTree</cite> for the provided data row to produce a <cite>ValueExpression</cite>.
Root expression should be assigned before calling <cite>evaluate</cite>; otherwise result will be a Null
<cite>ValueExpression</cite>. The <cite>datarow</cite> parameter can be None if there are no columns referenced in
expression tree. An error will be returned if the expresssion evaluation fails.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.expressiontree.ExpressionTree.orderbyterms">
<span class="sig-name descname"><span class="pre">orderbyterms</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.orderbyterm.OrderByTerm" title="sttp.data.orderbyterm.OrderByTerm"><span class="pre">OrderByTerm</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#sttp.data.expressiontree.ExpressionTree.orderbyterms" title="Link to this definition"></a></dt>
<dd><p>Represents the order by elements parsed from the “ORDER BY” keyword, if any.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.expressiontree.ExpressionTree.root">
<span class="sig-name descname"><span class="pre">root</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.expression.Expression" title="sttp.data.expression.Expression"><span class="pre">Expression</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#sttp.data.expressiontree.ExpressionTree.root" title="Link to this definition"></a></dt>
<dd><p>Defines the starting <cite>Expression</cite> for evaluation of the expression tree, or None if there
is not one. This is the root expression of the <cite>ExpressionTree</cite>. Value is automatically
managed by the <cite>FilterExpressionParser</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.expressiontree.ExpressionTree.select">
<span class="sig-name descname"><span class="pre">select</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">table</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datatable.DataTable" title="sttp.data.datatable.DataTable"><span class="pre">DataTable</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.datarow.DataRow" title="sttp.data.datarow.DataRow"><span class="pre">DataRow</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/expressiontree.html#ExpressionTree.select"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.expressiontree.ExpressionTree.select" title="Link to this definition"></a></dt>
<dd><p>Returns the rows matching the the <cite>ExpressionTree</cite>.</p>
<p>The expression tree result type is expected to be a boolean for this filtering operation.
This works like the “WHERE” clause of a SQL expression. Any “TOP” limit and “ORDER BY”
sorting clauses found in filter expressions will be respected. An error will be returned
if the table parameter is None, the expression tree does not yield a boolean value or any
row expresssion evaluation fails.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.expressiontree.ExpressionTree.selectwhere">
<span class="sig-name descname"><span class="pre">selectwhere</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">table</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datatable.DataTable" title="sttp.data.datatable.DataTable"><span class="pre">DataTable</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">predicate</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression" title="sttp.data.valueexpression.ValueExpression"><span class="pre">ValueExpression</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">applylimit</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">applysort</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.datarow.DataRow" title="sttp.data.datarow.DataRow"><span class="pre">DataRow</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/expressiontree.html#ExpressionTree.selectwhere"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.expressiontree.ExpressionTree.selectwhere" title="Link to this definition"></a></dt>
<dd><p>Returns each table row evaluated from the <cite>ExpressionTree</cite> that matches the specified predicate.</p>
<p>The <cite>applylimit</cite> and <cite>applysort</cite> parameters determine if any encountered “TOP” limit and “ORDER BY”
sorting clauses will be respected. An error will be returned if the table parameter is nil or any
row expresssion evaluation fails.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.expressiontree.ExpressionTree.tablename">
<span class="sig-name descname"><span class="pre">tablename</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#sttp.data.expressiontree.ExpressionTree.tablename" title="Link to this definition"></a></dt>
<dd><p>Defines the associated table name parsed from “FILTER” statement, if any.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.expressiontree.ExpressionTree.toplimit">
<span class="sig-name descname"><span class="pre">toplimit</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">int</span></em><a class="headerlink" href="#sttp.data.expressiontree.ExpressionTree.toplimit" title="Link to this definition"></a></dt>
<dd><p>Defined the parsed value associated with the “TOP” keyword, if any.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.data.filterexpressionparser">
<span id="sttp-data-filterexpressionparser-module"></span><h2>sttp.data.filterexpressionparser module<a class="headerlink" href="#module-sttp.data.filterexpressionparser" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.filterexpressionparser.</span></span><span class="sig-name descname"><span class="pre">FilterExpressionParser</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filterexpression</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">suppress_console_erroroutput</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">FilterExpressionSyntaxListener</span></code></p>
<p>Represents a parser for STTP filter expressions.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.dataset">
<span class="sig-name descname"><span class="pre">dataset</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.dataset.DataSet" title="sttp.data.dataset.DataSet"><span class="pre">DataSet</span></a></em><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.dataset" title="Link to this definition"></a></dt>
<dd><p>Defines the source metadata used for parsing the filter expression.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.enterExpression">
<span class="sig-name descname"><span class="pre">enterExpression</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ctx</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">ExpressionContext</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.enterExpression"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.enterExpression" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.enterFilterExpressionStatement">
<span class="sig-name descname"><span class="pre">enterFilterExpressionStatement</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ctx</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">FilterExpressionStatementContext</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.enterFilterExpressionStatement"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.enterFilterExpressionStatement" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.enterFilterStatement">
<span class="sig-name descname"><span class="pre">enterFilterStatement</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ctx</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">FilterStatementContext</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.enterFilterStatement"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.enterFilterStatement" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.evaluate">
<span class="sig-name descname"><span class="pre">evaluate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">applylimit</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">applysort</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.evaluate"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.evaluate" title="Link to this definition"></a></dt>
<dd><p>Evaluate parses each statement in the filter expression and tracks the results.</p>
<p>Filter expressions can contain multiple statements, separated by semi-colons, where each
statement results in a unique expression tree; this function returns the combined results
of each encountered filter expression statement, yielding all filtered rows and/or signal
IDs that match the target filter expression. The <cite>applylimit</cite> and <cite>applysort</cite> flags
determine if any encountered “TOP” limit and “ORDER BY” sorting clauses will be respected.
Access matching results via <cite>filtered_rows</cite> and/or <cite>filtered_signalids</cite>, or related set
functions. An error will be returned if expression fails to parse or any row expression
evaluation fails.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.evaluate_datarowexpression">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">evaluate_datarowexpression</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">datarow</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datarow.DataRow" title="sttp.data.datarow.DataRow"><span class="pre">DataRow</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">filterexpression</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">suppress_console_erroroutput</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression" title="sttp.data.valueexpression.ValueExpression"><span class="pre">ValueExpression</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.evaluate_datarowexpression"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.evaluate_datarowexpression" title="Link to this definition"></a></dt>
<dd><p>Returns the result of the evaluated <cite>filterexpression</cite> using the specified <cite>datarow</cite>.</p>
<p>If <cite>filterexpression</cite> contains multiple semi-colon separated statements, only the first expression is evaluated.
An error will be returned if <cite>datarow</cite> parameter is None, the <cite>filterexpression</cite> is empty, expression fails to
parse or row expression evaluation fails.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.evaluate_expression">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">evaluate_expression</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filterexpression</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">suppress_console_erroroutput</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression" title="sttp.data.valueexpression.ValueExpression"><span class="pre">ValueExpression</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.evaluate_expression"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.evaluate_expression" title="Link to this definition"></a></dt>
<dd><p>Returns the result of the evaluated <cite>filterexpression</cite>.</p>
<p>This expression evaluation function is only for simple expressions that do not reference any <cite>DataSet</cite> columns.
Use <cite>evaluate_datarowexpression</cite> for evaluating filter expressions that contain column references.
If <cite>filterexpression</cite> contains multiple semi-colon separated statements, only the first expression is evaluated.
An error will be returned if the <cite>filterexpression</cite> is empty or expression fails to parse.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.exitColumnName">
<span class="sig-name descname"><span class="pre">exitColumnName</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ctx</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">ColumnNameContext</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.exitColumnName"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.exitColumnName" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.exitExpression">
<span class="sig-name descname"><span class="pre">exitExpression</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ctx</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">ExpressionContext</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.exitExpression"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.exitExpression" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.exitFunctionExpression">
<span class="sig-name descname"><span class="pre">exitFunctionExpression</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ctx</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">FunctionExpressionContext</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.exitFunctionExpression"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.exitFunctionExpression" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.exitIdentifierStatement">
<span class="sig-name descname"><span class="pre">exitIdentifierStatement</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ctx</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">IdentifierStatementContext</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.exitIdentifierStatement"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.exitIdentifierStatement" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.exitLiteralValue">
<span class="sig-name descname"><span class="pre">exitLiteralValue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ctx</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">LiteralValueContext</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.exitLiteralValue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.exitLiteralValue" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.exitPredicateExpression">
<span class="sig-name descname"><span class="pre">exitPredicateExpression</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ctx</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">PredicateExpressionContext</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.exitPredicateExpression"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.exitPredicateExpression" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.exitValueExpression">
<span class="sig-name descname"><span class="pre">exitValueExpression</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ctx</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">ValueExpressionContext</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.exitValueExpression"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.exitValueExpression" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.expressiontrees">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">expressiontrees</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.expressiontree.ExpressionTree" title="sttp.data.expressiontree.ExpressionTree"><span class="pre">ExpressionTree</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.expressiontrees" title="Link to this definition"></a></dt>
<dd><p>Returns the list of expression trees parsed from the filter expression.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.filtered_rows">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">filtered_rows</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.datarow.DataRow" title="sttp.data.datarow.DataRow"><span class="pre">DataRow</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.filtered_rows" title="Link to this definition"></a></dt>
<dd><p>Gets the rows matching the parsed filter expression.</p>
<p>Results could contain duplicates if source <cite>DataSet</cite> has duplicated rows.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.filtered_rowset">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">filtered_rowset</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Set</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.datarow.DataRow" title="sttp.data.datarow.DataRow"><span class="pre">DataRow</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.filtered_rowset" title="Link to this definition"></a></dt>
<dd><p>Gets the unique row set matching the parsed filter expression.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.filtered_signalids">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">filtered_signalids</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">List</span><span class="p"><span class="pre">[</span></span><span class="pre">UUID</span><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.filtered_signalids" title="Link to this definition"></a></dt>
<dd><p>Gets the Guid-based signal IDs matching the parsed filter expression.</p>
<p>Results could contain duplicates if source <cite>DataSet</cite> has duplicated rows.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.filtered_signalidset">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">filtered_signalidset</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Set</span><span class="p"><span class="pre">[</span></span><span class="pre">UUID</span><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.filtered_signalidset" title="Link to this definition"></a></dt>
<dd><p>Gets the unique Guid-based signal ID set matching the parsed filter expression.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.filterexpression_statementcount">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">filterexpression_statementcount</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">int</span></em><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.filterexpression_statementcount" title="Link to this definition"></a></dt>
<dd><p>Gets the number of filter expression statements encountered while parsing.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.from_dataset">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">from_dataset</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dataset</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.dataset.DataSet" title="sttp.data.dataset.DataSet"><span class="pre">DataSet</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">filterexpression</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">primary_table</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tableidfields</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.tableidfields.TableIDFields" title="sttp.data.tableidfields.TableIDFields"><span class="pre">TableIDFields</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">suppress_console_erroroutput</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.filterexpressionparser.FilterExpressionParser" title="sttp.data.filterexpressionparser.FilterExpressionParser"><span class="pre">FilterExpressionParser</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.from_dataset"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.from_dataset" title="Link to this definition"></a></dt>
<dd><p>Creates a new filter expression parser associated with the provided <cite>dataSet</cite>
and provided table details. Error will be returned if <cite>dataset</cite> parameter is
None or the <cite>filterexpression</cite> is empty.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.generate_expressiontree">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">generate_expressiontree</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">datatable</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datatable.DataTable" title="sttp.data.datatable.DataTable"><span class="pre">DataTable</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">filterexpression</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">suppress_console_erroroutput</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.expressiontree.ExpressionTree" title="sttp.data.expressiontree.ExpressionTree"><span class="pre">ExpressionTree</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.generate_expressiontree"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.generate_expressiontree" title="Link to this definition"></a></dt>
<dd><p>Gets the first produced expression tree for the provided <cite>filterexpression</cite> and <cite>datatable</cite>.</p>
<p>If <cite>filterexpression</cite> contains multiple semi-colon separated statements, only the first expression is returned.
An error will be returned if <cite>datatable</cite> parameter is None, the <cite>filterexpression</cite> is empty or expression fails to parse.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.generate_expressiontrees">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">generate_expressiontrees</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dataset</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.dataset.DataSet" title="sttp.data.dataset.DataSet"><span class="pre">DataSet</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">primarytable</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filterexpression</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">suppress_console_erroroutput</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.expressiontree.ExpressionTree" title="sttp.data.expressiontree.ExpressionTree"><span class="pre">ExpressionTree</span></a><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.generate_expressiontrees"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.generate_expressiontrees" title="Link to this definition"></a></dt>
<dd><p>Produces a set of expression trees for the provided <cite>filterexpression</cite> and <cite>dataset</cite>.</p>
<p>One expression tree will be produced per filter expression statement encountered in the specified <cite>filterexpression</cite>.
If <cite>primarytable</cite> parameter is not defined, then filter expression should not contain directly defined signal IDs.
An error will be returned if <cite>dataSet</cite> parameter is None, the <cite>filterexpression</cite> is empty or expression fails to parse.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.generate_expressiontrees_fromtable">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">generate_expressiontrees_fromtable</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">datatable</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datatable.DataTable" title="sttp.data.datatable.DataTable"><span class="pre">DataTable</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">filterexpression</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">suppress_console_erroroutput</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.expressiontree.ExpressionTree" title="sttp.data.expressiontree.ExpressionTree"><span class="pre">ExpressionTree</span></a><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.generate_expressiontrees_fromtable"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.generate_expressiontrees_fromtable" title="Link to this definition"></a></dt>
<dd><p>Produces a set of expression trees for the provided <cite>filterexpression</cite> and <cite>datatable</cite>.</p>
<p>One expression tree will be produced per filter expression statement encountered in the specified <cite>filterexpression</cite>.
An error will be returned if <cite>datatable</cite> parameter is None, the <cite>filterexpression</cite> is empty or expression fails to parse.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.primary_tablename">
<span class="sig-name descname"><span class="pre">primary_tablename</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.primary_tablename" title="Link to this definition"></a></dt>
<dd><p>Defines the name of the table to use in the DataSet when filter expressions do not specify
a table name, e.g., direct signal identification. See:
<a class="reference external" href="https://sttp.github.io/documentation/filter-expressions/#direct-signal-identification">https://sttp.github.io/documentation/filter-expressions/#direct-signal-identification</a></p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.select_datarows">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">select_datarows</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dataset</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.dataset.DataSet" title="sttp.data.dataset.DataSet"><span class="pre">DataSet</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">filterexpression</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">primarytable</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tableidfields</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.tableidfields.TableIDFields" title="sttp.data.tableidfields.TableIDFields"><span class="pre">TableIDFields</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">suppress_console_erroroutput</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.datarow.DataRow" title="sttp.data.datarow.DataRow"><span class="pre">DataRow</span></a><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.select_datarows"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.select_datarows" title="Link to this definition"></a></dt>
<dd><p>Returns all rows matching the provided <cite>filterexpression</cite> and <cite>dataset</cite>.</p>
<p>Filter expressions can contain multiple statements, separated by semi-colons, where each statement results in a
unique expression tree; this function returns the combined results of each encountered filter expression statement.
Returned <cite>DataRow</cite> list will contain all matching rows, order preserved. If <cite>dataset</cite> includes duplicated rows, it
will be possible for the result set to contain duplicates. Any encountered “TOP” limit or “ORDER BY” clauses will
be respected. An error will be returned if <cite>dataset</cite> parameter is None, the <cite>filterexpression</cite> is empty, expression
fails to parse or any row expression evaluation fails.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.select_datarows_fromtable">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">select_datarows_fromtable</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">datatable</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datatable.DataTable" title="sttp.data.datatable.DataTable"><span class="pre">DataTable</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">filterexpression</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tableidfields</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.tableidfields.TableIDFields" title="sttp.data.tableidfields.TableIDFields"><span class="pre">TableIDFields</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">suppress_console_erroroutput</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.datarow.DataRow" title="sttp.data.datarow.DataRow"><span class="pre">DataRow</span></a><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.select_datarows_fromtable"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.select_datarows_fromtable" title="Link to this definition"></a></dt>
<dd><p>Returns all rows matching the provided <cite>filterexpression</cite> and <cite>datatable</cite>.</p>
<p>Filter expressions can contain multiple statements, separated by semi-colons, where each statement results
in a unique expression tree; this function returns the combined results of each encountered filter expression
statement. Returned <cite>DataRow</cite> list will contain all matching rows, order preserved. If dataSet includes
duplicated rows, it will be possible for the result set to contain duplicates. Any encountered “TOP” limit or
“ORDER BY” clauses will be respected. An error will be returned if <cite>datatable</cite> parameter (or its parent DataSet)
is None, the <cite>filterexpression</cite> is empty, expression fails to parse or any row expression evaluation fails.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.select_datarowset">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">select_datarowset</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dataset</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.dataset.DataSet" title="sttp.data.dataset.DataSet"><span class="pre">DataSet</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">filterexpression</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">primarytable</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tableidfields</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.tableidfields.TableIDFields" title="sttp.data.tableidfields.TableIDFields"><span class="pre">TableIDFields</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">suppress_console_erroroutput</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">Set</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.datarow.DataRow" title="sttp.data.datarow.DataRow"><span class="pre">DataRow</span></a><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.select_datarowset"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.select_datarowset" title="Link to this definition"></a></dt>
<dd><p>Returns all unique rows matching the provided <cite>filterexpression</cite> and <cite>dataset</cite>.</p>
<p>Filter expressions can contain multiple statements, separated by semi-colons, where each statement results
in a unique expression tree; this function returns the combined results of each encountered filter expression
statement. Returned <cite>DataRow</cite> set will contain only unique rows, in arbitrary order. Any encountered “TOP” limit
clauses for individual filter expression statements will be respected, but “ORDER BY” clauses will be ignored.
An error will be returned if <cite>dataset</cite> parameter is None, the <cite>filterexpression</cite> is empty, expression fails to
parse or any row expression evaluation fails.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.select_datarowset_fromtable">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">select_datarowset_fromtable</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">datatable</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datatable.DataTable" title="sttp.data.datatable.DataTable"><span class="pre">DataTable</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">filterexpression</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tableidfields</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.tableidfields.TableIDFields" title="sttp.data.tableidfields.TableIDFields"><span class="pre">TableIDFields</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">suppress_console_erroroutput</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">Set</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.datarow.DataRow" title="sttp.data.datarow.DataRow"><span class="pre">DataRow</span></a><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.select_datarowset_fromtable"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.select_datarowset_fromtable" title="Link to this definition"></a></dt>
<dd><p>Returns all unique rows matching the provided <cite>filterexpression</cite> and <cite>datatable</cite>.</p>
<p>Filter expressions can contain multiple statements, separated by semi-colons, where each statement results
in a unique expression tree; this function returns the combined results of each encountered filter expression
statement. Returned <cite>DataRow</cite> set will contain only unique rows, in arbitrary order. Any encountered “TOP” limit
clauses for individual filter expression statements will be respected, but “ORDER BY” clauses will be ignored.
An error will be returned if <cite>datatable</cite> parameter (or its parent DataSet) is None, the <cite>filterexpression</cite> is
empty, expression fails to parse or any row expression evaluation fails.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.select_signalidset">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">select_signalidset</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dataset</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.dataset.DataSet" title="sttp.data.dataset.DataSet"><span class="pre">DataSet</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">filterexpression</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">primarytable</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tableidfields</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.tableidfields.TableIDFields" title="sttp.data.tableidfields.TableIDFields"><span class="pre">TableIDFields</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">suppress_console_erroroutput</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">Set</span><span class="p"><span class="pre">[</span></span><span class="pre">UUID</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.select_signalidset"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.select_signalidset" title="Link to this definition"></a></dt>
<dd><p>Returns all unique signal IDs matching the provided <cite>filterexpression</cite> and <cite>dataset</cite>.</p>
<p>Filter expressions can contain multiple statements, separated by semi-colons, where each statement results
in a unique expression tree; this function returns the combined results of each encountered filter expression
statement. Returned <cite>UUID</cite> set will contain only unique values, in arbitrary order. Any encountered “TOP” limit
clauses for individual filter expression statements will be respected, but “ORDER BY” clauses will be ignored.
An error will be returned if <cite>dataset</cite> parameter is None, the <cite>filterexpression</cite> is empty, expression fails to
parse or any row expression evaluation fails.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.select_signalidset_fromtable">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">select_signalidset_fromtable</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">datatable</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datatable.DataTable" title="sttp.data.datatable.DataTable"><span class="pre">DataTable</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">filterexpression</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tableidfields</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.tableidfields.TableIDFields" title="sttp.data.tableidfields.TableIDFields"><span class="pre">TableIDFields</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">suppress_console_erroroutput</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">Set</span><span class="p"><span class="pre">[</span></span><span class="pre">UUID</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.select_signalidset_fromtable"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.select_signalidset_fromtable" title="Link to this definition"></a></dt>
<dd><p>Returns all unique signal IDs matching the provided <cite>filterexpression</cite> and <cite>datatable</cite>.</p>
<p>Filter expressions can contain multiple statements, separated by semi-colons, where each statement results
in a unique expression tree; this function returns the combined results of each encountered filter expression
statement. Returned <cite>UUID</cite> set will contain only unique values, in arbitrary order. Any encountered “TOP” limit
clauses for individual filter expression statements will be respected, but “ORDER BY” clauses will be ignored.
An error will be returned if <cite>datatable</cite> parameter (or its parent DataSet) is None, the <cite>filterexpression</cite> is
empty, expression fails to parse or any row expression evaluation fails.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.set_parsingexception_callback">
<span class="sig-name descname"><span class="pre">set_parsingexception_callback</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">callback</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.set_parsingexception_callback"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.set_parsingexception_callback" title="Link to this definition"></a></dt>
<dd><p>Registers a callback for receiving parsing exception messages.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.table">
<span class="sig-name descname"><span class="pre">table</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tablename</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.datatable.DataTable" title="sttp.data.datatable.DataTable"><span class="pre">DataTable</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/filterexpressionparser.html#FilterExpressionParser.table"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.table" title="Link to this definition"></a></dt>
<dd><p>Gets the DataTable for the specified tableName from the FilterExpressionParser DataSet.</p>
<p>An error will be returned if no DataSet has been defined or the tableName cannot be found.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.tableidfields_map">
<span class="sig-name descname"><span class="pre">tableidfields_map</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Dict</span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.tableidfields.TableIDFields" title="sttp.data.tableidfields.TableIDFields"><span class="pre">TableIDFields</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.tableidfields_map" title="Link to this definition"></a></dt>
<dd><p>Defines a map of table ID fields associated with table names.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.track_filteredrows">
<span class="sig-name descname"><span class="pre">track_filteredrows</span></span><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.track_filteredrows" title="Link to this definition"></a></dt>
<dd><p>Defines a flag that enables tracking of matching rows during filter expression evaluation.
Value defaults to True. Set value to False and set <cite>track_filteredsignalids</cite> to True if
only signal IDs are needed post filter expression evaluation.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.filterexpressionparser.FilterExpressionParser.track_filteredsignalids">
<span class="sig-name descname"><span class="pre">track_filteredsignalids</span></span><a class="headerlink" href="#sttp.data.filterexpressionparser.FilterExpressionParser.track_filteredsignalids" title="Link to this definition"></a></dt>
<dd><p>Defines a flag that enables tracking of matching signal IDs during filter expression evaluation.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.data.functionexpression">
<span id="sttp-data-functionexpression-module"></span><h2>sttp.data.functionexpression module<a class="headerlink" href="#module-sttp.data.functionexpression" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.functionexpression.FunctionExpression">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.functionexpression.</span></span><span class="sig-name descname"><span class="pre">FunctionExpression</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">functiontype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType" title="sttp.data.constants.ExpressionFunctionType"><span class="pre">ExpressionFunctionType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">arguments</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">list</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.expression.Expression" title="sttp.data.expression.Expression"><span class="pre">Expression</span></a><span class="p"><span class="pre">]</span></span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/functionexpression.html#FunctionExpression"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.functionexpression.FunctionExpression" title="Link to this definition"></a></dt>
<dd><p>Bases: <a class="reference internal" href="#sttp.data.expression.Expression" title="sttp.data.expression.Expression"><code class="xref py py-class docutils literal notranslate"><span class="pre">Expression</span></code></a></p>
<p>Represents a function expression.</p>
<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.functionexpression.FunctionExpression.arguments">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">arguments</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">list</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.expression.Expression" title="sttp.data.expression.Expression"><span class="pre">Expression</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#sttp.data.functionexpression.FunctionExpression.arguments" title="Link to this definition"></a></dt>
<dd><p>Gets the arguments of this <cite>FunctionExpression</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.functionexpression.FunctionExpression.expressiontype">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">expressiontype</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.constants.ExpressionType" title="sttp.data.constants.ExpressionType"><span class="pre">ExpressionType</span></a></em><a class="headerlink" href="#sttp.data.functionexpression.FunctionExpression.expressiontype" title="Link to this definition"></a></dt>
<dd><p>Gets the type of this <cite>FunctionExpression</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.functionexpression.FunctionExpression.functiontype">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">functiontype</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.constants.ExpressionFunctionType" title="sttp.data.constants.ExpressionFunctionType"><span class="pre">ExpressionFunctionType</span></a></em><a class="headerlink" href="#sttp.data.functionexpression.FunctionExpression.functiontype" title="Link to this definition"></a></dt>
<dd><p>Gets the type of function of this <cite>FunctionExpression</cite>.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.data.inlistexpression">
<span id="sttp-data-inlistexpression-module"></span><h2>sttp.data.inlistexpression module<a class="headerlink" href="#module-sttp.data.inlistexpression" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.inlistexpression.InListExpression">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.inlistexpression.</span></span><span class="sig-name descname"><span class="pre">InListExpression</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.expression.Expression" title="sttp.data.expression.Expression"><span class="pre">Expression</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">arguments</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">list</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.expression.Expression" title="sttp.data.expression.Expression"><span class="pre">Expression</span></a><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">has_notkeyword</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exactmatch</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/inlistexpression.html#InListExpression"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.inlistexpression.InListExpression" title="Link to this definition"></a></dt>
<dd><p>Bases: <a class="reference internal" href="#sttp.data.expression.Expression" title="sttp.data.expression.Expression"><code class="xref py py-class docutils literal notranslate"><span class="pre">Expression</span></code></a></p>
<p>Represents an in-list expression.</p>
<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.inlistexpression.InListExpression.arguments">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">arguments</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">list</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.expression.Expression" title="sttp.data.expression.Expression"><span class="pre">Expression</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#sttp.data.inlistexpression.InListExpression.arguments" title="Link to this definition"></a></dt>
<dd><p>Gets the arguments of this <cite>FunctionExpression</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.inlistexpression.InListExpression.exactmatch">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">exactmatch</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">bool</span></em><a class="headerlink" href="#sttp.data.inlistexpression.InListExpression.exactmatch" title="Link to this definition"></a></dt>
<dd><p>Gets a flag that indicates whether this <cite>InListExpression</cite> is an exact match. i.e., has the <cite>BINARY</cite> or <cite>===</cite> keyword.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.inlistexpression.InListExpression.expressiontype">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">expressiontype</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.constants.ExpressionType" title="sttp.data.constants.ExpressionType"><span class="pre">ExpressionType</span></a></em><a class="headerlink" href="#sttp.data.inlistexpression.InListExpression.expressiontype" title="Link to this definition"></a></dt>
<dd><p>Gets the type of this <cite>InListExpression</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.inlistexpression.InListExpression.has_notkeyword">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">has_notkeyword</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">bool</span></em><a class="headerlink" href="#sttp.data.inlistexpression.InListExpression.has_notkeyword" title="Link to this definition"></a></dt>
<dd><p>Gets a flag that indicates whether this <cite>InListExpression</cite> has a <cite>NOT</cite> keyword.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.inlistexpression.InListExpression.value">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.expression.Expression" title="sttp.data.expression.Expression"><span class="pre">Expression</span></a></em><a class="headerlink" href="#sttp.data.inlistexpression.InListExpression.value" title="Link to this definition"></a></dt>
<dd><p>Gets the value of this <cite>InListExpression</cite>.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.data.operatorexpression">
<span id="sttp-data-operatorexpression-module"></span><h2>sttp.data.operatorexpression module<a class="headerlink" href="#module-sttp.data.operatorexpression" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.operatorexpression.OperatorExpression">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.operatorexpression.</span></span><span class="sig-name descname"><span class="pre">OperatorExpression</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">operatortype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType" title="sttp.data.constants.ExpressionOperatorType"><span class="pre">ExpressionOperatorType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">leftvalue</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.expression.Expression" title="sttp.data.expression.Expression"><span class="pre">Expression</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">rightvalue</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.expression.Expression" title="sttp.data.expression.Expression"><span class="pre">Expression</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/operatorexpression.html#OperatorExpression"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.operatorexpression.OperatorExpression" title="Link to this definition"></a></dt>
<dd><p>Bases: <a class="reference internal" href="#sttp.data.expression.Expression" title="sttp.data.expression.Expression"><code class="xref py py-class docutils literal notranslate"><span class="pre">Expression</span></code></a></p>
<p>Represents an operator expression.</p>
<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.operatorexpression.OperatorExpression.expressiontype">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">expressiontype</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.constants.ExpressionType" title="sttp.data.constants.ExpressionType"><span class="pre">ExpressionType</span></a></em><a class="headerlink" href="#sttp.data.operatorexpression.OperatorExpression.expressiontype" title="Link to this definition"></a></dt>
<dd><p>Gets the type of this <cite>OperatorExpression</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.operatorexpression.OperatorExpression.leftvalue">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">leftvalue</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.expression.Expression" title="sttp.data.expression.Expression"><span class="pre">Expression</span></a></em><a class="headerlink" href="#sttp.data.operatorexpression.OperatorExpression.leftvalue" title="Link to this definition"></a></dt>
<dd><p>Gets the left value expression for this <cite>OperatorExpression</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.operatorexpression.OperatorExpression.operatortype">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">operatortype</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.constants.ExpressionOperatorType" title="sttp.data.constants.ExpressionOperatorType"><span class="pre">ExpressionOperatorType</span></a></em><a class="headerlink" href="#sttp.data.operatorexpression.OperatorExpression.operatortype" title="Link to this definition"></a></dt>
<dd><p>Gets the operator type for this <cite>OperatorExpression</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.operatorexpression.OperatorExpression.rightvalue">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">rightvalue</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.expression.Expression" title="sttp.data.expression.Expression"><span class="pre">Expression</span></a></em><a class="headerlink" href="#sttp.data.operatorexpression.OperatorExpression.rightvalue" title="Link to this definition"></a></dt>
<dd><p>Gets the right value expression for this <cite>OperatorExpression</cite>.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.data.orderbyterm">
<span id="sttp-data-orderbyterm-module"></span><h2>sttp.data.orderbyterm module<a class="headerlink" href="#module-sttp.data.orderbyterm" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.orderbyterm.OrderByTerm">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.orderbyterm.</span></span><span class="sig-name descname"><span class="pre">OrderByTerm</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">column</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.datacolumn.DataColumn" title="sttp.data.datacolumn.DataColumn"><span class="pre">DataColumn</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">ascending</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">extactmatch</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/orderbyterm.html#OrderByTerm"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.orderbyterm.OrderByTerm" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Represents the elements parsed for a <cite>DataColumn</cite> specified in the “ORDER BY” keyword.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.orderbyterm.OrderByTerm.ascending">
<span class="sig-name descname"><span class="pre">ascending</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">bool</span></em><a class="headerlink" href="#sttp.data.orderbyterm.OrderByTerm.ascending" title="Link to this definition"></a></dt>
<dd><p>Defines the sort direction parsed from “ORDER BY” statement.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.orderbyterm.OrderByTerm.column">
<span class="sig-name descname"><span class="pre">column</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.datacolumn.DataColumn" title="sttp.data.datacolumn.DataColumn"><span class="pre">DataColumn</span></a></em><a class="headerlink" href="#sttp.data.orderbyterm.OrderByTerm.column" title="Link to this definition"></a></dt>
<dd><p>Defines the data column referenced from an “ORDER BY” statement.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.orderbyterm.OrderByTerm.extactmatch">
<span class="sig-name descname"><span class="pre">extactmatch</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">bool</span></em><a class="headerlink" href="#sttp.data.orderbyterm.OrderByTerm.extactmatch" title="Link to this definition"></a></dt>
<dd><p>Defines the exact match flag parsed from “ORDER BY” statement.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.data.tableidfields">
<span id="sttp-data-tableidfields-module"></span><h2>sttp.data.tableidfields module<a class="headerlink" href="#module-sttp.data.tableidfields" title="Link to this heading"></a></h2>
<dl class="py data">
<dt class="sig sig-object py" id="sttp.data.tableidfields.DEFAULT_TABLEIDFIELDS">
<span class="sig-prename descclassname"><span class="pre">sttp.data.tableidfields.</span></span><span class="sig-name descname"><span class="pre">DEFAULT_TABLEIDFIELDS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">&lt;sttp.data.tableidfields.TableIDFields</span> <span class="pre">object&gt;</span></em><a class="headerlink" href="#sttp.data.tableidfields.DEFAULT_TABLEIDFIELDS" title="Link to this definition"></a></dt>
<dd><p>Defines the common default table ID field names.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.tableidfields.TableIDFields">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.tableidfields.</span></span><span class="sig-name descname"><span class="pre">TableIDFields</span></span><a class="reference internal" href="_modules/sttp/data/tableidfields.html#TableIDFields"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.tableidfields.TableIDFields" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Represents the primary identification field names for a metadata table that
is being used as the source for an STTP filter expression. See:
<a class="reference external" href="https://sttp.github.io/documentation/filter-expressions/#activemeasurements">https://sttp.github.io/documentation/filter-expressions/#activemeasurements</a></p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.tableidfields.TableIDFields.measurementkey_fieldname">
<span class="sig-name descname"><span class="pre">measurementkey_fieldname</span></span><a class="headerlink" href="#sttp.data.tableidfields.TableIDFields.measurementkey_fieldname" title="Link to this definition"></a></dt>
<dd><p>id”),
type string. Common value is “ID”.</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>Defines the name of the measurement key field (format like “instance</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.tableidfields.TableIDFields.pointtag_fieldname">
<span class="sig-name descname"><span class="pre">pointtag_fieldname</span></span><a class="headerlink" href="#sttp.data.tableidfields.TableIDFields.pointtag_fieldname" title="Link to this definition"></a></dt>
<dd><p>Defines the name of the point tag field, type string.
Common value is “PointTag”.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.data.tableidfields.TableIDFields.signalid_fieldname">
<span class="sig-name descname"><span class="pre">signalid_fieldname</span></span><a class="headerlink" href="#sttp.data.tableidfields.TableIDFields.signalid_fieldname" title="Link to this definition"></a></dt>
<dd><p>Defines the field name of the signal ID field, type Guid.
Common value is “SignalID”.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.data.unaryexpression">
<span id="sttp-data-unaryexpression-module"></span><h2>sttp.data.unaryexpression module<a class="headerlink" href="#module-sttp.data.unaryexpression" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.unaryexpression.UnaryExpression">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.unaryexpression.</span></span><span class="sig-name descname"><span class="pre">UnaryExpression</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">unarytype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionUnaryType" title="sttp.data.constants.ExpressionUnaryType"><span class="pre">ExpressionUnaryType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.expression.Expression" title="sttp.data.expression.Expression"><span class="pre">Expression</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/unaryexpression.html#UnaryExpression"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.unaryexpression.UnaryExpression" title="Link to this definition"></a></dt>
<dd><p>Bases: <a class="reference internal" href="#sttp.data.expression.Expression" title="sttp.data.expression.Expression"><code class="xref py py-class docutils literal notranslate"><span class="pre">Expression</span></code></a></p>
<p>Represents a unary expression.</p>
<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.unaryexpression.UnaryExpression.applyto_bool">
<span class="sig-name descname"><span class="pre">applyto_bool</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression" title="sttp.data.valueexpression.ValueExpression"><span class="pre">ValueExpression</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/unaryexpression.html#UnaryExpression.applyto_bool"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.unaryexpression.UnaryExpression.applyto_bool" title="Link to this definition"></a></dt>
<dd><p>Applies the <cite>UnaryExpression</cite> prefix to a boolean and returns <cite>ValueExpression</cite> of result, if possible.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.unaryexpression.UnaryExpression.applyto_decimal">
<span class="sig-name descname"><span class="pre">applyto_decimal</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Decimal</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression" title="sttp.data.valueexpression.ValueExpression"><span class="pre">ValueExpression</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/unaryexpression.html#UnaryExpression.applyto_decimal"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.unaryexpression.UnaryExpression.applyto_decimal" title="Link to this definition"></a></dt>
<dd><p>Applies the <cite>UnaryExpression</cite> prefix to a <cite>Decimal</cite> value and returns <cite>ValueExpression</cite> of result, if possible.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.unaryexpression.UnaryExpression.applyto_double">
<span class="sig-name descname"><span class="pre">applyto_double</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">float64</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression" title="sttp.data.valueexpression.ValueExpression"><span class="pre">ValueExpression</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/unaryexpression.html#UnaryExpression.applyto_double"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.unaryexpression.UnaryExpression.applyto_double" title="Link to this definition"></a></dt>
<dd><p>Applies the <cite>UnaryExpression</cite> prefix to a <cite>Double</cite> value and returns <cite>ValueExpression</cite> of result, if possible.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.unaryexpression.UnaryExpression.applyto_int32">
<span class="sig-name descname"><span class="pre">applyto_int32</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int32</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression" title="sttp.data.valueexpression.ValueExpression"><span class="pre">ValueExpression</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/unaryexpression.html#UnaryExpression.applyto_int32"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.unaryexpression.UnaryExpression.applyto_int32" title="Link to this definition"></a></dt>
<dd><p>Applies the <cite>UnaryExpression</cite> prefix to a 32-bit integer value and returns <cite>ValueExpression</cite> of result.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.unaryexpression.UnaryExpression.applyto_int64">
<span class="sig-name descname"><span class="pre">applyto_int64</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int64</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression" title="sttp.data.valueexpression.ValueExpression"><span class="pre">ValueExpression</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/unaryexpression.html#UnaryExpression.applyto_int64"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.unaryexpression.UnaryExpression.applyto_int64" title="Link to this definition"></a></dt>
<dd><p>Applies the <cite>UnaryExpression</cite> prefix to a 64-bit integer value and returns <cite>ValueExpression</cite> of result.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.unaryexpression.UnaryExpression.expressiontype">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">expressiontype</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.constants.ExpressionType" title="sttp.data.constants.ExpressionType"><span class="pre">ExpressionType</span></a></em><a class="headerlink" href="#sttp.data.unaryexpression.UnaryExpression.expressiontype" title="Link to this definition"></a></dt>
<dd><p>Gets the type of this <cite>UnaryExpression</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.unaryexpression.UnaryExpression.unarytype">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">unarytype</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.constants.ExpressionUnaryType" title="sttp.data.constants.ExpressionUnaryType"><span class="pre">ExpressionUnaryType</span></a></em><a class="headerlink" href="#sttp.data.unaryexpression.UnaryExpression.unarytype" title="Link to this definition"></a></dt>
<dd><p>Gets the type of unary operation of this <cite>UnaryExpression</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.unaryexpression.UnaryExpression.value">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.expression.Expression" title="sttp.data.expression.Expression"><span class="pre">Expression</span></a></em><a class="headerlink" href="#sttp.data.unaryexpression.UnaryExpression.value" title="Link to this definition"></a></dt>
<dd><p>Gets the value of this <cite>UnaryExpression</cite>.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.data.valueexpression">
<span id="sttp-data-valueexpression-module"></span><h2>sttp.data.valueexpression module<a class="headerlink" href="#module-sttp.data.valueexpression" title="Link to this heading"></a></h2>
<dl class="py data">
<dt class="sig sig-object py" id="sttp.data.valueexpression.EMPTYSTRINGVALUE">
<span class="sig-prename descclassname"><span class="pre">sttp.data.valueexpression.</span></span><span class="sig-name descname"><span class="pre">EMPTYSTRINGVALUE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">5</span></em><a class="headerlink" href="#sttp.data.valueexpression.EMPTYSTRINGVALUE" title="Link to this definition"></a></dt>
<dd><p>Defines a <cite>ValueExpression</cite> that represents an empty string.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sttp.data.valueexpression.FALSEVALUE">
<span class="sig-prename descclassname"><span class="pre">sttp.data.valueexpression.</span></span><span class="sig-name descname"><span class="pre">FALSEVALUE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#sttp.data.valueexpression.FALSEVALUE" title="Link to this definition"></a></dt>
<dd><p>Defines a <cite>ValueExpression</cite> that represents <cite>False</cite>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sttp.data.valueexpression.NULLBOOLVALUE">
<span class="sig-prename descclassname"><span class="pre">sttp.data.valueexpression.</span></span><span class="sig-name descname"><span class="pre">NULLBOOLVALUE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#sttp.data.valueexpression.NULLBOOLVALUE" title="Link to this definition"></a></dt>
<dd><p>Defines a <cite>ValueExpression</cite> that represents a null, i.e., <cite>None</cite>, value of type <cite>Boolean</cite>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sttp.data.valueexpression.NULLDATETIMEVALUE">
<span class="sig-prename descclassname"><span class="pre">sttp.data.valueexpression.</span></span><span class="sig-name descname"><span class="pre">NULLDATETIMEVALUE</span></span><a class="headerlink" href="#sttp.data.valueexpression.NULLDATETIMEVALUE" title="Link to this definition"></a></dt>
<dd><p>Defines a <cite>ValueExpression</cite> that represents a null, i.e., <cite>None</cite>, value of type <cite>DateTime</cite>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sttp.data.valueexpression.NULLINT32VALUE">
<span class="sig-prename descclassname"><span class="pre">sttp.data.valueexpression.</span></span><span class="sig-name descname"><span class="pre">NULLINT32VALUE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1</span></em><a class="headerlink" href="#sttp.data.valueexpression.NULLINT32VALUE" title="Link to this definition"></a></dt>
<dd><p>Defines a <cite>ValueExpression</cite> that represents a null, i.e., <cite>None</cite>, value of type <cite>Int32</cite>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sttp.data.valueexpression.NULLSTRINGVALUE">
<span class="sig-prename descclassname"><span class="pre">sttp.data.valueexpression.</span></span><span class="sig-name descname"><span class="pre">NULLSTRINGVALUE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">5</span></em><a class="headerlink" href="#sttp.data.valueexpression.NULLSTRINGVALUE" title="Link to this definition"></a></dt>
<dd><p>Defines a <cite>ValueExpression</cite> that represents a null, i.e., <cite>None</cite>, value of type <cite>String</cite>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sttp.data.valueexpression.NULLVALUE">
<span class="sig-prename descclassname"><span class="pre">sttp.data.valueexpression.</span></span><span class="sig-name descname"><span class="pre">NULLVALUE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">8</span></em><a class="headerlink" href="#sttp.data.valueexpression.NULLVALUE" title="Link to this definition"></a></dt>
<dd><p>Defines a <cite>ValueExpression</cite> that represents a null, i.e., <cite>None</cite>, value, value type <cite>Undefined</cite>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sttp.data.valueexpression.TRUEVALUE">
<span class="sig-prename descclassname"><span class="pre">sttp.data.valueexpression.</span></span><span class="sig-name descname"><span class="pre">TRUEVALUE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#sttp.data.valueexpression.TRUEVALUE" title="Link to this definition"></a></dt>
<dd><p>Defines a <cite>ValueExpression</cite> that represents <cite>True</cite>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sttp.data.valueexpression.ValueExpression">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.data.valueexpression.</span></span><span class="sig-name descname"><span class="pre">ValueExpression</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">valuetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/data/valueexpression.html#ValueExpression"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.valueexpression.ValueExpression" title="Link to this definition"></a></dt>
<dd><p>Bases: <a class="reference internal" href="#sttp.data.expression.Expression" title="sttp.data.expression.Expression"><code class="xref py py-class docutils literal notranslate"><span class="pre">Expression</span></code></a></p>
<p>Represents a value expression.</p>
<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.valueexpression.ValueExpression.booleanvalue">
<span class="sig-name descname"><span class="pre">booleanvalue</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">bool</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/valueexpression.html#ValueExpression.booleanvalue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.valueexpression.ValueExpression.booleanvalue" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>ValueExpression</cite> as a boolean value.
An error will be returned if value type is not <cite>ExpressionValueType.BOOLEAN</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.valueexpression.ValueExpression.convert">
<span class="sig-name descname"><span class="pre">convert</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_typevalue</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression" title="sttp.data.valueexpression.ValueExpression"><span class="pre">ValueExpression</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/valueexpression.html#ValueExpression.convert"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.valueexpression.ValueExpression.convert" title="Link to this definition"></a></dt>
<dd><p>Attempts to convert the <cite>ValueExpression</cite> to the specified type.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.valueexpression.ValueExpression.datetimevalue">
<span class="sig-name descname"><span class="pre">datetimevalue</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">datetime</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/valueexpression.html#ValueExpression.datetimevalue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.valueexpression.ValueExpression.datetimevalue" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>ValueExpression</cite> as a datetime value.
An error will be returned if value type is not <cite>ExpressionValueType.DATETIME</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.valueexpression.ValueExpression.decimalvalue">
<span class="sig-name descname"><span class="pre">decimalvalue</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">Decimal</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/valueexpression.html#ValueExpression.decimalvalue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.valueexpression.ValueExpression.decimalvalue" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>ValueExpression</cite> as a Decimal value.
An error will be returned if value type is not <cite>ExpressionValueType.DECIMAL</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.valueexpression.ValueExpression.doublevalue">
<span class="sig-name descname"><span class="pre">doublevalue</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">float64</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/valueexpression.html#ValueExpression.doublevalue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.valueexpression.ValueExpression.doublevalue" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>ValueExpression</cite> as a double value.
An error will be returned if value type is not <cite>ExpressionValueType.DOUBLE</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.valueexpression.ValueExpression.expressiontype">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">expressiontype</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.constants.ExpressionType" title="sttp.data.constants.ExpressionType"><span class="pre">ExpressionType</span></a></em><a class="headerlink" href="#sttp.data.valueexpression.ValueExpression.expressiontype" title="Link to this definition"></a></dt>
<dd><p>Gets the type of this <cite>ValueExpression</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.valueexpression.ValueExpression.guidvalue">
<span class="sig-name descname"><span class="pre">guidvalue</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">UUID</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/valueexpression.html#ValueExpression.guidvalue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.valueexpression.ValueExpression.guidvalue" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>ValueExpression</cite> as a GUID value.
An error will be returned if value type is not <cite>ExpressionValueType.GUID</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.valueexpression.ValueExpression.int32value">
<span class="sig-name descname"><span class="pre">int32value</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">int32</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/valueexpression.html#ValueExpression.int32value"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.valueexpression.ValueExpression.int32value" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>ValueExpression</cite> as a 32-bit integer value.
An error will be returned if value type is not <cite>ExpressionValueType.INT32</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.valueexpression.ValueExpression.int64value">
<span class="sig-name descname"><span class="pre">int64value</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">int64</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/valueexpression.html#ValueExpression.int64value"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.valueexpression.ValueExpression.int64value" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>ValueExpression</cite> as a 64-bit integer value.
An error will be returned if value type is not <cite>ExpressionValueType.INT64</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.valueexpression.ValueExpression.integervalue">
<span class="sig-name descname"><span class="pre">integervalue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">defaultvalue</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/sttp/data/valueexpression.html#ValueExpression.integervalue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.valueexpression.ValueExpression.integervalue" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>ValueExpression</cite> as an integer value or specified default value if not possible.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.valueexpression.ValueExpression.is_null">
<span class="sig-name descname"><span class="pre">is_null</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">bool</span></span></span><a class="reference internal" href="_modules/sttp/data/valueexpression.html#ValueExpression.is_null"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.valueexpression.ValueExpression.is_null" title="Link to this definition"></a></dt>
<dd><p>Gets a flag that determines if this <cite>ValueExpression</cite> is null, i.e., <cite>None</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.valueexpression.ValueExpression.nullvalue">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">nullvalue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target_valuetype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="#sttp.data.valueexpression.ValueExpression" title="sttp.data.valueexpression.ValueExpression"><span class="pre">ValueExpression</span></a></span></span><a class="reference internal" href="_modules/sttp/data/valueexpression.html#ValueExpression.nullvalue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.valueexpression.ValueExpression.nullvalue" title="Link to this definition"></a></dt>
<dd><p>Gets a <cite>ValueExpression</cite> that represents a null, i.e., <cite>None</cite>, value of the specified <cite>ExpressionValueType</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.data.valueexpression.ValueExpression.stringvalue">
<span class="sig-name descname"><span class="pre">stringvalue</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/data/valueexpression.html#ValueExpression.stringvalue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.data.valueexpression.ValueExpression.stringvalue" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>ValueExpression</cite> as a string value.
An error will be returned if value type is not <cite>ExpressionValueType.STRING</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.valueexpression.ValueExpression.value">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">value</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">object</span></em><a class="headerlink" href="#sttp.data.valueexpression.ValueExpression.value" title="Link to this definition"></a></dt>
<dd><p>Gets the value of this <cite>ValueExpression</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.data.valueexpression.ValueExpression.valuetype">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">valuetype</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.data.constants.ExpressionValueType" title="sttp.data.constants.ExpressionValueType"><span class="pre">ExpressionValueType</span></a></em><a class="headerlink" href="#sttp.data.valueexpression.ValueExpression.valuetype" title="Link to this definition"></a></dt>
<dd><p>Gets the value type of this <cite>ValueExpression</cite>.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.data">
<span id="module-contents"></span><h2>Module contents<a class="headerlink" href="#module-sttp.data" title="Link to this heading"></a></h2>
</section>
</section>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
  
 
<hr />
<p>
    <a href="https://github.com/sttp/pyapi">GitHub Home</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://pypi.org/project/sttpapi/" target="_blank">PyPi Package: sttpapi</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://sttp.github.io/documentation/" target="_blank">General STTP Documentation</a>
</p>
<br /><br />
Copyright © 2022, <a href="https://gridprotectionalliance.org/" target="_blank">Grid&nbsp;Protection&nbsp;Alliance</a>
<a href="https://github.com/sttp/pyapi">
    <img align="right" style="margin-top: -50px"
        src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/LockPython_64High.png">
</a>


</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>