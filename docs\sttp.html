

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>sttp package &mdash; sttp/pyapi 0.6.4 documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=92fd9be5" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=8209df7b" />

  
    <link rel="shortcut icon" href="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/favicon.ico"/>
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=bbec6902"></script>
      <script src="_static/documentation_options.js?v=b1f64a84"></script>
      <script src="_static/doctools.js?v=92e14aea"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            sttp/pyapi
              <img src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/sttp.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <!-- Local TOC -->
              <div class="local-toc"><ul>
<li><a class="reference internal" href="#">sttp package</a><ul>
<li><a class="reference internal" href="#subpackages">Subpackages</a></li>
<li><a class="reference internal" href="#submodules">Submodules</a></li>
<li><a class="reference internal" href="#module-sttp.config">sttp.config module</a><ul>
<li><a class="reference internal" href="#sttp.config.Config"><code class="docutils literal notranslate"><span class="pre">Config</span></code></a><ul>
<li><a class="reference internal" href="#sttp.config.Config.DEFAULT_AUTORECONNECT"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_AUTORECONNECT</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.DEFAULT_AUTOREQUESTMETADATA"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_AUTOREQUESTMETADATA</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.DEFAULT_AUTOSUBSCRIBE"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_AUTOSUBSCRIBE</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.DEFAULT_COMPRESS_METADATA"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_COMPRESS_METADATA</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.DEFAULT_COMPRESS_PAYLOADDATA"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_COMPRESS_PAYLOADDATA</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.DEFAULT_COMPRESS_SIGNALINDEXCACHE"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_COMPRESS_SIGNALINDEXCACHE</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.DEFAULT_MAXRETRIES"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_MAXRETRIES</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.DEFAULT_MAXRETRYINTERVAL"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_MAXRETRYINTERVAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.DEFAULT_METADATAFILTERS"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_METADATAFILTERS</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.DEFAULT_RETRYINTERVAL"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_RETRYINTERVAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.DEFAULT_SOCKET_TIMEOUT"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_SOCKET_TIMEOUT</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.DEFAULT_VERSION"><code class="docutils literal notranslate"><span class="pre">Config.DEFAULT_VERSION</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.autoreconnect"><code class="docutils literal notranslate"><span class="pre">Config.autoreconnect</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.autorequestmetadata"><code class="docutils literal notranslate"><span class="pre">Config.autorequestmetadata</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.autosubscribe"><code class="docutils literal notranslate"><span class="pre">Config.autosubscribe</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.compress_metadata"><code class="docutils literal notranslate"><span class="pre">Config.compress_metadata</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.compress_payloaddata"><code class="docutils literal notranslate"><span class="pre">Config.compress_payloaddata</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.compress_signalindexcache"><code class="docutils literal notranslate"><span class="pre">Config.compress_signalindexcache</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.maxretries"><code class="docutils literal notranslate"><span class="pre">Config.maxretries</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.maxretryinterval"><code class="docutils literal notranslate"><span class="pre">Config.maxretryinterval</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.metadatafilters"><code class="docutils literal notranslate"><span class="pre">Config.metadatafilters</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.retryinterval"><code class="docutils literal notranslate"><span class="pre">Config.retryinterval</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.socket_timeout"><code class="docutils literal notranslate"><span class="pre">Config.socket_timeout</span></code></a></li>
<li><a class="reference internal" href="#sttp.config.Config.version"><code class="docutils literal notranslate"><span class="pre">Config.version</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.reader">sttp.reader module</a><ul>
<li><a class="reference internal" href="#sttp.reader.MeasurementReader"><code class="docutils literal notranslate"><span class="pre">MeasurementReader</span></code></a><ul>
<li><a class="reference internal" href="#sttp.reader.MeasurementReader.dispose"><code class="docutils literal notranslate"><span class="pre">MeasurementReader.dispose()</span></code></a></li>
<li><a class="reference internal" href="#sttp.reader.MeasurementReader.next_measurement"><code class="docutils literal notranslate"><span class="pre">MeasurementReader.next_measurement()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.settings">sttp.settings module</a><ul>
<li><a class="reference internal" href="#sttp.settings.Settings"><code class="docutils literal notranslate"><span class="pre">Settings</span></code></a><ul>
<li><a class="reference internal" href="#sttp.settings.Settings.DEFAULT_CONSTRAINTPARAMETERS"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_CONSTRAINTPARAMETERS</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.DEFAULT_ENABLE_TIME_REASONABILITY_CHECK"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_ENABLE_TIME_REASONABILITY_CHECK</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.DEFAULT_EXTRA_CONNECTIONSTRING_PARAMETERS"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_EXTRA_CONNECTIONSTRING_PARAMETERS</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.DEFAULT_INCLUDETIME"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_INCLUDETIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.DEFAULT_LAGTIME"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_LAGTIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.DEFAULT_LEADTIME"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_LEADTIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.DEFAULT_PROCESSINGINTERVAL"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_PROCESSINGINTERVAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.DEFAULT_PUBLISHINTERVAL"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_PUBLISHINTERVAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.DEFAULT_REQUEST_NANVALUEFILTER"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_REQUEST_NANVALUEFILTER</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.DEFAULT_STARTTIME"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_STARTTIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.DEFAULT_STOPTIME"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_STOPTIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.DEFAULT_THROTTLED"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_THROTTLED</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.DEFAULT_UDPINTERFACE"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_UDPINTERFACE</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.DEFAULT_UDPPORT"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_UDPPORT</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.DEFAULT_USE_LOCALCLOCK_AS_REALTIME"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_USE_LOCALCLOCK_AS_REALTIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.DEFAULT_USE_MILLISECONDRESOLUTION"><code class="docutils literal notranslate"><span class="pre">Settings.DEFAULT_USE_MILLISECONDRESOLUTION</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.constraintparameters"><code class="docutils literal notranslate"><span class="pre">Settings.constraintparameters</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.enabletimereasonabilitycheck"><code class="docutils literal notranslate"><span class="pre">Settings.enabletimereasonabilitycheck</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.extra_connectionstring_parameters"><code class="docutils literal notranslate"><span class="pre">Settings.extra_connectionstring_parameters</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.includetime"><code class="docutils literal notranslate"><span class="pre">Settings.includetime</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.lagtime"><code class="docutils literal notranslate"><span class="pre">Settings.lagtime</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.leadtime"><code class="docutils literal notranslate"><span class="pre">Settings.leadtime</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.processinginterval"><code class="docutils literal notranslate"><span class="pre">Settings.processinginterval</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.publishinterval"><code class="docutils literal notranslate"><span class="pre">Settings.publishinterval</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.request_nanvaluefilter"><code class="docutils literal notranslate"><span class="pre">Settings.request_nanvaluefilter</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.starttime"><code class="docutils literal notranslate"><span class="pre">Settings.starttime</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.stoptime"><code class="docutils literal notranslate"><span class="pre">Settings.stoptime</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.throttled"><code class="docutils literal notranslate"><span class="pre">Settings.throttled</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.udpinterface"><code class="docutils literal notranslate"><span class="pre">Settings.udpinterface</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.udpport"><code class="docutils literal notranslate"><span class="pre">Settings.udpport</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.use_millisecondresolution"><code class="docutils literal notranslate"><span class="pre">Settings.use_millisecondresolution</span></code></a></li>
<li><a class="reference internal" href="#sttp.settings.Settings.uselocalclockasrealtime"><code class="docutils literal notranslate"><span class="pre">Settings.uselocalclockasrealtime</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.subscriber">sttp.subscriber module</a><ul>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber"><code class="docutils literal notranslate"><span class="pre">Subscriber</span></code></a><ul>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.activesignalindexcache"><code class="docutils literal notranslate"><span class="pre">Subscriber.activesignalindexcache</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.adjustedvalue"><code class="docutils literal notranslate"><span class="pre">Subscriber.adjustedvalue()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.connect"><code class="docutils literal notranslate"><span class="pre">Subscriber.connect()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.connected"><code class="docutils literal notranslate"><span class="pre">Subscriber.connected</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.default_connectionestablished_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.default_connectionestablished_receiver()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.default_connectionterminated_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.default_connectionterminated_receiver()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.default_errormessage_logger"><code class="docutils literal notranslate"><span class="pre">Subscriber.default_errormessage_logger()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.default_statusmessage_logger"><code class="docutils literal notranslate"><span class="pre">Subscriber.default_statusmessage_logger()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.disconnect"><code class="docutils literal notranslate"><span class="pre">Subscriber.disconnect()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.dispose"><code class="docutils literal notranslate"><span class="pre">Subscriber.dispose()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.errormessage"><code class="docutils literal notranslate"><span class="pre">Subscriber.errormessage()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.measurement_metadata"><code class="docutils literal notranslate"><span class="pre">Subscriber.measurement_metadata()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.metadatacache"><code class="docutils literal notranslate"><span class="pre">Subscriber.metadatacache</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.read_measurements"><code class="docutils literal notranslate"><span class="pre">Subscriber.read_measurements()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.request_metadata"><code class="docutils literal notranslate"><span class="pre">Subscriber.request_metadata()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.set_configurationchanged_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_configurationchanged_receiver()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.set_connectionestablished_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_connectionestablished_receiver()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.set_connectionterminated_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_connectionterminated_receiver()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.set_data_starttime_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_data_starttime_receiver()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.set_errormessage_logger"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_errormessage_logger()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.set_historicalreadcomplete_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_historicalreadcomplete_receiver()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.set_metadatanotification_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_metadatanotification_receiver()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.set_newbufferblock_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_newbufferblock_receiver()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.set_newmeasurements_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_newmeasurements_receiver()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.set_notification_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_notification_receiver()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.set_statusmessage_logger"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_statusmessage_logger()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.set_subscriptionupdated_receiver"><code class="docutils literal notranslate"><span class="pre">Subscriber.set_subscriptionupdated_receiver()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.statusmessage"><code class="docutils literal notranslate"><span class="pre">Subscriber.statusmessage()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.subscribe"><code class="docutils literal notranslate"><span class="pre">Subscriber.subscribe()</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.subscribed"><code class="docutils literal notranslate"><span class="pre">Subscriber.subscribed</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.subscriberid"><code class="docutils literal notranslate"><span class="pre">Subscriber.subscriberid</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.total_commandchannel_bytesreceived"><code class="docutils literal notranslate"><span class="pre">Subscriber.total_commandchannel_bytesreceived</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.total_datachannel_bytesreceived"><code class="docutils literal notranslate"><span class="pre">Subscriber.total_datachannel_bytesreceived</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.total_measurementsreceived"><code class="docutils literal notranslate"><span class="pre">Subscriber.total_measurementsreceived</span></code></a></li>
<li><a class="reference internal" href="#sttp.subscriber.Subscriber.unsubscribe"><code class="docutils literal notranslate"><span class="pre">Subscriber.unsubscribe()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.ticks">sttp.ticks module</a><ul>
<li><a class="reference internal" href="#sttp.ticks.Ticks"><code class="docutils literal notranslate"><span class="pre">Ticks</span></code></a><ul>
<li><a class="reference internal" href="#sttp.ticks.Ticks.LEAPSECOND_DIRECTION"><code class="docutils literal notranslate"><span class="pre">Ticks.LEAPSECOND_DIRECTION</span></code></a></li>
<li><a class="reference internal" href="#sttp.ticks.Ticks.LEAPSECOND_FLAG"><code class="docutils literal notranslate"><span class="pre">Ticks.LEAPSECOND_FLAG</span></code></a></li>
<li><a class="reference internal" href="#sttp.ticks.Ticks.PERDAY"><code class="docutils literal notranslate"><span class="pre">Ticks.PERDAY</span></code></a></li>
<li><a class="reference internal" href="#sttp.ticks.Ticks.PERHOUR"><code class="docutils literal notranslate"><span class="pre">Ticks.PERHOUR</span></code></a></li>
<li><a class="reference internal" href="#sttp.ticks.Ticks.PERMICROSECOND"><code class="docutils literal notranslate"><span class="pre">Ticks.PERMICROSECOND</span></code></a></li>
<li><a class="reference internal" href="#sttp.ticks.Ticks.PERMILLISECOND"><code class="docutils literal notranslate"><span class="pre">Ticks.PERMILLISECOND</span></code></a></li>
<li><a class="reference internal" href="#sttp.ticks.Ticks.PERMINUTE"><code class="docutils literal notranslate"><span class="pre">Ticks.PERMINUTE</span></code></a></li>
<li><a class="reference internal" href="#sttp.ticks.Ticks.PERSECOND"><code class="docutils literal notranslate"><span class="pre">Ticks.PERSECOND</span></code></a></li>
<li><a class="reference internal" href="#sttp.ticks.Ticks.UNIXBASEOFFSET"><code class="docutils literal notranslate"><span class="pre">Ticks.UNIXBASEOFFSET</span></code></a></li>
<li><a class="reference internal" href="#sttp.ticks.Ticks.VALUEMASK"><code class="docutils literal notranslate"><span class="pre">Ticks.VALUEMASK</span></code></a></li>
<li><a class="reference internal" href="#sttp.ticks.Ticks.from_datetime"><code class="docutils literal notranslate"><span class="pre">Ticks.from_datetime()</span></code></a></li>
<li><a class="reference internal" href="#sttp.ticks.Ticks.from_timedelta"><code class="docutils literal notranslate"><span class="pre">Ticks.from_timedelta()</span></code></a></li>
<li><a class="reference internal" href="#sttp.ticks.Ticks.is_leapsecond"><code class="docutils literal notranslate"><span class="pre">Ticks.is_leapsecond()</span></code></a></li>
<li><a class="reference internal" href="#sttp.ticks.Ticks.is_negative_leapsecond"><code class="docutils literal notranslate"><span class="pre">Ticks.is_negative_leapsecond()</span></code></a></li>
<li><a class="reference internal" href="#sttp.ticks.Ticks.now"><code class="docutils literal notranslate"><span class="pre">Ticks.now()</span></code></a></li>
<li><a class="reference internal" href="#sttp.ticks.Ticks.set_leapsecond"><code class="docutils literal notranslate"><span class="pre">Ticks.set_leapsecond()</span></code></a></li>
<li><a class="reference internal" href="#sttp.ticks.Ticks.set_negative_leapsecond"><code class="docutils literal notranslate"><span class="pre">Ticks.set_negative_leapsecond()</span></code></a></li>
<li><a class="reference internal" href="#sttp.ticks.Ticks.timestampvalue"><code class="docutils literal notranslate"><span class="pre">Ticks.timestampvalue()</span></code></a></li>
<li><a class="reference internal" href="#sttp.ticks.Ticks.to_datetime"><code class="docutils literal notranslate"><span class="pre">Ticks.to_datetime()</span></code></a></li>
<li><a class="reference internal" href="#sttp.ticks.Ticks.to_shortstring"><code class="docutils literal notranslate"><span class="pre">Ticks.to_shortstring()</span></code></a></li>
<li><a class="reference internal" href="#sttp.ticks.Ticks.to_string"><code class="docutils literal notranslate"><span class="pre">Ticks.to_string()</span></code></a></li>
<li><a class="reference internal" href="#sttp.ticks.Ticks.utcnow"><code class="docutils literal notranslate"><span class="pre">Ticks.utcnow()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.version">sttp.version module</a><ul>
<li><a class="reference internal" href="#sttp.version.Version"><code class="docutils literal notranslate"><span class="pre">Version</span></code></a><ul>
<li><a class="reference internal" href="#sttp.version.Version.STTP_SOURCE"><code class="docutils literal notranslate"><span class="pre">Version.STTP_SOURCE</span></code></a></li>
<li><a class="reference internal" href="#sttp.version.Version.STTP_UPDATEDON"><code class="docutils literal notranslate"><span class="pre">Version.STTP_UPDATEDON</span></code></a></li>
<li><a class="reference internal" href="#sttp.version.Version.STTP_VERSION"><code class="docutils literal notranslate"><span class="pre">Version.STTP_VERSION</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp">Module contents</a></li>
</ul>
</li>
</ul>
</div>
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">sttp/pyapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">sttp package</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/sttp.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="sttp-package">
<h1>sttp package<a class="headerlink" href="#sttp-package" title="Link to this heading"></a></h1>
<section id="subpackages">
<h2>Subpackages<a class="headerlink" href="#subpackages" title="Link to this heading"></a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="sttp.data.html">sttp.data package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="sttp.data.html#submodules">Submodules</a></li>
<li class="toctree-l2"><a class="reference internal" href="sttp.data.html#module-sttp.data.callbackerrorlistener">sttp.data.callbackerrorlistener module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.callbackerrorlistener.CallbackErrorListener"><code class="docutils literal notranslate"><span class="pre">CallbackErrorListener</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.callbackerrorlistener.CallbackErrorListener.parsingexception_callback"><code class="docutils literal notranslate"><span class="pre">CallbackErrorListener.parsingexception_callback</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.callbackerrorlistener.CallbackErrorListener.syntaxError"><code class="docutils literal notranslate"><span class="pre">CallbackErrorListener.syntaxError()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.data.html#module-sttp.data.columnexpression">sttp.data.columnexpression module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.columnexpression.ColumnExpression"><code class="docutils literal notranslate"><span class="pre">ColumnExpression</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.columnexpression.ColumnExpression.datacolumn"><code class="docutils literal notranslate"><span class="pre">ColumnExpression.datacolumn</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.columnexpression.ColumnExpression.expressiontype"><code class="docutils literal notranslate"><span class="pre">ColumnExpression.expressiontype</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.data.html#module-sttp.data.constants">sttp.data.constants module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.EXPRESSIONVALUETYPELEN"><code class="docutils literal notranslate"><span class="pre">EXPRESSIONVALUETYPELEN</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.ABS"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.ABS</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.CEILING"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.CEILING</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.COALESCE"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.COALESCE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.CONTAINS"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.CONTAINS</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.CONVERT"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.CONVERT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.DATEADD"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.DATEADD</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.DATEDIFF"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.DATEDIFF</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.DATEPART"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.DATEPART</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.ENDSWITH"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.ENDSWITH</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.FLOOR"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.FLOOR</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.IIF"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.IIF</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.INDEXOF"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.INDEXOF</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.ISDATE"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.ISDATE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.ISGUID"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.ISGUID</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.ISINTEGER"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.ISINTEGER</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.ISNULL"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.ISNULL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.ISNUMERIC"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.ISNUMERIC</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.LASTINDEXOF"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.LASTINDEXOF</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.LEN"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.LEN</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.LOWER"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.LOWER</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.MAXOF"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.MAXOF</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.MINOF"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.MINOF</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.NOW"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.NOW</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.NTHINDEXOF"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.NTHINDEXOF</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.POWER"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.POWER</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.REGEXMATCH"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.REGEXMATCH</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.REGEXVAL"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.REGEXVAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.REPLACE"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.REPLACE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.REVERSE"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.REVERSE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.ROUND"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.ROUND</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.SPLIT"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.SPLIT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.SQRT"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.SQRT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.STARTSWITH"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.STARTSWITH</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.STRCMP"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.STRCMP</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.STRCOUNT"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.STRCOUNT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.SUBSTR"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.SUBSTR</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.TRIM"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.TRIM</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.TRIMLEFT"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.TRIMLEFT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.TRIMRIGHT"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.TRIMRIGHT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.UPPER"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.UPPER</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionFunctionType.UTCNOW"><code class="docutils literal notranslate"><span class="pre">ExpressionFunctionType.UTCNOW</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.ADD"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.ADD</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.AND"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.AND</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.BITSHIFTLEFT"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.BITSHIFTLEFT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.BITSHIFTRIGHT"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.BITSHIFTRIGHT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.BITWISEAND"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.BITWISEAND</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.BITWISEOR"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.BITWISEOR</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.BITWISEXOR"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.BITWISEXOR</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.DIVIDE"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.DIVIDE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.EQUAL"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.EQUAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.EQUALEXACTMATCH"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.EQUALEXACTMATCH</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.GREATERTHAN"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.GREATERTHAN</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.GREATERTHANOREQUAL"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.GREATERTHANOREQUAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.ISNOTNULL"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.ISNOTNULL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.ISNULL"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.ISNULL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.LESSTHAN"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.LESSTHAN</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.LESSTHANOREQUAL"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.LESSTHANOREQUAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.LIKE"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.LIKE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.LIKEEXACTMATCH"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.LIKEEXACTMATCH</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.MODULUS"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.MODULUS</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.MULTIPLY"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.MULTIPLY</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.NOTEQUAL"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.NOTEQUAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.NOTEQUALEXACTMATCH"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.NOTEQUALEXACTMATCH</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.NOTLIKE"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.NOTLIKE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.NOTLIKEEXACTMATCH"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.NOTLIKEEXACTMATCH</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.OR"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.OR</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionOperatorType.SUBTRACT"><code class="docutils literal notranslate"><span class="pre">ExpressionOperatorType.SUBTRACT</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionType"><code class="docutils literal notranslate"><span class="pre">ExpressionType</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionType.COLUMN"><code class="docutils literal notranslate"><span class="pre">ExpressionType.COLUMN</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionType.FUNCTION"><code class="docutils literal notranslate"><span class="pre">ExpressionType.FUNCTION</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionType.INLIST"><code class="docutils literal notranslate"><span class="pre">ExpressionType.INLIST</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionType.OPERATOR"><code class="docutils literal notranslate"><span class="pre">ExpressionType.OPERATOR</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionType.UNARY"><code class="docutils literal notranslate"><span class="pre">ExpressionType.UNARY</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionType.VALUE"><code class="docutils literal notranslate"><span class="pre">ExpressionType.VALUE</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionUnaryType"><code class="docutils literal notranslate"><span class="pre">ExpressionUnaryType</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionUnaryType.MINUS"><code class="docutils literal notranslate"><span class="pre">ExpressionUnaryType.MINUS</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionUnaryType.NOT"><code class="docutils literal notranslate"><span class="pre">ExpressionUnaryType.NOT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionUnaryType.PLUS"><code class="docutils literal notranslate"><span class="pre">ExpressionUnaryType.PLUS</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionValueType"><code class="docutils literal notranslate"><span class="pre">ExpressionValueType</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionValueType.BOOLEAN"><code class="docutils literal notranslate"><span class="pre">ExpressionValueType.BOOLEAN</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionValueType.DATETIME"><code class="docutils literal notranslate"><span class="pre">ExpressionValueType.DATETIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionValueType.DECIMAL"><code class="docutils literal notranslate"><span class="pre">ExpressionValueType.DECIMAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionValueType.DOUBLE"><code class="docutils literal notranslate"><span class="pre">ExpressionValueType.DOUBLE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionValueType.GUID"><code class="docutils literal notranslate"><span class="pre">ExpressionValueType.GUID</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionValueType.INT32"><code class="docutils literal notranslate"><span class="pre">ExpressionValueType.INT32</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionValueType.INT64"><code class="docutils literal notranslate"><span class="pre">ExpressionValueType.INT64</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionValueType.STRING"><code class="docutils literal notranslate"><span class="pre">ExpressionValueType.STRING</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.ExpressionValueType.UNDEFINED"><code class="docutils literal notranslate"><span class="pre">ExpressionValueType.UNDEFINED</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.TimeInterval"><code class="docutils literal notranslate"><span class="pre">TimeInterval</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.TimeInterval.DAY"><code class="docutils literal notranslate"><span class="pre">TimeInterval.DAY</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.TimeInterval.DAYOFYEAR"><code class="docutils literal notranslate"><span class="pre">TimeInterval.DAYOFYEAR</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.TimeInterval.HOUR"><code class="docutils literal notranslate"><span class="pre">TimeInterval.HOUR</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.TimeInterval.MILLISECOND"><code class="docutils literal notranslate"><span class="pre">TimeInterval.MILLISECOND</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.TimeInterval.MINUTE"><code class="docutils literal notranslate"><span class="pre">TimeInterval.MINUTE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.TimeInterval.MONTH"><code class="docutils literal notranslate"><span class="pre">TimeInterval.MONTH</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.TimeInterval.SECOND"><code class="docutils literal notranslate"><span class="pre">TimeInterval.SECOND</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.TimeInterval.WEEK"><code class="docutils literal notranslate"><span class="pre">TimeInterval.WEEK</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.TimeInterval.WEEKDAY"><code class="docutils literal notranslate"><span class="pre">TimeInterval.WEEKDAY</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.TimeInterval.YEAR"><code class="docutils literal notranslate"><span class="pre">TimeInterval.YEAR</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.constants.TimeInterval.parse"><code class="docutils literal notranslate"><span class="pre">TimeInterval.parse()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.derive_arithmetic_operationvaluetype"><code class="docutils literal notranslate"><span class="pre">derive_arithmetic_operationvaluetype()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.derive_arithmetic_operationvaluetype_fromboolean"><code class="docutils literal notranslate"><span class="pre">derive_arithmetic_operationvaluetype_fromboolean()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.derive_arithmetic_operationvaluetype_fromdecimal"><code class="docutils literal notranslate"><span class="pre">derive_arithmetic_operationvaluetype_fromdecimal()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.derive_arithmetic_operationvaluetype_fromdouble"><code class="docutils literal notranslate"><span class="pre">derive_arithmetic_operationvaluetype_fromdouble()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.derive_arithmetic_operationvaluetype_fromint32"><code class="docutils literal notranslate"><span class="pre">derive_arithmetic_operationvaluetype_fromint32()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.derive_arithmetic_operationvaluetype_fromint64"><code class="docutils literal notranslate"><span class="pre">derive_arithmetic_operationvaluetype_fromint64()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.derive_boolean_operationvaluetype"><code class="docutils literal notranslate"><span class="pre">derive_boolean_operationvaluetype()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.derive_comparison_operationvaluetype"><code class="docutils literal notranslate"><span class="pre">derive_comparison_operationvaluetype()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.derive_comparison_operationvaluetype_fromboolean"><code class="docutils literal notranslate"><span class="pre">derive_comparison_operationvaluetype_fromboolean()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.derive_comparison_operationvaluetype_fromdatetime"><code class="docutils literal notranslate"><span class="pre">derive_comparison_operationvaluetype_fromdatetime()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.derive_comparison_operationvaluetype_fromdecimal"><code class="docutils literal notranslate"><span class="pre">derive_comparison_operationvaluetype_fromdecimal()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.derive_comparison_operationvaluetype_fromdouble"><code class="docutils literal notranslate"><span class="pre">derive_comparison_operationvaluetype_fromdouble()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.derive_comparison_operationvaluetype_fromguid"><code class="docutils literal notranslate"><span class="pre">derive_comparison_operationvaluetype_fromguid()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.derive_comparison_operationvaluetype_fromint32"><code class="docutils literal notranslate"><span class="pre">derive_comparison_operationvaluetype_fromint32()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.derive_comparison_operationvaluetype_fromint64"><code class="docutils literal notranslate"><span class="pre">derive_comparison_operationvaluetype_fromint64()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.derive_integer_operationvaluetype"><code class="docutils literal notranslate"><span class="pre">derive_integer_operationvaluetype()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.derive_integer_operationvaluetype_fromboolean"><code class="docutils literal notranslate"><span class="pre">derive_integer_operationvaluetype_fromboolean()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.derive_integer_operationvaluetype_fromint32"><code class="docutils literal notranslate"><span class="pre">derive_integer_operationvaluetype_fromint32()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.derive_integer_operationvaluetype_fromint64"><code class="docutils literal notranslate"><span class="pre">derive_integer_operationvaluetype_fromint64()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.derive_operationvaluetype"><code class="docutils literal notranslate"><span class="pre">derive_operationvaluetype()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.is_integertype"><code class="docutils literal notranslate"><span class="pre">is_integertype()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.constants.is_numerictype"><code class="docutils literal notranslate"><span class="pre">is_numerictype()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.data.html#module-sttp.data.datacolumn">sttp.data.datacolumn module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.datacolumn.DataColumn"><code class="docutils literal notranslate"><span class="pre">DataColumn</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datacolumn.DataColumn.computed"><code class="docutils literal notranslate"><span class="pre">DataColumn.computed</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datacolumn.DataColumn.datatype"><code class="docutils literal notranslate"><span class="pre">DataColumn.datatype</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datacolumn.DataColumn.expression"><code class="docutils literal notranslate"><span class="pre">DataColumn.expression</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datacolumn.DataColumn.index"><code class="docutils literal notranslate"><span class="pre">DataColumn.index</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datacolumn.DataColumn.name"><code class="docutils literal notranslate"><span class="pre">DataColumn.name</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datacolumn.DataColumn.parent"><code class="docutils literal notranslate"><span class="pre">DataColumn.parent</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.data.html#module-sttp.data.datarow">sttp.data.datarow module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow"><code class="docutils literal notranslate"><span class="pre">DataRow</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.booleanvalue"><code class="docutils literal notranslate"><span class="pre">DataRow.booleanvalue()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.booleanvalue_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.booleanvalue_byname()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.columnvalue_as_string"><code class="docutils literal notranslate"><span class="pre">DataRow.columnvalue_as_string()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.compare_datarowcolumns"><code class="docutils literal notranslate"><span class="pre">DataRow.compare_datarowcolumns()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.datetimevalue"><code class="docutils literal notranslate"><span class="pre">DataRow.datetimevalue()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.datetimevalue_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.datetimevalue_byname()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.decimalvalue"><code class="docutils literal notranslate"><span class="pre">DataRow.decimalvalue()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.decimalvalue_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.decimalvalue_byname()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.doublevalue"><code class="docutils literal notranslate"><span class="pre">DataRow.doublevalue()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.doublevalue_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.doublevalue_byname()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.guidvalue"><code class="docutils literal notranslate"><span class="pre">DataRow.guidvalue()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.guidvalue_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.guidvalue_byname()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.int16value"><code class="docutils literal notranslate"><span class="pre">DataRow.int16value()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.int16value_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.int16value_byname()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.int32value"><code class="docutils literal notranslate"><span class="pre">DataRow.int32value()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.int32value_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.int32value_byname()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.int64value"><code class="docutils literal notranslate"><span class="pre">DataRow.int64value()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.int64value_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.int64value_byname()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.int8value"><code class="docutils literal notranslate"><span class="pre">DataRow.int8value()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.int8value_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.int8value_byname()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.parent"><code class="docutils literal notranslate"><span class="pre">DataRow.parent</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.set_value"><code class="docutils literal notranslate"><span class="pre">DataRow.set_value()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.set_value_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.set_value_byname()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.singlevalue"><code class="docutils literal notranslate"><span class="pre">DataRow.singlevalue()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.singlevalue_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.singlevalue_byname()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.stringvalue"><code class="docutils literal notranslate"><span class="pre">DataRow.stringvalue()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.stringvalue_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.stringvalue_byname()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.uint16value"><code class="docutils literal notranslate"><span class="pre">DataRow.uint16value()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.uint16value_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.uint16value_byname()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.uint32value"><code class="docutils literal notranslate"><span class="pre">DataRow.uint32value()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.uint32value_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.uint32value_byname()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.uint64value"><code class="docutils literal notranslate"><span class="pre">DataRow.uint64value()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.uint64value_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.uint64value_byname()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.uint8value"><code class="docutils literal notranslate"><span class="pre">DataRow.uint8value()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.uint8value_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.uint8value_byname()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.value"><code class="docutils literal notranslate"><span class="pre">DataRow.value()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.value_as_string"><code class="docutils literal notranslate"><span class="pre">DataRow.value_as_string()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.value_as_string_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.value_as_string_byname()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datarow.DataRow.value_byname"><code class="docutils literal notranslate"><span class="pre">DataRow.value_byname()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.data.html#module-sttp.data.dataset">sttp.data.dataset module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.dataset.DataSet"><code class="docutils literal notranslate"><span class="pre">DataSet</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.dataset.DataSet.DEFAULT_NAME"><code class="docutils literal notranslate"><span class="pre">DataSet.DEFAULT_NAME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.dataset.DataSet.add_table"><code class="docutils literal notranslate"><span class="pre">DataSet.add_table()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.dataset.DataSet.clear_tables"><code class="docutils literal notranslate"><span class="pre">DataSet.clear_tables()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.dataset.DataSet.create_table"><code class="docutils literal notranslate"><span class="pre">DataSet.create_table()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.dataset.DataSet.from_xml"><code class="docutils literal notranslate"><span class="pre">DataSet.from_xml()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.dataset.DataSet.name"><code class="docutils literal notranslate"><span class="pre">DataSet.name</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.dataset.DataSet.parse_xml"><code class="docutils literal notranslate"><span class="pre">DataSet.parse_xml()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.dataset.DataSet.parse_xmldoc"><code class="docutils literal notranslate"><span class="pre">DataSet.parse_xmldoc()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.dataset.DataSet.remove_table"><code class="docutils literal notranslate"><span class="pre">DataSet.remove_table()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.dataset.DataSet.table"><code class="docutils literal notranslate"><span class="pre">DataSet.table()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.dataset.DataSet.tablecount"><code class="docutils literal notranslate"><span class="pre">DataSet.tablecount</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.dataset.DataSet.tablenames"><code class="docutils literal notranslate"><span class="pre">DataSet.tablenames()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.dataset.DataSet.tables"><code class="docutils literal notranslate"><span class="pre">DataSet.tables()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.dataset.EXT_XMLSCHEMADATA_NAMESPACE"><code class="docutils literal notranslate"><span class="pre">EXT_XMLSCHEMADATA_NAMESPACE</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.dataset.XMLSCHEMA_NAMESPACE"><code class="docutils literal notranslate"><span class="pre">XMLSCHEMA_NAMESPACE</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.dataset.xsdformat"><code class="docutils literal notranslate"><span class="pre">xsdformat()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.data.html#module-sttp.data.datatable">sttp.data.datatable module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.datatable.DataTable"><code class="docutils literal notranslate"><span class="pre">DataTable</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatable.DataTable.add_column"><code class="docutils literal notranslate"><span class="pre">DataTable.add_column()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatable.DataTable.add_row"><code class="docutils literal notranslate"><span class="pre">DataTable.add_row()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatable.DataTable.clear_columns"><code class="docutils literal notranslate"><span class="pre">DataTable.clear_columns()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatable.DataTable.clear_rows"><code class="docutils literal notranslate"><span class="pre">DataTable.clear_rows()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatable.DataTable.clone_column"><code class="docutils literal notranslate"><span class="pre">DataTable.clone_column()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatable.DataTable.clone_row"><code class="docutils literal notranslate"><span class="pre">DataTable.clone_row()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatable.DataTable.column"><code class="docutils literal notranslate"><span class="pre">DataTable.column()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatable.DataTable.column_byname"><code class="docutils literal notranslate"><span class="pre">DataTable.column_byname()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatable.DataTable.columncount"><code class="docutils literal notranslate"><span class="pre">DataTable.columncount</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatable.DataTable.columnindex"><code class="docutils literal notranslate"><span class="pre">DataTable.columnindex()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatable.DataTable.create_column"><code class="docutils literal notranslate"><span class="pre">DataTable.create_column()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatable.DataTable.create_row"><code class="docutils literal notranslate"><span class="pre">DataTable.create_row()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatable.DataTable.name"><code class="docutils literal notranslate"><span class="pre">DataTable.name</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatable.DataTable.parent"><code class="docutils literal notranslate"><span class="pre">DataTable.parent</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatable.DataTable.row"><code class="docutils literal notranslate"><span class="pre">DataTable.row()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatable.DataTable.rowcount"><code class="docutils literal notranslate"><span class="pre">DataTable.rowcount</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatable.DataTable.rowswhere"><code class="docutils literal notranslate"><span class="pre">DataTable.rowswhere()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatable.DataTable.rowvalue_as_string"><code class="docutils literal notranslate"><span class="pre">DataTable.rowvalue_as_string()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatable.DataTable.rowvalue_as_string_byname"><code class="docutils literal notranslate"><span class="pre">DataTable.rowvalue_as_string_byname()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatable.DataTable.select"><code class="docutils literal notranslate"><span class="pre">DataTable.select()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.data.html#module-sttp.data.datatype">sttp.data.datatype module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.datatype.DataType"><code class="docutils literal notranslate"><span class="pre">DataType</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatype.DataType.BOOLEAN"><code class="docutils literal notranslate"><span class="pre">DataType.BOOLEAN</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatype.DataType.DATETIME"><code class="docutils literal notranslate"><span class="pre">DataType.DATETIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatype.DataType.DECIMAL"><code class="docutils literal notranslate"><span class="pre">DataType.DECIMAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatype.DataType.DOUBLE"><code class="docutils literal notranslate"><span class="pre">DataType.DOUBLE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatype.DataType.GUID"><code class="docutils literal notranslate"><span class="pre">DataType.GUID</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatype.DataType.INT16"><code class="docutils literal notranslate"><span class="pre">DataType.INT16</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatype.DataType.INT32"><code class="docutils literal notranslate"><span class="pre">DataType.INT32</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatype.DataType.INT64"><code class="docutils literal notranslate"><span class="pre">DataType.INT64</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatype.DataType.INT8"><code class="docutils literal notranslate"><span class="pre">DataType.INT8</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatype.DataType.SINGLE"><code class="docutils literal notranslate"><span class="pre">DataType.SINGLE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatype.DataType.STRING"><code class="docutils literal notranslate"><span class="pre">DataType.STRING</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatype.DataType.UINT16"><code class="docutils literal notranslate"><span class="pre">DataType.UINT16</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatype.DataType.UINT32"><code class="docutils literal notranslate"><span class="pre">DataType.UINT32</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatype.DataType.UINT64"><code class="docutils literal notranslate"><span class="pre">DataType.UINT64</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.datatype.DataType.UINT8"><code class="docutils literal notranslate"><span class="pre">DataType.UINT8</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.datatype.default_datatype"><code class="docutils literal notranslate"><span class="pre">default_datatype()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.datatype.parse_xsddatatype"><code class="docutils literal notranslate"><span class="pre">parse_xsddatatype()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.data.html#module-sttp.data.errors">sttp.data.errors module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.errors.EvaluateError"><code class="docutils literal notranslate"><span class="pre">EvaluateError</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.data.html#module-sttp.data.expression">sttp.data.expression module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.expression.Expression"><code class="docutils literal notranslate"><span class="pre">Expression</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.expression.Expression.expressiontype"><code class="docutils literal notranslate"><span class="pre">Expression.expressiontype</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.data.html#module-sttp.data.expressiontree">sttp.data.expressiontree module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.expressiontree.ExpressionTree"><code class="docutils literal notranslate"><span class="pre">ExpressionTree</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.expressiontree.ExpressionTree.evaluate"><code class="docutils literal notranslate"><span class="pre">ExpressionTree.evaluate()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.expressiontree.ExpressionTree.orderbyterms"><code class="docutils literal notranslate"><span class="pre">ExpressionTree.orderbyterms</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.expressiontree.ExpressionTree.root"><code class="docutils literal notranslate"><span class="pre">ExpressionTree.root</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.expressiontree.ExpressionTree.select"><code class="docutils literal notranslate"><span class="pre">ExpressionTree.select()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.expressiontree.ExpressionTree.selectwhere"><code class="docutils literal notranslate"><span class="pre">ExpressionTree.selectwhere()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.expressiontree.ExpressionTree.tablename"><code class="docutils literal notranslate"><span class="pre">ExpressionTree.tablename</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.expressiontree.ExpressionTree.toplimit"><code class="docutils literal notranslate"><span class="pre">ExpressionTree.toplimit</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.data.html#module-sttp.data.filterexpressionparser">sttp.data.filterexpressionparser module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.dataset"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.dataset</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.enterExpression"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.enterExpression()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.enterFilterExpressionStatement"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.enterFilterExpressionStatement()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.enterFilterStatement"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.enterFilterStatement()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.evaluate"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.evaluate()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.evaluate_datarowexpression"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.evaluate_datarowexpression()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.evaluate_expression"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.evaluate_expression()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.exitColumnName"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.exitColumnName()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.exitExpression"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.exitExpression()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.exitFunctionExpression"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.exitFunctionExpression()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.exitIdentifierStatement"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.exitIdentifierStatement()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.exitLiteralValue"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.exitLiteralValue()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.exitPredicateExpression"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.exitPredicateExpression()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.exitValueExpression"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.exitValueExpression()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.expressiontrees"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.expressiontrees</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.filtered_rows"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.filtered_rows</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.filtered_rowset"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.filtered_rowset</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.filtered_signalids"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.filtered_signalids</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.filtered_signalidset"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.filtered_signalidset</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.filterexpression_statementcount"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.filterexpression_statementcount</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.from_dataset"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.from_dataset()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.generate_expressiontree"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.generate_expressiontree()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.generate_expressiontrees"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.generate_expressiontrees()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.generate_expressiontrees_fromtable"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.generate_expressiontrees_fromtable()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.primary_tablename"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.primary_tablename</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.select_datarows"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.select_datarows()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.select_datarows_fromtable"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.select_datarows_fromtable()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.select_datarowset"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.select_datarowset()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.select_datarowset_fromtable"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.select_datarowset_fromtable()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.select_signalidset"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.select_signalidset()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.select_signalidset_fromtable"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.select_signalidset_fromtable()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.set_parsingexception_callback"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.set_parsingexception_callback()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.table"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.table()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.tableidfields_map"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.tableidfields_map</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.track_filteredrows"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.track_filteredrows</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.filterexpressionparser.FilterExpressionParser.track_filteredsignalids"><code class="docutils literal notranslate"><span class="pre">FilterExpressionParser.track_filteredsignalids</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.data.html#module-sttp.data.functionexpression">sttp.data.functionexpression module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.functionexpression.FunctionExpression"><code class="docutils literal notranslate"><span class="pre">FunctionExpression</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.functionexpression.FunctionExpression.arguments"><code class="docutils literal notranslate"><span class="pre">FunctionExpression.arguments</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.functionexpression.FunctionExpression.expressiontype"><code class="docutils literal notranslate"><span class="pre">FunctionExpression.expressiontype</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.functionexpression.FunctionExpression.functiontype"><code class="docutils literal notranslate"><span class="pre">FunctionExpression.functiontype</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.data.html#module-sttp.data.inlistexpression">sttp.data.inlistexpression module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.inlistexpression.InListExpression"><code class="docutils literal notranslate"><span class="pre">InListExpression</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.inlistexpression.InListExpression.arguments"><code class="docutils literal notranslate"><span class="pre">InListExpression.arguments</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.inlistexpression.InListExpression.exactmatch"><code class="docutils literal notranslate"><span class="pre">InListExpression.exactmatch</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.inlistexpression.InListExpression.expressiontype"><code class="docutils literal notranslate"><span class="pre">InListExpression.expressiontype</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.inlistexpression.InListExpression.has_notkeyword"><code class="docutils literal notranslate"><span class="pre">InListExpression.has_notkeyword</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.inlistexpression.InListExpression.value"><code class="docutils literal notranslate"><span class="pre">InListExpression.value</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.data.html#module-sttp.data.operatorexpression">sttp.data.operatorexpression module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.operatorexpression.OperatorExpression"><code class="docutils literal notranslate"><span class="pre">OperatorExpression</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.operatorexpression.OperatorExpression.expressiontype"><code class="docutils literal notranslate"><span class="pre">OperatorExpression.expressiontype</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.operatorexpression.OperatorExpression.leftvalue"><code class="docutils literal notranslate"><span class="pre">OperatorExpression.leftvalue</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.operatorexpression.OperatorExpression.operatortype"><code class="docutils literal notranslate"><span class="pre">OperatorExpression.operatortype</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.operatorexpression.OperatorExpression.rightvalue"><code class="docutils literal notranslate"><span class="pre">OperatorExpression.rightvalue</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.data.html#module-sttp.data.orderbyterm">sttp.data.orderbyterm module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.orderbyterm.OrderByTerm"><code class="docutils literal notranslate"><span class="pre">OrderByTerm</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.orderbyterm.OrderByTerm.ascending"><code class="docutils literal notranslate"><span class="pre">OrderByTerm.ascending</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.orderbyterm.OrderByTerm.column"><code class="docutils literal notranslate"><span class="pre">OrderByTerm.column</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.orderbyterm.OrderByTerm.extactmatch"><code class="docutils literal notranslate"><span class="pre">OrderByTerm.extactmatch</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.data.html#module-sttp.data.tableidfields">sttp.data.tableidfields module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.tableidfields.DEFAULT_TABLEIDFIELDS"><code class="docutils literal notranslate"><span class="pre">DEFAULT_TABLEIDFIELDS</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.tableidfields.TableIDFields"><code class="docutils literal notranslate"><span class="pre">TableIDFields</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.tableidfields.TableIDFields.measurementkey_fieldname"><code class="docutils literal notranslate"><span class="pre">TableIDFields.measurementkey_fieldname</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.tableidfields.TableIDFields.pointtag_fieldname"><code class="docutils literal notranslate"><span class="pre">TableIDFields.pointtag_fieldname</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.tableidfields.TableIDFields.signalid_fieldname"><code class="docutils literal notranslate"><span class="pre">TableIDFields.signalid_fieldname</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.data.html#module-sttp.data.unaryexpression">sttp.data.unaryexpression module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.unaryexpression.UnaryExpression"><code class="docutils literal notranslate"><span class="pre">UnaryExpression</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.unaryexpression.UnaryExpression.applyto_bool"><code class="docutils literal notranslate"><span class="pre">UnaryExpression.applyto_bool()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.unaryexpression.UnaryExpression.applyto_decimal"><code class="docutils literal notranslate"><span class="pre">UnaryExpression.applyto_decimal()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.unaryexpression.UnaryExpression.applyto_double"><code class="docutils literal notranslate"><span class="pre">UnaryExpression.applyto_double()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.unaryexpression.UnaryExpression.applyto_int32"><code class="docutils literal notranslate"><span class="pre">UnaryExpression.applyto_int32()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.unaryexpression.UnaryExpression.applyto_int64"><code class="docutils literal notranslate"><span class="pre">UnaryExpression.applyto_int64()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.unaryexpression.UnaryExpression.expressiontype"><code class="docutils literal notranslate"><span class="pre">UnaryExpression.expressiontype</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.unaryexpression.UnaryExpression.unarytype"><code class="docutils literal notranslate"><span class="pre">UnaryExpression.unarytype</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.unaryexpression.UnaryExpression.value"><code class="docutils literal notranslate"><span class="pre">UnaryExpression.value</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.data.html#module-sttp.data.valueexpression">sttp.data.valueexpression module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.EMPTYSTRINGVALUE"><code class="docutils literal notranslate"><span class="pre">EMPTYSTRINGVALUE</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.FALSEVALUE"><code class="docutils literal notranslate"><span class="pre">FALSEVALUE</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.NULLBOOLVALUE"><code class="docutils literal notranslate"><span class="pre">NULLBOOLVALUE</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.NULLDATETIMEVALUE"><code class="docutils literal notranslate"><span class="pre">NULLDATETIMEVALUE</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.NULLINT32VALUE"><code class="docutils literal notranslate"><span class="pre">NULLINT32VALUE</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.NULLSTRINGVALUE"><code class="docutils literal notranslate"><span class="pre">NULLSTRINGVALUE</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.NULLVALUE"><code class="docutils literal notranslate"><span class="pre">NULLVALUE</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.TRUEVALUE"><code class="docutils literal notranslate"><span class="pre">TRUEVALUE</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.ValueExpression"><code class="docutils literal notranslate"><span class="pre">ValueExpression</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.ValueExpression.booleanvalue"><code class="docutils literal notranslate"><span class="pre">ValueExpression.booleanvalue()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.ValueExpression.convert"><code class="docutils literal notranslate"><span class="pre">ValueExpression.convert()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.ValueExpression.datetimevalue"><code class="docutils literal notranslate"><span class="pre">ValueExpression.datetimevalue()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.ValueExpression.decimalvalue"><code class="docutils literal notranslate"><span class="pre">ValueExpression.decimalvalue()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.ValueExpression.doublevalue"><code class="docutils literal notranslate"><span class="pre">ValueExpression.doublevalue()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.ValueExpression.expressiontype"><code class="docutils literal notranslate"><span class="pre">ValueExpression.expressiontype</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.ValueExpression.guidvalue"><code class="docutils literal notranslate"><span class="pre">ValueExpression.guidvalue()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.ValueExpression.int32value"><code class="docutils literal notranslate"><span class="pre">ValueExpression.int32value()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.ValueExpression.int64value"><code class="docutils literal notranslate"><span class="pre">ValueExpression.int64value()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.ValueExpression.integervalue"><code class="docutils literal notranslate"><span class="pre">ValueExpression.integervalue()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.ValueExpression.is_null"><code class="docutils literal notranslate"><span class="pre">ValueExpression.is_null()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.ValueExpression.nullvalue"><code class="docutils literal notranslate"><span class="pre">ValueExpression.nullvalue()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.ValueExpression.stringvalue"><code class="docutils literal notranslate"><span class="pre">ValueExpression.stringvalue()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.ValueExpression.value"><code class="docutils literal notranslate"><span class="pre">ValueExpression.value</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.data.html#sttp.data.valueexpression.ValueExpression.valuetype"><code class="docutils literal notranslate"><span class="pre">ValueExpression.valuetype</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.data.html#module-sttp.data">Module contents</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="sttp.metadata.html">sttp.metadata package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="sttp.metadata.html#subpackages">Subpackages</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.metadata.record.html">sttp.metadata.record package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#module-sttp.metadata.record.device">sttp.metadata.record.device module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#module-sttp.metadata.record.measurement">sttp.metadata.record.measurement module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#module-sttp.metadata.record.phasor">sttp.metadata.record.phasor module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#module-sttp.metadata.record">Module contents</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.metadata.html#submodules">Submodules</a></li>
<li class="toctree-l2"><a class="reference internal" href="sttp.metadata.html#module-sttp.metadata.cache">sttp.metadata.cache module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.metadata.html#sttp.metadata.cache.MetadataCache"><code class="docutils literal notranslate"><span class="pre">MetadataCache</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#sttp.metadata.cache.MetadataCache.add_measurement"><code class="docutils literal notranslate"><span class="pre">MetadataCache.add_measurement()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#sttp.metadata.cache.MetadataCache.device_records"><code class="docutils literal notranslate"><span class="pre">MetadataCache.device_records</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#sttp.metadata.cache.MetadataCache.deviceacronym_device_map"><code class="docutils literal notranslate"><span class="pre">MetadataCache.deviceacronym_device_map</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#sttp.metadata.cache.MetadataCache.deviceid_device_map"><code class="docutils literal notranslate"><span class="pre">MetadataCache.deviceid_device_map</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#sttp.metadata.cache.MetadataCache.find_device_acronym"><code class="docutils literal notranslate"><span class="pre">MetadataCache.find_device_acronym()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#sttp.metadata.cache.MetadataCache.find_device_id"><code class="docutils literal notranslate"><span class="pre">MetadataCache.find_device_id()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#sttp.metadata.cache.MetadataCache.find_devices"><code class="docutils literal notranslate"><span class="pre">MetadataCache.find_devices()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#sttp.metadata.cache.MetadataCache.find_measurement_id"><code class="docutils literal notranslate"><span class="pre">MetadataCache.find_measurement_id()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#sttp.metadata.cache.MetadataCache.find_measurement_pointtag"><code class="docutils literal notranslate"><span class="pre">MetadataCache.find_measurement_pointtag()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#sttp.metadata.cache.MetadataCache.find_measurement_signalid"><code class="docutils literal notranslate"><span class="pre">MetadataCache.find_measurement_signalid()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#sttp.metadata.cache.MetadataCache.find_measurement_signalreference"><code class="docutils literal notranslate"><span class="pre">MetadataCache.find_measurement_signalreference()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#sttp.metadata.cache.MetadataCache.find_measurements"><code class="docutils literal notranslate"><span class="pre">MetadataCache.find_measurements()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#sttp.metadata.cache.MetadataCache.find_measurements_signaltype"><code class="docutils literal notranslate"><span class="pre">MetadataCache.find_measurements_signaltype()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#sttp.metadata.cache.MetadataCache.find_measurements_signaltypename"><code class="docutils literal notranslate"><span class="pre">MetadataCache.find_measurements_signaltypename()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#sttp.metadata.cache.MetadataCache.id_measurement_map"><code class="docutils literal notranslate"><span class="pre">MetadataCache.id_measurement_map</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#sttp.metadata.cache.MetadataCache.measurement_records"><code class="docutils literal notranslate"><span class="pre">MetadataCache.measurement_records</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#sttp.metadata.cache.MetadataCache.phasorRecords"><code class="docutils literal notranslate"><span class="pre">MetadataCache.phasorRecords</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#sttp.metadata.cache.MetadataCache.pointtag_measurement_map"><code class="docutils literal notranslate"><span class="pre">MetadataCache.pointtag_measurement_map</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#sttp.metadata.cache.MetadataCache.signalid_measurement_map"><code class="docutils literal notranslate"><span class="pre">MetadataCache.signalid_measurement_map</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.html#sttp.metadata.cache.MetadataCache.signalref_measurement_map"><code class="docutils literal notranslate"><span class="pre">MetadataCache.signalref_measurement_map</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.metadata.html#module-sttp.metadata">Module contents</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="sttp.transport.html">sttp.transport package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="sttp.transport.html#subpackages">Subpackages</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.tssc.html">sttp.transport.tssc package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#module-sttp.transport.tssc.decoder">sttp.transport.tssc.decoder module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#module-sttp.transport.tssc.pointmetadata">sttp.transport.tssc.pointmetadata module</a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#module-sttp.transport.tssc">Module contents</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.transport.html#submodules">Submodules</a></li>
<li class="toctree-l2"><a class="reference internal" href="sttp.transport.html#module-sttp.transport.bufferblock">sttp.transport.bufferblock module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.html#sttp.transport.bufferblock.BufferBlock"><code class="docutils literal notranslate"><span class="pre">BufferBlock</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.bufferblock.BufferBlock.DEFAULT_BUFFER"><code class="docutils literal notranslate"><span class="pre">BufferBlock.DEFAULT_BUFFER</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.bufferblock.BufferBlock.DEFAULT_SIGNALID"><code class="docutils literal notranslate"><span class="pre">BufferBlock.DEFAULT_SIGNALID</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.bufferblock.BufferBlock.buffer"><code class="docutils literal notranslate"><span class="pre">BufferBlock.buffer</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.bufferblock.BufferBlock.signalid"><code class="docutils literal notranslate"><span class="pre">BufferBlock.signalid</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.transport.html#module-sttp.transport.compactmeasurement">sttp.transport.compactmeasurement module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.html#sttp.transport.compactmeasurement.CompactMeasurement"><code class="docutils literal notranslate"><span class="pre">CompactMeasurement</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.compactmeasurement.CompactMeasurement.decode"><code class="docutils literal notranslate"><span class="pre">CompactMeasurement.decode()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.compactmeasurement.CompactMeasurement.get_binarylength"><code class="docutils literal notranslate"><span class="pre">CompactMeasurement.get_binarylength()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.compactmeasurement.CompactMeasurement.get_compact_stateflags"><code class="docutils literal notranslate"><span class="pre">CompactMeasurement.get_compact_stateflags()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.compactmeasurement.CompactMeasurement.get_timestamp_c2"><code class="docutils literal notranslate"><span class="pre">CompactMeasurement.get_timestamp_c2()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.compactmeasurement.CompactMeasurement.get_timestamp_c4"><code class="docutils literal notranslate"><span class="pre">CompactMeasurement.get_timestamp_c4()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.compactmeasurement.CompactMeasurement.runtimeid"><code class="docutils literal notranslate"><span class="pre">CompactMeasurement.runtimeid</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.compactmeasurement.CompactMeasurement.set_compact_stateflags"><code class="docutils literal notranslate"><span class="pre">CompactMeasurement.set_compact_stateflags()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.html#sttp.transport.compactmeasurement.CompactStateFlags"><code class="docutils literal notranslate"><span class="pre">CompactStateFlags</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.compactmeasurement.CompactStateFlags.BASETIMEOFFSET"><code class="docutils literal notranslate"><span class="pre">CompactStateFlags.BASETIMEOFFSET</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.compactmeasurement.CompactStateFlags.CALCULATEDVALUE"><code class="docutils literal notranslate"><span class="pre">CompactStateFlags.CALCULATEDVALUE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.compactmeasurement.CompactStateFlags.DATAQUALITY"><code class="docutils literal notranslate"><span class="pre">CompactStateFlags.DATAQUALITY</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.compactmeasurement.CompactStateFlags.DATARANGE"><code class="docutils literal notranslate"><span class="pre">CompactStateFlags.DATARANGE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.compactmeasurement.CompactStateFlags.DISCARDEDVALUE"><code class="docutils literal notranslate"><span class="pre">CompactStateFlags.DISCARDEDVALUE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.compactmeasurement.CompactStateFlags.SYSTEMISSUE"><code class="docutils literal notranslate"><span class="pre">CompactStateFlags.SYSTEMISSUE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.compactmeasurement.CompactStateFlags.TIMEINDEX"><code class="docutils literal notranslate"><span class="pre">CompactStateFlags.TIMEINDEX</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.compactmeasurement.CompactStateFlags.TIMEQUALITY"><code class="docutils literal notranslate"><span class="pre">CompactStateFlags.TIMEQUALITY</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.transport.html#module-sttp.transport.constants">sttp.transport.constants module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.CompressionModes"><code class="docutils literal notranslate"><span class="pre">CompressionModes</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.CompressionModes.GZIP"><code class="docutils literal notranslate"><span class="pre">CompressionModes.GZIP</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.CompressionModes.NOFLAGS"><code class="docutils literal notranslate"><span class="pre">CompressionModes.NOFLAGS</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.CompressionModes.TSSC"><code class="docutils literal notranslate"><span class="pre">CompressionModes.TSSC</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ConnectStatus"><code class="docutils literal notranslate"><span class="pre">ConnectStatus</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ConnectStatus.CANCELED"><code class="docutils literal notranslate"><span class="pre">ConnectStatus.CANCELED</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ConnectStatus.FAILED"><code class="docutils literal notranslate"><span class="pre">ConnectStatus.FAILED</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ConnectStatus.SUCCESS"><code class="docutils literal notranslate"><span class="pre">ConnectStatus.SUCCESS</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.DataPacketFlags"><code class="docutils literal notranslate"><span class="pre">DataPacketFlags</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.DataPacketFlags.CACHEINDEX"><code class="docutils literal notranslate"><span class="pre">DataPacketFlags.CACHEINDEX</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.DataPacketFlags.CIPHERINDEX"><code class="docutils literal notranslate"><span class="pre">DataPacketFlags.CIPHERINDEX</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.DataPacketFlags.COMPACT"><code class="docutils literal notranslate"><span class="pre">DataPacketFlags.COMPACT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.DataPacketFlags.COMPRESSED"><code class="docutils literal notranslate"><span class="pre">DataPacketFlags.COMPRESSED</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.DataPacketFlags.NOFLAGS"><code class="docutils literal notranslate"><span class="pre">DataPacketFlags.NOFLAGS</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults"><code class="docutils literal notranslate"><span class="pre">Defaults</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.AUTORECONNECT"><code class="docutils literal notranslate"><span class="pre">Defaults.AUTORECONNECT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.AUTOREQUESTMETADATA"><code class="docutils literal notranslate"><span class="pre">Defaults.AUTOREQUESTMETADATA</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.AUTOSUBSCRIBE"><code class="docutils literal notranslate"><span class="pre">Defaults.AUTOSUBSCRIBE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.COMPRESS_METADATA"><code class="docutils literal notranslate"><span class="pre">Defaults.COMPRESS_METADATA</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.COMPRESS_PAYLOADDATA"><code class="docutils literal notranslate"><span class="pre">Defaults.COMPRESS_PAYLOADDATA</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.COMPRESS_SIGNALINDEXCACHE"><code class="docutils literal notranslate"><span class="pre">Defaults.COMPRESS_SIGNALINDEXCACHE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.CONSTRAINTPARAMETERS"><code class="docutils literal notranslate"><span class="pre">Defaults.CONSTRAINTPARAMETERS</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.DATACHANNEL_INTERFACE"><code class="docutils literal notranslate"><span class="pre">Defaults.DATACHANNEL_INTERFACE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.DATACHANNEL_LOCALPORT"><code class="docutils literal notranslate"><span class="pre">Defaults.DATACHANNEL_LOCALPORT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.ENABLE_TIME_REASONABILITY_CHECK"><code class="docutils literal notranslate"><span class="pre">Defaults.ENABLE_TIME_REASONABILITY_CHECK</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.EXTRA_CONNECTIONSTRING_PARAMETERS"><code class="docutils literal notranslate"><span class="pre">Defaults.EXTRA_CONNECTIONSTRING_PARAMETERS</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.FILTEREXPRESSION"><code class="docutils literal notranslate"><span class="pre">Defaults.FILTEREXPRESSION</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.INCLUDETIME"><code class="docutils literal notranslate"><span class="pre">Defaults.INCLUDETIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.LAGTIME"><code class="docutils literal notranslate"><span class="pre">Defaults.LAGTIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.LEADTIME"><code class="docutils literal notranslate"><span class="pre">Defaults.LEADTIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.MAXRETRIES"><code class="docutils literal notranslate"><span class="pre">Defaults.MAXRETRIES</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.MAXRETRYINTERVAL"><code class="docutils literal notranslate"><span class="pre">Defaults.MAXRETRYINTERVAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.METADATAFILTERS"><code class="docutils literal notranslate"><span class="pre">Defaults.METADATAFILTERS</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.PROCESSINGINTERVAL"><code class="docutils literal notranslate"><span class="pre">Defaults.PROCESSINGINTERVAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.PUBLISHINTERVAL"><code class="docutils literal notranslate"><span class="pre">Defaults.PUBLISHINTERVAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.REQUEST_NANVALUEFILTER"><code class="docutils literal notranslate"><span class="pre">Defaults.REQUEST_NANVALUEFILTER</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.RETRYINTERVAL"><code class="docutils literal notranslate"><span class="pre">Defaults.RETRYINTERVAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.SOCKET_TIMEOUT"><code class="docutils literal notranslate"><span class="pre">Defaults.SOCKET_TIMEOUT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.STARTTIME"><code class="docutils literal notranslate"><span class="pre">Defaults.STARTTIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.STOPTIME"><code class="docutils literal notranslate"><span class="pre">Defaults.STOPTIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.THROTTLED"><code class="docutils literal notranslate"><span class="pre">Defaults.THROTTLED</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.UDPDATACHANNEL"><code class="docutils literal notranslate"><span class="pre">Defaults.UDPDATACHANNEL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.USE_LOCALCLOCK_AS_REALTIME"><code class="docutils literal notranslate"><span class="pre">Defaults.USE_LOCALCLOCK_AS_REALTIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.USE_MILLISECONDRESOLUTION"><code class="docutils literal notranslate"><span class="pre">Defaults.USE_MILLISECONDRESOLUTION</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.Defaults.VERSION"><code class="docutils literal notranslate"><span class="pre">Defaults.VERSION</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.OperationalEncoding"><code class="docutils literal notranslate"><span class="pre">OperationalEncoding</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.OperationalEncoding.UTF16BE"><code class="docutils literal notranslate"><span class="pre">OperationalEncoding.UTF16BE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.OperationalEncoding.UTF16LE"><code class="docutils literal notranslate"><span class="pre">OperationalEncoding.UTF16LE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.OperationalEncoding.UTF8"><code class="docutils literal notranslate"><span class="pre">OperationalEncoding.UTF8</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.OperationalModes"><code class="docutils literal notranslate"><span class="pre">OperationalModes</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.OperationalModes.COMPRESSMETADATA"><code class="docutils literal notranslate"><span class="pre">OperationalModes.COMPRESSMETADATA</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.OperationalModes.COMPRESSPAYLOADDATA"><code class="docutils literal notranslate"><span class="pre">OperationalModes.COMPRESSPAYLOADDATA</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.OperationalModes.COMPRESSSIGNALINDEXCACHE"><code class="docutils literal notranslate"><span class="pre">OperationalModes.COMPRESSSIGNALINDEXCACHE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.OperationalModes.ENCODINGMASK"><code class="docutils literal notranslate"><span class="pre">OperationalModes.ENCODINGMASK</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.OperationalModes.IMPLEMENTATIONSPECIFICEXTENSIONMASK"><code class="docutils literal notranslate"><span class="pre">OperationalModes.IMPLEMENTATIONSPECIFICEXTENSIONMASK</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.OperationalModes.NOFLAGS"><code class="docutils literal notranslate"><span class="pre">OperationalModes.NOFLAGS</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.OperationalModes.RECEIVEEXTERNALMETADATA"><code class="docutils literal notranslate"><span class="pre">OperationalModes.RECEIVEEXTERNALMETADATA</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.OperationalModes.RECEIVEINTERNALMETADATA"><code class="docutils literal notranslate"><span class="pre">OperationalModes.RECEIVEINTERNALMETADATA</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.OperationalModes.VERSIONMASK"><code class="docutils literal notranslate"><span class="pre">OperationalModes.VERSIONMASK</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.SecurityMode"><code class="docutils literal notranslate"><span class="pre">SecurityMode</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.SecurityMode.OFF"><code class="docutils literal notranslate"><span class="pre">SecurityMode.OFF</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.SecurityMode.TLS"><code class="docutils literal notranslate"><span class="pre">SecurityMode.TLS</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand"><code class="docutils literal notranslate"><span class="pre">ServerCommand</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.CONFIRMBUFFERBLOCK"><code class="docutils literal notranslate"><span class="pre">ServerCommand.CONFIRMBUFFERBLOCK</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.CONFIRMNOTIFICATION"><code class="docutils literal notranslate"><span class="pre">ServerCommand.CONFIRMNOTIFICATION</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.CONFIRMUPDATEBASETIMES"><code class="docutils literal notranslate"><span class="pre">ServerCommand.CONFIRMUPDATEBASETIMES</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.CONFIRMUPDATECIPHERKEYS"><code class="docutils literal notranslate"><span class="pre">ServerCommand.CONFIRMUPDATECIPHERKEYS</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.CONFIRMUPDATESIGNALINDEXCACHE"><code class="docutils literal notranslate"><span class="pre">ServerCommand.CONFIRMUPDATESIGNALINDEXCACHE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.CONNECT"><code class="docutils literal notranslate"><span class="pre">ServerCommand.CONNECT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.DEFINEOPERATIONALMODES"><code class="docutils literal notranslate"><span class="pre">ServerCommand.DEFINEOPERATIONALMODES</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.GETPRIMARYMETADATASCHEMA"><code class="docutils literal notranslate"><span class="pre">ServerCommand.GETPRIMARYMETADATASCHEMA</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.GETSIGNALSELECTIONSCHEMA"><code class="docutils literal notranslate"><span class="pre">ServerCommand.GETSIGNALSELECTIONSCHEMA</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.METADATAREFRESH"><code class="docutils literal notranslate"><span class="pre">ServerCommand.METADATAREFRESH</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.ROTATECIPHERKEYS"><code class="docutils literal notranslate"><span class="pre">ServerCommand.ROTATECIPHERKEYS</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.SUBSCRIBE"><code class="docutils literal notranslate"><span class="pre">ServerCommand.SUBSCRIBE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.UNSUBSCRIBE"><code class="docutils literal notranslate"><span class="pre">ServerCommand.UNSUBSCRIBE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.UPDATEPROCESSINGINTERVAL"><code class="docutils literal notranslate"><span class="pre">ServerCommand.UPDATEPROCESSINGINTERVAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.USERCOMMAND00"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND00</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.USERCOMMAND01"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND01</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.USERCOMMAND02"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND02</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.USERCOMMAND03"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND03</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.USERCOMMAND04"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND04</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.USERCOMMAND05"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND05</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.USERCOMMAND06"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND06</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.USERCOMMAND07"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND07</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.USERCOMMAND08"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND08</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.USERCOMMAND09"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND09</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.USERCOMMAND10"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND10</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.USERCOMMAND11"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND11</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.USERCOMMAND12"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND12</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.USERCOMMAND13"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND13</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.USERCOMMAND14"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND14</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerCommand.USERCOMMAND15"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND15</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse"><code class="docutils literal notranslate"><span class="pre">ServerResponse</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.BUFFERBLOCK"><code class="docutils literal notranslate"><span class="pre">ServerResponse.BUFFERBLOCK</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.CONFIGURATIONCHANGED"><code class="docutils literal notranslate"><span class="pre">ServerResponse.CONFIGURATIONCHANGED</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.DATAPACKET"><code class="docutils literal notranslate"><span class="pre">ServerResponse.DATAPACKET</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.DATASTARTTIME"><code class="docutils literal notranslate"><span class="pre">ServerResponse.DATASTARTTIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.FAILED"><code class="docutils literal notranslate"><span class="pre">ServerResponse.FAILED</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.NOOP"><code class="docutils literal notranslate"><span class="pre">ServerResponse.NOOP</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.NOTIFY"><code class="docutils literal notranslate"><span class="pre">ServerResponse.NOTIFY</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.PROCESSINGCOMPLETE"><code class="docutils literal notranslate"><span class="pre">ServerResponse.PROCESSINGCOMPLETE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.SUCCEEDED"><code class="docutils literal notranslate"><span class="pre">ServerResponse.SUCCEEDED</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.UPDATEBASETIMES"><code class="docutils literal notranslate"><span class="pre">ServerResponse.UPDATEBASETIMES</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.UPDATECIPHERKEYS"><code class="docutils literal notranslate"><span class="pre">ServerResponse.UPDATECIPHERKEYS</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.UPDATESIGNALINDEXCACHE"><code class="docutils literal notranslate"><span class="pre">ServerResponse.UPDATESIGNALINDEXCACHE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.USERRESPONSE00"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE00</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.USERRESPONSE01"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE01</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.USERRESPONSE02"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE02</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.USERRESPONSE03"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE03</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.USERRESPONSE04"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE04</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.USERRESPONSE05"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE05</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.USERRESPONSE06"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE06</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.USERRESPONSE07"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE07</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.USERRESPONSE08"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE08</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.USERRESPONSE09"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE09</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.USERRESPONSE10"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE10</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.USERRESPONSE11"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE11</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.USERRESPONSE12"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE12</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.USERRESPONSE13"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE13</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.USERRESPONSE14"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE14</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.ServerResponse.USERRESPONSE15"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE15</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags"><code class="docutils literal notranslate"><span class="pre">StateFlags</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.ALARMHIGH"><code class="docutils literal notranslate"><span class="pre">StateFlags.ALARMHIGH</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.ALARMLOW"><code class="docutils literal notranslate"><span class="pre">StateFlags.ALARMLOW</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.BADDATA"><code class="docutils literal notranslate"><span class="pre">StateFlags.BADDATA</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.BADTIME"><code class="docutils literal notranslate"><span class="pre">StateFlags.BADTIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.CALCULATEDVALUE"><code class="docutils literal notranslate"><span class="pre">StateFlags.CALCULATEDVALUE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.CALCULATIONERROR"><code class="docutils literal notranslate"><span class="pre">StateFlags.CALCULATIONERROR</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.CALCULATIONWARNING"><code class="docutils literal notranslate"><span class="pre">StateFlags.CALCULATIONWARNING</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.COMPARISONALARM"><code class="docutils literal notranslate"><span class="pre">StateFlags.COMPARISONALARM</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.DISCARDEDVALUE"><code class="docutils literal notranslate"><span class="pre">StateFlags.DISCARDEDVALUE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.DOWNSAMPLED"><code class="docutils literal notranslate"><span class="pre">StateFlags.DOWNSAMPLED</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.FLATLINEALARM"><code class="docutils literal notranslate"><span class="pre">StateFlags.FLATLINEALARM</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.FUTURETIMEALARM"><code class="docutils literal notranslate"><span class="pre">StateFlags.FUTURETIMEALARM</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.LATETIMEALARM"><code class="docutils literal notranslate"><span class="pre">StateFlags.LATETIMEALARM</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.MEASUREMENTERROR"><code class="docutils literal notranslate"><span class="pre">StateFlags.MEASUREMENTERROR</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.NORMAL"><code class="docutils literal notranslate"><span class="pre">StateFlags.NORMAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.OVERRANGEERROR"><code class="docutils literal notranslate"><span class="pre">StateFlags.OVERRANGEERROR</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.RECEIVEDASBAD"><code class="docutils literal notranslate"><span class="pre">StateFlags.RECEIVEDASBAD</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.RESERVEDQUALITYFLAG"><code class="docutils literal notranslate"><span class="pre">StateFlags.RESERVEDQUALITYFLAG</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.RESERVEDTIMEFLAG"><code class="docutils literal notranslate"><span class="pre">StateFlags.RESERVEDTIMEFLAG</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.ROCALARM"><code class="docutils literal notranslate"><span class="pre">StateFlags.ROCALARM</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.SUSPECTDATA"><code class="docutils literal notranslate"><span class="pre">StateFlags.SUSPECTDATA</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.SUSPECTTIME"><code class="docutils literal notranslate"><span class="pre">StateFlags.SUSPECTTIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.SYSTEMERROR"><code class="docutils literal notranslate"><span class="pre">StateFlags.SYSTEMERROR</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.SYSTEMWARNING"><code class="docutils literal notranslate"><span class="pre">StateFlags.SYSTEMWARNING</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.UNDERRANGEERROR"><code class="docutils literal notranslate"><span class="pre">StateFlags.UNDERRANGEERROR</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.UPSAMPLED"><code class="docutils literal notranslate"><span class="pre">StateFlags.UPSAMPLED</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.USERDEFINEDFLAG1"><code class="docutils literal notranslate"><span class="pre">StateFlags.USERDEFINEDFLAG1</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.USERDEFINEDFLAG2"><code class="docutils literal notranslate"><span class="pre">StateFlags.USERDEFINEDFLAG2</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.USERDEFINEDFLAG3"><code class="docutils literal notranslate"><span class="pre">StateFlags.USERDEFINEDFLAG3</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.USERDEFINEDFLAG4"><code class="docutils literal notranslate"><span class="pre">StateFlags.USERDEFINEDFLAG4</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.USERDEFINEDFLAG5"><code class="docutils literal notranslate"><span class="pre">StateFlags.USERDEFINEDFLAG5</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.WARNINGHIGH"><code class="docutils literal notranslate"><span class="pre">StateFlags.WARNINGHIGH</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.constants.StateFlags.WARNINGLOW"><code class="docutils literal notranslate"><span class="pre">StateFlags.WARNINGLOW</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.transport.html#module-sttp.transport.datasubscriber">sttp.transport.datasubscriber module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber"><code class="docutils literal notranslate"><span class="pre">DataSubscriber</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_COMPRESS_METADATA"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.DEFAULT_COMPRESS_METADATA</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_COMPRESS_PAYLOADDATA"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.DEFAULT_COMPRESS_PAYLOADDATA</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_COMPRESS_SIGNALINDEXCACHE"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.DEFAULT_COMPRESS_SIGNALINDEXCACHE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_SOCKET_TIMEOUT"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.DEFAULT_SOCKET_TIMEOUT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_STTP_SOURCEINFO"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.DEFAULT_STTP_SOURCEINFO</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_STTP_UPDATEDONINFO"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.DEFAULT_STTP_UPDATEDONINFO</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_STTP_VERSIONINFO"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.DEFAULT_STTP_VERSIONINFO</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_VERSION"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.DEFAULT_VERSION</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.activesignalindexcache"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.activesignalindexcache</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.adjustedvalue"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.adjustedvalue()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.autoreconnect_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.autoreconnect_callback</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.compress_metadata"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.compress_metadata</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.compress_payloaddata"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.compress_payloaddata</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.compress_signalindexcache"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.compress_signalindexcache</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.configurationchanged_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.configurationchanged_callback</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.connect"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.connect()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.connected"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.connected</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.connectionterminated_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.connectionterminated_callback</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.connector"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.connector</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.data_starttime_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.data_starttime_callback</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.decodestr"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.decodestr()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.disconnect"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.disconnect()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.dispose"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.dispose()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.disposing"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.disposing</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.encodestr"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.encodestr()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.errormessage_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.errormessage_callback</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.lookup_metadata"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.lookup_metadata()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.metadatacache"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.metadatacache</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.metadatareceived_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.metadatareceived_callback</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.newbufferblocks_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.newbufferblocks_callback</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.newmeasurements_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.newmeasurements_callback</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.notificationreceived_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.notificationreceived_callback</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.processingcomplete_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.processingcomplete_callback</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.send_servercommand"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.send_servercommand()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.send_servercommand_withmessage"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.send_servercommand_withmessage()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.socket_timeout"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.socket_timeout</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.statusmessage_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.statusmessage_callback</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.sttp_sourceinfo"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.sttp_sourceinfo</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.sttp_updatedoninfo"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.sttp_updatedoninfo</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.sttp_versioninfo"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.sttp_versioninfo</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.subscribe"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.subscribe()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.subscribed"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.subscribed</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.subscriberid"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.subscriberid</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.subscription"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.subscription</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.subscriptionupdated_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.subscriptionupdated_callback</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.total_commandchannel_bytesreceived"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.total_commandchannel_bytesreceived</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.total_datachannel_bytesreceived"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.total_datachannel_bytesreceived</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.total_measurementsreceived"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.total_measurementsreceived</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.unsubscribe"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.unsubscribe()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.datasubscriber.DataSubscriber.version"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.version</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.transport.html#module-sttp.transport.measurement">sttp.transport.measurement module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.html#sttp.transport.measurement.Measurement"><code class="docutils literal notranslate"><span class="pre">Measurement</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.measurement.Measurement.DEFAULT_FLAGS"><code class="docutils literal notranslate"><span class="pre">Measurement.DEFAULT_FLAGS</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.measurement.Measurement.DEFAULT_SIGNALID"><code class="docutils literal notranslate"><span class="pre">Measurement.DEFAULT_SIGNALID</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.measurement.Measurement.DEFAULT_TIMESTAMP"><code class="docutils literal notranslate"><span class="pre">Measurement.DEFAULT_TIMESTAMP</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.measurement.Measurement.DEFAULT_VALUE"><code class="docutils literal notranslate"><span class="pre">Measurement.DEFAULT_VALUE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.measurement.Measurement.datetime"><code class="docutils literal notranslate"><span class="pre">Measurement.datetime</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.measurement.Measurement.flags"><code class="docutils literal notranslate"><span class="pre">Measurement.flags</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.measurement.Measurement.signalid"><code class="docutils literal notranslate"><span class="pre">Measurement.signalid</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.measurement.Measurement.timestamp"><code class="docutils literal notranslate"><span class="pre">Measurement.timestamp</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.measurement.Measurement.timestampvalue"><code class="docutils literal notranslate"><span class="pre">Measurement.timestampvalue</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.measurement.Measurement.value"><code class="docutils literal notranslate"><span class="pre">Measurement.value</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.transport.html#module-sttp.transport.signalindexcache">sttp.transport.signalindexcache module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalindexcache.SignalIndexCache"><code class="docutils literal notranslate"><span class="pre">SignalIndexCache</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalindexcache.SignalIndexCache.contains"><code class="docutils literal notranslate"><span class="pre">SignalIndexCache.contains()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalindexcache.SignalIndexCache.count"><code class="docutils literal notranslate"><span class="pre">SignalIndexCache.count</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalindexcache.SignalIndexCache.decode"><code class="docutils literal notranslate"><span class="pre">SignalIndexCache.decode()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalindexcache.SignalIndexCache.id"><code class="docutils literal notranslate"><span class="pre">SignalIndexCache.id()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalindexcache.SignalIndexCache.record"><code class="docutils literal notranslate"><span class="pre">SignalIndexCache.record()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalindexcache.SignalIndexCache.signalid"><code class="docutils literal notranslate"><span class="pre">SignalIndexCache.signalid()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalindexcache.SignalIndexCache.signalids"><code class="docutils literal notranslate"><span class="pre">SignalIndexCache.signalids</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalindexcache.SignalIndexCache.signalindex"><code class="docutils literal notranslate"><span class="pre">SignalIndexCache.signalindex()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalindexcache.SignalIndexCache.source"><code class="docutils literal notranslate"><span class="pre">SignalIndexCache.source()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.transport.html#module-sttp.transport.signalkind">sttp.transport.signalkind module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalkind.SignalKind"><code class="docutils literal notranslate"><span class="pre">SignalKind</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalkind.SignalKind.ALARM"><code class="docutils literal notranslate"><span class="pre">SignalKind.ALARM</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalkind.SignalKind.ANALOG"><code class="docutils literal notranslate"><span class="pre">SignalKind.ANALOG</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalkind.SignalKind.ANGLE"><code class="docutils literal notranslate"><span class="pre">SignalKind.ANGLE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalkind.SignalKind.CALCULATION"><code class="docutils literal notranslate"><span class="pre">SignalKind.CALCULATION</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalkind.SignalKind.DFDT"><code class="docutils literal notranslate"><span class="pre">SignalKind.DFDT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalkind.SignalKind.DIGITAL"><code class="docutils literal notranslate"><span class="pre">SignalKind.DIGITAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalkind.SignalKind.FREQUENCY"><code class="docutils literal notranslate"><span class="pre">SignalKind.FREQUENCY</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalkind.SignalKind.MAGNITUDE"><code class="docutils literal notranslate"><span class="pre">SignalKind.MAGNITUDE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalkind.SignalKind.QUALITY"><code class="docutils literal notranslate"><span class="pre">SignalKind.QUALITY</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalkind.SignalKind.STATISTIC"><code class="docutils literal notranslate"><span class="pre">SignalKind.STATISTIC</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalkind.SignalKind.STATUS"><code class="docutils literal notranslate"><span class="pre">SignalKind.STATUS</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalkind.SignalKind.UNKNOWN"><code class="docutils literal notranslate"><span class="pre">SignalKind.UNKNOWN</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalkind.SignalKindEnum"><code class="docutils literal notranslate"><span class="pre">SignalKindEnum</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalkind.SignalKindEnum.acronym"><code class="docutils literal notranslate"><span class="pre">SignalKindEnum.acronym()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalkind.SignalKindEnum.parse_acronym"><code class="docutils literal notranslate"><span class="pre">SignalKindEnum.parse_acronym()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.signalkind.SignalKindEnum.signaltype"><code class="docutils literal notranslate"><span class="pre">SignalKindEnum.signaltype()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.transport.html#module-sttp.transport.subscriberconnector">sttp.transport.subscriberconnector module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriberconnector.SubscriberConnector"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_AUTORECONNECT"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.DEFAULT_AUTORECONNECT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_ERRORMESSAGE_CALLBACK"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.DEFAULT_ERRORMESSAGE_CALLBACK()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_HOSTNAME"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.DEFAULT_HOSTNAME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_MAXRETRIES"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.DEFAULT_MAXRETRIES</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_MAXRETRYINTERVAL"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.DEFAULT_MAXRETRYINTERVAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_PORT"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.DEFAULT_PORT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_RECONNECT_CALLBACK"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.DEFAULT_RECONNECT_CALLBACK()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_RETRYINTERVAL"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.DEFAULT_RETRYINTERVAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriberconnector.SubscriberConnector.autoreconnect"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.autoreconnect</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriberconnector.SubscriberConnector.cancel"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.cancel()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriberconnector.SubscriberConnector.connect"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.connect()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriberconnector.SubscriberConnector.dispose"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.dispose()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriberconnector.SubscriberConnector.errormessage_callback"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.errormessage_callback</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriberconnector.SubscriberConnector.hostname"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.hostname</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriberconnector.SubscriberConnector.maxretries"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.maxretries</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriberconnector.SubscriberConnector.maxretryinterval"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.maxretryinterval</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriberconnector.SubscriberConnector.port"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.port</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriberconnector.SubscriberConnector.reconnect_callback"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.reconnect_callback</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriberconnector.SubscriberConnector.reset_connection"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.reset_connection()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriberconnector.SubscriberConnector.retryinterval"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.retryinterval</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.transport.html#module-sttp.transport.subscriptioninfo">sttp.transport.subscriptioninfo module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_CONSTRAINTPARAMETERS"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_CONSTRAINTPARAMETERS</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_DATACHANNEL_INTERFACE"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_DATACHANNEL_INTERFACE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_DATACHANNEL_LOCALPORT"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_DATACHANNEL_LOCALPORT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_ENABLE_TIME_REASONABILITY_CHECK"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_ENABLE_TIME_REASONABILITY_CHECK</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_EXTRA_CONNECTIONSTRING_PARAMETERS"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_EXTRA_CONNECTIONSTRING_PARAMETERS</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_FILTEREXPRESSION"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_FILTEREXPRESSION</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_INCLUDETIME"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_INCLUDETIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_LAGTIME"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_LAGTIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_LEADTIME"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_LEADTIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_PROCESSINGINTERVAL"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_PROCESSINGINTERVAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_PUBLISHINTERVAL"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_PUBLISHINTERVAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_REQUEST_NANVALUEFILTER"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_REQUEST_NANVALUEFILTER</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_STARTTIME"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_STARTTIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_STOPTIME"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_STOPTIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_THROTTLED"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_THROTTLED</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_UDPDATACHANNEL"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_UDPDATACHANNEL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_USE_LOCALCLOCK_AS_REALTIME"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_USE_LOCALCLOCK_AS_REALTIME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_USE_MILLISECONDRESOLUTION"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_USE_MILLISECONDRESOLUTION</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.constraintparameters"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.constraintparameters</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.datachannel_interface"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.datachannel_interface</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.datachannel_localport"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.datachannel_localport</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.enabletimereasonabilitycheck"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.enabletimereasonabilitycheck</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.extra_connectionstring_parameters"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.extra_connectionstring_parameters</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.filterexpression"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.filterexpression</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.includetime"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.includetime</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.lagtime"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.lagtime</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.leadtime"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.leadtime</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.processinginterval"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.processinginterval</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.publishinterval"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.publishinterval</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.request_nanvaluefilter"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.request_nanvaluefilter</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.starttime"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.starttime</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.stoptime"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.stoptime</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.throttled"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.throttled</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.udpdatachannel"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.udpdatachannel</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.use_millisecondresolution"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.use_millisecondresolution</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.html#sttp.transport.subscriptioninfo.SubscriptionInfo.uselocalclockasrealtime"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.uselocalclockasrealtime</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.transport.html#module-sttp.transport">Module contents</a></li>
</ul>
</li>
</ul>
</div>
</section>
<section id="submodules">
<h2>Submodules<a class="headerlink" href="#submodules" title="Link to this heading"></a></h2>
</section>
<section id="module-sttp.config">
<span id="sttp-config-module"></span><h2>sttp.config module<a class="headerlink" href="#module-sttp.config" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.config.Config">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.config.</span></span><span class="sig-name descname"><span class="pre">Config</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">maxretries</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">retryinterval</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">float</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maxretryinterval</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">float</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">autoreconnect</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">autorequestmetadata</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">autosubscribe</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">compress_payloaddata</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">compress_metadata</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">compress_signalindexcache</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">metadatafilters</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">socket_timeout</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">float</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">version</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int8</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/config.html#Config"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.config.Config" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Defines the STTP connection related configuration parameters.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.DEFAULT_AUTORECONNECT">
<span class="sig-name descname"><span class="pre">DEFAULT_AUTORECONNECT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">True</span></em><a class="headerlink" href="#sttp.config.Config.DEFAULT_AUTORECONNECT" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.DEFAULT_AUTOREQUESTMETADATA">
<span class="sig-name descname"><span class="pre">DEFAULT_AUTOREQUESTMETADATA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">True</span></em><a class="headerlink" href="#sttp.config.Config.DEFAULT_AUTOREQUESTMETADATA" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.DEFAULT_AUTOSUBSCRIBE">
<span class="sig-name descname"><span class="pre">DEFAULT_AUTOSUBSCRIBE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">True</span></em><a class="headerlink" href="#sttp.config.Config.DEFAULT_AUTOSUBSCRIBE" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.DEFAULT_COMPRESS_METADATA">
<span class="sig-name descname"><span class="pre">DEFAULT_COMPRESS_METADATA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">True</span></em><a class="headerlink" href="#sttp.config.Config.DEFAULT_COMPRESS_METADATA" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.DEFAULT_COMPRESS_PAYLOADDATA">
<span class="sig-name descname"><span class="pre">DEFAULT_COMPRESS_PAYLOADDATA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">True</span></em><a class="headerlink" href="#sttp.config.Config.DEFAULT_COMPRESS_PAYLOADDATA" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.DEFAULT_COMPRESS_SIGNALINDEXCACHE">
<span class="sig-name descname"><span class="pre">DEFAULT_COMPRESS_SIGNALINDEXCACHE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">True</span></em><a class="headerlink" href="#sttp.config.Config.DEFAULT_COMPRESS_SIGNALINDEXCACHE" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.DEFAULT_MAXRETRIES">
<span class="sig-name descname"><span class="pre">DEFAULT_MAXRETRIES</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">-1</span></em><a class="headerlink" href="#sttp.config.Config.DEFAULT_MAXRETRIES" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.DEFAULT_MAXRETRYINTERVAL">
<span class="sig-name descname"><span class="pre">DEFAULT_MAXRETRYINTERVAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">30.0</span></em><a class="headerlink" href="#sttp.config.Config.DEFAULT_MAXRETRYINTERVAL" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.DEFAULT_METADATAFILTERS">
<span class="sig-name descname"><span class="pre">DEFAULT_METADATAFILTERS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.config.Config.DEFAULT_METADATAFILTERS" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.DEFAULT_RETRYINTERVAL">
<span class="sig-name descname"><span class="pre">DEFAULT_RETRYINTERVAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1.0</span></em><a class="headerlink" href="#sttp.config.Config.DEFAULT_RETRYINTERVAL" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.DEFAULT_SOCKET_TIMEOUT">
<span class="sig-name descname"><span class="pre">DEFAULT_SOCKET_TIMEOUT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2.0</span></em><a class="headerlink" href="#sttp.config.Config.DEFAULT_SOCKET_TIMEOUT" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.DEFAULT_VERSION">
<span class="sig-name descname"><span class="pre">DEFAULT_VERSION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.int8(2)</span></em><a class="headerlink" href="#sttp.config.Config.DEFAULT_VERSION" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.autoreconnect">
<span class="sig-name descname"><span class="pre">autoreconnect</span></span><a class="headerlink" href="#sttp.config.Config.autoreconnect" title="Link to this definition"></a></dt>
<dd><p>Defines flag that determines if connections should be automatically reattempted.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.autorequestmetadata">
<span class="sig-name descname"><span class="pre">autorequestmetadata</span></span><a class="headerlink" href="#sttp.config.Config.autorequestmetadata" title="Link to this definition"></a></dt>
<dd><p>Defines the flag that determines if metadata should be automatically requested
upon successful connection. When True, metadata will be requested upon connection
before subscription; otherwise, any metadata operations must be handled manually.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.autosubscribe">
<span class="sig-name descname"><span class="pre">autosubscribe</span></span><a class="headerlink" href="#sttp.config.Config.autosubscribe" title="Link to this definition"></a></dt>
<dd><p>Defines the flag that determines if subscription should be handled automatically
upon successful connection. When <cite>autorequestmetadata</cite> is True and
<cite>autosubscribe</cite> is True, subscription will occur after reception of metadata.
When <cite>autorequestmetadata</cite> is False and <cite>autosubscribe</cite> is True, subscription
will occur at successful connection. When <cite>autosubscribe</cite> is False, any
subscribe operations must be handled manually.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.compress_metadata">
<span class="sig-name descname"><span class="pre">compress_metadata</span></span><a class="headerlink" href="#sttp.config.Config.compress_metadata" title="Link to this definition"></a></dt>
<dd><p>Determines whether the metadata transfer is compressed.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.compress_payloaddata">
<span class="sig-name descname"><span class="pre">compress_payloaddata</span></span><a class="headerlink" href="#sttp.config.Config.compress_payloaddata" title="Link to this definition"></a></dt>
<dd><p>Determines whether payload data is compressed.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.compress_signalindexcache">
<span class="sig-name descname"><span class="pre">compress_signalindexcache</span></span><a class="headerlink" href="#sttp.config.Config.compress_signalindexcache" title="Link to this definition"></a></dt>
<dd><p>Determines whether the signal index cache is compressed.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.maxretries">
<span class="sig-name descname"><span class="pre">maxretries</span></span><a class="headerlink" href="#sttp.config.Config.maxretries" title="Link to this definition"></a></dt>
<dd><p>Defines the maximum number of times to retry a connection.
Set value to -1 to retry infinitely.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.maxretryinterval">
<span class="sig-name descname"><span class="pre">maxretryinterval</span></span><a class="headerlink" href="#sttp.config.Config.maxretryinterval" title="Link to this definition"></a></dt>
<dd><p>Defines the maximum retry interval, in seconds.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.metadatafilters">
<span class="sig-name descname"><span class="pre">metadatafilters</span></span><a class="headerlink" href="#sttp.config.Config.metadatafilters" title="Link to this definition"></a></dt>
<dd><p>Defines any filters to be applied to incoming metadata to reduce total received metadata.
Each filter expression should be separated by semi-colon.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.retryinterval">
<span class="sig-name descname"><span class="pre">retryinterval</span></span><a class="headerlink" href="#sttp.config.Config.retryinterval" title="Link to this definition"></a></dt>
<dd><p>Defines the base retry interval, in seconds. Retries will exponentially back-off
starting from this interval.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.socket_timeout">
<span class="sig-name descname"><span class="pre">socket_timeout</span></span><a class="headerlink" href="#sttp.config.Config.socket_timeout" title="Link to this definition"></a></dt>
<dd><p>Defines the timeout in seconds for all socket connections.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.config.Config.version">
<span class="sig-name descname"><span class="pre">version</span></span><a class="headerlink" href="#sttp.config.Config.version" title="Link to this definition"></a></dt>
<dd><p>Defines the target STTP protocol version. This currently defaults to 2.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.reader">
<span id="sttp-reader-module"></span><h2>sttp.reader module<a class="headerlink" href="#module-sttp.reader" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.reader.MeasurementReader">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.reader.</span></span><span class="sig-name descname"><span class="pre">MeasurementReader</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">subscriber</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.subscriber.Subscriber" title="sttp.subscriber.Subscriber"><span class="pre">Subscriber</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/reader.html#MeasurementReader"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.reader.MeasurementReader" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Defines an STTP measurement reader.</p>
<dl class="py method">
<dt class="sig sig-object py" id="sttp.reader.MeasurementReader.dispose">
<span class="sig-name descname"><span class="pre">dispose</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/reader.html#MeasurementReader.dispose"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.reader.MeasurementReader.dispose" title="Link to this definition"></a></dt>
<dd><p>Cleanly shuts down a <cite>MeasurmentReader</cite> that is no longer being used.
This method will release any waiting threads.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.reader.MeasurementReader.next_measurement">
<span class="sig-name descname"><span class="pre">next_measurement</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="sttp.transport.html#sttp.transport.measurement.Measurement" title="sttp.transport.measurement.Measurement"><span class="pre">Measurement</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/reader.html#MeasurementReader.next_measurement"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.reader.MeasurementReader.next_measurement" title="Link to this definition"></a></dt>
<dd><p>Blocks current thread until a new measurement arrived.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.settings">
<span id="sttp-settings-module"></span><h2>sttp.settings module<a class="headerlink" href="#module-sttp.settings" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.settings.Settings">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.settings.</span></span><span class="sig-name descname"><span class="pre">Settings</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">throttled</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">publishinterval</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">float</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">udpport</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint16</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">udpinterface</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">includetime</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">enabletimereasonabilitycheck</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lagtime</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">float64</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">leadtime</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">float64</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">uselocalclockasrealtime</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usemillisecondresolution</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">requestnanvaluefilter</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">starttime</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stoptime</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">constraintparameters</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">processinginterval</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">extra_connectionstring_parameters</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/settings.html#Settings"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.settings.Settings" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Defines the STTP subscription related settings.</p>
<p class="rubric">Notes</p>
<p>The <cite>Settings</cite> class exists as a simplified implementation of the <cite>SubscriptionInfo</cite>
class found in the <cite>transport</cite> namespace. Internally, the <cite>Subscriber</cite> class maps
<cite>Settings</cite> values to a <cite>SubscriptionInfo</cite> instance for use with a <cite>DataSubscriber</cite>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.DEFAULT_CONSTRAINTPARAMETERS">
<span class="sig-name descname"><span class="pre">DEFAULT_CONSTRAINTPARAMETERS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.settings.Settings.DEFAULT_CONSTRAINTPARAMETERS" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.DEFAULT_ENABLE_TIME_REASONABILITY_CHECK">
<span class="sig-name descname"><span class="pre">DEFAULT_ENABLE_TIME_REASONABILITY_CHECK</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">False</span></em><a class="headerlink" href="#sttp.settings.Settings.DEFAULT_ENABLE_TIME_REASONABILITY_CHECK" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.DEFAULT_EXTRA_CONNECTIONSTRING_PARAMETERS">
<span class="sig-name descname"><span class="pre">DEFAULT_EXTRA_CONNECTIONSTRING_PARAMETERS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.settings.Settings.DEFAULT_EXTRA_CONNECTIONSTRING_PARAMETERS" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.DEFAULT_INCLUDETIME">
<span class="sig-name descname"><span class="pre">DEFAULT_INCLUDETIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">True</span></em><a class="headerlink" href="#sttp.settings.Settings.DEFAULT_INCLUDETIME" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.DEFAULT_LAGTIME">
<span class="sig-name descname"><span class="pre">DEFAULT_LAGTIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.float64(10.0)</span></em><a class="headerlink" href="#sttp.settings.Settings.DEFAULT_LAGTIME" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.DEFAULT_LEADTIME">
<span class="sig-name descname"><span class="pre">DEFAULT_LEADTIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.float64(5.0)</span></em><a class="headerlink" href="#sttp.settings.Settings.DEFAULT_LEADTIME" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.DEFAULT_PROCESSINGINTERVAL">
<span class="sig-name descname"><span class="pre">DEFAULT_PROCESSINGINTERVAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.int32(-1)</span></em><a class="headerlink" href="#sttp.settings.Settings.DEFAULT_PROCESSINGINTERVAL" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.DEFAULT_PUBLISHINTERVAL">
<span class="sig-name descname"><span class="pre">DEFAULT_PUBLISHINTERVAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.float64(1.0)</span></em><a class="headerlink" href="#sttp.settings.Settings.DEFAULT_PUBLISHINTERVAL" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.DEFAULT_REQUEST_NANVALUEFILTER">
<span class="sig-name descname"><span class="pre">DEFAULT_REQUEST_NANVALUEFILTER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">False</span></em><a class="headerlink" href="#sttp.settings.Settings.DEFAULT_REQUEST_NANVALUEFILTER" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.DEFAULT_STARTTIME">
<span class="sig-name descname"><span class="pre">DEFAULT_STARTTIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.settings.Settings.DEFAULT_STARTTIME" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.DEFAULT_STOPTIME">
<span class="sig-name descname"><span class="pre">DEFAULT_STOPTIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.settings.Settings.DEFAULT_STOPTIME" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.DEFAULT_THROTTLED">
<span class="sig-name descname"><span class="pre">DEFAULT_THROTTLED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">False</span></em><a class="headerlink" href="#sttp.settings.Settings.DEFAULT_THROTTLED" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.DEFAULT_UDPINTERFACE">
<span class="sig-name descname"><span class="pre">DEFAULT_UDPINTERFACE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.settings.Settings.DEFAULT_UDPINTERFACE" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.DEFAULT_UDPPORT">
<span class="sig-name descname"><span class="pre">DEFAULT_UDPPORT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.uint16(0)</span></em><a class="headerlink" href="#sttp.settings.Settings.DEFAULT_UDPPORT" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.DEFAULT_USE_LOCALCLOCK_AS_REALTIME">
<span class="sig-name descname"><span class="pre">DEFAULT_USE_LOCALCLOCK_AS_REALTIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">False</span></em><a class="headerlink" href="#sttp.settings.Settings.DEFAULT_USE_LOCALCLOCK_AS_REALTIME" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.DEFAULT_USE_MILLISECONDRESOLUTION">
<span class="sig-name descname"><span class="pre">DEFAULT_USE_MILLISECONDRESOLUTION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">False</span></em><a class="headerlink" href="#sttp.settings.Settings.DEFAULT_USE_MILLISECONDRESOLUTION" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.constraintparameters">
<span class="sig-name descname"><span class="pre">constraintparameters</span></span><a class="headerlink" href="#sttp.settings.Settings.constraintparameters" title="Link to this definition"></a></dt>
<dd><p>Defines any custom constraint parameters for a requested temporal data playback. This can
include parameters that may be needed to initiate, filter, or control historical data access.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.enabletimereasonabilitycheck">
<span class="sig-name descname"><span class="pre">enabletimereasonabilitycheck</span></span><a class="headerlink" href="#sttp.settings.Settings.enabletimereasonabilitycheck" title="Link to this definition"></a></dt>
<dd><p>Determines  if publisher should perform time reasonability checks.
When enabled <cite>lagtime</cite> and <cite>leadtime</cite> will be used to determine if a measurement timestamp is reasonable.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.extra_connectionstring_parameters">
<span class="sig-name descname"><span class="pre">extra_connectionstring_parameters</span></span><a class="headerlink" href="#sttp.settings.Settings.extra_connectionstring_parameters" title="Link to this definition"></a></dt>
<dd><p>Defines any extra custom connection string parameters that may be needed for a subscription.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.includetime">
<span class="sig-name descname"><span class="pre">includetime</span></span><a class="headerlink" href="#sttp.settings.Settings.includetime" title="Link to this definition"></a></dt>
<dd><p>Determines if time should be included in non-compressed, compact measurements.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.lagtime">
<span class="sig-name descname"><span class="pre">lagtime</span></span><a class="headerlink" href="#sttp.settings.Settings.lagtime" title="Link to this definition"></a></dt>
<dd><p>Defines defines the allowed past time deviation tolerance in seconds (can be sub-second).
Value is used to determine if a measurement timestamp is reasonable.
Only applicable when <cite>enabletimereasonabilitycheck</cite> is <cite>true</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.leadtime">
<span class="sig-name descname"><span class="pre">leadtime</span></span><a class="headerlink" href="#sttp.settings.Settings.leadtime" title="Link to this definition"></a></dt>
<dd><p>Defines defines the allowed future time deviation tolerance in seconds (can be sub-second).
Value is used to determine if a measurement timestamp is reasonable.
Only applicable when <cite>enabletimereasonabilitycheck</cite> is <cite>true</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.processinginterval">
<span class="sig-name descname"><span class="pre">processinginterval</span></span><a class="headerlink" href="#sttp.settings.Settings.processinginterval" title="Link to this definition"></a></dt>
<dd><p>Defines the initial playback speed, in milliseconds, for a requested temporal data playback.
With the exception of the values of -1 and 0, this value specifies the desired processing interval for data, i.e.,
basically a delay, or timer interval, over which to process data. A value of -1 means to use the default processing
interval while a value of 0 means to process data as fast as possible.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.publishinterval">
<span class="sig-name descname"><span class="pre">publishinterval</span></span><a class="headerlink" href="#sttp.settings.Settings.publishinterval" title="Link to this definition"></a></dt>
<dd><p>Defines the down-sampling publish interval, in seconds, to use when <cite>throttled</cite> is True.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.request_nanvaluefilter">
<span class="sig-name descname"><span class="pre">request_nanvaluefilter</span></span><a class="headerlink" href="#sttp.settings.Settings.request_nanvaluefilter" title="Link to this definition"></a></dt>
<dd><p>Requests that the publisher filter, i.e., does not send, any NaN values.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.starttime">
<span class="sig-name descname"><span class="pre">starttime</span></span><a class="headerlink" href="#sttp.settings.Settings.starttime" title="Link to this definition"></a></dt>
<dd><p>Defines the start time for a requested temporal data playback, i.e., a historical subscription.
Simply by specifying a <cite>starttime</cite> and <cite>stoptime</cite>, a subscription is considered a historical subscription.
Note that the publisher may not support historical subscriptions, in which case the subscribe will fail.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.stoptime">
<span class="sig-name descname"><span class="pre">stoptime</span></span><a class="headerlink" href="#sttp.settings.Settings.stoptime" title="Link to this definition"></a></dt>
<dd><p>Defines the stop time for a requested temporal data playback, i.e., a historical subscription.
Simply by specifying a <cite>starttime</cite> and <cite>stoptime</cite>, a subscription is considered a historical subscription.
Note that the publisher may not support historical subscriptions, in which case the subscribe will fail.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.throttled">
<span class="sig-name descname"><span class="pre">throttled</span></span><a class="headerlink" href="#sttp.settings.Settings.throttled" title="Link to this definition"></a></dt>
<dd><p>Determines if data will be published using down-sampling.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.udpinterface">
<span class="sig-name descname"><span class="pre">udpinterface</span></span><a class="headerlink" href="#sttp.settings.Settings.udpinterface" title="Link to this definition"></a></dt>
<dd><p>Defines the desired UDP binding interface to use for publication. Empty string means to bind
to all interfaces.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.udpport">
<span class="sig-name descname"><span class="pre">udpport</span></span><a class="headerlink" href="#sttp.settings.Settings.udpport" title="Link to this definition"></a></dt>
<dd><p>Defines the desired UDP port to use for publication. Zero value means do not receive data on UDP,
i.e., data will be delivered to the STTP client via TCP.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.use_millisecondresolution">
<span class="sig-name descname"><span class="pre">use_millisecondresolution</span></span><a class="headerlink" href="#sttp.settings.Settings.use_millisecondresolution" title="Link to this definition"></a></dt>
<dd><p>Determines if time should be restricted to milliseconds in non-compressed, compact measurements.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.settings.Settings.uselocalclockasrealtime">
<span class="sig-name descname"><span class="pre">uselocalclockasrealtime</span></span><a class="headerlink" href="#sttp.settings.Settings.uselocalclockasrealtime" title="Link to this definition"></a></dt>
<dd><p>Determines if publisher should use local clock as real time. If false,
the timestamp of the latest measurement will be used as real-time.
Only applicable when <cite>enabletimereasonabilitycheck</cite> is <cite>true</cite>.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.subscriber">
<span id="sttp-subscriber-module"></span><h2>sttp.subscriber module<a class="headerlink" href="#module-sttp.subscriber" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.subscriber.</span></span><span class="sig-name descname"><span class="pre">Subscriber</span></span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Represents an STTP data subscriber.</p>
<p class="rubric">Notes</p>
<p>The <cite>Subscriber</cite> class exists as a simplified implementation of the <cite>DataSubscriber</cite>
class found in the <cite>transport</cite> namespace. This class maintains an internal instance
of the <cite>DataSubscriber</cite> class for subscription based functionality and is intended
to simplify common uses of STTP data reception.</p>
<dl class="py property">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.activesignalindexcache">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">activesignalindexcache</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="sttp.transport.html#sttp.transport.signalindexcache.SignalIndexCache" title="sttp.transport.signalindexcache.SignalIndexCache"><span class="pre">SignalIndexCache</span></a></em><a class="headerlink" href="#sttp.subscriber.Subscriber.activesignalindexcache" title="Link to this definition"></a></dt>
<dd><p>Gets the active signal index cache.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.adjustedvalue">
<span class="sig-name descname"><span class="pre">adjustedvalue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">measurement</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="sttp.transport.html#sttp.transport.measurement.Measurement" title="sttp.transport.measurement.Measurement"><span class="pre">Measurement</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">float64</span></span></span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.adjustedvalue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.adjustedvalue" title="Link to this definition"></a></dt>
<dd><p>Gets the Value of a <cite>Measurement</cite> with any linear adjustments applied from the
measurement’s Adder and Multiplier metadata, if found.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.connect">
<span class="sig-name descname"><span class="pre">connect</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">address</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">config</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.config.Config" title="sttp.config.Config"><span class="pre">Config</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.connect"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.connect" title="Link to this definition"></a></dt>
<dd><p>Starts the client-based connection cycle to an STTP publisher. Config parameter controls
connection related settings. When the config defines <cite>AutoReconnect</cite> as True, the connection
will automatically be retried when the connection drops. If the config parameter defines
<cite>AutoRequestMetadata</cite> as True, then upon successful connection, meta-data will be requested.
When the config defines both <cite>AutoRequestMetadata</cite> and <cite>AutoSubscribe</cite> as True, subscription
will occur after reception of metadata. When the config defines <cite>AutoRequestMetadata</cite> as
False and <cite>AutoSubscribe</cite> as True, subscription will occur at successful connection.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.connected">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">connected</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">bool</span></em><a class="headerlink" href="#sttp.subscriber.Subscriber.connected" title="Link to this definition"></a></dt>
<dd><p>Gets flag that determines if <cite>Subscriber</cite> is currently connected to a data publisher.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.default_connectionestablished_receiver">
<span class="sig-name descname"><span class="pre">default_connectionestablished_receiver</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.default_connectionestablished_receiver"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.default_connectionestablished_receiver" title="Link to this definition"></a></dt>
<dd><p>Implements the default handler for the connection established callback.
Default implementation simply writes connection feedback to status message callback.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.default_connectionterminated_receiver">
<span class="sig-name descname"><span class="pre">default_connectionterminated_receiver</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.default_connectionterminated_receiver"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.default_connectionterminated_receiver" title="Link to this definition"></a></dt>
<dd><p>Implements the default handler for the connection terminated callback.
Default implementation simply writes connection terminated feedback to error message callback.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.default_errormessage_logger">
<span class="sig-name descname"><span class="pre">default_errormessage_logger</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.default_errormessage_logger"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.default_errormessage_logger" title="Link to this definition"></a></dt>
<dd><p>Implements the default handler for the error message callback.
Default implementation synchronously writes output to stderr.
Logging is recommended.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.default_statusmessage_logger">
<span class="sig-name descname"><span class="pre">default_statusmessage_logger</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.default_statusmessage_logger"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.default_statusmessage_logger" title="Link to this definition"></a></dt>
<dd><p>Implements the default handler for the status message callback.
Default implementation synchronously writes output to stdio.
Logging is recommended.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.disconnect">
<span class="sig-name descname"><span class="pre">disconnect</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.disconnect"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.disconnect" title="Link to this definition"></a></dt>
<dd><p>Disconnects from an STTP publisher.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.dispose">
<span class="sig-name descname"><span class="pre">dispose</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.dispose"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.dispose" title="Link to this definition"></a></dt>
<dd><p>Cleanly shuts down a <cite>Subscriber</cite> that is no longer being used, e.g.,
during a normal application exit.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.errormessage">
<span class="sig-name descname"><span class="pre">errormessage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.errormessage"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.errormessage" title="Link to this definition"></a></dt>
<dd><p>Executes the defined error message logger callback.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.measurement_metadata">
<span class="sig-name descname"><span class="pre">measurement_metadata</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">measurement</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="sttp.transport.html#sttp.transport.measurement.Measurement" title="sttp.transport.measurement.Measurement"><span class="pre">Measurement</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord" title="sttp.metadata.record.measurement.MeasurementRecord"><span class="pre">MeasurementRecord</span></a></span></span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.measurement_metadata"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.measurement_metadata" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>MeasurementRecord</cite> for the specified measurement, based on its signal ID,
from the local metadata cache; or, None if the measurement cannot be found.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.metadatacache">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">metadatacache</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="sttp.metadata.html#sttp.metadata.cache.MetadataCache" title="sttp.metadata.cache.MetadataCache"><span class="pre">MetadataCache</span></a></em><a class="headerlink" href="#sttp.subscriber.Subscriber.metadatacache" title="Link to this definition"></a></dt>
<dd><p>Gets the current metadata cache.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.read_measurements">
<span class="sig-name descname"><span class="pre">read_measurements</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="#sttp.reader.MeasurementReader" title="sttp.reader.MeasurementReader"><span class="pre">MeasurementReader</span></a></span></span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.read_measurements"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.read_measurements" title="Link to this definition"></a></dt>
<dd><p>Sets up a new <cite>MeasurementReader</cite> to start reading measurements.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.request_metadata">
<span class="sig-name descname"><span class="pre">request_metadata</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.request_metadata"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.request_metadata" title="Link to this definition"></a></dt>
<dd><p>Sends a request to the data publisher indicating that the <cite>Subscriber</cite> would
like new metadata. Any defined MetadataFilters will be included in request.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.set_configurationchanged_receiver">
<span class="sig-name descname"><span class="pre">set_configurationchanged_receiver</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">callback</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.set_configurationchanged_receiver"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.set_configurationchanged_receiver" title="Link to this definition"></a></dt>
<dd><p>Defines the callback that handles notifications that the data publisher configuration has changed.
Assignment will take effect immediately, even while subscription is active.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.set_connectionestablished_receiver">
<span class="sig-name descname"><span class="pre">set_connectionestablished_receiver</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">callback</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.set_connectionestablished_receiver"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.set_connectionestablished_receiver" title="Link to this definition"></a></dt>
<dd><p>Defines the callback that handles notification that a connection has been established.
Default implementation simply writes connection feedback to status message handler.
Assignment will take effect immediately, even while subscription is active.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.set_connectionterminated_receiver">
<span class="sig-name descname"><span class="pre">set_connectionterminated_receiver</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">callback</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.set_connectionterminated_receiver"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.set_connectionterminated_receiver" title="Link to this definition"></a></dt>
<dd><p>Defines the callback that handles notification that a connection has been terminated.
Default implementation simply writes connection terminated feedback to error message handler.
Assignment will take effect immediately, even while subscription is active.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.set_data_starttime_receiver">
<span class="sig-name descname"><span class="pre">set_data_starttime_receiver</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">callback</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">int64</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.set_data_starttime_receiver"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.set_data_starttime_receiver" title="Link to this definition"></a></dt>
<dd><p>Defines the callback that handles notification of first received measurement.
Assignment will take effect immediately, even while subscription is active.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.set_errormessage_logger">
<span class="sig-name descname"><span class="pre">set_errormessage_logger</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">callback</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.set_errormessage_logger"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.set_errormessage_logger" title="Link to this definition"></a></dt>
<dd><p>Defines the callback that handles error message logging.
Assignment will take effect immediately, even while subscription is active.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.set_historicalreadcomplete_receiver">
<span class="sig-name descname"><span class="pre">set_historicalreadcomplete_receiver</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">callback</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.set_historicalreadcomplete_receiver"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.set_historicalreadcomplete_receiver" title="Link to this definition"></a></dt>
<dd><p>Defines the callback that handles notification that temporal processing has completed, i.e.,
the end of a historical playback data stream has been reached.
Assignment will take effect immediately, even while subscription is active.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.set_metadatanotification_receiver">
<span class="sig-name descname"><span class="pre">set_metadatanotification_receiver</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">callback</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="sttp.data.html#sttp.data.dataset.DataSet" title="sttp.data.dataset.DataSet"><span class="pre">DataSet</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.set_metadatanotification_receiver"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.set_metadatanotification_receiver" title="Link to this definition"></a></dt>
<dd><p>Defines the callback that handles reception of the metadata received notification response.
Receiver parameter defines full XML response received from publisher.
Parsed metadata available via <cite>Subscriber.metadatacache</cite> property.
Assignment will take effect immediately, even while subscription is active.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.set_newbufferblock_receiver">
<span class="sig-name descname"><span class="pre">set_newbufferblock_receiver</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">callback</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="sttp.transport.html#sttp.transport.bufferblock.BufferBlock" title="sttp.transport.bufferblock.BufferBlock"><span class="pre">BufferBlock</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.set_newbufferblock_receiver"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.set_newbufferblock_receiver" title="Link to this definition"></a></dt>
<dd><p>Defines the callback that handles reception of new buffer blocks.
Assignment will take effect immediately, even while subscription is active.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.set_newmeasurements_receiver">
<span class="sig-name descname"><span class="pre">set_newmeasurements_receiver</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">callback</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="sttp.transport.html#sttp.transport.measurement.Measurement" title="sttp.transport.measurement.Measurement"><span class="pre">Measurement</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.set_newmeasurements_receiver"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.set_newmeasurements_receiver" title="Link to this definition"></a></dt>
<dd><p>Defines the callback that handles reception of new measurements.
Assignment will take effect immediately, even while subscription is active.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.set_notification_receiver">
<span class="sig-name descname"><span class="pre">set_notification_receiver</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">callback</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.set_notification_receiver"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.set_notification_receiver" title="Link to this definition"></a></dt>
<dd><p>Defines the callback that handles reception of a notification.
Assignment will take effect immediately, even while subscription is active.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.set_statusmessage_logger">
<span class="sig-name descname"><span class="pre">set_statusmessage_logger</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">callback</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.set_statusmessage_logger"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.set_statusmessage_logger" title="Link to this definition"></a></dt>
<dd><p>Defines the callback that handles informational message logging.
Assignment will take effect immediately, even while subscription is active.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.set_subscriptionupdated_receiver">
<span class="sig-name descname"><span class="pre">set_subscriptionupdated_receiver</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">callback</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="sttp.transport.html#sttp.transport.signalindexcache.SignalIndexCache" title="sttp.transport.signalindexcache.SignalIndexCache"><span class="pre">SignalIndexCache</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.set_subscriptionupdated_receiver"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.set_subscriptionupdated_receiver" title="Link to this definition"></a></dt>
<dd><p>Defines the callback that handles notifications that a new <cite>SignalIndexCache</cite> has been received.
Assignment will take effect immediately, even while subscription is active.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.statusmessage">
<span class="sig-name descname"><span class="pre">statusmessage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.statusmessage"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.statusmessage" title="Link to this definition"></a></dt>
<dd><p>Executes the defined status message logger callback.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.subscribe">
<span class="sig-name descname"><span class="pre">subscribe</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filterexpression</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">settings</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.settings.Settings" title="sttp.settings.Settings"><span class="pre">Settings</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.subscribe"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.subscribe" title="Link to this definition"></a></dt>
<dd><p>Subscribe sets up a request indicating that the <cite>Subscriber</cite> would like to start receiving
streaming data from a data publisher. If the subscriber is already connected, the updated
filter expression and subscription settings will be requested immediately; otherwise, the
settings will be used when the connection to the data publisher is established.</p>
<p>The filterExpression defines the desired measurements for a subscription. Examples include:</p>
<ul class="simple">
<li><dl class="simple">
<dt>Directly specified signal IDs (UUID values in string format):</dt><dd><p>38A47B0-F10B-4143-9A0A-0DBC4FFEF1E8; E4BBFE6A-35BD-4E5B-92C9-11FF913E7877</p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt>Directly specified tag names:</dt><dd><p>DOM_GPLAINS-BUS1:VH; TVA_SHELBY-BUS1:VH</p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt>Directly specified identifiers in “measurement key” format:</dt><dd><p>PPA:15; STAT:20</p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt>A filter expression against a selection view:</dt><dd><p>FILTER ActiveMeasurements WHERE Company=’GPA’ AND SignalType=’FREQ’</p>
</dd>
</dl>
</li>
</ul>
<p>Settings parameter controls subscription related settings.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.subscribed">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">subscribed</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">bool</span></em><a class="headerlink" href="#sttp.subscriber.Subscriber.subscribed" title="Link to this definition"></a></dt>
<dd><p>Gets flag that determines if <cite>Subscriber</cite> is currently subscribed to a data stream.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.subscriberid">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">subscriberid</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">UUID</span></em><a class="headerlink" href="#sttp.subscriber.Subscriber.subscriberid" title="Link to this definition"></a></dt>
<dd><p>Gets the subscriber ID as assigned by the data publisher upon receipt of the <cite>SignalIndexCache</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.total_commandchannel_bytesreceived">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">total_commandchannel_bytesreceived</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">uint64</span></em><a class="headerlink" href="#sttp.subscriber.Subscriber.total_commandchannel_bytesreceived" title="Link to this definition"></a></dt>
<dd><p>Gets the total number of bytes received via the command channel since last connection.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.total_datachannel_bytesreceived">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">total_datachannel_bytesreceived</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">uint64</span></em><a class="headerlink" href="#sttp.subscriber.Subscriber.total_datachannel_bytesreceived" title="Link to this definition"></a></dt>
<dd><p>Gets the total number of bytes received via the data channel since last connection.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.total_measurementsreceived">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">total_measurementsreceived</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">uint64</span></em><a class="headerlink" href="#sttp.subscriber.Subscriber.total_measurementsreceived" title="Link to this definition"></a></dt>
<dd><p>Gets the total number of measurements received since last subscription.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.subscriber.Subscriber.unsubscribe">
<span class="sig-name descname"><span class="pre">unsubscribe</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/subscriber.html#Subscriber.unsubscribe"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.subscriber.Subscriber.unsubscribe" title="Link to this definition"></a></dt>
<dd><p>Sends a request to the data publisher indicating that the Subscriber would
like to stop receiving streaming data.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.ticks">
<span id="sttp-ticks-module"></span><h2>sttp.ticks module<a class="headerlink" href="#module-sttp.ticks" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.ticks.Ticks">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.ticks.</span></span><span class="sig-name descname"><span class="pre">Ticks</span></span><a class="reference internal" href="_modules/sttp/ticks.html#Ticks"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.ticks.Ticks" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Defines constants and functions for tick values, 64-bit integers used to designate time in STTP. A tick value represents
the number of 100-nanosecond intervals that have elapsed since 12:00:00 midnight, January 1, 0001 UTC, Gregorian calendar.
A single tick represents one hundred nanoseconds, or one ten-millionth of a second. There are 10,000 ticks in a millisecond
and 10 million ticks in a second. Only bits 01 to 62 (0x3FFFFFFFFFFFFFFF) are used to represent the timestamp value.
Bit 64 (0x8000000000000000) is used to denote leap second, i.e., second 60, where actual second value would remain at 59.
Bit 63 (0x4000000000000000) is used to denote leap second direction, 0 for add, 1 for delete.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.ticks.Ticks.LEAPSECOND_DIRECTION">
<span class="sig-name descname"><span class="pre">LEAPSECOND_DIRECTION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.uint64(4611686018427387904)</span></em><a class="headerlink" href="#sttp.ticks.Ticks.LEAPSECOND_DIRECTION" title="Link to this definition"></a></dt>
<dd><p>Flag (63rd bit) that indicates if leap second is positive or negative; 0 for add, 1 for delete.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.ticks.Ticks.LEAPSECOND_FLAG">
<span class="sig-name descname"><span class="pre">LEAPSECOND_FLAG</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.uint64(9223372036854775808)</span></em><a class="headerlink" href="#sttp.ticks.Ticks.LEAPSECOND_FLAG" title="Link to this definition"></a></dt>
<dd><p>Flag (64th bit) that marks a Ticks value as a leap second, i.e., second 60 (one beyond normal second 59).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.ticks.Ticks.PERDAY">
<span class="sig-name descname"><span class="pre">PERDAY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.uint64(864000000000)</span></em><a class="headerlink" href="#sttp.ticks.Ticks.PERDAY" title="Link to this definition"></a></dt>
<dd><p>Number of Ticks that occur in a day.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.ticks.Ticks.PERHOUR">
<span class="sig-name descname"><span class="pre">PERHOUR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.uint64(36000000000)</span></em><a class="headerlink" href="#sttp.ticks.Ticks.PERHOUR" title="Link to this definition"></a></dt>
<dd><p>Number of Ticks that occur in an hour.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.ticks.Ticks.PERMICROSECOND">
<span class="sig-name descname"><span class="pre">PERMICROSECOND</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.uint64(10)</span></em><a class="headerlink" href="#sttp.ticks.Ticks.PERMICROSECOND" title="Link to this definition"></a></dt>
<dd><p>Number of Ticks that occur in a microsecond.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.ticks.Ticks.PERMILLISECOND">
<span class="sig-name descname"><span class="pre">PERMILLISECOND</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.uint64(10000)</span></em><a class="headerlink" href="#sttp.ticks.Ticks.PERMILLISECOND" title="Link to this definition"></a></dt>
<dd><p>Number of Ticks that occur in a millisecond.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.ticks.Ticks.PERMINUTE">
<span class="sig-name descname"><span class="pre">PERMINUTE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.uint64(600000000)</span></em><a class="headerlink" href="#sttp.ticks.Ticks.PERMINUTE" title="Link to this definition"></a></dt>
<dd><p>Number of Ticks that occur in a minute.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.ticks.Ticks.PERSECOND">
<span class="sig-name descname"><span class="pre">PERSECOND</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.uint64(10000000)</span></em><a class="headerlink" href="#sttp.ticks.Ticks.PERSECOND" title="Link to this definition"></a></dt>
<dd><p>Number of Ticks that occur in a second.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.ticks.Ticks.UNIXBASEOFFSET">
<span class="sig-name descname"><span class="pre">UNIXBASEOFFSET</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.uint64(621355968000000000)</span></em><a class="headerlink" href="#sttp.ticks.Ticks.UNIXBASEOFFSET" title="Link to this definition"></a></dt>
<dd><p>Ticks representation of the Unix epoch timestamp starting at January 1, 1970.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.ticks.Ticks.VALUEMASK">
<span class="sig-name descname"><span class="pre">VALUEMASK</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.uint64(4611686018427387903)</span></em><a class="headerlink" href="#sttp.ticks.Ticks.VALUEMASK" title="Link to this definition"></a></dt>
<dd><p>All bits (bits 1 to 62) that make up the value portion of a Ticks that represent time.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.ticks.Ticks.from_datetime">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">from_datetime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dt</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">datetime</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint64</span></span></span><a class="reference internal" href="_modules/sttp/ticks.html#Ticks.from_datetime"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.ticks.Ticks.from_datetime" title="Link to this definition"></a></dt>
<dd><p>Converts a standard Python dattime value to a Ticks value.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.ticks.Ticks.from_timedelta">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">from_timedelta</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">td</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">timedelta</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint64</span></span></span><a class="reference internal" href="_modules/sttp/ticks.html#Ticks.from_timedelta"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.ticks.Ticks.from_timedelta" title="Link to this definition"></a></dt>
<dd><p>Converts a standard Python timedelta value to a Ticks value.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.ticks.Ticks.is_leapsecond">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">is_leapsecond</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ticks</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint64</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">bool</span></span></span><a class="reference internal" href="_modules/sttp/ticks.html#Ticks.is_leapsecond"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.ticks.Ticks.is_leapsecond" title="Link to this definition"></a></dt>
<dd><p>Determines if the deserialized Ticks value represents a leap second, i.e., second 60.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.ticks.Ticks.is_negative_leapsecond">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">is_negative_leapsecond</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ticks</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint64</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">bool</span></span></span><a class="reference internal" href="_modules/sttp/ticks.html#Ticks.is_negative_leapsecond"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.ticks.Ticks.is_negative_leapsecond" title="Link to this definition"></a></dt>
<dd><p>Determines if the deserialized Ticks value represents a negative leap second, i.e., checks flag on second 58 to see if second 59 will be missing.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.ticks.Ticks.now">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">now</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint64</span></span></span><a class="reference internal" href="_modules/sttp/ticks.html#Ticks.now"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.ticks.Ticks.now" title="Link to this definition"></a></dt>
<dd><p>Gets the current local time as a Ticks value.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.ticks.Ticks.set_leapsecond">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">set_leapsecond</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ticks</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint64</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint64</span></span></span><a class="reference internal" href="_modules/sttp/ticks.html#Ticks.set_leapsecond"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.ticks.Ticks.set_leapsecond" title="Link to this definition"></a></dt>
<dd><p>Flags a Ticks value to represent a leap second, i.e., second 60, before wire serialization.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.ticks.Ticks.set_negative_leapsecond">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">set_negative_leapsecond</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ticks</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint64</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint64</span></span></span><a class="reference internal" href="_modules/sttp/ticks.html#Ticks.set_negative_leapsecond"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.ticks.Ticks.set_negative_leapsecond" title="Link to this definition"></a></dt>
<dd><p>Flags a Ticks value to represent a negative leap second, i.e., sets flag on second 58 to mark that second 59 will be missing, before wire serialization.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.ticks.Ticks.timestampvalue">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">timestampvalue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ticks</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint64</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint64</span></span></span><a class="reference internal" href="_modules/sttp/ticks.html#Ticks.timestampvalue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.ticks.Ticks.timestampvalue" title="Link to this definition"></a></dt>
<dd><p>Gets the timestamp portion of the <cite>Ticks</cite> value, i.e.,
the 62-bit time value excluding any leap second flags.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.ticks.Ticks.to_datetime">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">to_datetime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ticks</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint64</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">datetime</span></span></span><a class="reference internal" href="_modules/sttp/ticks.html#Ticks.to_datetime"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.ticks.Ticks.to_datetime" title="Link to this definition"></a></dt>
<dd><p>Converts a Ticks value to standard Python datetime value.</p>
<p>Note: Python <cite>datetime</cite> values have a maximum resolution of 1 microsecond, so any Ticks values,
which have 100 nanosecond resolution, will be rounded to the nearest microsecond.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.ticks.Ticks.to_shortstring">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">to_shortstring</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ticks</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint64</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="reference internal" href="_modules/sttp/ticks.html#Ticks.to_shortstring"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.ticks.Ticks.to_shortstring" title="Link to this definition"></a></dt>
<dd><p>Shows just the timestamp portion of a Ticks value with milliseconds, e.g., 15:04:05.999.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.ticks.Ticks.to_string">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">to_string</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ticks</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint64</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timespec</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'microseconds'</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="reference internal" href="_modules/sttp/ticks.html#Ticks.to_string"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.ticks.Ticks.to_string" title="Link to this definition"></a></dt>
<dd><p>Standard timestamp representation for a Ticks value, e.g., 2006-01-02 15:04:05.999999999.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.ticks.Ticks.utcnow">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">utcnow</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint64</span></span></span><a class="reference internal" href="_modules/sttp/ticks.html#Ticks.utcnow"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.ticks.Ticks.utcnow" title="Link to this definition"></a></dt>
<dd><p>Gets the current time in UTC as a Ticks value.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.version">
<span id="sttp-version-module"></span><h2>sttp.version module<a class="headerlink" href="#module-sttp.version" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.version.Version">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.version.</span></span><span class="sig-name descname"><span class="pre">Version</span></span><a class="reference internal" href="_modules/sttp/version.html#Version"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.version.Version" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.version.Version.STTP_SOURCE">
<span class="sig-name descname"><span class="pre">STTP_SOURCE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'STTP</span> <span class="pre">Python</span> <span class="pre">Library'</span></em><a class="headerlink" href="#sttp.version.Version.STTP_SOURCE" title="Link to this definition"></a></dt>
<dd><p>Defines the STTP library API title used for data subscriber identification.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.version.Version.STTP_UPDATEDON">
<span class="sig-name descname"><span class="pre">STTP_UPDATEDON</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'2024-10-16'</span></em><a class="headerlink" href="#sttp.version.Version.STTP_UPDATEDON" title="Link to this definition"></a></dt>
<dd><p>Defines when the STTP library API was last updated used for data subscriber identification.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.version.Version.STTP_VERSION">
<span class="sig-name descname"><span class="pre">STTP_VERSION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'0.6.4'</span></em><a class="headerlink" href="#sttp.version.Version.STTP_VERSION" title="Link to this definition"></a></dt>
<dd><p>Defines the STTP library API version used for data subscriber identification.
Note: This is not the STTP protocol version, but the version of the STTP library API.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp">
<span id="module-contents"></span><h2>Module contents<a class="headerlink" href="#module-sttp" title="Link to this heading"></a></h2>
</section>
</section>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
  
 
<hr />
<p>
    <a href="https://github.com/sttp/pyapi">GitHub Home</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://pypi.org/project/sttpapi/" target="_blank">PyPi Package: sttpapi</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://sttp.github.io/documentation/" target="_blank">General STTP Documentation</a>
</p>
<br /><br />
Copyright © 2022, <a href="https://gridprotectionalliance.org/" target="_blank">Grid&nbsp;Protection&nbsp;Alliance</a>
<a href="https://github.com/sttp/pyapi">
    <img align="right" style="margin-top: -50px"
        src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/LockPython_64High.png">
</a>


</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>