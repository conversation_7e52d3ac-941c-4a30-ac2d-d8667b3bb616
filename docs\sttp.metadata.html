

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>sttp.metadata package &mdash; sttp/pyapi 0.6.4 documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=92fd9be5" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=8209df7b" />

  
    <link rel="shortcut icon" href="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/favicon.ico"/>
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=bbec6902"></script>
      <script src="_static/documentation_options.js?v=b1f64a84"></script>
      <script src="_static/doctools.js?v=92e14aea"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            sttp/pyapi
              <img src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/sttp.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <!-- Local TOC -->
              <div class="local-toc"><ul>
<li><a class="reference internal" href="#">sttp.metadata package</a><ul>
<li><a class="reference internal" href="#subpackages">Subpackages</a></li>
<li><a class="reference internal" href="#submodules">Submodules</a></li>
<li><a class="reference internal" href="#module-sttp.metadata.cache">sttp.metadata.cache module</a><ul>
<li><a class="reference internal" href="#sttp.metadata.cache.MetadataCache"><code class="docutils literal notranslate"><span class="pre">MetadataCache</span></code></a><ul>
<li><a class="reference internal" href="#sttp.metadata.cache.MetadataCache.add_measurement"><code class="docutils literal notranslate"><span class="pre">MetadataCache.add_measurement()</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.cache.MetadataCache.device_records"><code class="docutils literal notranslate"><span class="pre">MetadataCache.device_records</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.cache.MetadataCache.deviceacronym_device_map"><code class="docutils literal notranslate"><span class="pre">MetadataCache.deviceacronym_device_map</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.cache.MetadataCache.deviceid_device_map"><code class="docutils literal notranslate"><span class="pre">MetadataCache.deviceid_device_map</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.cache.MetadataCache.find_device_acronym"><code class="docutils literal notranslate"><span class="pre">MetadataCache.find_device_acronym()</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.cache.MetadataCache.find_device_id"><code class="docutils literal notranslate"><span class="pre">MetadataCache.find_device_id()</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.cache.MetadataCache.find_devices"><code class="docutils literal notranslate"><span class="pre">MetadataCache.find_devices()</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.cache.MetadataCache.find_measurement_id"><code class="docutils literal notranslate"><span class="pre">MetadataCache.find_measurement_id()</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.cache.MetadataCache.find_measurement_pointtag"><code class="docutils literal notranslate"><span class="pre">MetadataCache.find_measurement_pointtag()</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.cache.MetadataCache.find_measurement_signalid"><code class="docutils literal notranslate"><span class="pre">MetadataCache.find_measurement_signalid()</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.cache.MetadataCache.find_measurement_signalreference"><code class="docutils literal notranslate"><span class="pre">MetadataCache.find_measurement_signalreference()</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.cache.MetadataCache.find_measurements"><code class="docutils literal notranslate"><span class="pre">MetadataCache.find_measurements()</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.cache.MetadataCache.find_measurements_signaltype"><code class="docutils literal notranslate"><span class="pre">MetadataCache.find_measurements_signaltype()</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.cache.MetadataCache.find_measurements_signaltypename"><code class="docutils literal notranslate"><span class="pre">MetadataCache.find_measurements_signaltypename()</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.cache.MetadataCache.id_measurement_map"><code class="docutils literal notranslate"><span class="pre">MetadataCache.id_measurement_map</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.cache.MetadataCache.measurement_records"><code class="docutils literal notranslate"><span class="pre">MetadataCache.measurement_records</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.cache.MetadataCache.phasorRecords"><code class="docutils literal notranslate"><span class="pre">MetadataCache.phasorRecords</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.cache.MetadataCache.pointtag_measurement_map"><code class="docutils literal notranslate"><span class="pre">MetadataCache.pointtag_measurement_map</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.cache.MetadataCache.signalid_measurement_map"><code class="docutils literal notranslate"><span class="pre">MetadataCache.signalid_measurement_map</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.cache.MetadataCache.signalref_measurement_map"><code class="docutils literal notranslate"><span class="pre">MetadataCache.signalref_measurement_map</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.metadata">Module contents</a></li>
</ul>
</li>
</ul>
</div>
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">sttp/pyapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">sttp.metadata package</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/sttp.metadata.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="sttp-metadata-package">
<h1>sttp.metadata package<a class="headerlink" href="#sttp-metadata-package" title="Link to this heading"></a></h1>
<section id="subpackages">
<h2>Subpackages<a class="headerlink" href="#subpackages" title="Link to this heading"></a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="sttp.metadata.record.html">sttp.metadata.record package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="sttp.metadata.record.html#submodules">Submodules</a></li>
<li class="toctree-l2"><a class="reference internal" href="sttp.metadata.record.html#module-sttp.metadata.record.device">sttp.metadata.record.device module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord"><code class="docutils literal notranslate"><span class="pre">DeviceRecord</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.DEFAULT_COMPANYNAME"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.DEFAULT_COMPANYNAME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.DEFAULT_FRAMESPERSECOND"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.DEFAULT_FRAMESPERSECOND</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.DEFAULT_LATITUDE"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.DEFAULT_LATITUDE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.DEFAULT_LONGITUDE"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.DEFAULT_LONGITUDE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.DEFAULT_PARENTACRONYM"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.DEFAULT_PARENTACRONYM</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.DEFAULT_PROTOCOLNAME"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.DEFAULT_PROTOCOLNAME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.DEFAULT_UPDATEDON"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.DEFAULT_UPDATEDON</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.DEFAULT_VENDORACRONYM"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.DEFAULT_VENDORACRONYM</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.DEFAULT_VENDORDEVICENAME"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.DEFAULT_VENDORDEVICENAME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.accessid"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.accessid</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.acronym"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.acronym</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.companyacronym"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.companyacronym</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.deviceid"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.deviceid</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.framespersecond"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.framespersecond</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.latitude"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.latitude</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.longitude"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.longitude</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.measurements"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.measurements</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.name"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.name</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.nodeid"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.nodeid</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.parentacronym"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.parentacronym</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.phasors"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.phasors</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.protocolname"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.protocolname</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.updatedon"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.updatedon</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.vendoracronym"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.vendoracronym</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord.vendordevicename"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.vendordevicename</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.metadata.record.html#module-sttp.metadata.record.measurement">sttp.metadata.record.measurement module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_ADDER"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.DEFAULT_ADDER</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_DESCRIPTION"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.DEFAULT_DESCRIPTION</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_DEVICEACRONYM"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.DEFAULT_DEVICEACRONYM</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_ID"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.DEFAULT_ID</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_MULTIPLIER"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.DEFAULT_MULTIPLIER</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_POINTTAG"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.DEFAULT_POINTTAG</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_SIGNALID"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.DEFAULT_SIGNALID</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_SIGNALREFERENCE"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.DEFAULT_SIGNALREFERENCE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_SIGNALTYPENAME"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.DEFAULT_SIGNALTYPENAME</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_SOURCE"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.DEFAULT_SOURCE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_UPDATEDON"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.DEFAULT_UPDATEDON</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.adder"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.adder</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.description"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.description</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.device"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.device</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.deviceacronym"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.deviceacronym</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.id"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.id</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.multiplier"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.multiplier</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.phasor"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.phasor</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.pointtag"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.pointtag</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.signalid"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.signalid</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.signalreference"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.signalreference</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.signaltype"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.signaltype</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.signaltypename"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.signaltypename</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.source"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.source</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord.updatedon"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.updatedon</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.SignalType"><code class="docutils literal notranslate"><span class="pre">SignalType</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.SignalType.ALOG"><code class="docutils literal notranslate"><span class="pre">SignalType.ALOG</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.SignalType.ALRM"><code class="docutils literal notranslate"><span class="pre">SignalType.ALRM</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.SignalType.CALC"><code class="docutils literal notranslate"><span class="pre">SignalType.CALC</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.SignalType.DFDT"><code class="docutils literal notranslate"><span class="pre">SignalType.DFDT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.SignalType.DIGI"><code class="docutils literal notranslate"><span class="pre">SignalType.DIGI</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.SignalType.FLAG"><code class="docutils literal notranslate"><span class="pre">SignalType.FLAG</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.SignalType.FREQ"><code class="docutils literal notranslate"><span class="pre">SignalType.FREQ</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.SignalType.IPHA"><code class="docutils literal notranslate"><span class="pre">SignalType.IPHA</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.SignalType.IPHM"><code class="docutils literal notranslate"><span class="pre">SignalType.IPHM</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.SignalType.QUAL"><code class="docutils literal notranslate"><span class="pre">SignalType.QUAL</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.SignalType.STAT"><code class="docutils literal notranslate"><span class="pre">SignalType.STAT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.SignalType.UNKN"><code class="docutils literal notranslate"><span class="pre">SignalType.UNKN</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.SignalType.VPHA"><code class="docutils literal notranslate"><span class="pre">SignalType.VPHA</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.SignalType.VPHM"><code class="docutils literal notranslate"><span class="pre">SignalType.VPHM</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.SignalType.parse"><code class="docutils literal notranslate"><span class="pre">SignalType.parse()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.metadata.record.html#module-sttp.metadata.record.phasor">sttp.metadata.record.phasor module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.phasor.CompositePhasorMeasurement"><code class="docutils literal notranslate"><span class="pre">CompositePhasorMeasurement</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.phasor.CompositePhasorMeasurement.ANGLE"><code class="docutils literal notranslate"><span class="pre">CompositePhasorMeasurement.ANGLE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.phasor.CompositePhasorMeasurement.MAGNITUDE"><code class="docutils literal notranslate"><span class="pre">CompositePhasorMeasurement.MAGNITUDE</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.phasor.PhasorRecord"><code class="docutils literal notranslate"><span class="pre">PhasorRecord</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.phasor.PhasorRecord.DEFAULT_BASEKV"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.DEFAULT_BASEKV</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.phasor.PhasorRecord.DEFAULT_PHASE"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.DEFAULT_PHASE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.phasor.PhasorRecord.DEFAULT_TYPE"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.DEFAULT_TYPE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.phasor.PhasorRecord.DEFAULT_UPDATEDON"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.DEFAULT_UPDATEDON</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.phasor.PhasorRecord.angle_measurement"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.angle_measurement</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.phasor.PhasorRecord.basekv"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.basekv</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.phasor.PhasorRecord.device"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.device</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.phasor.PhasorRecord.deviceacronym"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.deviceacronym</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.phasor.PhasorRecord.id"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.id</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.phasor.PhasorRecord.label"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.label</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.phasor.PhasorRecord.magnitude_measurement"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.magnitude_measurement</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.phasor.PhasorRecord.measurements"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.measurements</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.phasor.PhasorRecord.phase"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.phase</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.phasor.PhasorRecord.sourceindex"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.sourceindex</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.phasor.PhasorRecord.type"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.type</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.phasor.PhasorRecord.updatedon"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.updatedon</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.metadata.record.html#module-sttp.metadata.record">Module contents</a></li>
</ul>
</li>
</ul>
</div>
</section>
<section id="submodules">
<h2>Submodules<a class="headerlink" href="#submodules" title="Link to this heading"></a></h2>
</section>
<section id="module-sttp.metadata.cache">
<span id="sttp-metadata-cache-module"></span><h2>sttp.metadata.cache module<a class="headerlink" href="#module-sttp.metadata.cache" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.metadata.cache.MetadataCache">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.metadata.cache.</span></span><span class="sig-name descname"><span class="pre">MetadataCache</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dataset</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="sttp.data.html#sttp.data.dataset.DataSet" title="sttp.data.dataset.DataSet"><span class="pre">DataSet</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/metadata/cache.html#MetadataCache"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.metadata.cache.MetadataCache" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Represents a collection of parsed STTP metadata records.</p>
<dl class="py method">
<dt class="sig sig-object py" id="sttp.metadata.cache.MetadataCache.add_measurement">
<span class="sig-name descname"><span class="pre">add_measurement</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">measurement</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord" title="sttp.metadata.record.measurement.MeasurementRecord"><span class="pre">MeasurementRecord</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/metadata/cache.html#MetadataCache.add_measurement"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.metadata.cache.MetadataCache.add_measurement" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.cache.MetadataCache.device_records">
<span class="sig-name descname"><span class="pre">device_records</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord" title="sttp.metadata.record.device.DeviceRecord"><span class="pre">DeviceRecord</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#sttp.metadata.cache.MetadataCache.device_records" title="Link to this definition"></a></dt>
<dd><p>Defines list of device records in the cache.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.cache.MetadataCache.deviceacronym_device_map">
<span class="sig-name descname"><span class="pre">deviceacronym_device_map</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Dict</span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord" title="sttp.metadata.record.device.DeviceRecord"><span class="pre">DeviceRecord</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#sttp.metadata.cache.MetadataCache.deviceacronym_device_map" title="Link to this definition"></a></dt>
<dd><p>Defines map of device acronym to device records.
Device acronyms are typically unique for a given publisher.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.cache.MetadataCache.deviceid_device_map">
<span class="sig-name descname"><span class="pre">deviceid_device_map</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Dict</span><span class="p"><span class="pre">[</span></span><span class="pre">UUID</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord" title="sttp.metadata.record.device.DeviceRecord"><span class="pre">DeviceRecord</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#sttp.metadata.cache.MetadataCache.deviceid_device_map" title="Link to this definition"></a></dt>
<dd><p>Defines map of unique device IDs to device records.
Device IDs (a UUID) are typically unique across disparate systems.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.metadata.cache.MetadataCache.find_device_acronym">
<span class="sig-name descname"><span class="pre">find_device_acronym</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">deviceacronym</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord" title="sttp.metadata.record.device.DeviceRecord"><span class="pre">DeviceRecord</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></span><a class="reference internal" href="_modules/sttp/metadata/cache.html#MetadataCache.find_device_acronym"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.metadata.cache.MetadataCache.find_device_acronym" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.metadata.cache.MetadataCache.find_device_id">
<span class="sig-name descname"><span class="pre">find_device_id</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">deviceid</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">UUID</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord" title="sttp.metadata.record.device.DeviceRecord"><span class="pre">DeviceRecord</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></span><a class="reference internal" href="_modules/sttp/metadata/cache.html#MetadataCache.find_device_id"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.metadata.cache.MetadataCache.find_device_id" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.metadata.cache.MetadataCache.find_devices">
<span class="sig-name descname"><span class="pre">find_devices</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">searchval</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.device.DeviceRecord" title="sttp.metadata.record.device.DeviceRecord"><span class="pre">DeviceRecord</span></a><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/metadata/cache.html#MetadataCache.find_devices"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.metadata.cache.MetadataCache.find_devices" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.metadata.cache.MetadataCache.find_measurement_id">
<span class="sig-name descname"><span class="pre">find_measurement_id</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint64</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord" title="sttp.metadata.record.measurement.MeasurementRecord"><span class="pre">MeasurementRecord</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></span><a class="reference internal" href="_modules/sttp/metadata/cache.html#MetadataCache.find_measurement_id"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.metadata.cache.MetadataCache.find_measurement_id" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.metadata.cache.MetadataCache.find_measurement_pointtag">
<span class="sig-name descname"><span class="pre">find_measurement_pointtag</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pointtag</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord" title="sttp.metadata.record.measurement.MeasurementRecord"><span class="pre">MeasurementRecord</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></span><a class="reference internal" href="_modules/sttp/metadata/cache.html#MetadataCache.find_measurement_pointtag"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.metadata.cache.MetadataCache.find_measurement_pointtag" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.metadata.cache.MetadataCache.find_measurement_signalid">
<span class="sig-name descname"><span class="pre">find_measurement_signalid</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">signalid</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">UUID</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord" title="sttp.metadata.record.measurement.MeasurementRecord"><span class="pre">MeasurementRecord</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></span><a class="reference internal" href="_modules/sttp/metadata/cache.html#MetadataCache.find_measurement_signalid"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.metadata.cache.MetadataCache.find_measurement_signalid" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.metadata.cache.MetadataCache.find_measurement_signalreference">
<span class="sig-name descname"><span class="pre">find_measurement_signalreference</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">signalreference</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord" title="sttp.metadata.record.measurement.MeasurementRecord"><span class="pre">MeasurementRecord</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></span><a class="reference internal" href="_modules/sttp/metadata/cache.html#MetadataCache.find_measurement_signalreference"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.metadata.cache.MetadataCache.find_measurement_signalreference" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.metadata.cache.MetadataCache.find_measurements">
<span class="sig-name descname"><span class="pre">find_measurements</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">searchval</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">instancename</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord" title="sttp.metadata.record.measurement.MeasurementRecord"><span class="pre">MeasurementRecord</span></a><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/metadata/cache.html#MetadataCache.find_measurements"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.metadata.cache.MetadataCache.find_measurements" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.metadata.cache.MetadataCache.find_measurements_signaltype">
<span class="sig-name descname"><span class="pre">find_measurements_signaltype</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">signaltype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.SignalType" title="sttp.metadata.record.measurement.SignalType"><span class="pre">SignalType</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">instancename</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord" title="sttp.metadata.record.measurement.MeasurementRecord"><span class="pre">MeasurementRecord</span></a><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/metadata/cache.html#MetadataCache.find_measurements_signaltype"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.metadata.cache.MetadataCache.find_measurements_signaltype" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.metadata.cache.MetadataCache.find_measurements_signaltypename">
<span class="sig-name descname"><span class="pre">find_measurements_signaltypename</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">signaltypename</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">instancename</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord" title="sttp.metadata.record.measurement.MeasurementRecord"><span class="pre">MeasurementRecord</span></a><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/metadata/cache.html#MetadataCache.find_measurements_signaltypename"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.metadata.cache.MetadataCache.find_measurements_signaltypename" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.cache.MetadataCache.id_measurement_map">
<span class="sig-name descname"><span class="pre">id_measurement_map</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Dict</span><span class="p"><span class="pre">[</span></span><span class="pre">uint64</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord" title="sttp.metadata.record.measurement.MeasurementRecord"><span class="pre">MeasurementRecord</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#sttp.metadata.cache.MetadataCache.id_measurement_map" title="Link to this definition"></a></dt>
<dd><p>Defines map of measurement key IDs to measurement records.
Measurement key IDs are typically unique for a given publisher.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.cache.MetadataCache.measurement_records">
<span class="sig-name descname"><span class="pre">measurement_records</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord" title="sttp.metadata.record.measurement.MeasurementRecord"><span class="pre">MeasurementRecord</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#sttp.metadata.cache.MetadataCache.measurement_records" title="Link to this definition"></a></dt>
<dd><p>Defines list of measurement records in the cache.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.cache.MetadataCache.phasorRecords">
<span class="sig-name descname"><span class="pre">phasorRecords</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.phasor.PhasorRecord" title="sttp.metadata.record.phasor.PhasorRecord"><span class="pre">PhasorRecord</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#sttp.metadata.cache.MetadataCache.phasorRecords" title="Link to this definition"></a></dt>
<dd><p>Defines list of phasor records in the cache.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.cache.MetadataCache.pointtag_measurement_map">
<span class="sig-name descname"><span class="pre">pointtag_measurement_map</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Dict</span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord" title="sttp.metadata.record.measurement.MeasurementRecord"><span class="pre">MeasurementRecord</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#sttp.metadata.cache.MetadataCache.pointtag_measurement_map" title="Link to this definition"></a></dt>
<dd><p>Defines map of measurement point tags to measurement records.
Measurement point tags are typically unique for a given publisher.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.cache.MetadataCache.signalid_measurement_map">
<span class="sig-name descname"><span class="pre">signalid_measurement_map</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Dict</span><span class="p"><span class="pre">[</span></span><span class="pre">UUID</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord" title="sttp.metadata.record.measurement.MeasurementRecord"><span class="pre">MeasurementRecord</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#sttp.metadata.cache.MetadataCache.signalid_measurement_map" title="Link to this definition"></a></dt>
<dd><p>Defines map of unique measurement signal IDs to measurement records.
Measurement signal IDs (a UUID) are typically unique across disparate systems.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.cache.MetadataCache.signalref_measurement_map">
<span class="sig-name descname"><span class="pre">signalref_measurement_map</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Dict</span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord" title="sttp.metadata.record.measurement.MeasurementRecord"><span class="pre">MeasurementRecord</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#sttp.metadata.cache.MetadataCache.signalref_measurement_map" title="Link to this definition"></a></dt>
<dd><p>Defines map of measurement signal references to measurement records.
Measurement signal references are typically unique for a given publisher.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.metadata">
<span id="module-contents"></span><h2>Module contents<a class="headerlink" href="#module-sttp.metadata" title="Link to this heading"></a></h2>
</section>
</section>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
  
 
<hr />
<p>
    <a href="https://github.com/sttp/pyapi">GitHub Home</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://pypi.org/project/sttpapi/" target="_blank">PyPi Package: sttpapi</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://sttp.github.io/documentation/" target="_blank">General STTP Documentation</a>
</p>
<br /><br />
Copyright © 2022, <a href="https://gridprotectionalliance.org/" target="_blank">Grid&nbsp;Protection&nbsp;Alliance</a>
<a href="https://github.com/sttp/pyapi">
    <img align="right" style="margin-top: -50px"
        src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/LockPython_64High.png">
</a>


</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>