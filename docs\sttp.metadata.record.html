

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>sttp.metadata.record package &mdash; sttp/pyapi 0.6.4 documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=92fd9be5" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=8209df7b" />

  
    <link rel="shortcut icon" href="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/favicon.ico"/>
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=bbec6902"></script>
      <script src="_static/documentation_options.js?v=b1f64a84"></script>
      <script src="_static/doctools.js?v=92e14aea"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            sttp/pyapi
              <img src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/sttp.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <!-- Local TOC -->
              <div class="local-toc"><ul>
<li><a class="reference internal" href="#">sttp.metadata.record package</a><ul>
<li><a class="reference internal" href="#submodules">Submodules</a></li>
<li><a class="reference internal" href="#module-sttp.metadata.record.device">sttp.metadata.record.device module</a><ul>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord"><code class="docutils literal notranslate"><span class="pre">DeviceRecord</span></code></a><ul>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.DEFAULT_COMPANYNAME"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.DEFAULT_COMPANYNAME</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.DEFAULT_FRAMESPERSECOND"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.DEFAULT_FRAMESPERSECOND</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.DEFAULT_LATITUDE"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.DEFAULT_LATITUDE</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.DEFAULT_LONGITUDE"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.DEFAULT_LONGITUDE</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.DEFAULT_PARENTACRONYM"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.DEFAULT_PARENTACRONYM</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.DEFAULT_PROTOCOLNAME"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.DEFAULT_PROTOCOLNAME</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.DEFAULT_UPDATEDON"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.DEFAULT_UPDATEDON</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.DEFAULT_VENDORACRONYM"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.DEFAULT_VENDORACRONYM</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.DEFAULT_VENDORDEVICENAME"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.DEFAULT_VENDORDEVICENAME</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.accessid"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.accessid</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.acronym"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.acronym</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.companyacronym"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.companyacronym</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.deviceid"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.deviceid</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.framespersecond"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.framespersecond</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.latitude"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.latitude</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.longitude"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.longitude</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.measurements"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.measurements</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.name"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.name</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.nodeid"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.nodeid</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.parentacronym"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.parentacronym</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.phasors"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.phasors</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.protocolname"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.protocolname</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.updatedon"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.updatedon</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.vendoracronym"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.vendoracronym</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord.vendordevicename"><code class="docutils literal notranslate"><span class="pre">DeviceRecord.vendordevicename</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.metadata.record.measurement">sttp.metadata.record.measurement module</a><ul>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord</span></code></a><ul>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_ADDER"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.DEFAULT_ADDER</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_DESCRIPTION"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.DEFAULT_DESCRIPTION</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_DEVICEACRONYM"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.DEFAULT_DEVICEACRONYM</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_ID"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.DEFAULT_ID</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_MULTIPLIER"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.DEFAULT_MULTIPLIER</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_POINTTAG"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.DEFAULT_POINTTAG</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_SIGNALID"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.DEFAULT_SIGNALID</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_SIGNALREFERENCE"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.DEFAULT_SIGNALREFERENCE</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_SIGNALTYPENAME"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.DEFAULT_SIGNALTYPENAME</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_SOURCE"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.DEFAULT_SOURCE</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_UPDATEDON"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.DEFAULT_UPDATEDON</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.adder"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.adder</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.description"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.description</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.device"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.device</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.deviceacronym"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.deviceacronym</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.id"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.id</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.multiplier"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.multiplier</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.phasor"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.phasor</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.pointtag"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.pointtag</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.signalid"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.signalid</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.signalreference"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.signalreference</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.signaltype"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.signaltype</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.signaltypename"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.signaltypename</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.source"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.source</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord.updatedon"><code class="docutils literal notranslate"><span class="pre">MeasurementRecord.updatedon</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.SignalType"><code class="docutils literal notranslate"><span class="pre">SignalType</span></code></a><ul>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.SignalType.ALOG"><code class="docutils literal notranslate"><span class="pre">SignalType.ALOG</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.SignalType.ALRM"><code class="docutils literal notranslate"><span class="pre">SignalType.ALRM</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.SignalType.CALC"><code class="docutils literal notranslate"><span class="pre">SignalType.CALC</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.SignalType.DFDT"><code class="docutils literal notranslate"><span class="pre">SignalType.DFDT</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.SignalType.DIGI"><code class="docutils literal notranslate"><span class="pre">SignalType.DIGI</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.SignalType.FLAG"><code class="docutils literal notranslate"><span class="pre">SignalType.FLAG</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.SignalType.FREQ"><code class="docutils literal notranslate"><span class="pre">SignalType.FREQ</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.SignalType.IPHA"><code class="docutils literal notranslate"><span class="pre">SignalType.IPHA</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.SignalType.IPHM"><code class="docutils literal notranslate"><span class="pre">SignalType.IPHM</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.SignalType.QUAL"><code class="docutils literal notranslate"><span class="pre">SignalType.QUAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.SignalType.STAT"><code class="docutils literal notranslate"><span class="pre">SignalType.STAT</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.SignalType.UNKN"><code class="docutils literal notranslate"><span class="pre">SignalType.UNKN</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.SignalType.VPHA"><code class="docutils literal notranslate"><span class="pre">SignalType.VPHA</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.SignalType.VPHM"><code class="docutils literal notranslate"><span class="pre">SignalType.VPHM</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.measurement.SignalType.parse"><code class="docutils literal notranslate"><span class="pre">SignalType.parse()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.metadata.record.phasor">sttp.metadata.record.phasor module</a><ul>
<li><a class="reference internal" href="#sttp.metadata.record.phasor.CompositePhasorMeasurement"><code class="docutils literal notranslate"><span class="pre">CompositePhasorMeasurement</span></code></a><ul>
<li><a class="reference internal" href="#sttp.metadata.record.phasor.CompositePhasorMeasurement.ANGLE"><code class="docutils literal notranslate"><span class="pre">CompositePhasorMeasurement.ANGLE</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.phasor.CompositePhasorMeasurement.MAGNITUDE"><code class="docutils literal notranslate"><span class="pre">CompositePhasorMeasurement.MAGNITUDE</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#sttp.metadata.record.phasor.PhasorRecord"><code class="docutils literal notranslate"><span class="pre">PhasorRecord</span></code></a><ul>
<li><a class="reference internal" href="#sttp.metadata.record.phasor.PhasorRecord.DEFAULT_BASEKV"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.DEFAULT_BASEKV</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.phasor.PhasorRecord.DEFAULT_PHASE"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.DEFAULT_PHASE</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.phasor.PhasorRecord.DEFAULT_TYPE"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.DEFAULT_TYPE</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.phasor.PhasorRecord.DEFAULT_UPDATEDON"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.DEFAULT_UPDATEDON</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.phasor.PhasorRecord.angle_measurement"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.angle_measurement</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.phasor.PhasorRecord.basekv"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.basekv</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.phasor.PhasorRecord.device"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.device</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.phasor.PhasorRecord.deviceacronym"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.deviceacronym</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.phasor.PhasorRecord.id"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.id</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.phasor.PhasorRecord.label"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.label</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.phasor.PhasorRecord.magnitude_measurement"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.magnitude_measurement</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.phasor.PhasorRecord.measurements"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.measurements</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.phasor.PhasorRecord.phase"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.phase</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.phasor.PhasorRecord.sourceindex"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.sourceindex</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.phasor.PhasorRecord.type"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.type</span></code></a></li>
<li><a class="reference internal" href="#sttp.metadata.record.phasor.PhasorRecord.updatedon"><code class="docutils literal notranslate"><span class="pre">PhasorRecord.updatedon</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.metadata.record">Module contents</a></li>
</ul>
</li>
</ul>
</div>
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">sttp/pyapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">sttp.metadata.record package</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/sttp.metadata.record.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="sttp-metadata-record-package">
<h1>sttp.metadata.record package<a class="headerlink" href="#sttp-metadata-record-package" title="Link to this heading"></a></h1>
<section id="submodules">
<h2>Submodules<a class="headerlink" href="#submodules" title="Link to this heading"></a></h2>
</section>
<section id="module-sttp.metadata.record.device">
<span id="sttp-metadata-record-device-module"></span><h2>sttp.metadata.record.device module<a class="headerlink" href="#module-sttp.metadata.record.device" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.metadata.record.device.</span></span><span class="sig-name descname"><span class="pre">DeviceRecord</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">nodeid</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">UUID</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">deviceid</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">UUID</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">acronym</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">accessid</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parentacronym</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">protocolname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">framespersecond</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">companyacronym</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">vendoracronym</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">vendordevicename</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">longitude</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Decimal</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">latitude</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Decimal</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">updatedon</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">datetime</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/metadata/record/device.html#DeviceRecord"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Represents a record of device metadata in the STTP.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.DEFAULT_COMPANYNAME">
<span class="sig-name descname"><span class="pre">DEFAULT_COMPANYNAME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.DEFAULT_COMPANYNAME" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.DEFAULT_FRAMESPERSECOND">
<span class="sig-name descname"><span class="pre">DEFAULT_FRAMESPERSECOND</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">30</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.DEFAULT_FRAMESPERSECOND" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.DEFAULT_LATITUDE">
<span class="sig-name descname"><span class="pre">DEFAULT_LATITUDE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">Decimal('0')</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.DEFAULT_LATITUDE" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.DEFAULT_LONGITUDE">
<span class="sig-name descname"><span class="pre">DEFAULT_LONGITUDE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">Decimal('0')</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.DEFAULT_LONGITUDE" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.DEFAULT_PARENTACRONYM">
<span class="sig-name descname"><span class="pre">DEFAULT_PARENTACRONYM</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.DEFAULT_PARENTACRONYM" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.DEFAULT_PROTOCOLNAME">
<span class="sig-name descname"><span class="pre">DEFAULT_PROTOCOLNAME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.DEFAULT_PROTOCOLNAME" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.DEFAULT_UPDATEDON">
<span class="sig-name descname"><span class="pre">DEFAULT_UPDATEDON</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">datetime.datetime(1,</span> <span class="pre">1,</span> <span class="pre">1,</span> <span class="pre">0,</span> <span class="pre">0,</span> <span class="pre">tzinfo=datetime.timezone.utc)</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.DEFAULT_UPDATEDON" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.DEFAULT_VENDORACRONYM">
<span class="sig-name descname"><span class="pre">DEFAULT_VENDORACRONYM</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.DEFAULT_VENDORACRONYM" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.DEFAULT_VENDORDEVICENAME">
<span class="sig-name descname"><span class="pre">DEFAULT_VENDORDEVICENAME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.DEFAULT_VENDORDEVICENAME" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.accessid">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">accessid</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">int</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.accessid" title="Link to this definition"></a></dt>
<dd><p>Gets the access ID (a.k.a. ID code) for this <cite>DeviceRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.acronym">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">acronym</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.acronym" title="Link to this definition"></a></dt>
<dd><p>Gets the unique alpha-numeric identifier for this <cite>DeviceRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.companyacronym">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">companyacronym</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.companyacronym" title="Link to this definition"></a></dt>
<dd><p>Gets the acronym of the company associated with this <cite>DeviceRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.deviceid">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">deviceid</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">UUID</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.deviceid" title="Link to this definition"></a></dt>
<dd><p>Gets the unique guid-based identifier for this <cite>DeviceRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.framespersecond">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">framespersecond</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">int</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.framespersecond" title="Link to this definition"></a></dt>
<dd><p>Gets the data reporting rate, in data frames per second, for this <cite>DeviceRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.latitude">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">latitude</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Decimal</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.latitude" title="Link to this definition"></a></dt>
<dd><p>Gets the latitude of this <cite>DeviceRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.longitude">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">longitude</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Decimal</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.longitude" title="Link to this definition"></a></dt>
<dd><p>Gets the longitude of this <cite>DeviceRecord</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.measurements">
<span class="sig-name descname"><span class="pre">measurements</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Set</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord" title="sttp.metadata.record.measurement.MeasurementRecord"><span class="pre">MeasurementRecord</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.measurements" title="Link to this definition"></a></dt>
<dd><p>Gets <cite>MeasurementRecord</cite> values associated with this <cite>DeviceRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.name">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.name" title="Link to this definition"></a></dt>
<dd><p>Gets the free form name of this <cite>DeviceRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.nodeid">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">nodeid</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">UUID</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.nodeid" title="Link to this definition"></a></dt>
<dd><p>Gets the guid-based STTP node identifier for this <cite>DeviceRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.parentacronym">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">parentacronym</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.parentacronym" title="Link to this definition"></a></dt>
<dd><p>Gets the parent device alpha-numeric identifier for this <cite>DeviceRecord</cite>, if any.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.phasors">
<span class="sig-name descname"><span class="pre">phasors</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Set</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.metadata.record.phasor.PhasorRecord" title="sttp.metadata.record.phasor.PhasorRecord"><span class="pre">PhasorRecord</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.phasors" title="Link to this definition"></a></dt>
<dd><p>Gets <cite>PhasorRecord</cite> values associated with this <cite>DeviceRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.protocolname">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">protocolname</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.protocolname" title="Link to this definition"></a></dt>
<dd><p>Gets the name of the source protocol for this <cite>DeviceRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.updatedon">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">updatedon</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">datetime</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.updatedon" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>datetime</cite> of when this <cite>DeviceRecord</cite> was last updated.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.vendoracronym">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">vendoracronym</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.vendoracronym" title="Link to this definition"></a></dt>
<dd><p>Gets the acronym of the vendor associated with this <cite>DeviceRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.device.DeviceRecord.vendordevicename">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">vendordevicename</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#sttp.metadata.record.device.DeviceRecord.vendordevicename" title="Link to this definition"></a></dt>
<dd><p>Gets the acronym of the vendor device name associated with this <cite>DeviceRecord</cite>.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.metadata.record.measurement">
<span id="sttp-metadata-record-measurement-module"></span><h2>sttp.metadata.record.measurement module<a class="headerlink" href="#module-sttp.metadata.record.measurement" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.metadata.record.measurement.</span></span><span class="sig-name descname"><span class="pre">MeasurementRecord</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">signalid</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">UUID</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">adder</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">float64</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">multiplier</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">float64</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint64</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">signaltypename</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">signalreference</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pointtag</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">deviceacronym</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">description</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">updatedon</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">datetime</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/metadata/record/measurement.html#MeasurementRecord"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Represents a record of measurement metadata in the STTP.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <cite>MeasurementRecord</cite> defines  ancillary information associated with a <cite>Measurement</cite>.
Metadata gets cached in a registry associated with a <cite>DataSubscriber</cite>.</p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_ADDER">
<span class="sig-name descname"><span class="pre">DEFAULT_ADDER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.float64(0.0)</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_ADDER" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_DESCRIPTION">
<span class="sig-name descname"><span class="pre">DEFAULT_DESCRIPTION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_DESCRIPTION" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_DEVICEACRONYM">
<span class="sig-name descname"><span class="pre">DEFAULT_DEVICEACRONYM</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_DEVICEACRONYM" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_ID">
<span class="sig-name descname"><span class="pre">DEFAULT_ID</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.uint64(0)</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_ID" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_MULTIPLIER">
<span class="sig-name descname"><span class="pre">DEFAULT_MULTIPLIER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.float64(1.0)</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_MULTIPLIER" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_POINTTAG">
<span class="sig-name descname"><span class="pre">DEFAULT_POINTTAG</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_POINTTAG" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_SIGNALID">
<span class="sig-name descname"><span class="pre">DEFAULT_SIGNALID</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">UUID('00000000-0000-0000-0000-000000000000')</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_SIGNALID" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_SIGNALREFERENCE">
<span class="sig-name descname"><span class="pre">DEFAULT_SIGNALREFERENCE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_SIGNALREFERENCE" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_SIGNALTYPENAME">
<span class="sig-name descname"><span class="pre">DEFAULT_SIGNALTYPENAME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'UNKN'</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_SIGNALTYPENAME" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_SOURCE">
<span class="sig-name descname"><span class="pre">DEFAULT_SOURCE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_SOURCE" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_UPDATEDON">
<span class="sig-name descname"><span class="pre">DEFAULT_UPDATEDON</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">datetime.datetime(1,</span> <span class="pre">1,</span> <span class="pre">1,</span> <span class="pre">0,</span> <span class="pre">0,</span> <span class="pre">tzinfo=datetime.timezone.utc)</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.DEFAULT_UPDATEDON" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.adder">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">adder</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">float64</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.adder" title="Link to this definition"></a></dt>
<dd><p>Gets the additive value modifier. Allows for linear value adjustment. Defaults to zero.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.description">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">description</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.description" title="Link to this definition"></a></dt>
<dd><p>Gets the description for this <cite>MeasurementRecord</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.device">
<span class="sig-name descname"><span class="pre">device</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord" title="sttp.metadata.record.device.DeviceRecord"><span class="pre">DeviceRecord</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.device" title="Link to this definition"></a></dt>
<dd><p>Defines the associated <cite>DeviceRecord</cite> for this <cite>MeasurementRecord</cite>.
Set to <cite>None</cite> if not applicable.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.deviceacronym">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">deviceacronym</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.deviceacronym" title="Link to this definition"></a></dt>
<dd><p>Gets the alpha-numeric identifier of the associated device for this <cite>MeasurementRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.id">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">uint64</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.id" title="Link to this definition"></a></dt>
<dd><p>Gets the STTP numeric ID number (from measurement key) for this <cite>MeasurementRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.multiplier">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">multiplier</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">float64</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.multiplier" title="Link to this definition"></a></dt>
<dd><p>Gets the multiplicative value modifier. Allows for linear value adjustment. Defaults to one.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.phasor">
<span class="sig-name descname"><span class="pre">phasor</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.metadata.record.phasor.PhasorRecord" title="sttp.metadata.record.phasor.PhasorRecord"><span class="pre">PhasorRecord</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.phasor" title="Link to this definition"></a></dt>
<dd><p>Defines the associated <cite>PhasorRecord</cite> for this <cite>MeasurementRecord</cite>.
Set to <cite>None</cite> if not applicable.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.pointtag">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">pointtag</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.pointtag" title="Link to this definition"></a></dt>
<dd><p>Gets the unique point tag for this <cite>MeasurementRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.signalid">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">signalid</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">UUID</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.signalid" title="Link to this definition"></a></dt>
<dd><p>Gets the unique guid-based signal identifier for this <cite>MeasurementRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.signalreference">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">signalreference</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.signalreference" title="Link to this definition"></a></dt>
<dd><p>Gets the unique signal reference for this <cite>MeasurementRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.signaltype">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">signaltype</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.metadata.record.measurement.SignalType" title="sttp.metadata.record.measurement.SignalType"><span class="pre">SignalType</span></a></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.signaltype" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>SignalType</cite> enumeration for this <cite>MeasurementRecord</cite>, if it can
be parsed from <cite>signaltypename</cite>; otherwise, returns <cite>SignalType.UNKN</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.signaltypename">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">signaltypename</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.signaltypename" title="Link to this definition"></a></dt>
<dd><p>Gets the signal type name for this <cite>MeasurementRecord</cite>, e.g., “FREQ”.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.source">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">source</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.source" title="Link to this definition"></a></dt>
<dd><p>Gets the STTP source instance (from measurement key) for this <cite>MeasurementRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.MeasurementRecord.updatedon">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">updatedon</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">datetime</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.MeasurementRecord.updatedon" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>datetime</cite> of when this <cite>MeasurementRecord</cite> was last updated.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.SignalType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.metadata.record.measurement.</span></span><span class="sig-name descname"><span class="pre">SignalType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names=&lt;not</span> <span class="pre">given&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start=1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary=None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/metadata/record/measurement.html#SignalType"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.metadata.record.measurement.SignalType" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></p>
<p>Represents common signal types for STTP metadata. This list may
not be exhaustive for some STTP deployments. If value is set to
<cite>UNKN</cite>, check the string based <cite>SignalTypeName</cite> in the <cite>MeasurementRecord</cite>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.SignalType.ALOG">
<span class="sig-name descname"><span class="pre">ALOG</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">7</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.SignalType.ALOG" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.SignalType.ALRM">
<span class="sig-name descname"><span class="pre">ALRM</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">12</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.SignalType.ALRM" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.SignalType.CALC">
<span class="sig-name descname"><span class="pre">CALC</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">10</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.SignalType.CALC" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.SignalType.DFDT">
<span class="sig-name descname"><span class="pre">DFDT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">6</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.SignalType.DFDT" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.SignalType.DIGI">
<span class="sig-name descname"><span class="pre">DIGI</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">9</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.SignalType.DIGI" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.SignalType.FLAG">
<span class="sig-name descname"><span class="pre">FLAG</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">8</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.SignalType.FLAG" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.SignalType.FREQ">
<span class="sig-name descname"><span class="pre">FREQ</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">5</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.SignalType.FREQ" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.SignalType.IPHA">
<span class="sig-name descname"><span class="pre">IPHA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.SignalType.IPHA" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.SignalType.IPHM">
<span class="sig-name descname"><span class="pre">IPHM</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.SignalType.IPHM" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.SignalType.QUAL">
<span class="sig-name descname"><span class="pre">QUAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">13</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.SignalType.QUAL" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.SignalType.STAT">
<span class="sig-name descname"><span class="pre">STAT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">11</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.SignalType.STAT" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.SignalType.UNKN">
<span class="sig-name descname"><span class="pre">UNKN</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">-1</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.SignalType.UNKN" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.SignalType.VPHA">
<span class="sig-name descname"><span class="pre">VPHA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">4</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.SignalType.VPHA" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.SignalType.VPHM">
<span class="sig-name descname"><span class="pre">VPHM</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">3</span></em><a class="headerlink" href="#sttp.metadata.record.measurement.SignalType.VPHM" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.metadata.record.measurement.SignalType.parse">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="#sttp.metadata.record.measurement.SignalType" title="sttp.metadata.record.measurement.SignalType"><span class="pre">SignalType</span></a></span></span><a class="reference internal" href="_modules/sttp/metadata/record/measurement.html#SignalType.parse"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.metadata.record.measurement.SignalType.parse" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-sttp.metadata.record.phasor">
<span id="sttp-metadata-record-phasor-module"></span><h2>sttp.metadata.record.phasor module<a class="headerlink" href="#module-sttp.metadata.record.phasor" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.metadata.record.phasor.CompositePhasorMeasurement">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.metadata.record.phasor.</span></span><span class="sig-name descname"><span class="pre">CompositePhasorMeasurement</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names=&lt;not</span> <span class="pre">given&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start=1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary=None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/metadata/record/phasor.html#CompositePhasorMeasurement"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.metadata.record.phasor.CompositePhasorMeasurement" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.phasor.CompositePhasorMeasurement.ANGLE">
<span class="sig-name descname"><span class="pre">ANGLE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#sttp.metadata.record.phasor.CompositePhasorMeasurement.ANGLE" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.phasor.CompositePhasorMeasurement.MAGNITUDE">
<span class="sig-name descname"><span class="pre">MAGNITUDE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1</span></em><a class="headerlink" href="#sttp.metadata.record.phasor.CompositePhasorMeasurement.MAGNITUDE" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sttp.metadata.record.phasor.PhasorRecord">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.metadata.record.phasor.</span></span><span class="sig-name descname"><span class="pre">PhasorRecord</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">deviceacronym</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">label</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">phase</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sourceindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">basekv</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">updatedon</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">datetime</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/metadata/record/phasor.html#PhasorRecord"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.metadata.record.phasor.PhasorRecord" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Represents a record of phasor metadata in the STTP.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.phasor.PhasorRecord.DEFAULT_BASEKV">
<span class="sig-name descname"><span class="pre">DEFAULT_BASEKV</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">500</span></em><a class="headerlink" href="#sttp.metadata.record.phasor.PhasorRecord.DEFAULT_BASEKV" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.phasor.PhasorRecord.DEFAULT_PHASE">
<span class="sig-name descname"><span class="pre">DEFAULT_PHASE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'+'</span></em><a class="headerlink" href="#sttp.metadata.record.phasor.PhasorRecord.DEFAULT_PHASE" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.phasor.PhasorRecord.DEFAULT_TYPE">
<span class="sig-name descname"><span class="pre">DEFAULT_TYPE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'V'</span></em><a class="headerlink" href="#sttp.metadata.record.phasor.PhasorRecord.DEFAULT_TYPE" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.phasor.PhasorRecord.DEFAULT_UPDATEDON">
<span class="sig-name descname"><span class="pre">DEFAULT_UPDATEDON</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">datetime.datetime(1,</span> <span class="pre">1,</span> <span class="pre">1,</span> <span class="pre">0,</span> <span class="pre">0,</span> <span class="pre">tzinfo=datetime.timezone.utc)</span></em><a class="headerlink" href="#sttp.metadata.record.phasor.PhasorRecord.DEFAULT_UPDATEDON" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.phasor.PhasorRecord.angle_measurement">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">angle_measurement</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord" title="sttp.metadata.record.measurement.MeasurementRecord"><span class="pre">MeasurementRecord</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#sttp.metadata.record.phasor.PhasorRecord.angle_measurement" title="Link to this definition"></a></dt>
<dd><p>Gets the associated angle <cite>MeasurementRecord</cite>, or <cite>None</cite> if not available.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.phasor.PhasorRecord.basekv">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">basekv</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">int</span></em><a class="headerlink" href="#sttp.metadata.record.phasor.PhasorRecord.basekv" title="Link to this definition"></a></dt>
<dd><p>Gets the base, i.e., nominal, kV level for this <cite>PhasorRecord</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.phasor.PhasorRecord.device">
<span class="sig-name descname"><span class="pre">device</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.metadata.record.device.DeviceRecord" title="sttp.metadata.record.device.DeviceRecord"><span class="pre">DeviceRecord</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#sttp.metadata.record.phasor.PhasorRecord.device" title="Link to this definition"></a></dt>
<dd><p>Defines the associated <cite>DeviceRecord</cite> for this <cite>PhasorRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.phasor.PhasorRecord.deviceacronym">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">deviceacronym</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#sttp.metadata.record.phasor.PhasorRecord.deviceacronym" title="Link to this definition"></a></dt>
<dd><p>Gets the alpha-numeric identifier of the associated device for this <cite>PhasorRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.phasor.PhasorRecord.id">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">int</span></em><a class="headerlink" href="#sttp.metadata.record.phasor.PhasorRecord.id" title="Link to this definition"></a></dt>
<dd><p>Gets the unique integer identifier for this <cite>PhasorRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.phasor.PhasorRecord.label">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">label</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#sttp.metadata.record.phasor.PhasorRecord.label" title="Link to this definition"></a></dt>
<dd><p>Gets the free form label for this <cite>PhasorRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.phasor.PhasorRecord.magnitude_measurement">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">magnitude_measurement</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord" title="sttp.metadata.record.measurement.MeasurementRecord"><span class="pre">MeasurementRecord</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#sttp.metadata.record.phasor.PhasorRecord.magnitude_measurement" title="Link to this definition"></a></dt>
<dd><p>Gets the associated magnitude <cite>MeasurementRecord</cite>, or <cite>None</cite> if not available.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.metadata.record.phasor.PhasorRecord.measurements">
<span class="sig-name descname"><span class="pre">measurements</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.metadata.record.measurement.MeasurementRecord" title="sttp.metadata.record.measurement.MeasurementRecord"><span class="pre">MeasurementRecord</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#sttp.metadata.record.phasor.PhasorRecord.measurements" title="Link to this definition"></a></dt>
<dd><p>Defines the two <cite>MeasurementRecord</cite> values, i.e., the angle and magnitude, associated with this <cite>PhasorRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.phasor.PhasorRecord.phase">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">phase</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#sttp.metadata.record.phasor.PhasorRecord.phase" title="Link to this definition"></a></dt>
<dd><p>Gets the phase of this <cite>PhasorRecord</cite>, e.g., “A”, “B”, “C”, “+”, “-”, “0”, etc.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.phasor.PhasorRecord.sourceindex">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">sourceindex</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">int</span></em><a class="headerlink" href="#sttp.metadata.record.phasor.PhasorRecord.sourceindex" title="Link to this definition"></a></dt>
<dd><p>Gets the source index, i.e., the 1-based ordering index of the phasor in its original context, for this <cite>PhasorRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.phasor.PhasorRecord.type">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">type</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#sttp.metadata.record.phasor.PhasorRecord.type" title="Link to this definition"></a></dt>
<dd><p>Gets the phasor type, i.e., “I” or “V”, for current or voltage, respectively, for this <cite>PhasorRecord</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.metadata.record.phasor.PhasorRecord.updatedon">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">updatedon</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">datetime</span></em><a class="headerlink" href="#sttp.metadata.record.phasor.PhasorRecord.updatedon" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>datetime</cite> of when this <cite>PhasorRecord</cite> was last updated.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.metadata.record">
<span id="module-contents"></span><h2>Module contents<a class="headerlink" href="#module-sttp.metadata.record" title="Link to this heading"></a></h2>
</section>
</section>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
  
 
<hr />
<p>
    <a href="https://github.com/sttp/pyapi">GitHub Home</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://pypi.org/project/sttpapi/" target="_blank">PyPi Package: sttpapi</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://sttp.github.io/documentation/" target="_blank">General STTP Documentation</a>
</p>
<br /><br />
Copyright © 2022, <a href="https://gridprotectionalliance.org/" target="_blank">Grid&nbsp;Protection&nbsp;Alliance</a>
<a href="https://github.com/sttp/pyapi">
    <img align="right" style="margin-top: -50px"
        src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/LockPython_64High.png">
</a>


</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>