

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>sttp.transport package &mdash; sttp/pyapi 0.6.4 documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=92fd9be5" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=8209df7b" />

  
    <link rel="shortcut icon" href="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/favicon.ico"/>
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=bbec6902"></script>
      <script src="_static/documentation_options.js?v=b1f64a84"></script>
      <script src="_static/doctools.js?v=92e14aea"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            sttp/pyapi
              <img src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/sttp.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <!-- Local TOC -->
              <div class="local-toc"><ul>
<li><a class="reference internal" href="#">sttp.transport package</a><ul>
<li><a class="reference internal" href="#subpackages">Subpackages</a></li>
<li><a class="reference internal" href="#submodules">Submodules</a></li>
<li><a class="reference internal" href="#module-sttp.transport.bufferblock">sttp.transport.bufferblock module</a><ul>
<li><a class="reference internal" href="#sttp.transport.bufferblock.BufferBlock"><code class="docutils literal notranslate"><span class="pre">BufferBlock</span></code></a><ul>
<li><a class="reference internal" href="#sttp.transport.bufferblock.BufferBlock.DEFAULT_BUFFER"><code class="docutils literal notranslate"><span class="pre">BufferBlock.DEFAULT_BUFFER</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.bufferblock.BufferBlock.DEFAULT_SIGNALID"><code class="docutils literal notranslate"><span class="pre">BufferBlock.DEFAULT_SIGNALID</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.bufferblock.BufferBlock.buffer"><code class="docutils literal notranslate"><span class="pre">BufferBlock.buffer</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.bufferblock.BufferBlock.signalid"><code class="docutils literal notranslate"><span class="pre">BufferBlock.signalid</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.transport.compactmeasurement">sttp.transport.compactmeasurement module</a><ul>
<li><a class="reference internal" href="#sttp.transport.compactmeasurement.CompactMeasurement"><code class="docutils literal notranslate"><span class="pre">CompactMeasurement</span></code></a><ul>
<li><a class="reference internal" href="#sttp.transport.compactmeasurement.CompactMeasurement.decode"><code class="docutils literal notranslate"><span class="pre">CompactMeasurement.decode()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.compactmeasurement.CompactMeasurement.get_binarylength"><code class="docutils literal notranslate"><span class="pre">CompactMeasurement.get_binarylength()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.compactmeasurement.CompactMeasurement.get_compact_stateflags"><code class="docutils literal notranslate"><span class="pre">CompactMeasurement.get_compact_stateflags()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.compactmeasurement.CompactMeasurement.get_timestamp_c2"><code class="docutils literal notranslate"><span class="pre">CompactMeasurement.get_timestamp_c2()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.compactmeasurement.CompactMeasurement.get_timestamp_c4"><code class="docutils literal notranslate"><span class="pre">CompactMeasurement.get_timestamp_c4()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.compactmeasurement.CompactMeasurement.runtimeid"><code class="docutils literal notranslate"><span class="pre">CompactMeasurement.runtimeid</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.compactmeasurement.CompactMeasurement.set_compact_stateflags"><code class="docutils literal notranslate"><span class="pre">CompactMeasurement.set_compact_stateflags()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#sttp.transport.compactmeasurement.CompactStateFlags"><code class="docutils literal notranslate"><span class="pre">CompactStateFlags</span></code></a><ul>
<li><a class="reference internal" href="#sttp.transport.compactmeasurement.CompactStateFlags.BASETIMEOFFSET"><code class="docutils literal notranslate"><span class="pre">CompactStateFlags.BASETIMEOFFSET</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.compactmeasurement.CompactStateFlags.CALCULATEDVALUE"><code class="docutils literal notranslate"><span class="pre">CompactStateFlags.CALCULATEDVALUE</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.compactmeasurement.CompactStateFlags.DATAQUALITY"><code class="docutils literal notranslate"><span class="pre">CompactStateFlags.DATAQUALITY</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.compactmeasurement.CompactStateFlags.DATARANGE"><code class="docutils literal notranslate"><span class="pre">CompactStateFlags.DATARANGE</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.compactmeasurement.CompactStateFlags.DISCARDEDVALUE"><code class="docutils literal notranslate"><span class="pre">CompactStateFlags.DISCARDEDVALUE</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.compactmeasurement.CompactStateFlags.SYSTEMISSUE"><code class="docutils literal notranslate"><span class="pre">CompactStateFlags.SYSTEMISSUE</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.compactmeasurement.CompactStateFlags.TIMEINDEX"><code class="docutils literal notranslate"><span class="pre">CompactStateFlags.TIMEINDEX</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.compactmeasurement.CompactStateFlags.TIMEQUALITY"><code class="docutils literal notranslate"><span class="pre">CompactStateFlags.TIMEQUALITY</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.transport.constants">sttp.transport.constants module</a><ul>
<li><a class="reference internal" href="#sttp.transport.constants.CompressionModes"><code class="docutils literal notranslate"><span class="pre">CompressionModes</span></code></a><ul>
<li><a class="reference internal" href="#sttp.transport.constants.CompressionModes.GZIP"><code class="docutils literal notranslate"><span class="pre">CompressionModes.GZIP</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.CompressionModes.NOFLAGS"><code class="docutils literal notranslate"><span class="pre">CompressionModes.NOFLAGS</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.CompressionModes.TSSC"><code class="docutils literal notranslate"><span class="pre">CompressionModes.TSSC</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#sttp.transport.constants.ConnectStatus"><code class="docutils literal notranslate"><span class="pre">ConnectStatus</span></code></a><ul>
<li><a class="reference internal" href="#sttp.transport.constants.ConnectStatus.CANCELED"><code class="docutils literal notranslate"><span class="pre">ConnectStatus.CANCELED</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ConnectStatus.FAILED"><code class="docutils literal notranslate"><span class="pre">ConnectStatus.FAILED</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ConnectStatus.SUCCESS"><code class="docutils literal notranslate"><span class="pre">ConnectStatus.SUCCESS</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#sttp.transport.constants.DataPacketFlags"><code class="docutils literal notranslate"><span class="pre">DataPacketFlags</span></code></a><ul>
<li><a class="reference internal" href="#sttp.transport.constants.DataPacketFlags.CACHEINDEX"><code class="docutils literal notranslate"><span class="pre">DataPacketFlags.CACHEINDEX</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.DataPacketFlags.CIPHERINDEX"><code class="docutils literal notranslate"><span class="pre">DataPacketFlags.CIPHERINDEX</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.DataPacketFlags.COMPACT"><code class="docutils literal notranslate"><span class="pre">DataPacketFlags.COMPACT</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.DataPacketFlags.COMPRESSED"><code class="docutils literal notranslate"><span class="pre">DataPacketFlags.COMPRESSED</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.DataPacketFlags.NOFLAGS"><code class="docutils literal notranslate"><span class="pre">DataPacketFlags.NOFLAGS</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults"><code class="docutils literal notranslate"><span class="pre">Defaults</span></code></a><ul>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.AUTORECONNECT"><code class="docutils literal notranslate"><span class="pre">Defaults.AUTORECONNECT</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.AUTOREQUESTMETADATA"><code class="docutils literal notranslate"><span class="pre">Defaults.AUTOREQUESTMETADATA</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.AUTOSUBSCRIBE"><code class="docutils literal notranslate"><span class="pre">Defaults.AUTOSUBSCRIBE</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.COMPRESS_METADATA"><code class="docutils literal notranslate"><span class="pre">Defaults.COMPRESS_METADATA</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.COMPRESS_PAYLOADDATA"><code class="docutils literal notranslate"><span class="pre">Defaults.COMPRESS_PAYLOADDATA</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.COMPRESS_SIGNALINDEXCACHE"><code class="docutils literal notranslate"><span class="pre">Defaults.COMPRESS_SIGNALINDEXCACHE</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.CONSTRAINTPARAMETERS"><code class="docutils literal notranslate"><span class="pre">Defaults.CONSTRAINTPARAMETERS</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.DATACHANNEL_INTERFACE"><code class="docutils literal notranslate"><span class="pre">Defaults.DATACHANNEL_INTERFACE</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.DATACHANNEL_LOCALPORT"><code class="docutils literal notranslate"><span class="pre">Defaults.DATACHANNEL_LOCALPORT</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.ENABLE_TIME_REASONABILITY_CHECK"><code class="docutils literal notranslate"><span class="pre">Defaults.ENABLE_TIME_REASONABILITY_CHECK</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.EXTRA_CONNECTIONSTRING_PARAMETERS"><code class="docutils literal notranslate"><span class="pre">Defaults.EXTRA_CONNECTIONSTRING_PARAMETERS</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.FILTEREXPRESSION"><code class="docutils literal notranslate"><span class="pre">Defaults.FILTEREXPRESSION</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.INCLUDETIME"><code class="docutils literal notranslate"><span class="pre">Defaults.INCLUDETIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.LAGTIME"><code class="docutils literal notranslate"><span class="pre">Defaults.LAGTIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.LEADTIME"><code class="docutils literal notranslate"><span class="pre">Defaults.LEADTIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.MAXRETRIES"><code class="docutils literal notranslate"><span class="pre">Defaults.MAXRETRIES</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.MAXRETRYINTERVAL"><code class="docutils literal notranslate"><span class="pre">Defaults.MAXRETRYINTERVAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.METADATAFILTERS"><code class="docutils literal notranslate"><span class="pre">Defaults.METADATAFILTERS</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.PROCESSINGINTERVAL"><code class="docutils literal notranslate"><span class="pre">Defaults.PROCESSINGINTERVAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.PUBLISHINTERVAL"><code class="docutils literal notranslate"><span class="pre">Defaults.PUBLISHINTERVAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.REQUEST_NANVALUEFILTER"><code class="docutils literal notranslate"><span class="pre">Defaults.REQUEST_NANVALUEFILTER</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.RETRYINTERVAL"><code class="docutils literal notranslate"><span class="pre">Defaults.RETRYINTERVAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.SOCKET_TIMEOUT"><code class="docutils literal notranslate"><span class="pre">Defaults.SOCKET_TIMEOUT</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.STARTTIME"><code class="docutils literal notranslate"><span class="pre">Defaults.STARTTIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.STOPTIME"><code class="docutils literal notranslate"><span class="pre">Defaults.STOPTIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.THROTTLED"><code class="docutils literal notranslate"><span class="pre">Defaults.THROTTLED</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.UDPDATACHANNEL"><code class="docutils literal notranslate"><span class="pre">Defaults.UDPDATACHANNEL</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.USE_LOCALCLOCK_AS_REALTIME"><code class="docutils literal notranslate"><span class="pre">Defaults.USE_LOCALCLOCK_AS_REALTIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.USE_MILLISECONDRESOLUTION"><code class="docutils literal notranslate"><span class="pre">Defaults.USE_MILLISECONDRESOLUTION</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.Defaults.VERSION"><code class="docutils literal notranslate"><span class="pre">Defaults.VERSION</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#sttp.transport.constants.OperationalEncoding"><code class="docutils literal notranslate"><span class="pre">OperationalEncoding</span></code></a><ul>
<li><a class="reference internal" href="#sttp.transport.constants.OperationalEncoding.UTF16BE"><code class="docutils literal notranslate"><span class="pre">OperationalEncoding.UTF16BE</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.OperationalEncoding.UTF16LE"><code class="docutils literal notranslate"><span class="pre">OperationalEncoding.UTF16LE</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.OperationalEncoding.UTF8"><code class="docutils literal notranslate"><span class="pre">OperationalEncoding.UTF8</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#sttp.transport.constants.OperationalModes"><code class="docutils literal notranslate"><span class="pre">OperationalModes</span></code></a><ul>
<li><a class="reference internal" href="#sttp.transport.constants.OperationalModes.COMPRESSMETADATA"><code class="docutils literal notranslate"><span class="pre">OperationalModes.COMPRESSMETADATA</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.OperationalModes.COMPRESSPAYLOADDATA"><code class="docutils literal notranslate"><span class="pre">OperationalModes.COMPRESSPAYLOADDATA</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.OperationalModes.COMPRESSSIGNALINDEXCACHE"><code class="docutils literal notranslate"><span class="pre">OperationalModes.COMPRESSSIGNALINDEXCACHE</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.OperationalModes.ENCODINGMASK"><code class="docutils literal notranslate"><span class="pre">OperationalModes.ENCODINGMASK</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.OperationalModes.IMPLEMENTATIONSPECIFICEXTENSIONMASK"><code class="docutils literal notranslate"><span class="pre">OperationalModes.IMPLEMENTATIONSPECIFICEXTENSIONMASK</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.OperationalModes.NOFLAGS"><code class="docutils literal notranslate"><span class="pre">OperationalModes.NOFLAGS</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.OperationalModes.RECEIVEEXTERNALMETADATA"><code class="docutils literal notranslate"><span class="pre">OperationalModes.RECEIVEEXTERNALMETADATA</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.OperationalModes.RECEIVEINTERNALMETADATA"><code class="docutils literal notranslate"><span class="pre">OperationalModes.RECEIVEINTERNALMETADATA</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.OperationalModes.VERSIONMASK"><code class="docutils literal notranslate"><span class="pre">OperationalModes.VERSIONMASK</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#sttp.transport.constants.SecurityMode"><code class="docutils literal notranslate"><span class="pre">SecurityMode</span></code></a><ul>
<li><a class="reference internal" href="#sttp.transport.constants.SecurityMode.OFF"><code class="docutils literal notranslate"><span class="pre">SecurityMode.OFF</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.SecurityMode.TLS"><code class="docutils literal notranslate"><span class="pre">SecurityMode.TLS</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand"><code class="docutils literal notranslate"><span class="pre">ServerCommand</span></code></a><ul>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.CONFIRMBUFFERBLOCK"><code class="docutils literal notranslate"><span class="pre">ServerCommand.CONFIRMBUFFERBLOCK</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.CONFIRMNOTIFICATION"><code class="docutils literal notranslate"><span class="pre">ServerCommand.CONFIRMNOTIFICATION</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.CONFIRMUPDATEBASETIMES"><code class="docutils literal notranslate"><span class="pre">ServerCommand.CONFIRMUPDATEBASETIMES</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.CONFIRMUPDATECIPHERKEYS"><code class="docutils literal notranslate"><span class="pre">ServerCommand.CONFIRMUPDATECIPHERKEYS</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.CONFIRMUPDATESIGNALINDEXCACHE"><code class="docutils literal notranslate"><span class="pre">ServerCommand.CONFIRMUPDATESIGNALINDEXCACHE</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.CONNECT"><code class="docutils literal notranslate"><span class="pre">ServerCommand.CONNECT</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.DEFINEOPERATIONALMODES"><code class="docutils literal notranslate"><span class="pre">ServerCommand.DEFINEOPERATIONALMODES</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.GETPRIMARYMETADATASCHEMA"><code class="docutils literal notranslate"><span class="pre">ServerCommand.GETPRIMARYMETADATASCHEMA</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.GETSIGNALSELECTIONSCHEMA"><code class="docutils literal notranslate"><span class="pre">ServerCommand.GETSIGNALSELECTIONSCHEMA</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.METADATAREFRESH"><code class="docutils literal notranslate"><span class="pre">ServerCommand.METADATAREFRESH</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.ROTATECIPHERKEYS"><code class="docutils literal notranslate"><span class="pre">ServerCommand.ROTATECIPHERKEYS</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.SUBSCRIBE"><code class="docutils literal notranslate"><span class="pre">ServerCommand.SUBSCRIBE</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.UNSUBSCRIBE"><code class="docutils literal notranslate"><span class="pre">ServerCommand.UNSUBSCRIBE</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.UPDATEPROCESSINGINTERVAL"><code class="docutils literal notranslate"><span class="pre">ServerCommand.UPDATEPROCESSINGINTERVAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.USERCOMMAND00"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND00</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.USERCOMMAND01"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND01</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.USERCOMMAND02"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND02</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.USERCOMMAND03"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND03</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.USERCOMMAND04"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND04</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.USERCOMMAND05"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND05</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.USERCOMMAND06"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND06</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.USERCOMMAND07"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND07</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.USERCOMMAND08"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND08</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.USERCOMMAND09"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND09</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.USERCOMMAND10"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND10</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.USERCOMMAND11"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND11</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.USERCOMMAND12"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND12</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.USERCOMMAND13"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND13</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.USERCOMMAND14"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND14</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerCommand.USERCOMMAND15"><code class="docutils literal notranslate"><span class="pre">ServerCommand.USERCOMMAND15</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse"><code class="docutils literal notranslate"><span class="pre">ServerResponse</span></code></a><ul>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.BUFFERBLOCK"><code class="docutils literal notranslate"><span class="pre">ServerResponse.BUFFERBLOCK</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.CONFIGURATIONCHANGED"><code class="docutils literal notranslate"><span class="pre">ServerResponse.CONFIGURATIONCHANGED</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.DATAPACKET"><code class="docutils literal notranslate"><span class="pre">ServerResponse.DATAPACKET</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.DATASTARTTIME"><code class="docutils literal notranslate"><span class="pre">ServerResponse.DATASTARTTIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.FAILED"><code class="docutils literal notranslate"><span class="pre">ServerResponse.FAILED</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.NOOP"><code class="docutils literal notranslate"><span class="pre">ServerResponse.NOOP</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.NOTIFY"><code class="docutils literal notranslate"><span class="pre">ServerResponse.NOTIFY</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.PROCESSINGCOMPLETE"><code class="docutils literal notranslate"><span class="pre">ServerResponse.PROCESSINGCOMPLETE</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.SUCCEEDED"><code class="docutils literal notranslate"><span class="pre">ServerResponse.SUCCEEDED</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.UPDATEBASETIMES"><code class="docutils literal notranslate"><span class="pre">ServerResponse.UPDATEBASETIMES</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.UPDATECIPHERKEYS"><code class="docutils literal notranslate"><span class="pre">ServerResponse.UPDATECIPHERKEYS</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.UPDATESIGNALINDEXCACHE"><code class="docutils literal notranslate"><span class="pre">ServerResponse.UPDATESIGNALINDEXCACHE</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.USERRESPONSE00"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE00</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.USERRESPONSE01"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE01</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.USERRESPONSE02"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE02</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.USERRESPONSE03"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE03</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.USERRESPONSE04"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE04</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.USERRESPONSE05"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE05</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.USERRESPONSE06"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE06</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.USERRESPONSE07"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE07</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.USERRESPONSE08"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE08</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.USERRESPONSE09"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE09</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.USERRESPONSE10"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE10</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.USERRESPONSE11"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE11</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.USERRESPONSE12"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE12</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.USERRESPONSE13"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE13</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.USERRESPONSE14"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE14</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.ServerResponse.USERRESPONSE15"><code class="docutils literal notranslate"><span class="pre">ServerResponse.USERRESPONSE15</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags"><code class="docutils literal notranslate"><span class="pre">StateFlags</span></code></a><ul>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.ALARMHIGH"><code class="docutils literal notranslate"><span class="pre">StateFlags.ALARMHIGH</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.ALARMLOW"><code class="docutils literal notranslate"><span class="pre">StateFlags.ALARMLOW</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.BADDATA"><code class="docutils literal notranslate"><span class="pre">StateFlags.BADDATA</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.BADTIME"><code class="docutils literal notranslate"><span class="pre">StateFlags.BADTIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.CALCULATEDVALUE"><code class="docutils literal notranslate"><span class="pre">StateFlags.CALCULATEDVALUE</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.CALCULATIONERROR"><code class="docutils literal notranslate"><span class="pre">StateFlags.CALCULATIONERROR</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.CALCULATIONWARNING"><code class="docutils literal notranslate"><span class="pre">StateFlags.CALCULATIONWARNING</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.COMPARISONALARM"><code class="docutils literal notranslate"><span class="pre">StateFlags.COMPARISONALARM</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.DISCARDEDVALUE"><code class="docutils literal notranslate"><span class="pre">StateFlags.DISCARDEDVALUE</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.DOWNSAMPLED"><code class="docutils literal notranslate"><span class="pre">StateFlags.DOWNSAMPLED</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.FLATLINEALARM"><code class="docutils literal notranslate"><span class="pre">StateFlags.FLATLINEALARM</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.FUTURETIMEALARM"><code class="docutils literal notranslate"><span class="pre">StateFlags.FUTURETIMEALARM</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.LATETIMEALARM"><code class="docutils literal notranslate"><span class="pre">StateFlags.LATETIMEALARM</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.MEASUREMENTERROR"><code class="docutils literal notranslate"><span class="pre">StateFlags.MEASUREMENTERROR</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.NORMAL"><code class="docutils literal notranslate"><span class="pre">StateFlags.NORMAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.OVERRANGEERROR"><code class="docutils literal notranslate"><span class="pre">StateFlags.OVERRANGEERROR</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.RECEIVEDASBAD"><code class="docutils literal notranslate"><span class="pre">StateFlags.RECEIVEDASBAD</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.RESERVEDQUALITYFLAG"><code class="docutils literal notranslate"><span class="pre">StateFlags.RESERVEDQUALITYFLAG</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.RESERVEDTIMEFLAG"><code class="docutils literal notranslate"><span class="pre">StateFlags.RESERVEDTIMEFLAG</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.ROCALARM"><code class="docutils literal notranslate"><span class="pre">StateFlags.ROCALARM</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.SUSPECTDATA"><code class="docutils literal notranslate"><span class="pre">StateFlags.SUSPECTDATA</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.SUSPECTTIME"><code class="docutils literal notranslate"><span class="pre">StateFlags.SUSPECTTIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.SYSTEMERROR"><code class="docutils literal notranslate"><span class="pre">StateFlags.SYSTEMERROR</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.SYSTEMWARNING"><code class="docutils literal notranslate"><span class="pre">StateFlags.SYSTEMWARNING</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.UNDERRANGEERROR"><code class="docutils literal notranslate"><span class="pre">StateFlags.UNDERRANGEERROR</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.UPSAMPLED"><code class="docutils literal notranslate"><span class="pre">StateFlags.UPSAMPLED</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.USERDEFINEDFLAG1"><code class="docutils literal notranslate"><span class="pre">StateFlags.USERDEFINEDFLAG1</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.USERDEFINEDFLAG2"><code class="docutils literal notranslate"><span class="pre">StateFlags.USERDEFINEDFLAG2</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.USERDEFINEDFLAG3"><code class="docutils literal notranslate"><span class="pre">StateFlags.USERDEFINEDFLAG3</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.USERDEFINEDFLAG4"><code class="docutils literal notranslate"><span class="pre">StateFlags.USERDEFINEDFLAG4</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.USERDEFINEDFLAG5"><code class="docutils literal notranslate"><span class="pre">StateFlags.USERDEFINEDFLAG5</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.WARNINGHIGH"><code class="docutils literal notranslate"><span class="pre">StateFlags.WARNINGHIGH</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.constants.StateFlags.WARNINGLOW"><code class="docutils literal notranslate"><span class="pre">StateFlags.WARNINGLOW</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.transport.datasubscriber">sttp.transport.datasubscriber module</a><ul>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber"><code class="docutils literal notranslate"><span class="pre">DataSubscriber</span></code></a><ul>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_COMPRESS_METADATA"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.DEFAULT_COMPRESS_METADATA</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_COMPRESS_PAYLOADDATA"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.DEFAULT_COMPRESS_PAYLOADDATA</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_COMPRESS_SIGNALINDEXCACHE"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.DEFAULT_COMPRESS_SIGNALINDEXCACHE</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_SOCKET_TIMEOUT"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.DEFAULT_SOCKET_TIMEOUT</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_STTP_SOURCEINFO"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.DEFAULT_STTP_SOURCEINFO</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_STTP_UPDATEDONINFO"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.DEFAULT_STTP_UPDATEDONINFO</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_STTP_VERSIONINFO"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.DEFAULT_STTP_VERSIONINFO</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_VERSION"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.DEFAULT_VERSION</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.activesignalindexcache"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.activesignalindexcache</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.adjustedvalue"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.adjustedvalue()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.autoreconnect_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.autoreconnect_callback</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.compress_metadata"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.compress_metadata</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.compress_payloaddata"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.compress_payloaddata</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.compress_signalindexcache"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.compress_signalindexcache</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.configurationchanged_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.configurationchanged_callback</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.connect"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.connect()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.connected"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.connected</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.connectionterminated_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.connectionterminated_callback</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.connector"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.connector</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.data_starttime_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.data_starttime_callback</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.decodestr"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.decodestr()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.disconnect"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.disconnect()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.dispose"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.dispose()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.disposing"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.disposing</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.encodestr"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.encodestr()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.errormessage_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.errormessage_callback</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.lookup_metadata"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.lookup_metadata()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.metadatacache"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.metadatacache</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.metadatareceived_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.metadatareceived_callback</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.newbufferblocks_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.newbufferblocks_callback</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.newmeasurements_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.newmeasurements_callback</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.notificationreceived_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.notificationreceived_callback</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.processingcomplete_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.processingcomplete_callback</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.send_servercommand"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.send_servercommand()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.send_servercommand_withmessage"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.send_servercommand_withmessage()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.socket_timeout"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.socket_timeout</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.statusmessage_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.statusmessage_callback</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.sttp_sourceinfo"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.sttp_sourceinfo</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.sttp_updatedoninfo"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.sttp_updatedoninfo</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.sttp_versioninfo"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.sttp_versioninfo</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.subscribe"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.subscribe()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.subscribed"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.subscribed</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.subscriberid"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.subscriberid</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.subscription"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.subscription</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.subscriptionupdated_callback"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.subscriptionupdated_callback</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.total_commandchannel_bytesreceived"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.total_commandchannel_bytesreceived</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.total_datachannel_bytesreceived"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.total_datachannel_bytesreceived</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.total_measurementsreceived"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.total_measurementsreceived</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.unsubscribe"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.unsubscribe()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber.version"><code class="docutils literal notranslate"><span class="pre">DataSubscriber.version</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.transport.measurement">sttp.transport.measurement module</a><ul>
<li><a class="reference internal" href="#sttp.transport.measurement.Measurement"><code class="docutils literal notranslate"><span class="pre">Measurement</span></code></a><ul>
<li><a class="reference internal" href="#sttp.transport.measurement.Measurement.DEFAULT_FLAGS"><code class="docutils literal notranslate"><span class="pre">Measurement.DEFAULT_FLAGS</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.measurement.Measurement.DEFAULT_SIGNALID"><code class="docutils literal notranslate"><span class="pre">Measurement.DEFAULT_SIGNALID</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.measurement.Measurement.DEFAULT_TIMESTAMP"><code class="docutils literal notranslate"><span class="pre">Measurement.DEFAULT_TIMESTAMP</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.measurement.Measurement.DEFAULT_VALUE"><code class="docutils literal notranslate"><span class="pre">Measurement.DEFAULT_VALUE</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.measurement.Measurement.datetime"><code class="docutils literal notranslate"><span class="pre">Measurement.datetime</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.measurement.Measurement.flags"><code class="docutils literal notranslate"><span class="pre">Measurement.flags</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.measurement.Measurement.signalid"><code class="docutils literal notranslate"><span class="pre">Measurement.signalid</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.measurement.Measurement.timestamp"><code class="docutils literal notranslate"><span class="pre">Measurement.timestamp</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.measurement.Measurement.timestampvalue"><code class="docutils literal notranslate"><span class="pre">Measurement.timestampvalue</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.measurement.Measurement.value"><code class="docutils literal notranslate"><span class="pre">Measurement.value</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.transport.signalindexcache">sttp.transport.signalindexcache module</a><ul>
<li><a class="reference internal" href="#sttp.transport.signalindexcache.SignalIndexCache"><code class="docutils literal notranslate"><span class="pre">SignalIndexCache</span></code></a><ul>
<li><a class="reference internal" href="#sttp.transport.signalindexcache.SignalIndexCache.contains"><code class="docutils literal notranslate"><span class="pre">SignalIndexCache.contains()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.signalindexcache.SignalIndexCache.count"><code class="docutils literal notranslate"><span class="pre">SignalIndexCache.count</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.signalindexcache.SignalIndexCache.decode"><code class="docutils literal notranslate"><span class="pre">SignalIndexCache.decode()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.signalindexcache.SignalIndexCache.id"><code class="docutils literal notranslate"><span class="pre">SignalIndexCache.id()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.signalindexcache.SignalIndexCache.record"><code class="docutils literal notranslate"><span class="pre">SignalIndexCache.record()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.signalindexcache.SignalIndexCache.signalid"><code class="docutils literal notranslate"><span class="pre">SignalIndexCache.signalid()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.signalindexcache.SignalIndexCache.signalids"><code class="docutils literal notranslate"><span class="pre">SignalIndexCache.signalids</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.signalindexcache.SignalIndexCache.signalindex"><code class="docutils literal notranslate"><span class="pre">SignalIndexCache.signalindex()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.signalindexcache.SignalIndexCache.source"><code class="docutils literal notranslate"><span class="pre">SignalIndexCache.source()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.transport.signalkind">sttp.transport.signalkind module</a><ul>
<li><a class="reference internal" href="#sttp.transport.signalkind.SignalKind"><code class="docutils literal notranslate"><span class="pre">SignalKind</span></code></a><ul>
<li><a class="reference internal" href="#sttp.transport.signalkind.SignalKind.ALARM"><code class="docutils literal notranslate"><span class="pre">SignalKind.ALARM</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.signalkind.SignalKind.ANALOG"><code class="docutils literal notranslate"><span class="pre">SignalKind.ANALOG</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.signalkind.SignalKind.ANGLE"><code class="docutils literal notranslate"><span class="pre">SignalKind.ANGLE</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.signalkind.SignalKind.CALCULATION"><code class="docutils literal notranslate"><span class="pre">SignalKind.CALCULATION</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.signalkind.SignalKind.DFDT"><code class="docutils literal notranslate"><span class="pre">SignalKind.DFDT</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.signalkind.SignalKind.DIGITAL"><code class="docutils literal notranslate"><span class="pre">SignalKind.DIGITAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.signalkind.SignalKind.FREQUENCY"><code class="docutils literal notranslate"><span class="pre">SignalKind.FREQUENCY</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.signalkind.SignalKind.MAGNITUDE"><code class="docutils literal notranslate"><span class="pre">SignalKind.MAGNITUDE</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.signalkind.SignalKind.QUALITY"><code class="docutils literal notranslate"><span class="pre">SignalKind.QUALITY</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.signalkind.SignalKind.STATISTIC"><code class="docutils literal notranslate"><span class="pre">SignalKind.STATISTIC</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.signalkind.SignalKind.STATUS"><code class="docutils literal notranslate"><span class="pre">SignalKind.STATUS</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.signalkind.SignalKind.UNKNOWN"><code class="docutils literal notranslate"><span class="pre">SignalKind.UNKNOWN</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#sttp.transport.signalkind.SignalKindEnum"><code class="docutils literal notranslate"><span class="pre">SignalKindEnum</span></code></a><ul>
<li><a class="reference internal" href="#sttp.transport.signalkind.SignalKindEnum.acronym"><code class="docutils literal notranslate"><span class="pre">SignalKindEnum.acronym()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.signalkind.SignalKindEnum.parse_acronym"><code class="docutils literal notranslate"><span class="pre">SignalKindEnum.parse_acronym()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.signalkind.SignalKindEnum.signaltype"><code class="docutils literal notranslate"><span class="pre">SignalKindEnum.signaltype()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.transport.subscriberconnector">sttp.transport.subscriberconnector module</a><ul>
<li><a class="reference internal" href="#sttp.transport.subscriberconnector.SubscriberConnector"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector</span></code></a><ul>
<li><a class="reference internal" href="#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_AUTORECONNECT"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.DEFAULT_AUTORECONNECT</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_ERRORMESSAGE_CALLBACK"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.DEFAULT_ERRORMESSAGE_CALLBACK()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_HOSTNAME"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.DEFAULT_HOSTNAME</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_MAXRETRIES"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.DEFAULT_MAXRETRIES</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_MAXRETRYINTERVAL"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.DEFAULT_MAXRETRYINTERVAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_PORT"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.DEFAULT_PORT</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_RECONNECT_CALLBACK"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.DEFAULT_RECONNECT_CALLBACK()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_RETRYINTERVAL"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.DEFAULT_RETRYINTERVAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriberconnector.SubscriberConnector.autoreconnect"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.autoreconnect</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriberconnector.SubscriberConnector.cancel"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.cancel()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriberconnector.SubscriberConnector.connect"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.connect()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriberconnector.SubscriberConnector.dispose"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.dispose()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriberconnector.SubscriberConnector.errormessage_callback"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.errormessage_callback</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriberconnector.SubscriberConnector.hostname"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.hostname</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriberconnector.SubscriberConnector.maxretries"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.maxretries</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriberconnector.SubscriberConnector.maxretryinterval"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.maxretryinterval</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriberconnector.SubscriberConnector.port"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.port</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriberconnector.SubscriberConnector.reconnect_callback"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.reconnect_callback</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriberconnector.SubscriberConnector.reset_connection"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.reset_connection()</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriberconnector.SubscriberConnector.retryinterval"><code class="docutils literal notranslate"><span class="pre">SubscriberConnector.retryinterval</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.transport.subscriptioninfo">sttp.transport.subscriptioninfo module</a><ul>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo</span></code></a><ul>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_CONSTRAINTPARAMETERS"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_CONSTRAINTPARAMETERS</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_DATACHANNEL_INTERFACE"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_DATACHANNEL_INTERFACE</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_DATACHANNEL_LOCALPORT"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_DATACHANNEL_LOCALPORT</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_ENABLE_TIME_REASONABILITY_CHECK"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_ENABLE_TIME_REASONABILITY_CHECK</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_EXTRA_CONNECTIONSTRING_PARAMETERS"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_EXTRA_CONNECTIONSTRING_PARAMETERS</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_FILTEREXPRESSION"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_FILTEREXPRESSION</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_INCLUDETIME"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_INCLUDETIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_LAGTIME"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_LAGTIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_LEADTIME"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_LEADTIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_PROCESSINGINTERVAL"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_PROCESSINGINTERVAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_PUBLISHINTERVAL"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_PUBLISHINTERVAL</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_REQUEST_NANVALUEFILTER"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_REQUEST_NANVALUEFILTER</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_STARTTIME"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_STARTTIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_STOPTIME"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_STOPTIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_THROTTLED"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_THROTTLED</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_UDPDATACHANNEL"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_UDPDATACHANNEL</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_USE_LOCALCLOCK_AS_REALTIME"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_USE_LOCALCLOCK_AS_REALTIME</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_USE_MILLISECONDRESOLUTION"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.DEFAULT_USE_MILLISECONDRESOLUTION</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.constraintparameters"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.constraintparameters</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.datachannel_interface"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.datachannel_interface</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.datachannel_localport"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.datachannel_localport</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.enabletimereasonabilitycheck"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.enabletimereasonabilitycheck</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.extra_connectionstring_parameters"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.extra_connectionstring_parameters</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.filterexpression"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.filterexpression</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.includetime"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.includetime</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.lagtime"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.lagtime</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.leadtime"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.leadtime</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.processinginterval"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.processinginterval</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.publishinterval"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.publishinterval</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.request_nanvaluefilter"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.request_nanvaluefilter</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.starttime"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.starttime</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.stoptime"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.stoptime</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.throttled"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.throttled</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.udpdatachannel"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.udpdatachannel</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.use_millisecondresolution"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.use_millisecondresolution</span></code></a></li>
<li><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.uselocalclockasrealtime"><code class="docutils literal notranslate"><span class="pre">SubscriptionInfo.uselocalclockasrealtime</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#module-sttp.transport">Module contents</a></li>
</ul>
</li>
</ul>
</div>
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">sttp/pyapi</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">sttp.transport package</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/sttp.transport.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="sttp-transport-package">
<h1>sttp.transport package<a class="headerlink" href="#sttp-transport-package" title="Link to this heading"></a></h1>
<section id="subpackages">
<h2>Subpackages<a class="headerlink" href="#subpackages" title="Link to this heading"></a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="sttp.transport.tssc.html">sttp.transport.tssc package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="sttp.transport.tssc.html#submodules">Submodules</a></li>
<li class="toctree-l2"><a class="reference internal" href="sttp.transport.tssc.html#module-sttp.transport.tssc.decoder">sttp.transport.tssc.decoder module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.decoder.Decoder"><code class="docutils literal notranslate"><span class="pre">Decoder</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.decoder.Decoder.sequencenumber"><code class="docutils literal notranslate"><span class="pre">Decoder.sequencenumber</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.decoder.Decoder.set_buffer"><code class="docutils literal notranslate"><span class="pre">Decoder.set_buffer()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.decoder.Decoder.try_get_measurement"><code class="docutils literal notranslate"><span class="pre">Decoder.try_get_measurement()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.transport.tssc.html#module-sttp.transport.tssc.pointmetadata">sttp.transport.tssc.pointmetadata module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords"><code class="docutils literal notranslate"><span class="pre">CodeWords</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.ENDOFSTREAM"><code class="docutils literal notranslate"><span class="pre">CodeWords.ENDOFSTREAM</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.POINTIDXOR12"><code class="docutils literal notranslate"><span class="pre">CodeWords.POINTIDXOR12</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.POINTIDXOR16"><code class="docutils literal notranslate"><span class="pre">CodeWords.POINTIDXOR16</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.POINTIDXOR20"><code class="docutils literal notranslate"><span class="pre">CodeWords.POINTIDXOR20</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.POINTIDXOR24"><code class="docutils literal notranslate"><span class="pre">CodeWords.POINTIDXOR24</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.POINTIDXOR32"><code class="docutils literal notranslate"><span class="pre">CodeWords.POINTIDXOR32</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.POINTIDXOR4"><code class="docutils literal notranslate"><span class="pre">CodeWords.POINTIDXOR4</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.POINTIDXOR8"><code class="docutils literal notranslate"><span class="pre">CodeWords.POINTIDXOR8</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.STATEFLAGS2"><code class="docutils literal notranslate"><span class="pre">CodeWords.STATEFLAGS2</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.STATEFLAGS7BIT32"><code class="docutils literal notranslate"><span class="pre">CodeWords.STATEFLAGS7BIT32</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.TIMEDELTA1FORWARD"><code class="docutils literal notranslate"><span class="pre">CodeWords.TIMEDELTA1FORWARD</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.TIMEDELTA1REVERSE"><code class="docutils literal notranslate"><span class="pre">CodeWords.TIMEDELTA1REVERSE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.TIMEDELTA2FORWARD"><code class="docutils literal notranslate"><span class="pre">CodeWords.TIMEDELTA2FORWARD</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.TIMEDELTA2REVERSE"><code class="docutils literal notranslate"><span class="pre">CodeWords.TIMEDELTA2REVERSE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.TIMEDELTA3FORWARD"><code class="docutils literal notranslate"><span class="pre">CodeWords.TIMEDELTA3FORWARD</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.TIMEDELTA3REVERSE"><code class="docutils literal notranslate"><span class="pre">CodeWords.TIMEDELTA3REVERSE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.TIMEDELTA4FORWARD"><code class="docutils literal notranslate"><span class="pre">CodeWords.TIMEDELTA4FORWARD</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.TIMEDELTA4REVERSE"><code class="docutils literal notranslate"><span class="pre">CodeWords.TIMEDELTA4REVERSE</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.TIMESTAMP2"><code class="docutils literal notranslate"><span class="pre">CodeWords.TIMESTAMP2</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.TIMEXOR7BIT"><code class="docutils literal notranslate"><span class="pre">CodeWords.TIMEXOR7BIT</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.VALUE1"><code class="docutils literal notranslate"><span class="pre">CodeWords.VALUE1</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.VALUE2"><code class="docutils literal notranslate"><span class="pre">CodeWords.VALUE2</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.VALUE3"><code class="docutils literal notranslate"><span class="pre">CodeWords.VALUE3</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.VALUEXOR12"><code class="docutils literal notranslate"><span class="pre">CodeWords.VALUEXOR12</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.VALUEXOR16"><code class="docutils literal notranslate"><span class="pre">CodeWords.VALUEXOR16</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.VALUEXOR20"><code class="docutils literal notranslate"><span class="pre">CodeWords.VALUEXOR20</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.VALUEXOR24"><code class="docutils literal notranslate"><span class="pre">CodeWords.VALUEXOR24</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.VALUEXOR28"><code class="docutils literal notranslate"><span class="pre">CodeWords.VALUEXOR28</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.VALUEXOR32"><code class="docutils literal notranslate"><span class="pre">CodeWords.VALUEXOR32</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.VALUEXOR4"><code class="docutils literal notranslate"><span class="pre">CodeWords.VALUEXOR4</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.VALUEXOR8"><code class="docutils literal notranslate"><span class="pre">CodeWords.VALUEXOR8</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.CodeWords.VALUEZERO"><code class="docutils literal notranslate"><span class="pre">CodeWords.VALUEZERO</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.PointMetadata"><code class="docutils literal notranslate"><span class="pre">PointMetadata</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.PointMetadata.read_code"><code class="docutils literal notranslate"><span class="pre">PointMetadata.read_code()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="sttp.transport.tssc.html#sttp.transport.tssc.pointmetadata.PointMetadata.write_code"><code class="docutils literal notranslate"><span class="pre">PointMetadata.write_code()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sttp.transport.tssc.html#module-sttp.transport.tssc">Module contents</a></li>
</ul>
</li>
</ul>
</div>
</section>
<section id="submodules">
<h2>Submodules<a class="headerlink" href="#submodules" title="Link to this heading"></a></h2>
</section>
<section id="module-sttp.transport.bufferblock">
<span id="sttp-transport-bufferblock-module"></span><h2>sttp.transport.bufferblock module<a class="headerlink" href="#module-sttp.transport.bufferblock" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.transport.bufferblock.BufferBlock">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.transport.bufferblock.</span></span><span class="sig-name descname"><span class="pre">BufferBlock</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">signalid</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">UUID</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">buffer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bytearray</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/bufferblock.html#BufferBlock"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.bufferblock.BufferBlock" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>BufferBlock defines an atomic unit of data, i.e., a binary buffer, for transport in STTP.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.bufferblock.BufferBlock.DEFAULT_BUFFER">
<span class="sig-name descname"><span class="pre">DEFAULT_BUFFER</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">bytearray</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#sttp.transport.bufferblock.BufferBlock.DEFAULT_BUFFER" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.bufferblock.BufferBlock.DEFAULT_SIGNALID">
<span class="sig-name descname"><span class="pre">DEFAULT_SIGNALID</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">UUID('00000000-0000-0000-0000-000000000000')</span></em><a class="headerlink" href="#sttp.transport.bufferblock.BufferBlock.DEFAULT_SIGNALID" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.transport.bufferblock.BufferBlock.buffer">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">buffer</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">bytearray</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#sttp.transport.bufferblock.BufferBlock.buffer" title="Link to this definition"></a></dt>
<dd><p>Gets measurement buffer as an atomic unit of data, i.e., a binary buffer.
This buffer typically represents a partial image of a larger whole.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.bufferblock.BufferBlock.signalid">
<span class="sig-name descname"><span class="pre">signalid</span></span><a class="headerlink" href="#sttp.transport.bufferblock.BufferBlock.signalid" title="Link to this definition"></a></dt>
<dd><p>Defines measurement’s globally unique identifier.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.transport.compactmeasurement">
<span id="sttp-transport-compactmeasurement-module"></span><h2>sttp.transport.compactmeasurement module<a class="headerlink" href="#module-sttp.transport.compactmeasurement" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.transport.compactmeasurement.CompactMeasurement">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.transport.compactmeasurement.</span></span><span class="sig-name descname"><span class="pre">CompactMeasurement</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">signalindexcache</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.transport.signalindexcache.SignalIndexCache" title="sttp.transport.signalindexcache.SignalIndexCache"><span class="pre">SignalIndexCache</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">includetime</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usemillisecondresolution</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">basetimeoffsets</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">List</span><span class="p"><span class="pre">[</span></span><span class="pre">int64</span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">signalid</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">UUID</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">float64</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timestamp</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint64</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.transport.constants.StateFlags" title="sttp.transport.constants.StateFlags"><span class="pre">StateFlags</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/compactmeasurement.html#CompactMeasurement"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.compactmeasurement.CompactMeasurement" title="Link to this definition"></a></dt>
<dd><p>Bases: <a class="reference internal" href="#sttp.transport.measurement.Measurement" title="sttp.transport.measurement.Measurement"><code class="xref py py-class docutils literal notranslate"><span class="pre">Measurement</span></code></a></p>
<p>Represents a measured value, in simple compact format, for transmission or reception in STTP.</p>
<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.compactmeasurement.CompactMeasurement.decode">
<span class="sig-name descname"><span class="pre">decode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bytes</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">int</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/transport/compactmeasurement.html#CompactMeasurement.decode"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.compactmeasurement.CompactMeasurement.decode" title="Link to this definition"></a></dt>
<dd><p>Parses a <cite>CompactMeasurement</cite> from the specified byte buffer.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.compactmeasurement.CompactMeasurement.get_binarylength">
<span class="sig-name descname"><span class="pre">get_binarylength</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint32</span></span></span><a class="reference internal" href="_modules/sttp/transport/compactmeasurement.html#CompactMeasurement.get_binarylength"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.compactmeasurement.CompactMeasurement.get_binarylength" title="Link to this definition"></a></dt>
<dd><p>Gets the binary byte length of a <cite>CompactMeasurement</cite></p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.compactmeasurement.CompactMeasurement.get_compact_stateflags">
<span class="sig-name descname"><span class="pre">get_compact_stateflags</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int8</span></span></span><a class="reference internal" href="_modules/sttp/transport/compactmeasurement.html#CompactMeasurement.get_compact_stateflags"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.compactmeasurement.CompactMeasurement.get_compact_stateflags" title="Link to this definition"></a></dt>
<dd><p>Gets byte level compact state flags with encoded time index and base time offset bits.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.compactmeasurement.CompactMeasurement.get_timestamp_c2">
<span class="sig-name descname"><span class="pre">get_timestamp_c2</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint16</span></span></span><a class="reference internal" href="_modules/sttp/transport/compactmeasurement.html#CompactMeasurement.get_timestamp_c2"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.compactmeasurement.CompactMeasurement.get_timestamp_c2" title="Link to this definition"></a></dt>
<dd><p>Gets offset compressed millisecond-resolution 2-byte timestamp.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.compactmeasurement.CompactMeasurement.get_timestamp_c4">
<span class="sig-name descname"><span class="pre">get_timestamp_c4</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint32</span></span></span><a class="reference internal" href="_modules/sttp/transport/compactmeasurement.html#CompactMeasurement.get_timestamp_c4"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.compactmeasurement.CompactMeasurement.get_timestamp_c4" title="Link to this definition"></a></dt>
<dd><p>Gets offset compressed tick-resolution 4-byte timestamp.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.transport.compactmeasurement.CompactMeasurement.runtimeid">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">runtimeid</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">int32</span></em><a class="headerlink" href="#sttp.transport.compactmeasurement.CompactMeasurement.runtimeid" title="Link to this definition"></a></dt>
<dd><p>Gets the 4-byte run-time signal index for this measurement.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.compactmeasurement.CompactMeasurement.set_compact_stateflags">
<span class="sig-name descname"><span class="pre">set_compact_stateflags</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int8</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/compactmeasurement.html#CompactMeasurement.set_compact_stateflags"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.compactmeasurement.CompactMeasurement.set_compact_stateflags" title="Link to this definition"></a></dt>
<dd><p>Sets byte level compact state flags with encoded time index and base time offset bits.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sttp.transport.compactmeasurement.CompactStateFlags">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.transport.compactmeasurement.</span></span><span class="sig-name descname"><span class="pre">CompactStateFlags</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names=&lt;not</span> <span class="pre">given&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start=1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary=None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/compactmeasurement.html#CompactStateFlags"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.compactmeasurement.CompactStateFlags" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></p>
<p>Enumeration constants represent each flag in the 8-bit compact measurement state flags.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.compactmeasurement.CompactStateFlags.BASETIMEOFFSET">
<span class="sig-name descname"><span class="pre">BASETIMEOFFSET</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">64</span></em><a class="headerlink" href="#sttp.transport.compactmeasurement.CompactStateFlags.BASETIMEOFFSET" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.compactmeasurement.CompactStateFlags.CALCULATEDVALUE">
<span class="sig-name descname"><span class="pre">CALCULATEDVALUE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">16</span></em><a class="headerlink" href="#sttp.transport.compactmeasurement.CompactStateFlags.CALCULATEDVALUE" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.compactmeasurement.CompactStateFlags.DATAQUALITY">
<span class="sig-name descname"><span class="pre">DATAQUALITY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2</span></em><a class="headerlink" href="#sttp.transport.compactmeasurement.CompactStateFlags.DATAQUALITY" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.compactmeasurement.CompactStateFlags.DATARANGE">
<span class="sig-name descname"><span class="pre">DATARANGE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1</span></em><a class="headerlink" href="#sttp.transport.compactmeasurement.CompactStateFlags.DATARANGE" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.compactmeasurement.CompactStateFlags.DISCARDEDVALUE">
<span class="sig-name descname"><span class="pre">DISCARDEDVALUE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">32</span></em><a class="headerlink" href="#sttp.transport.compactmeasurement.CompactStateFlags.DISCARDEDVALUE" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.compactmeasurement.CompactStateFlags.SYSTEMISSUE">
<span class="sig-name descname"><span class="pre">SYSTEMISSUE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">8</span></em><a class="headerlink" href="#sttp.transport.compactmeasurement.CompactStateFlags.SYSTEMISSUE" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.compactmeasurement.CompactStateFlags.TIMEINDEX">
<span class="sig-name descname"><span class="pre">TIMEINDEX</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">128</span></em><a class="headerlink" href="#sttp.transport.compactmeasurement.CompactStateFlags.TIMEINDEX" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.compactmeasurement.CompactStateFlags.TIMEQUALITY">
<span class="sig-name descname"><span class="pre">TIMEQUALITY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">4</span></em><a class="headerlink" href="#sttp.transport.compactmeasurement.CompactStateFlags.TIMEQUALITY" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

</section>
<section id="module-sttp.transport.constants">
<span id="sttp-transport-constants-module"></span><h2>sttp.transport.constants module<a class="headerlink" href="#module-sttp.transport.constants" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.transport.constants.CompressionModes">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.transport.constants.</span></span><span class="sig-name descname"><span class="pre">CompressionModes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names=&lt;not</span> <span class="pre">given&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start=1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary=None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/constants.html#CompressionModes"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.constants.CompressionModes" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></p>
<p>Enumeration of the possible compression modes supported by STTP.</p>
<p>Obsolete: Only used for backwards compatibility with pre-standard STTP implementations.
OperationalModes now supports custom compression types</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.CompressionModes.GZIP">
<span class="sig-name descname"><span class="pre">GZIP</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">32</span></em><a class="headerlink" href="#sttp.transport.constants.CompressionModes.GZIP" title="Link to this definition"></a></dt>
<dd><p>Bit flag used determine if GZip compression will be used to metadata exchange.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.CompressionModes.NOFLAGS">
<span class="sig-name descname"><span class="pre">NOFLAGS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#sttp.transport.constants.CompressionModes.NOFLAGS" title="Link to this definition"></a></dt>
<dd><p>Defines state where no compression will be used.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.CompressionModes.TSSC">
<span class="sig-name descname"><span class="pre">TSSC</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">64</span></em><a class="headerlink" href="#sttp.transport.constants.CompressionModes.TSSC" title="Link to this definition"></a></dt>
<dd><p>Bit flag used determine if the time-series special compression algorithm will be used for data exchange.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sttp.transport.constants.ConnectStatus">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.transport.constants.</span></span><span class="sig-name descname"><span class="pre">ConnectStatus</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names=&lt;not</span> <span class="pre">given&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start=1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary=None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/constants.html#ConnectStatus"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.constants.ConnectStatus" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></p>
<p>Enumeration of the possible connection status results used by the SubscriberConnector.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ConnectStatus.CANCELED">
<span class="sig-name descname"><span class="pre">CANCELED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">-1</span></em><a class="headerlink" href="#sttp.transport.constants.ConnectStatus.CANCELED" title="Link to this definition"></a></dt>
<dd><p>Connection cancelled status.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ConnectStatus.FAILED">
<span class="sig-name descname"><span class="pre">FAILED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#sttp.transport.constants.ConnectStatus.FAILED" title="Link to this definition"></a></dt>
<dd><p>Connection failed status.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ConnectStatus.SUCCESS">
<span class="sig-name descname"><span class="pre">SUCCESS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1</span></em><a class="headerlink" href="#sttp.transport.constants.ConnectStatus.SUCCESS" title="Link to this definition"></a></dt>
<dd><p>Connection succeeded status.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sttp.transport.constants.DataPacketFlags">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.transport.constants.</span></span><span class="sig-name descname"><span class="pre">DataPacketFlags</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names=&lt;not</span> <span class="pre">given&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start=1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary=None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/constants.html#DataPacketFlags"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.constants.DataPacketFlags" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></p>
<p>Enumeration of the possible flags for a data packet.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.DataPacketFlags.CACHEINDEX">
<span class="sig-name descname"><span class="pre">CACHEINDEX</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">16</span></em><a class="headerlink" href="#sttp.transport.constants.DataPacketFlags.CACHEINDEX" title="Link to this definition"></a></dt>
<dd><p>Determines which signal index cache to use when decoding a data packet. Used by STTP version 2 or greater.</p>
<p>Bit set = use odd cache index (i.e., 1), bit clear = use even cache index (i.e., 0).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.DataPacketFlags.CIPHERINDEX">
<span class="sig-name descname"><span class="pre">CIPHERINDEX</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">4</span></em><a class="headerlink" href="#sttp.transport.constants.DataPacketFlags.CIPHERINDEX" title="Link to this definition"></a></dt>
<dd><p>Determines which cipher index to use when encrypting data packet.</p>
<p>Bit set = use odd cipher index (i.e., 1), bit clear = use even cipher index (i.e., 0).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.DataPacketFlags.COMPACT">
<span class="sig-name descname"><span class="pre">COMPACT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2</span></em><a class="headerlink" href="#sttp.transport.constants.DataPacketFlags.COMPACT" title="Link to this definition"></a></dt>
<dd><p>Determines if serialized measurement is compact.</p>
<p>Obsolete: Bit will be removed in future version. Currently this bit is always set.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.DataPacketFlags.COMPRESSED">
<span class="sig-name descname"><span class="pre">COMPRESSED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">8</span></em><a class="headerlink" href="#sttp.transport.constants.DataPacketFlags.COMPRESSED" title="Link to this definition"></a></dt>
<dd><p>Determines if data packet payload is compressed.</p>
<p>Bit set = payload compressed, bit clear = payload normal.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.DataPacketFlags.NOFLAGS">
<span class="sig-name descname"><span class="pre">NOFLAGS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#sttp.transport.constants.DataPacketFlags.NOFLAGS" title="Link to this definition"></a></dt>
<dd><p>Defines state where there are no flags set.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.transport.constants.</span></span><span class="sig-name descname"><span class="pre">Defaults</span></span><a class="reference internal" href="_modules/sttp/transport/constants.html#Defaults"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.constants.Defaults" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.AUTORECONNECT">
<span class="sig-name descname"><span class="pre">AUTORECONNECT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">True</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.AUTORECONNECT" title="Link to this definition"></a></dt>
<dd><p>Default for auto-reconnect flag.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.AUTOREQUESTMETADATA">
<span class="sig-name descname"><span class="pre">AUTOREQUESTMETADATA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">True</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.AUTOREQUESTMETADATA" title="Link to this definition"></a></dt>
<dd><p>Default for auto-request metadata flag.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.AUTOSUBSCRIBE">
<span class="sig-name descname"><span class="pre">AUTOSUBSCRIBE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">True</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.AUTOSUBSCRIBE" title="Link to this definition"></a></dt>
<dd><p>Default for auto-subscribe flag.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.COMPRESS_METADATA">
<span class="sig-name descname"><span class="pre">COMPRESS_METADATA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">True</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.COMPRESS_METADATA" title="Link to this definition"></a></dt>
<dd><p>Default for compress metadata flag.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.COMPRESS_PAYLOADDATA">
<span class="sig-name descname"><span class="pre">COMPRESS_PAYLOADDATA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">True</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.COMPRESS_PAYLOADDATA" title="Link to this definition"></a></dt>
<dd><p>Default for compress payload data flag.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.COMPRESS_SIGNALINDEXCACHE">
<span class="sig-name descname"><span class="pre">COMPRESS_SIGNALINDEXCACHE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">True</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.COMPRESS_SIGNALINDEXCACHE" title="Link to this definition"></a></dt>
<dd><p>Default for compress signal index cache flag.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.CONSTRAINTPARAMETERS">
<span class="sig-name descname"><span class="pre">CONSTRAINTPARAMETERS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.CONSTRAINTPARAMETERS" title="Link to this definition"></a></dt>
<dd><p>Default for constraint parameters.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.DATACHANNEL_INTERFACE">
<span class="sig-name descname"><span class="pre">DATACHANNEL_INTERFACE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.DATACHANNEL_INTERFACE" title="Link to this definition"></a></dt>
<dd><p>Default for interface for data channel.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.DATACHANNEL_LOCALPORT">
<span class="sig-name descname"><span class="pre">DATACHANNEL_LOCALPORT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.uint16(0)</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.DATACHANNEL_LOCALPORT" title="Link to this definition"></a></dt>
<dd><p>Default for local port for data channel.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.ENABLE_TIME_REASONABILITY_CHECK">
<span class="sig-name descname"><span class="pre">ENABLE_TIME_REASONABILITY_CHECK</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">False</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.ENABLE_TIME_REASONABILITY_CHECK" title="Link to this definition"></a></dt>
<dd><p>Default for enable time reasonability check flag.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.EXTRA_CONNECTIONSTRING_PARAMETERS">
<span class="sig-name descname"><span class="pre">EXTRA_CONNECTIONSTRING_PARAMETERS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.EXTRA_CONNECTIONSTRING_PARAMETERS" title="Link to this definition"></a></dt>
<dd><p>Default for extra connection string parameters.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.FILTEREXPRESSION">
<span class="sig-name descname"><span class="pre">FILTEREXPRESSION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.FILTEREXPRESSION" title="Link to this definition"></a></dt>
<dd><p>Default for filter expression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.INCLUDETIME">
<span class="sig-name descname"><span class="pre">INCLUDETIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">True</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.INCLUDETIME" title="Link to this definition"></a></dt>
<dd><p>Default for include time flag.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.LAGTIME">
<span class="sig-name descname"><span class="pre">LAGTIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.float64(10.0)</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.LAGTIME" title="Link to this definition"></a></dt>
<dd><p>Default for lag time in seconds.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.LEADTIME">
<span class="sig-name descname"><span class="pre">LEADTIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.float64(5.0)</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.LEADTIME" title="Link to this definition"></a></dt>
<dd><p>Default for lead time in seconds.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.MAXRETRIES">
<span class="sig-name descname"><span class="pre">MAXRETRIES</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">-1</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.MAXRETRIES" title="Link to this definition"></a></dt>
<dd><p>Default for maximum number of retries for a connection attempt.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.MAXRETRYINTERVAL">
<span class="sig-name descname"><span class="pre">MAXRETRYINTERVAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">30.0</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.MAXRETRYINTERVAL" title="Link to this definition"></a></dt>
<dd><p>Default for maximum retry interval in seconds.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.METADATAFILTERS">
<span class="sig-name descname"><span class="pre">METADATAFILTERS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.METADATAFILTERS" title="Link to this definition"></a></dt>
<dd><p>Default for metadata filters.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.PROCESSINGINTERVAL">
<span class="sig-name descname"><span class="pre">PROCESSINGINTERVAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.int32(-1)</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.PROCESSINGINTERVAL" title="Link to this definition"></a></dt>
<dd><p>Default for processing interval in seconds.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.PUBLISHINTERVAL">
<span class="sig-name descname"><span class="pre">PUBLISHINTERVAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.float64(1.0)</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.PUBLISHINTERVAL" title="Link to this definition"></a></dt>
<dd><p>Default for publish interval in seconds.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.REQUEST_NANVALUEFILTER">
<span class="sig-name descname"><span class="pre">REQUEST_NANVALUEFILTER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">False</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.REQUEST_NANVALUEFILTER" title="Link to this definition"></a></dt>
<dd><p>Default for request nan-value filter flag.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.RETRYINTERVAL">
<span class="sig-name descname"><span class="pre">RETRYINTERVAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1.0</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.RETRYINTERVAL" title="Link to this definition"></a></dt>
<dd><p>Default for retry interval in seconds.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.SOCKET_TIMEOUT">
<span class="sig-name descname"><span class="pre">SOCKET_TIMEOUT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2.0</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.SOCKET_TIMEOUT" title="Link to this definition"></a></dt>
<dd><p>Default for socket timeout in seconds.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.STARTTIME">
<span class="sig-name descname"><span class="pre">STARTTIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.STARTTIME" title="Link to this definition"></a></dt>
<dd><p>Default for start time.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.STOPTIME">
<span class="sig-name descname"><span class="pre">STOPTIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.STOPTIME" title="Link to this definition"></a></dt>
<dd><p>Default for stop time.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.THROTTLED">
<span class="sig-name descname"><span class="pre">THROTTLED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">False</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.THROTTLED" title="Link to this definition"></a></dt>
<dd><p>Default for throttled flag.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.UDPDATACHANNEL">
<span class="sig-name descname"><span class="pre">UDPDATACHANNEL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">False</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.UDPDATACHANNEL" title="Link to this definition"></a></dt>
<dd><p>Default for UDP data channel flag.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.USE_LOCALCLOCK_AS_REALTIME">
<span class="sig-name descname"><span class="pre">USE_LOCALCLOCK_AS_REALTIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">False</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.USE_LOCALCLOCK_AS_REALTIME" title="Link to this definition"></a></dt>
<dd><p>Default for use local clock as real time flag.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.USE_MILLISECONDRESOLUTION">
<span class="sig-name descname"><span class="pre">USE_MILLISECONDRESOLUTION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">False</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.USE_MILLISECONDRESOLUTION" title="Link to this definition"></a></dt>
<dd><p>Default for use millisecond resolution flag.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.Defaults.VERSION">
<span class="sig-name descname"><span class="pre">VERSION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.int8(2)</span></em><a class="headerlink" href="#sttp.transport.constants.Defaults.VERSION" title="Link to this definition"></a></dt>
<dd><p>Default for STTP version.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sttp.transport.constants.OperationalEncoding">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.transport.constants.</span></span><span class="sig-name descname"><span class="pre">OperationalEncoding</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names=&lt;not</span> <span class="pre">given&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start=1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary=None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/constants.html#OperationalEncoding"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.constants.OperationalEncoding" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></p>
<p>Enumeration of the possible string encoding options of an STTP session.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.OperationalEncoding.UTF16BE">
<span class="sig-name descname"><span class="pre">UTF16BE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">256</span></em><a class="headerlink" href="#sttp.transport.constants.OperationalEncoding.UTF16BE" title="Link to this definition"></a></dt>
<dd><p>Targets big-endian 16-bit Unicode character encoding for strings.</p>
<p>Obsolete: STTP currently only supports UTF-8 string encoding.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.OperationalEncoding.UTF16LE">
<span class="sig-name descname"><span class="pre">UTF16LE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#sttp.transport.constants.OperationalEncoding.UTF16LE" title="Link to this definition"></a></dt>
<dd><p>Targets little-endian 16-bit Unicode character encoding for strings.</p>
<p>Obsolete: STTP currently only supports UTF-8 string encoding.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.OperationalEncoding.UTF8">
<span class="sig-name descname"><span class="pre">UTF8</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">512</span></em><a class="headerlink" href="#sttp.transport.constants.OperationalEncoding.UTF8" title="Link to this definition"></a></dt>
<dd><p>Targets 8-bit variable-width Unicode character encoding for strings.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sttp.transport.constants.OperationalModes">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.transport.constants.</span></span><span class="sig-name descname"><span class="pre">OperationalModes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names=&lt;not</span> <span class="pre">given&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start=1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary=None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/constants.html#OperationalModes"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.constants.OperationalModes" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></p>
<p>Enumeration of the possible modes that affect how <cite>DataPublisher</cite> and <cite>DataSubscriber</cite> communicate during an STTP session.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.OperationalModes.COMPRESSMETADATA">
<span class="sig-name descname"><span class="pre">COMPRESSMETADATA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2147483648</span></em><a class="headerlink" href="#sttp.transport.constants.OperationalModes.COMPRESSMETADATA" title="Link to this definition"></a></dt>
<dd><p>Bit flag used to determine whether metadata is compressed when exchanging between publisher and subscriber.</p>
<p>Bit set = compress, bit clear = no compression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.OperationalModes.COMPRESSPAYLOADDATA">
<span class="sig-name descname"><span class="pre">COMPRESSPAYLOADDATA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">536870912</span></em><a class="headerlink" href="#sttp.transport.constants.OperationalModes.COMPRESSPAYLOADDATA" title="Link to this definition"></a></dt>
<dd><p>Bit flag used to determine whether payload data is compressed when exchanging between publisher and subscriber.</p>
<p>Bit set = compress, bit clear = no compression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.OperationalModes.COMPRESSSIGNALINDEXCACHE">
<span class="sig-name descname"><span class="pre">COMPRESSSIGNALINDEXCACHE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1073741824</span></em><a class="headerlink" href="#sttp.transport.constants.OperationalModes.COMPRESSSIGNALINDEXCACHE" title="Link to this definition"></a></dt>
<dd><p>Bit flag used to determine whether the signal index cache is compressed when exchanging between publisher and subscriber.</p>
<p>Bit set = compress, bit clear = no compression.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.OperationalModes.ENCODINGMASK">
<span class="sig-name descname"><span class="pre">ENCODINGMASK</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">768</span></em><a class="headerlink" href="#sttp.transport.constants.OperationalModes.ENCODINGMASK" title="Link to this definition"></a></dt>
<dd><p>Bit mask used to get character encoding used when exchanging messages between publisher and subscriber.</p>
<p>STTP currently only supports UTF-8 string encoding.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.OperationalModes.IMPLEMENTATIONSPECIFICEXTENSIONMASK">
<span class="sig-name descname"><span class="pre">IMPLEMENTATIONSPECIFICEXTENSIONMASK</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">16711680</span></em><a class="headerlink" href="#sttp.transport.constants.OperationalModes.IMPLEMENTATIONSPECIFICEXTENSIONMASK" title="Link to this definition"></a></dt>
<dd><p>Bit mask used to apply an implementation-specific extension to STTP.</p>
<p>If the value is zero, no implementation specific extensions are applied.
If the value is non-zero, an implementation specific extension is applied, and all parties need to coordinate and agree to the extension.
If extended flags are unsupported, returned failure message text should be prefixed with UNSUPPORTED EXTENSION: as the context reference.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.OperationalModes.NOFLAGS">
<span class="sig-name descname"><span class="pre">NOFLAGS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#sttp.transport.constants.OperationalModes.NOFLAGS" title="Link to this definition"></a></dt>
<dd><p>State where there are no flags set.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.OperationalModes.RECEIVEEXTERNALMETADATA">
<span class="sig-name descname"><span class="pre">RECEIVEEXTERNALMETADATA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">33554432</span></em><a class="headerlink" href="#sttp.transport.constants.OperationalModes.RECEIVEEXTERNALMETADATA" title="Link to this definition"></a></dt>
<dd><p>Bit flag used to determine whether external measurements are exchanged during metadata synchronization.</p>
<p>Bit set = external measurements are exchanged, bit clear = no external measurements are exchanged.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.OperationalModes.RECEIVEINTERNALMETADATA">
<span class="sig-name descname"><span class="pre">RECEIVEINTERNALMETADATA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">67108864</span></em><a class="headerlink" href="#sttp.transport.constants.OperationalModes.RECEIVEINTERNALMETADATA" title="Link to this definition"></a></dt>
<dd><p>Bit flag used to determine whether internal measurements are exchanged during metadata synchronization.</p>
<p>Bit set = internal measurements are exchanged, bit clear = no internal measurements are exchanged.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.OperationalModes.VERSIONMASK">
<span class="sig-name descname"><span class="pre">VERSIONMASK</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">255</span></em><a class="headerlink" href="#sttp.transport.constants.OperationalModes.VERSIONMASK" title="Link to this definition"></a></dt>
<dd><p>Bit mask used to get version number of protocol.</p>
<p>Version number is currently set to 2.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sttp.transport.constants.SecurityMode">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.transport.constants.</span></span><span class="sig-name descname"><span class="pre">SecurityMode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names=&lt;not</span> <span class="pre">given&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start=1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary=None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/constants.html#SecurityMode"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.constants.SecurityMode" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></p>
<p>Enumeration of the possible security modes used by the DataPublisher to secure data sent over the command channel in STTP.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.SecurityMode.OFF">
<span class="sig-name descname"><span class="pre">OFF</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#sttp.transport.constants.SecurityMode.OFF" title="Link to this definition"></a></dt>
<dd><p>Defines security mode where data will be sent over the wire unencrypted.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.SecurityMode.TLS">
<span class="sig-name descname"><span class="pre">TLS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1</span></em><a class="headerlink" href="#sttp.transport.constants.SecurityMode.TLS" title="Link to this definition"></a></dt>
<dd><p>Defines security mode where data will be sent over wire using Transport Layer Security (TLS).</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.transport.constants.</span></span><span class="sig-name descname"><span class="pre">ServerCommand</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names=&lt;not</span> <span class="pre">given&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start=1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary=None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/constants.html#ServerCommand"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.constants.ServerCommand" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></p>
<p>Enumeration of the possible server commands received by a <cite>DataPublisher</cite> and sent by a <cite>DataSubscriber</cite> during an STTP session.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.CONFIRMBUFFERBLOCK">
<span class="sig-name descname"><span class="pre">CONFIRMBUFFERBLOCK</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">8</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.CONFIRMBUFFERBLOCK" title="Link to this definition"></a></dt>
<dd><p>Command code for receipt of a buffer block measurement.</p>
<p>This message is sent in response to ServerResponse.BufferBlock.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.CONFIRMNOTIFICATION">
<span class="sig-name descname"><span class="pre">CONFIRMNOTIFICATION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">7</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.CONFIRMNOTIFICATION" title="Link to this definition"></a></dt>
<dd><p>Command code for receipt of a notification.</p>
<p>This message is sent in response to ServerResponse.Notify.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.CONFIRMUPDATEBASETIMES">
<span class="sig-name descname"><span class="pre">CONFIRMUPDATEBASETIMES</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">9</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.CONFIRMUPDATEBASETIMES" title="Link to this definition"></a></dt>
<dd><p>Command code for receipt of a base time update.</p>
<p>This message is sent in response to ServerResponse.UpdateBaseTimes.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.CONFIRMUPDATECIPHERKEYS">
<span class="sig-name descname"><span class="pre">CONFIRMUPDATECIPHERKEYS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">11</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.CONFIRMUPDATECIPHERKEYS" title="Link to this definition"></a></dt>
<dd><p>Command code for confirming the receipt of a cipher key update.</p>
<p>This verifies delivery of the cipher keys indicating that it is safe to transition to the new keys.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.CONFIRMUPDATESIGNALINDEXCACHE">
<span class="sig-name descname"><span class="pre">CONFIRMUPDATESIGNALINDEXCACHE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">10</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.CONFIRMUPDATESIGNALINDEXCACHE" title="Link to this definition"></a></dt>
<dd><p>Command code for confirming the receipt of a signal index cache.</p>
<p>This allows publisher to safely transition to next signal index cache.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.CONNECT">
<span class="sig-name descname"><span class="pre">CONNECT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.CONNECT" title="Link to this definition"></a></dt>
<dd><p>Command code handling connect operations.</p>
<p>Only used as part of connection refused response – value not sent on the wire.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.DEFINEOPERATIONALMODES">
<span class="sig-name descname"><span class="pre">DEFINEOPERATIONALMODES</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">6</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.DEFINEOPERATIONALMODES" title="Link to this definition"></a></dt>
<dd><p>Command code for establishing operational modes.</p>
<p>As soon as connection is established, requests that server set operational modes that affect how the subscriber and publisher will communicate.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.GETPRIMARYMETADATASCHEMA">
<span class="sig-name descname"><span class="pre">GETPRIMARYMETADATASCHEMA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">12</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.GETPRIMARYMETADATASCHEMA" title="Link to this definition"></a></dt>
<dd><p>Command code for requesting the primary metadata schema.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.GETSIGNALSELECTIONSCHEMA">
<span class="sig-name descname"><span class="pre">GETSIGNALSELECTIONSCHEMA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">13</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.GETSIGNALSELECTIONSCHEMA" title="Link to this definition"></a></dt>
<dd><p>Command code for requesting the signal selection schema.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.METADATAREFRESH">
<span class="sig-name descname"><span class="pre">METADATAREFRESH</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.METADATAREFRESH" title="Link to this definition"></a></dt>
<dd><p>Command code for requesting an updated set of metadata.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.ROTATECIPHERKEYS">
<span class="sig-name descname"><span class="pre">ROTATECIPHERKEYS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">4</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.ROTATECIPHERKEYS" title="Link to this definition"></a></dt>
<dd><p>Command code for manually requesting that server send a new set of cipher keys for data packet encryption (UDP only).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.SUBSCRIBE">
<span class="sig-name descname"><span class="pre">SUBSCRIBE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.SUBSCRIBE" title="Link to this definition"></a></dt>
<dd><p>Command code for requesting a subscription of streaming data from server based on connection string that follows.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.UNSUBSCRIBE">
<span class="sig-name descname"><span class="pre">UNSUBSCRIBE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">3</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.UNSUBSCRIBE" title="Link to this definition"></a></dt>
<dd><p>Command code for requesting that server stop sending streaming data to the client and cancel the current subscription.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.UPDATEPROCESSINGINTERVAL">
<span class="sig-name descname"><span class="pre">UPDATEPROCESSINGINTERVAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">5</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.UPDATEPROCESSINGINTERVAL" title="Link to this definition"></a></dt>
<dd><p>Command code for manually requesting that server to update the processing interval with the following specified value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.USERCOMMAND00">
<span class="sig-name descname"><span class="pre">USERCOMMAND00</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">208</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.USERCOMMAND00" title="Link to this definition"></a></dt>
<dd><p>Command code handling user-defined commands.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.USERCOMMAND01">
<span class="sig-name descname"><span class="pre">USERCOMMAND01</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">209</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.USERCOMMAND01" title="Link to this definition"></a></dt>
<dd><p>Command code handling user-defined commands.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.USERCOMMAND02">
<span class="sig-name descname"><span class="pre">USERCOMMAND02</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">210</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.USERCOMMAND02" title="Link to this definition"></a></dt>
<dd><p>Command code handling user-defined commands.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.USERCOMMAND03">
<span class="sig-name descname"><span class="pre">USERCOMMAND03</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">211</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.USERCOMMAND03" title="Link to this definition"></a></dt>
<dd><p>Command code handling user-defined commands.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.USERCOMMAND04">
<span class="sig-name descname"><span class="pre">USERCOMMAND04</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">212</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.USERCOMMAND04" title="Link to this definition"></a></dt>
<dd><p>Command code handling user-defined commands.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.USERCOMMAND05">
<span class="sig-name descname"><span class="pre">USERCOMMAND05</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">213</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.USERCOMMAND05" title="Link to this definition"></a></dt>
<dd><p>Command code handling user-defined commands.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.USERCOMMAND06">
<span class="sig-name descname"><span class="pre">USERCOMMAND06</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">214</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.USERCOMMAND06" title="Link to this definition"></a></dt>
<dd><p>Command code handling user-defined commands.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.USERCOMMAND07">
<span class="sig-name descname"><span class="pre">USERCOMMAND07</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">215</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.USERCOMMAND07" title="Link to this definition"></a></dt>
<dd><p>Command code handling user-defined commands.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.USERCOMMAND08">
<span class="sig-name descname"><span class="pre">USERCOMMAND08</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">216</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.USERCOMMAND08" title="Link to this definition"></a></dt>
<dd><p>Command code handling user-defined commands.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.USERCOMMAND09">
<span class="sig-name descname"><span class="pre">USERCOMMAND09</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">217</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.USERCOMMAND09" title="Link to this definition"></a></dt>
<dd><p>Command code handling user-defined commands.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.USERCOMMAND10">
<span class="sig-name descname"><span class="pre">USERCOMMAND10</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">218</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.USERCOMMAND10" title="Link to this definition"></a></dt>
<dd><p>Command code handling user-defined commands.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.USERCOMMAND11">
<span class="sig-name descname"><span class="pre">USERCOMMAND11</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">219</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.USERCOMMAND11" title="Link to this definition"></a></dt>
<dd><p>Command code handling user-defined commands.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.USERCOMMAND12">
<span class="sig-name descname"><span class="pre">USERCOMMAND12</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">220</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.USERCOMMAND12" title="Link to this definition"></a></dt>
<dd><p>Command code handling user-defined commands.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.USERCOMMAND13">
<span class="sig-name descname"><span class="pre">USERCOMMAND13</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">221</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.USERCOMMAND13" title="Link to this definition"></a></dt>
<dd><p>Command code handling user-defined commands.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.USERCOMMAND14">
<span class="sig-name descname"><span class="pre">USERCOMMAND14</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">222</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.USERCOMMAND14" title="Link to this definition"></a></dt>
<dd><p>Command code handling user-defined commands.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerCommand.USERCOMMAND15">
<span class="sig-name descname"><span class="pre">USERCOMMAND15</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">223</span></em><a class="headerlink" href="#sttp.transport.constants.ServerCommand.USERCOMMAND15" title="Link to this definition"></a></dt>
<dd><p>Command code handling user-defined commands.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.transport.constants.</span></span><span class="sig-name descname"><span class="pre">ServerResponse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names=&lt;not</span> <span class="pre">given&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start=1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary=None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/constants.html#ServerResponse"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.constants.ServerResponse" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></p>
<p>Enumeration of the possible server responses received sent by a <cite>DataPublisher</cite> and received by a <cite>DataSubscriber</cite> during an STTP session.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.BUFFERBLOCK">
<span class="sig-name descname"><span class="pre">BUFFERBLOCK</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">136</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.BUFFERBLOCK" title="Link to this definition"></a></dt>
<dd><p>Response code indicating a buffer block.</p>
<p>Unsolicited response informs client that a raw buffer block follows.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.CONFIGURATIONCHANGED">
<span class="sig-name descname"><span class="pre">CONFIGURATIONCHANGED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">138</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.CONFIGURATIONCHANGED" title="Link to this definition"></a></dt>
<dd><p>Response code indicating a that the publisher configuration metadata has changed.</p>
<p>Unsolicited response provides a notification that the publisher’s source configuration has changed and that client may want to request a meta-data refresh.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.DATAPACKET">
<span class="sig-name descname"><span class="pre">DATAPACKET</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">130</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.DATAPACKET" title="Link to this definition"></a></dt>
<dd><p>Response code indicating a data packet.</p>
<p>Unsolicited response informs client that a data packet follows.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.DATASTARTTIME">
<span class="sig-name descname"><span class="pre">DATASTARTTIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">134</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.DATASTARTTIME" title="Link to this definition"></a></dt>
<dd><p>Response code indicating the start time of data being published.</p>
<p>Unsolicited response provides the start time of data being processed from the first measurement.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.FAILED">
<span class="sig-name descname"><span class="pre">FAILED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">129</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.FAILED" title="Link to this definition"></a></dt>
<dd><p>Response code indicating a failed response.</p>
<p>Informs client that its solicited server command failed, original command and failure message follow.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.NOOP">
<span class="sig-name descname"><span class="pre">NOOP</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">255</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.NOOP" title="Link to this definition"></a></dt>
<dd><p>Response code indicating an empty-operation keep-alive ping.</p>
<p>The command channel can remain quiet for some time, this command allows a period test of client connectivity.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.NOTIFY">
<span class="sig-name descname"><span class="pre">NOTIFY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">137</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.NOTIFY" title="Link to this definition"></a></dt>
<dd><p>Response code indicating a notification.</p>
<p>Unsolicited response provides a notification message to the client.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.PROCESSINGCOMPLETE">
<span class="sig-name descname"><span class="pre">PROCESSINGCOMPLETE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">135</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.PROCESSINGCOMPLETE" title="Link to this definition"></a></dt>
<dd><p>Response code indicating that processing has completed.</p>
<p>Unsolicited response provides notification that input processing has completed, typically via temporal constraint.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.SUCCEEDED">
<span class="sig-name descname"><span class="pre">SUCCEEDED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">128</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.SUCCEEDED" title="Link to this definition"></a></dt>
<dd><p>Response code indicating a succeeded response.</p>
<p>Informs client that its solicited server command succeeded, original command and success message follow.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.UPDATEBASETIMES">
<span class="sig-name descname"><span class="pre">UPDATEBASETIMES</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">132</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.UPDATEBASETIMES" title="Link to this definition"></a></dt>
<dd><p>Response code indicating a runtime base-timestamp offsets have been updated.</p>
<p>Unsolicited response requests that client update its runtime base-timestamp offsets with those that follow.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.UPDATECIPHERKEYS">
<span class="sig-name descname"><span class="pre">UPDATECIPHERKEYS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">133</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.UPDATECIPHERKEYS" title="Link to this definition"></a></dt>
<dd><p>Response code indicating a runtime cipher keys have been updated.</p>
<p>Response, solicited or unsolicited, requests that client update its runtime data cipher keys with those that follow.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.UPDATESIGNALINDEXCACHE">
<span class="sig-name descname"><span class="pre">UPDATESIGNALINDEXCACHE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">131</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.UPDATESIGNALINDEXCACHE" title="Link to this definition"></a></dt>
<dd><p>Response code indicating a signal index cache update.</p>
<p>Unsolicited response requests that client update its runtime signal index cache with the one that follows.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.USERRESPONSE00">
<span class="sig-name descname"><span class="pre">USERRESPONSE00</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">224</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.USERRESPONSE00" title="Link to this definition"></a></dt>
<dd><p>Response code handling user-defined responses.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.USERRESPONSE01">
<span class="sig-name descname"><span class="pre">USERRESPONSE01</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">225</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.USERRESPONSE01" title="Link to this definition"></a></dt>
<dd><p>Response code handling user-defined responses.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.USERRESPONSE02">
<span class="sig-name descname"><span class="pre">USERRESPONSE02</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">226</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.USERRESPONSE02" title="Link to this definition"></a></dt>
<dd><p>Response code handling user-defined responses.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.USERRESPONSE03">
<span class="sig-name descname"><span class="pre">USERRESPONSE03</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">227</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.USERRESPONSE03" title="Link to this definition"></a></dt>
<dd><p>Response code handling user-defined responses.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.USERRESPONSE04">
<span class="sig-name descname"><span class="pre">USERRESPONSE04</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">228</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.USERRESPONSE04" title="Link to this definition"></a></dt>
<dd><p>Response code handling user-defined responses.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.USERRESPONSE05">
<span class="sig-name descname"><span class="pre">USERRESPONSE05</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">229</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.USERRESPONSE05" title="Link to this definition"></a></dt>
<dd><p>Response code handling user-defined responses.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.USERRESPONSE06">
<span class="sig-name descname"><span class="pre">USERRESPONSE06</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">230</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.USERRESPONSE06" title="Link to this definition"></a></dt>
<dd><p>Response code handling user-defined responses.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.USERRESPONSE07">
<span class="sig-name descname"><span class="pre">USERRESPONSE07</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">231</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.USERRESPONSE07" title="Link to this definition"></a></dt>
<dd><p>Response code handling user-defined responses.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.USERRESPONSE08">
<span class="sig-name descname"><span class="pre">USERRESPONSE08</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">232</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.USERRESPONSE08" title="Link to this definition"></a></dt>
<dd><p>Response code handling user-defined responses.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.USERRESPONSE09">
<span class="sig-name descname"><span class="pre">USERRESPONSE09</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">233</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.USERRESPONSE09" title="Link to this definition"></a></dt>
<dd><p>Response code handling user-defined responses.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.USERRESPONSE10">
<span class="sig-name descname"><span class="pre">USERRESPONSE10</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">234</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.USERRESPONSE10" title="Link to this definition"></a></dt>
<dd><p>Response code handling user-defined responses.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.USERRESPONSE11">
<span class="sig-name descname"><span class="pre">USERRESPONSE11</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">235</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.USERRESPONSE11" title="Link to this definition"></a></dt>
<dd><p>Response code handling user-defined responses.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.USERRESPONSE12">
<span class="sig-name descname"><span class="pre">USERRESPONSE12</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">236</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.USERRESPONSE12" title="Link to this definition"></a></dt>
<dd><p>Response code handling user-defined responses.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.USERRESPONSE13">
<span class="sig-name descname"><span class="pre">USERRESPONSE13</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">237</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.USERRESPONSE13" title="Link to this definition"></a></dt>
<dd><p>Response code handling user-defined responses.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.USERRESPONSE14">
<span class="sig-name descname"><span class="pre">USERRESPONSE14</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">238</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.USERRESPONSE14" title="Link to this definition"></a></dt>
<dd><p>Response code handling user-defined responses.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.ServerResponse.USERRESPONSE15">
<span class="sig-name descname"><span class="pre">USERRESPONSE15</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">239</span></em><a class="headerlink" href="#sttp.transport.constants.ServerResponse.USERRESPONSE15" title="Link to this definition"></a></dt>
<dd><p>Response code handling user-defined responses.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.transport.constants.</span></span><span class="sig-name descname"><span class="pre">StateFlags</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names=&lt;not</span> <span class="pre">given&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start=1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary=None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/constants.html#StateFlags"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.constants.StateFlags" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></p>
<p>Enumeration of the possible quality states of a <cite>Measurement</cite> value.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.ALARMHIGH">
<span class="sig-name descname"><span class="pre">ALARMHIGH</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">16</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.ALARMHIGH" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for an alarm for high value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.ALARMLOW">
<span class="sig-name descname"><span class="pre">ALARMLOW</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">32</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.ALARMLOW" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for an alarm for low value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.BADDATA">
<span class="sig-name descname"><span class="pre">BADDATA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.BADDATA" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for a bad data state.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.BADTIME">
<span class="sig-name descname"><span class="pre">BADTIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">65536</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.BADTIME" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for a bad time state.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.CALCULATEDVALUE">
<span class="sig-name descname"><span class="pre">CALCULATEDVALUE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">4096</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.CALCULATEDVALUE" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for a calculated value state.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.CALCULATIONERROR">
<span class="sig-name descname"><span class="pre">CALCULATIONERROR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">8192</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.CALCULATIONERROR" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for a calculation error with the value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.CALCULATIONWARNING">
<span class="sig-name descname"><span class="pre">CALCULATIONWARNING</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">16384</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.CALCULATIONWARNING" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for a calculation warning with the value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.COMPARISONALARM">
<span class="sig-name descname"><span class="pre">COMPARISONALARM</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">512</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.COMPARISONALARM" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for a comparison alarm, i.e., outside threshold of comparison with a real-time value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.DISCARDEDVALUE">
<span class="sig-name descname"><span class="pre">DISCARDEDVALUE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">4194304</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.DISCARDEDVALUE" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for a discarded value state.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.DOWNSAMPLED">
<span class="sig-name descname"><span class="pre">DOWNSAMPLED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2097152</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.DOWNSAMPLED" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for a down-sampled state.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.FLATLINEALARM">
<span class="sig-name descname"><span class="pre">FLATLINEALARM</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">256</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.FLATLINEALARM" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for an alarm for flat-lined value, i.e., latched value test alarm.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.FUTURETIMEALARM">
<span class="sig-name descname"><span class="pre">FUTURETIMEALARM</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">524288</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.FUTURETIMEALARM" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for a future time alarm.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.LATETIMEALARM">
<span class="sig-name descname"><span class="pre">LATETIMEALARM</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">262144</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.LATETIMEALARM" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for a late time alarm.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.MEASUREMENTERROR">
<span class="sig-name descname"><span class="pre">MEASUREMENTERROR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2147483648</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.MEASUREMENTERROR" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for a measurement error state.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.NORMAL">
<span class="sig-name descname"><span class="pre">NORMAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.NORMAL" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for a normal state.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.OVERRANGEERROR">
<span class="sig-name descname"><span class="pre">OVERRANGEERROR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">4</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.OVERRANGEERROR" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for an over range error, i.e., unreasonable high value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.RECEIVEDASBAD">
<span class="sig-name descname"><span class="pre">RECEIVEDASBAD</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2048</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.RECEIVEDASBAD" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for a bad value received.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.RESERVEDQUALITYFLAG">
<span class="sig-name descname"><span class="pre">RESERVEDQUALITYFLAG</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">32768</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.RESERVEDQUALITYFLAG" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for a reserved quality.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.RESERVEDTIMEFLAG">
<span class="sig-name descname"><span class="pre">RESERVEDTIMEFLAG</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">8388608</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.RESERVEDTIMEFLAG" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for a reserved time state.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.ROCALARM">
<span class="sig-name descname"><span class="pre">ROCALARM</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1024</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.ROCALARM" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for a rate-of-change alarm.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.SUSPECTDATA">
<span class="sig-name descname"><span class="pre">SUSPECTDATA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.SUSPECTDATA" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for a suspect data state.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.SUSPECTTIME">
<span class="sig-name descname"><span class="pre">SUSPECTTIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">131072</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.SUSPECTTIME" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for a suspect time state.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.SYSTEMERROR">
<span class="sig-name descname"><span class="pre">SYSTEMERROR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">536870912</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.SYSTEMERROR" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for a system error state.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.SYSTEMWARNING">
<span class="sig-name descname"><span class="pre">SYSTEMWARNING</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1073741824</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.SYSTEMWARNING" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for a system warning state.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.UNDERRANGEERROR">
<span class="sig-name descname"><span class="pre">UNDERRANGEERROR</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">8</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.UNDERRANGEERROR" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for an under range error, i.e., unreasonable low value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.UPSAMPLED">
<span class="sig-name descname"><span class="pre">UPSAMPLED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1048576</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.UPSAMPLED" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for an up-sampled state.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.USERDEFINEDFLAG1">
<span class="sig-name descname"><span class="pre">USERDEFINEDFLAG1</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">16777216</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.USERDEFINEDFLAG1" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for user defined state 1.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.USERDEFINEDFLAG2">
<span class="sig-name descname"><span class="pre">USERDEFINEDFLAG2</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">33554432</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.USERDEFINEDFLAG2" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for user defined state 2.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.USERDEFINEDFLAG3">
<span class="sig-name descname"><span class="pre">USERDEFINEDFLAG3</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">67108864</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.USERDEFINEDFLAG3" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for user defined state 3.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.USERDEFINEDFLAG4">
<span class="sig-name descname"><span class="pre">USERDEFINEDFLAG4</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">134217728</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.USERDEFINEDFLAG4" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for user defined state 4.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.USERDEFINEDFLAG5">
<span class="sig-name descname"><span class="pre">USERDEFINEDFLAG5</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">268435456</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.USERDEFINEDFLAG5" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for user defined state 5.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.WARNINGHIGH">
<span class="sig-name descname"><span class="pre">WARNINGHIGH</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">64</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.WARNINGHIGH" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for a warning for high value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.constants.StateFlags.WARNINGLOW">
<span class="sig-name descname"><span class="pre">WARNINGLOW</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">128</span></em><a class="headerlink" href="#sttp.transport.constants.StateFlags.WARNINGLOW" title="Link to this definition"></a></dt>
<dd><p>Measurement flag for a warning for low value.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.transport.datasubscriber">
<span id="sttp-transport-datasubscriber-module"></span><h2>sttp.transport.datasubscriber module<a class="headerlink" href="#module-sttp.transport.datasubscriber" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.transport.datasubscriber.</span></span><span class="sig-name descname"><span class="pre">DataSubscriber</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">compress_payloaddata</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">compress_metadata</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">compress_signalindexcache</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">version</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int8</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sttp_sourceinfo</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sttp_versioninfo</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sttp_updatedoninfo</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">socket_timeout</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">float</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/datasubscriber.html#DataSubscriber"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Represents a subscription for an STTP connection.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.DEFAULT_COMPRESS_METADATA">
<span class="sig-name descname"><span class="pre">DEFAULT_COMPRESS_METADATA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">True</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_COMPRESS_METADATA" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.DEFAULT_COMPRESS_PAYLOADDATA">
<span class="sig-name descname"><span class="pre">DEFAULT_COMPRESS_PAYLOADDATA</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">True</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_COMPRESS_PAYLOADDATA" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.DEFAULT_COMPRESS_SIGNALINDEXCACHE">
<span class="sig-name descname"><span class="pre">DEFAULT_COMPRESS_SIGNALINDEXCACHE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">True</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_COMPRESS_SIGNALINDEXCACHE" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.DEFAULT_SOCKET_TIMEOUT">
<span class="sig-name descname"><span class="pre">DEFAULT_SOCKET_TIMEOUT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2.0</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_SOCKET_TIMEOUT" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.DEFAULT_STTP_SOURCEINFO">
<span class="sig-name descname"><span class="pre">DEFAULT_STTP_SOURCEINFO</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'STTP</span> <span class="pre">Python</span> <span class="pre">Library'</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_STTP_SOURCEINFO" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.DEFAULT_STTP_UPDATEDONINFO">
<span class="sig-name descname"><span class="pre">DEFAULT_STTP_UPDATEDONINFO</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'2024-10-16'</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_STTP_UPDATEDONINFO" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.DEFAULT_STTP_VERSIONINFO">
<span class="sig-name descname"><span class="pre">DEFAULT_STTP_VERSIONINFO</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'0.6.4'</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_STTP_VERSIONINFO" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.DEFAULT_VERSION">
<span class="sig-name descname"><span class="pre">DEFAULT_VERSION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.int8(2)</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.DEFAULT_VERSION" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.activesignalindexcache">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">activesignalindexcache</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.transport.signalindexcache.SignalIndexCache" title="sttp.transport.signalindexcache.SignalIndexCache"><span class="pre">SignalIndexCache</span></a></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.activesignalindexcache" title="Link to this definition"></a></dt>
<dd><p>Gets the active signal index cache.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.adjustedvalue">
<span class="sig-name descname"><span class="pre">adjustedvalue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">measurement</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.transport.measurement.Measurement" title="sttp.transport.measurement.Measurement"><span class="pre">Measurement</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">float64</span></span></span><a class="reference internal" href="_modules/sttp/transport/datasubscriber.html#DataSubscriber.adjustedvalue"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.adjustedvalue" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>Value</cite> of a <cite>Measurement</cite> with any linear adjustments applied from the measurement’s <cite>Adder</cite> and <cite>Multiplier</cite> metadata, if found.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.autoreconnect_callback">
<span class="sig-name descname"><span class="pre">autoreconnect_callback</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.autoreconnect_callback" title="Link to this definition"></a></dt>
<dd><p>Called when <cite>DataSubscriber</cite> automatically reconnects.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.compress_metadata">
<span class="sig-name descname"><span class="pre">compress_metadata</span></span><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.compress_metadata" title="Link to this definition"></a></dt>
<dd><p>Determines whether the metadata transfer is compressed, defaults to GZip.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.compress_payloaddata">
<span class="sig-name descname"><span class="pre">compress_payloaddata</span></span><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.compress_payloaddata" title="Link to this definition"></a></dt>
<dd><p>Determines whether payload data is compressed, defaults to TSSC.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.compress_signalindexcache">
<span class="sig-name descname"><span class="pre">compress_signalindexcache</span></span><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.compress_signalindexcache" title="Link to this definition"></a></dt>
<dd><p>Determines whether the signal index cache is compressed, defaults to GZip.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.configurationchanged_callback">
<span class="sig-name descname"><span class="pre">configurationchanged_callback</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.configurationchanged_callback" title="Link to this definition"></a></dt>
<dd><p>Called when the <cite>DataPublisher</cite> sends a notification that configuration has changed.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.connect">
<span class="sig-name descname"><span class="pre">connect</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">hostname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint16</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></span><a class="reference internal" href="_modules/sttp/transport/datasubscriber.html#DataSubscriber.connect"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.connect" title="Link to this definition"></a></dt>
<dd><p>Requests the the <cite>DataSubscriber</cite> initiate a connection to the <cite>DataPublisher</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.connected">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">connected</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">bool</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.connected" title="Link to this definition"></a></dt>
<dd><p>Determines if a <cite>DataSubscriber</cite> is currently connected to a <cite>DataPublisher</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.connectionterminated_callback">
<span class="sig-name descname"><span class="pre">connectionterminated_callback</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.connectionterminated_callback" title="Link to this definition"></a></dt>
<dd><p>Called when <cite>DataSubscriber</cite> terminates its connection.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.connector">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">connector</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.transport.subscriberconnector.SubscriberConnector" title="sttp.transport.subscriberconnector.SubscriberConnector"><span class="pre">SubscriberConnector</span></a></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.connector" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>SubscriberConnector</cite> associated with this <cite>DataSubscriber</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.data_starttime_callback">
<span class="sig-name descname"><span class="pre">data_starttime_callback</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">uint64</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.data_starttime_callback" title="Link to this definition"></a></dt>
<dd><p>Called with timestamp of first received measurement in a subscription.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.decodestr">
<span class="sig-name descname"><span class="pre">decodestr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bytes</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="reference internal" href="_modules/sttp/transport/datasubscriber.html#DataSubscriber.decodestr"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.decodestr" title="Link to this definition"></a></dt>
<dd><p>Decodes an STTP string according to the defined operational modes.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.disconnect">
<span class="sig-name descname"><span class="pre">disconnect</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/datasubscriber.html#DataSubscriber.disconnect"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.disconnect" title="Link to this definition"></a></dt>
<dd><p>Initiates a <cite>DataSubscriber</cite> disconnect sequence.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.dispose">
<span class="sig-name descname"><span class="pre">dispose</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/datasubscriber.html#DataSubscriber.dispose"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.dispose" title="Link to this definition"></a></dt>
<dd><p>Cleanly shuts down a <cite>DataSubscriber</cite> that is no longer being used, e.g., during a normal application exit.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.disposing">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">disposing</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">bool</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.disposing" title="Link to this definition"></a></dt>
<dd><p>Determines if <cite>DataSubscriber</cite> is being disposed.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.encodestr">
<span class="sig-name descname"><span class="pre">encodestr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">bytes</span></span></span><a class="reference internal" href="_modules/sttp/transport/datasubscriber.html#DataSubscriber.encodestr"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.encodestr" title="Link to this definition"></a></dt>
<dd><p>Encodes an STTP string according to the defined operational modes.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.errormessage_callback">
<span class="sig-name descname"><span class="pre">errormessage_callback</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.errormessage_callback" title="Link to this definition"></a></dt>
<dd><p>Called when an error message should be logged.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.lookup_metadata">
<span class="sig-name descname"><span class="pre">lookup_metadata</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">signalid</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">UUID</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint64</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="sttp.metadata.record.html#sttp.metadata.record.measurement.MeasurementRecord" title="sttp.metadata.record.measurement.MeasurementRecord"><span class="pre">MeasurementRecord</span></a></span></span><a class="reference internal" href="_modules/sttp/transport/datasubscriber.html#DataSubscriber.lookup_metadata"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.lookup_metadata" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>MeasurementRecord</cite> for the specified signal ID from the local registry.
If the metadata does not exist, a new record is created and returned.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.metadatacache">
<span class="sig-name descname"><span class="pre">metadatacache</span></span><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.metadatacache" title="Link to this definition"></a></dt>
<dd><p>Defines the metadata cache associated with this <cite>DataSubscriber</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.metadatareceived_callback">
<span class="sig-name descname"><span class="pre">metadatareceived_callback</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">bytes</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.metadatareceived_callback" title="Link to this definition"></a></dt>
<dd><p>Called when <cite>DataSubscriber</cite> receives a metadata response.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.newbufferblocks_callback">
<span class="sig-name descname"><span class="pre">newbufferblocks_callback</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.transport.bufferblock.BufferBlock" title="sttp.transport.bufferblock.BufferBlock"><span class="pre">BufferBlock</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.newbufferblocks_callback" title="Link to this definition"></a></dt>
<dd><p>Called when <cite>DataSubscriber</cite> receives a set of new buffer block measurements from the <cite>DataPublisher</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.newmeasurements_callback">
<span class="sig-name descname"><span class="pre">newmeasurements_callback</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.transport.measurement.Measurement" title="sttp.transport.measurement.Measurement"><span class="pre">Measurement</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.newmeasurements_callback" title="Link to this definition"></a></dt>
<dd><p>Called when <cite>DataSubscriber</cite> receives a set of new measurements from the <cite>DataPublisher</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.notificationreceived_callback">
<span class="sig-name descname"><span class="pre">notificationreceived_callback</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.notificationreceived_callback" title="Link to this definition"></a></dt>
<dd><p>Called when the <cite>DataPublisher</cite> sends a notification that requires receipt.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.processingcomplete_callback">
<span class="sig-name descname"><span class="pre">processingcomplete_callback</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.processingcomplete_callback" title="Link to this definition"></a></dt>
<dd><p>Called when the <cite>DataPublisher</cite> sends a notification that temporal processing has completed, i.e., the end of a historical playback data stream has been reached.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.send_servercommand">
<span class="sig-name descname"><span class="pre">send_servercommand</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">commandcode</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.transport.constants.ServerCommand" title="sttp.transport.constants.ServerCommand"><span class="pre">ServerCommand</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">data</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bytes</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/datasubscriber.html#DataSubscriber.send_servercommand"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.send_servercommand" title="Link to this definition"></a></dt>
<dd><p>Sends a server command code to the <cite>DataPublisher</cite> with specified payload.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.send_servercommand_withmessage">
<span class="sig-name descname"><span class="pre">send_servercommand_withmessage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">commandcode</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.transport.constants.ServerCommand" title="sttp.transport.constants.ServerCommand"><span class="pre">ServerCommand</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/datasubscriber.html#DataSubscriber.send_servercommand_withmessage"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.send_servercommand_withmessage" title="Link to this definition"></a></dt>
<dd><p>Sends a server command code to the <cite>DataPublisher</cite> along with the specified string message as payload.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.socket_timeout">
<span class="sig-name descname"><span class="pre">socket_timeout</span></span><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.socket_timeout" title="Link to this definition"></a></dt>
<dd><p>Defines the socket timeout in seconds for the <cite>DataSubscriber</cite> connection.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.statusmessage_callback">
<span class="sig-name descname"><span class="pre">statusmessage_callback</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.statusmessage_callback" title="Link to this definition"></a></dt>
<dd><p>Called when a informational message should be logged.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.sttp_sourceinfo">
<span class="sig-name descname"><span class="pre">sttp_sourceinfo</span></span><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.sttp_sourceinfo" title="Link to this definition"></a></dt>
<dd><p>Defines the STTP library API title as identification information of <cite>DataSubscriber</cite> to a <cite>DataPublisher</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.sttp_updatedoninfo">
<span class="sig-name descname"><span class="pre">sttp_updatedoninfo</span></span><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.sttp_updatedoninfo" title="Link to this definition"></a></dt>
<dd><p>Defines when the STTP library API was last updated as identification information of <cite>DataSubscriber</cite> to a <cite>DataPublisher</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.sttp_versioninfo">
<span class="sig-name descname"><span class="pre">sttp_versioninfo</span></span><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.sttp_versioninfo" title="Link to this definition"></a></dt>
<dd><p>Defines the STTP library API version as identification information of <cite>DataSubscriber</cite> to a <cite>DataPublisher</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.subscribe">
<span class="sig-name descname"><span class="pre">subscribe</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span></span><a class="reference internal" href="_modules/sttp/transport/datasubscriber.html#DataSubscriber.subscribe"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.subscribe" title="Link to this definition"></a></dt>
<dd><p>Notifies the <cite>DataPublisher</cite> that a <cite>DataSubscriber</cite> would like to start receiving streaming data.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.subscribed">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">subscribed</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">bool</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.subscribed" title="Link to this definition"></a></dt>
<dd><p>Determines if a <cite>DataSubscriber</cite> is currently subscribed to a data stream.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.subscriberid">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">subscriberid</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">UUID</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.subscriberid" title="Link to this definition"></a></dt>
<dd><p>Gets the subscriber ID as assigned by the <cite>DataPublisher</cite> upon receipt of the <cite>SignalIndexCache</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.subscription">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">subscription</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="#sttp.transport.subscriptioninfo.SubscriptionInfo" title="sttp.transport.subscriptioninfo.SubscriptionInfo"><span class="pre">SubscriptionInfo</span></a></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.subscription" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>SubscriptionInfo</cite> associated with this <cite>DataSubscriber</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.subscriptionupdated_callback">
<span class="sig-name descname"><span class="pre">subscriptionupdated_callback</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.transport.signalindexcache.SignalIndexCache" title="sttp.transport.signalindexcache.SignalIndexCache"><span class="pre">SignalIndexCache</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.subscriptionupdated_callback" title="Link to this definition"></a></dt>
<dd><p>Called when <cite>DataSubscriber</cite> receives a new signal index cache.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.total_commandchannel_bytesreceived">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">total_commandchannel_bytesreceived</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">uint64</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.total_commandchannel_bytesreceived" title="Link to this definition"></a></dt>
<dd><p>Gets the total number of bytes received via the command channel since last connection.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.total_datachannel_bytesreceived">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">total_datachannel_bytesreceived</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">uint64</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.total_datachannel_bytesreceived" title="Link to this definition"></a></dt>
<dd><p>Gets the total number of bytes received via the data channel since last connection.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.total_measurementsreceived">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">total_measurementsreceived</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">uint64</span></em><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.total_measurementsreceived" title="Link to this definition"></a></dt>
<dd><p>Gets the total number of measurements received since last subscription.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.unsubscribe">
<span class="sig-name descname"><span class="pre">unsubscribe</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/datasubscriber.html#DataSubscriber.unsubscribe"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.unsubscribe" title="Link to this definition"></a></dt>
<dd><p>Notifies the <cite>DataPublisher</cite> that a <cite>DataSubscriber</cite> would like to stop receiving streaming data.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.datasubscriber.DataSubscriber.version">
<span class="sig-name descname"><span class="pre">version</span></span><a class="headerlink" href="#sttp.transport.datasubscriber.DataSubscriber.version" title="Link to this definition"></a></dt>
<dd><p>Defines the STTP protocol version used by this library.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.transport.measurement">
<span id="sttp-transport-measurement-module"></span><h2>sttp.transport.measurement module<a class="headerlink" href="#module-sttp.transport.measurement" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.transport.measurement.Measurement">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.transport.measurement.</span></span><span class="sig-name descname"><span class="pre">Measurement</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">signalid</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">UUID</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">float64</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timestamp</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint64</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.transport.constants.StateFlags" title="sttp.transport.constants.StateFlags"><span class="pre">StateFlags</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/measurement.html#Measurement"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.measurement.Measurement" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Represents a basic unit of measured data for transmission or reception in the STTP API.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.measurement.Measurement.DEFAULT_FLAGS">
<span class="sig-name descname"><span class="pre">DEFAULT_FLAGS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#sttp.transport.measurement.Measurement.DEFAULT_FLAGS" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.measurement.Measurement.DEFAULT_SIGNALID">
<span class="sig-name descname"><span class="pre">DEFAULT_SIGNALID</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">UUID('00000000-0000-0000-0000-000000000000')</span></em><a class="headerlink" href="#sttp.transport.measurement.Measurement.DEFAULT_SIGNALID" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.measurement.Measurement.DEFAULT_TIMESTAMP">
<span class="sig-name descname"><span class="pre">DEFAULT_TIMESTAMP</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.uint64(0)</span></em><a class="headerlink" href="#sttp.transport.measurement.Measurement.DEFAULT_TIMESTAMP" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.measurement.Measurement.DEFAULT_VALUE">
<span class="sig-name descname"><span class="pre">DEFAULT_VALUE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.float64(nan)</span></em><a class="headerlink" href="#sttp.transport.measurement.Measurement.DEFAULT_VALUE" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.transport.measurement.Measurement.datetime">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">datetime</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">datetime</span></em><a class="headerlink" href="#sttp.transport.measurement.Measurement.datetime" title="Link to this definition"></a></dt>
<dd><p>Gets <cite>Measurement</cite> ticks-based timestamp as a standard Python datetime value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.measurement.Measurement.flags">
<span class="sig-name descname"><span class="pre">flags</span></span><a class="headerlink" href="#sttp.transport.measurement.Measurement.flags" title="Link to this definition"></a></dt>
<dd><p>Defines flags indicating the state of the measurement as reported by the device that took it.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.measurement.Measurement.signalid">
<span class="sig-name descname"><span class="pre">signalid</span></span><a class="headerlink" href="#sttp.transport.measurement.Measurement.signalid" title="Link to this definition"></a></dt>
<dd><p>Defines measurement’s globally unique identifier.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.measurement.Measurement.timestamp">
<span class="sig-name descname"><span class="pre">timestamp</span></span><a class="headerlink" href="#sttp.transport.measurement.Measurement.timestamp" title="Link to this definition"></a></dt>
<dd><p>Defines the STTP uint64 timestamp, in ticks, that measurement was taken.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.transport.measurement.Measurement.timestampvalue">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">timestampvalue</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">int64</span></em><a class="headerlink" href="#sttp.transport.measurement.Measurement.timestampvalue" title="Link to this definition"></a></dt>
<dd><p>Gets the integer-based time from a <cite>Measurement</cite> ticks-based timestamp, i.e.,
the 62-bit time value excluding any leap-second flags.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.measurement.Measurement.value">
<span class="sig-name descname"><span class="pre">value</span></span><a class="headerlink" href="#sttp.transport.measurement.Measurement.value" title="Link to this definition"></a></dt>
<dd><p>Defines instantaneous value of the measurement.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.transport.signalindexcache">
<span id="sttp-transport-signalindexcache-module"></span><h2>sttp.transport.signalindexcache module<a class="headerlink" href="#module-sttp.transport.signalindexcache" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.transport.signalindexcache.SignalIndexCache">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.transport.signalindexcache.</span></span><span class="sig-name descname"><span class="pre">SignalIndexCache</span></span><a class="reference internal" href="_modules/sttp/transport/signalindexcache.html#SignalIndexCache"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.signalindexcache.SignalIndexCache" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Represents a mapping of 32-bit runtime IDs to 128-bit globally unique measurement IDs. The class
additionally provides reverse lookup and an extra mapping to human-readable measurement keys.</p>
<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.signalindexcache.SignalIndexCache.contains">
<span class="sig-name descname"><span class="pre">contains</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">signalindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int32</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">bool</span></span></span><a class="reference internal" href="_modules/sttp/transport/signalindexcache.html#SignalIndexCache.contains"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.signalindexcache.SignalIndexCache.contains" title="Link to this definition"></a></dt>
<dd><p>Determines if the specified signalindex exists with the <cite>SignalIndexCache</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.transport.signalindexcache.SignalIndexCache.count">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">count</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">uint32</span></em><a class="headerlink" href="#sttp.transport.signalindexcache.SignalIndexCache.count" title="Link to this definition"></a></dt>
<dd><p>Gets the number of <cite>Measurement</cite> records that can be found in the <cite>SignalIndexCache</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.signalindexcache.SignalIndexCache.decode">
<span class="sig-name descname"><span class="pre">decode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ds</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber" title="sttp.transport.datasubscriber.DataSubscriber"><span class="pre">DataSubscriber</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">buffer</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bytes</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">UUID</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Exception</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/transport/signalindexcache.html#SignalIndexCache.decode"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.signalindexcache.SignalIndexCache.decode" title="Link to this definition"></a></dt>
<dd><p>Parses a <cite>SignalIndexCache</cite> from the specified byte buffer received from a <cite>DataPublisher</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.signalindexcache.SignalIndexCache.id">
<span class="sig-name descname"><span class="pre">id</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">signalindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int32</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">uint64</span></span></span><a class="reference internal" href="_modules/sttp/transport/signalindexcache.html#SignalIndexCache.id"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.signalindexcache.SignalIndexCache.id" title="Link to this definition"></a></dt>
<dd><p>Returns the <cite>Measurement</cite> integer ID for the specified signalindex in the <cite>SignalIndexCache</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.signalindexcache.SignalIndexCache.record">
<span class="sig-name descname"><span class="pre">record</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">signalindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int32</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">UUID</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">str</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">uint64</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">bool</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="_modules/sttp/transport/signalindexcache.html#SignalIndexCache.record"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.signalindexcache.SignalIndexCache.record" title="Link to this definition"></a></dt>
<dd><p>Record returns the key <cite>Measurement</cite> values, signal ID Guid, source string, and integer ID and a
final boolean value representing find success for the specified signalindex in the <cite>SignalIndexCache</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.signalindexcache.SignalIndexCache.signalid">
<span class="sig-name descname"><span class="pre">signalid</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">signalindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int32</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">UUID</span></span></span><a class="reference internal" href="_modules/sttp/transport/signalindexcache.html#SignalIndexCache.signalid"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.signalindexcache.SignalIndexCache.signalid" title="Link to this definition"></a></dt>
<dd><p>Returns the signal ID Guid for the specified signalindex in the <cite>SignalIndexCache</cite>.</p>
</dd></dl>

<dl class="py property">
<dt class="sig sig-object py" id="sttp.transport.signalindexcache.SignalIndexCache.signalids">
<em class="property"><span class="pre">property</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">signalids</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">Set</span><span class="p"><span class="pre">[</span></span><span class="pre">UUID</span><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#sttp.transport.signalindexcache.SignalIndexCache.signalids" title="Link to this definition"></a></dt>
<dd><p>Gets a set for all the Guid values found in the <cite>SignalIndexCache</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.signalindexcache.SignalIndexCache.signalindex">
<span class="sig-name descname"><span class="pre">signalindex</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">signalid</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">UUID</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int32</span></span></span><a class="reference internal" href="_modules/sttp/transport/signalindexcache.html#SignalIndexCache.signalindex"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.signalindexcache.SignalIndexCache.signalindex" title="Link to this definition"></a></dt>
<dd><p>Returns the signal index for the specified signal ID Guid in the <cite>SignalIndexCache</cite>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.signalindexcache.SignalIndexCache.source">
<span class="sig-name descname"><span class="pre">source</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">signalindex</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int32</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="reference internal" href="_modules/sttp/transport/signalindexcache.html#SignalIndexCache.source"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.signalindexcache.SignalIndexCache.source" title="Link to this definition"></a></dt>
<dd><p>Returns the <cite>Measurement</cite> source string for the specified signalindex in the <cite>SignalIndexCache</cite>.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.transport.signalkind">
<span id="sttp-transport-signalkind-module"></span><h2>sttp.transport.signalkind module<a class="headerlink" href="#module-sttp.transport.signalkind" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.transport.signalkind.SignalKind">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.transport.signalkind.</span></span><span class="sig-name descname"><span class="pre">SignalKind</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names=&lt;not</span> <span class="pre">given&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*values</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start=1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary=None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/signalkind.html#SignalKind"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.signalkind.SignalKind" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></p>
<p>Enumeration of the possible kinds of signals a Measurement can represent.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.signalkind.SignalKind.ALARM">
<span class="sig-name descname"><span class="pre">ALARM</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">9</span></em><a class="headerlink" href="#sttp.transport.signalkind.SignalKind.ALARM" title="Link to this definition"></a></dt>
<dd><p>Alarm defines an alarm value signal kind.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.signalkind.SignalKind.ANALOG">
<span class="sig-name descname"><span class="pre">ANALOG</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">6</span></em><a class="headerlink" href="#sttp.transport.signalkind.SignalKind.ANALOG" title="Link to this definition"></a></dt>
<dd><p>Analog defines an analog value signal kind.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.signalkind.SignalKind.ANGLE">
<span class="sig-name descname"><span class="pre">ANGLE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#sttp.transport.signalkind.SignalKind.ANGLE" title="Link to this definition"></a></dt>
<dd><p>Angle defines a phase angle signal kind (could be a voltage or a current).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.signalkind.SignalKind.CALCULATION">
<span class="sig-name descname"><span class="pre">CALCULATION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">7</span></em><a class="headerlink" href="#sttp.transport.signalkind.SignalKind.CALCULATION" title="Link to this definition"></a></dt>
<dd><p>Calculation defines a calculated value signal kind.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.signalkind.SignalKind.DFDT">
<span class="sig-name descname"><span class="pre">DFDT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">3</span></em><a class="headerlink" href="#sttp.transport.signalkind.SignalKind.DFDT" title="Link to this definition"></a></dt>
<dd><p>DfDt defines a frequency delta over time(dF/dt) signal kind.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.signalkind.SignalKind.DIGITAL">
<span class="sig-name descname"><span class="pre">DIGITAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">5</span></em><a class="headerlink" href="#sttp.transport.signalkind.SignalKind.DIGITAL" title="Link to this definition"></a></dt>
<dd><p>Digital defines a digital value signal kind.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.signalkind.SignalKind.FREQUENCY">
<span class="sig-name descname"><span class="pre">FREQUENCY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2</span></em><a class="headerlink" href="#sttp.transport.signalkind.SignalKind.FREQUENCY" title="Link to this definition"></a></dt>
<dd><p>Frequency defines a line frequency signal kind.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.signalkind.SignalKind.MAGNITUDE">
<span class="sig-name descname"><span class="pre">MAGNITUDE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1</span></em><a class="headerlink" href="#sttp.transport.signalkind.SignalKind.MAGNITUDE" title="Link to this definition"></a></dt>
<dd><p>Magnitude defines a phase magnitude signal kind (could be a voltage or a current).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.signalkind.SignalKind.QUALITY">
<span class="sig-name descname"><span class="pre">QUALITY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">10</span></em><a class="headerlink" href="#sttp.transport.signalkind.SignalKind.QUALITY" title="Link to this definition"></a></dt>
<dd><p>Quality defines a quality flags signal kind.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.signalkind.SignalKind.STATISTIC">
<span class="sig-name descname"><span class="pre">STATISTIC</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">8</span></em><a class="headerlink" href="#sttp.transport.signalkind.SignalKind.STATISTIC" title="Link to this definition"></a></dt>
<dd><p>Statistic defines a statistical value signal kind.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.signalkind.SignalKind.STATUS">
<span class="sig-name descname"><span class="pre">STATUS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">4</span></em><a class="headerlink" href="#sttp.transport.signalkind.SignalKind.STATUS" title="Link to this definition"></a></dt>
<dd><p>Status defines a status flags signal kind.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.signalkind.SignalKind.UNKNOWN">
<span class="sig-name descname"><span class="pre">UNKNOWN</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">11</span></em><a class="headerlink" href="#sttp.transport.signalkind.SignalKind.UNKNOWN" title="Link to this definition"></a></dt>
<dd><p>Unknown defines an undetermined signal kind.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sttp.transport.signalkind.SignalKindEnum">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.transport.signalkind.</span></span><span class="sig-name descname"><span class="pre">SignalKindEnum</span></span><a class="reference internal" href="_modules/sttp/transport/signalkind.html#SignalKindEnum"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.signalkind.SignalKindEnum" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Helper functions for the <cite>SignalKind</cite> enumeration.</p>
<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.signalkind.SignalKindEnum.acronym">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">acronym</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">signalkind</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.transport.signalkind.SignalKind" title="sttp.transport.signalkind.SignalKind"><span class="pre">SignalKind</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="reference internal" href="_modules/sttp/transport/signalkind.html#SignalKindEnum.acronym"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.signalkind.SignalKindEnum.acronym" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>SignalKind</cite> enumeration value as its two-character acronym string.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.signalkind.SignalKindEnum.parse_acronym">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">parse_acronym</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">acronym</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="#sttp.transport.signalkind.SignalKind" title="sttp.transport.signalkind.SignalKind"><span class="pre">SignalKind</span></a></span></span><a class="reference internal" href="_modules/sttp/transport/signalkind.html#SignalKindEnum.parse_acronym"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.signalkind.SignalKindEnum.parse_acronym" title="Link to this definition"></a></dt>
<dd><p>Gets the <cite>SignalKind</cite> enumeration value for the specified two-character acronym.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.signalkind.SignalKindEnum.signaltype">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">signaltype</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">signalkind</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.transport.signalkind.SignalKind" title="sttp.transport.signalkind.SignalKind"><span class="pre">SignalKind</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">phasortype</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="reference internal" href="_modules/sttp/transport/signalkind.html#SignalKindEnum.signaltype"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.signalkind.SignalKindEnum.signaltype" title="Link to this definition"></a></dt>
<dd><p>Gets the specific four-character signal type acronym for a ‘SignalKind’
enumeration value and phasor type, i.e., “V” voltage or “I” current.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>signalkind</strong> (The <cite>SignalKind</cite> enumeration value for the acronym.)</p></li>
<li><p><strong>phasortype</strong> (“V” for voltage or “I” for current when <cite>signalkind</cite> is <cite>SignalKind.ANGLE</cite> or <cite>SignalKind.MAGNITUDE</cite>.)</p></li>
</ul>
</dd>
</dl>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.transport.subscriberconnector">
<span id="sttp-transport-subscriberconnector-module"></span><h2>sttp.transport.subscriberconnector module<a class="headerlink" href="#module-sttp.transport.subscriberconnector" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.transport.subscriberconnector.SubscriberConnector">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.transport.subscriberconnector.</span></span><span class="sig-name descname"><span class="pre">SubscriberConnector</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">errormessage_callback</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">reconnect_callback</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Callable</span><span class="p"><span class="pre">[</span></span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber" title="sttp.transport.datasubscriber.DataSubscriber"><span class="pre">DataSubscriber</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">None</span><span class="p"><span class="pre">]</span></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hostname</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">np.uint16</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maxretries</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">np.int32</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">retryinterval</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">float</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maxretryinterval</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">float</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">autoreconnect</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/subscriberconnector.html#SubscriberConnector"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.subscriberconnector.SubscriberConnector" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Represents a connector that will establish or automatically reestablish a connection from a <cite>DataSubscriber</cite> to a <cite>DataPublisher</cite>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_AUTORECONNECT">
<span class="sig-name descname"><span class="pre">DEFAULT_AUTORECONNECT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">True</span></em><a class="headerlink" href="#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_AUTORECONNECT" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_ERRORMESSAGE_CALLBACK">
<span class="sig-name descname"><span class="pre">DEFAULT_ERRORMESSAGE_CALLBACK</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_ERRORMESSAGE_CALLBACK" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_HOSTNAME">
<span class="sig-name descname"><span class="pre">DEFAULT_HOSTNAME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_HOSTNAME" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_MAXRETRIES">
<span class="sig-name descname"><span class="pre">DEFAULT_MAXRETRIES</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.int32(-1)</span></em><a class="headerlink" href="#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_MAXRETRIES" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_MAXRETRYINTERVAL">
<span class="sig-name descname"><span class="pre">DEFAULT_MAXRETRYINTERVAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">30.0</span></em><a class="headerlink" href="#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_MAXRETRYINTERVAL" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_PORT">
<span class="sig-name descname"><span class="pre">DEFAULT_PORT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.uint16(0)</span></em><a class="headerlink" href="#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_PORT" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_RECONNECT_CALLBACK">
<span class="sig-name descname"><span class="pre">DEFAULT_RECONNECT_CALLBACK</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_RECONNECT_CALLBACK" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_RETRYINTERVAL">
<span class="sig-name descname"><span class="pre">DEFAULT_RETRYINTERVAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1.0</span></em><a class="headerlink" href="#sttp.transport.subscriberconnector.SubscriberConnector.DEFAULT_RETRYINTERVAL" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriberconnector.SubscriberConnector.autoreconnect">
<span class="sig-name descname"><span class="pre">autoreconnect</span></span><a class="headerlink" href="#sttp.transport.subscriberconnector.SubscriberConnector.autoreconnect" title="Link to this definition"></a></dt>
<dd><p>Defines flag that determines if connections should be automatically reattempted.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.subscriberconnector.SubscriberConnector.cancel">
<span class="sig-name descname"><span class="pre">cancel</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/subscriberconnector.html#SubscriberConnector.cancel"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.subscriberconnector.SubscriberConnector.cancel" title="Link to this definition"></a></dt>
<dd><p>Stops all current and future connection sequences.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.subscriberconnector.SubscriberConnector.connect">
<span class="sig-name descname"><span class="pre">connect</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ds</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="#sttp.transport.datasubscriber.DataSubscriber" title="sttp.transport.datasubscriber.DataSubscriber"><span class="pre">DataSubscriber</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="#sttp.transport.constants.ConnectStatus" title="sttp.transport.constants.ConnectStatus"><span class="pre">ConnectStatus</span></a></span></span><a class="reference internal" href="_modules/sttp/transport/subscriberconnector.html#SubscriberConnector.connect"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.subscriberconnector.SubscriberConnector.connect" title="Link to this definition"></a></dt>
<dd><p>Initiates a connection sequence for a <cite>DataSubscriber</cite></p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.subscriberconnector.SubscriberConnector.dispose">
<span class="sig-name descname"><span class="pre">dispose</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/subscriberconnector.html#SubscriberConnector.dispose"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.subscriberconnector.SubscriberConnector.dispose" title="Link to this definition"></a></dt>
<dd><p>Cleanly shuts down a <cite>SubscriberConnector</cite> that is no longer being used, e.g., during a normal application exit.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriberconnector.SubscriberConnector.errormessage_callback">
<span class="sig-name descname"><span class="pre">errormessage_callback</span></span><a class="headerlink" href="#sttp.transport.subscriberconnector.SubscriberConnector.errormessage_callback" title="Link to this definition"></a></dt>
<dd><p>Called when an error message should be logged.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriberconnector.SubscriberConnector.hostname">
<span class="sig-name descname"><span class="pre">hostname</span></span><a class="headerlink" href="#sttp.transport.subscriberconnector.SubscriberConnector.hostname" title="Link to this definition"></a></dt>
<dd><p><cite>DataPublisher</cite> DNS name or IP.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriberconnector.SubscriberConnector.maxretries">
<span class="sig-name descname"><span class="pre">maxretries</span></span><a class="headerlink" href="#sttp.transport.subscriberconnector.SubscriberConnector.maxretries" title="Link to this definition"></a></dt>
<dd><p>MaxRetries defines the maximum number of times to retry a connection. Set value to -1 to retry infinitely.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriberconnector.SubscriberConnector.maxretryinterval">
<span class="sig-name descname"><span class="pre">maxretryinterval</span></span><a class="headerlink" href="#sttp.transport.subscriberconnector.SubscriberConnector.maxretryinterval" title="Link to this definition"></a></dt>
<dd><p>Defines the maximum retry interval, in milliseconds.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriberconnector.SubscriberConnector.port">
<span class="sig-name descname"><span class="pre">port</span></span><a class="headerlink" href="#sttp.transport.subscriberconnector.SubscriberConnector.port" title="Link to this definition"></a></dt>
<dd><p>TCP/IP listening port of the <cite>DataPublisher</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriberconnector.SubscriberConnector.reconnect_callback">
<span class="sig-name descname"><span class="pre">reconnect_callback</span></span><a class="headerlink" href="#sttp.transport.subscriberconnector.SubscriberConnector.reconnect_callback" title="Link to this definition"></a></dt>
<dd><p>Called when <cite>SubscriberConnector</cite> attempts to reconnect.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sttp.transport.subscriberconnector.SubscriberConnector.reset_connection">
<span class="sig-name descname"><span class="pre">reset_connection</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/subscriberconnector.html#SubscriberConnector.reset_connection"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.subscriberconnector.SubscriberConnector.reset_connection" title="Link to this definition"></a></dt>
<dd><p>Resets <cite>SubscriberConnector</cite> for a new connection.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriberconnector.SubscriberConnector.retryinterval">
<span class="sig-name descname"><span class="pre">retryinterval</span></span><a class="headerlink" href="#sttp.transport.subscriberconnector.SubscriberConnector.retryinterval" title="Link to this definition"></a></dt>
<dd><p>defines the base retry interval, in milliseconds. Retries will exponentially back-off starting from this interval.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.transport.subscriptioninfo">
<span id="sttp-transport-subscriptioninfo-module"></span><h2>sttp.transport.subscriptioninfo module<a class="headerlink" href="#module-sttp.transport.subscriptioninfo" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sttp.transport.subscriptioninfo.</span></span><span class="sig-name descname"><span class="pre">SubscriptionInfo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filterexpression</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">throttled</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">publishinterval</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">float64</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">udpdatachannel</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">datachannel_localport</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">uint16</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">datachannel_interface</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">includetime</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">enabletimereasonabilitycheck</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lagtime</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">float64</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">leadtime</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">float64</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">uselocalclockasrealtime</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usemillisecondresolution</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">requestnanvaluefilter</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">starttime</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stoptime</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">constraintparameters</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">processinginterval</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int32</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">extra_connectionstring_parameters</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/sttp/transport/subscriptioninfo.html#SubscriptionInfo"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Defines subscription related settings for a <cite>DataSubscriber</cite> instance.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_CONSTRAINTPARAMETERS">
<span class="sig-name descname"><span class="pre">DEFAULT_CONSTRAINTPARAMETERS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_CONSTRAINTPARAMETERS" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_DATACHANNEL_INTERFACE">
<span class="sig-name descname"><span class="pre">DEFAULT_DATACHANNEL_INTERFACE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_DATACHANNEL_INTERFACE" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_DATACHANNEL_LOCALPORT">
<span class="sig-name descname"><span class="pre">DEFAULT_DATACHANNEL_LOCALPORT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.uint16(0)</span></em><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_DATACHANNEL_LOCALPORT" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_ENABLE_TIME_REASONABILITY_CHECK">
<span class="sig-name descname"><span class="pre">DEFAULT_ENABLE_TIME_REASONABILITY_CHECK</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">False</span></em><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_ENABLE_TIME_REASONABILITY_CHECK" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_EXTRA_CONNECTIONSTRING_PARAMETERS">
<span class="sig-name descname"><span class="pre">DEFAULT_EXTRA_CONNECTIONSTRING_PARAMETERS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_EXTRA_CONNECTIONSTRING_PARAMETERS" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_FILTEREXPRESSION">
<span class="sig-name descname"><span class="pre">DEFAULT_FILTEREXPRESSION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_FILTEREXPRESSION" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_INCLUDETIME">
<span class="sig-name descname"><span class="pre">DEFAULT_INCLUDETIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">True</span></em><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_INCLUDETIME" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_LAGTIME">
<span class="sig-name descname"><span class="pre">DEFAULT_LAGTIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.float64(10.0)</span></em><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_LAGTIME" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_LEADTIME">
<span class="sig-name descname"><span class="pre">DEFAULT_LEADTIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.float64(5.0)</span></em><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_LEADTIME" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_PROCESSINGINTERVAL">
<span class="sig-name descname"><span class="pre">DEFAULT_PROCESSINGINTERVAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.int32(-1)</span></em><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_PROCESSINGINTERVAL" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_PUBLISHINTERVAL">
<span class="sig-name descname"><span class="pre">DEFAULT_PUBLISHINTERVAL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">np.float64(1.0)</span></em><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_PUBLISHINTERVAL" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_REQUEST_NANVALUEFILTER">
<span class="sig-name descname"><span class="pre">DEFAULT_REQUEST_NANVALUEFILTER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">False</span></em><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_REQUEST_NANVALUEFILTER" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_STARTTIME">
<span class="sig-name descname"><span class="pre">DEFAULT_STARTTIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_STARTTIME" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_STOPTIME">
<span class="sig-name descname"><span class="pre">DEFAULT_STOPTIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">''</span></em><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_STOPTIME" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_THROTTLED">
<span class="sig-name descname"><span class="pre">DEFAULT_THROTTLED</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">False</span></em><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_THROTTLED" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_UDPDATACHANNEL">
<span class="sig-name descname"><span class="pre">DEFAULT_UDPDATACHANNEL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">False</span></em><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_UDPDATACHANNEL" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_USE_LOCALCLOCK_AS_REALTIME">
<span class="sig-name descname"><span class="pre">DEFAULT_USE_LOCALCLOCK_AS_REALTIME</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">False</span></em><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_USE_LOCALCLOCK_AS_REALTIME" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_USE_MILLISECONDRESOLUTION">
<span class="sig-name descname"><span class="pre">DEFAULT_USE_MILLISECONDRESOLUTION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">False</span></em><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.DEFAULT_USE_MILLISECONDRESOLUTION" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.constraintparameters">
<span class="sig-name descname"><span class="pre">constraintparameters</span></span><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.constraintparameters" title="Link to this definition"></a></dt>
<dd><p>Defines any custom constraint parameters for a requested temporal data playback. This can include
parameters that may be needed to initiate, filter, or control historical data access.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.datachannel_interface">
<span class="sig-name descname"><span class="pre">datachannel_interface</span></span><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.datachannel_interface" title="Link to this definition"></a></dt>
<dd><p>Defines the desired network interface to use for UDP publication.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.datachannel_localport">
<span class="sig-name descname"><span class="pre">datachannel_localport</span></span><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.datachannel_localport" title="Link to this definition"></a></dt>
<dd><p>Defines the desired UDP port to use for publication.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.enabletimereasonabilitycheck">
<span class="sig-name descname"><span class="pre">enabletimereasonabilitycheck</span></span><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.enabletimereasonabilitycheck" title="Link to this definition"></a></dt>
<dd><p>Determines  if publisher should perform time reasonability checks.
When enabled <cite>lagtime</cite> and <cite>leadtime</cite> will be used to determine if a measurement timestamp is reasonable.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.extra_connectionstring_parameters">
<span class="sig-name descname"><span class="pre">extra_connectionstring_parameters</span></span><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.extra_connectionstring_parameters" title="Link to this definition"></a></dt>
<dd><p>Defines any extra or custom connection string parameters that may be needed for a subscription.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.filterexpression">
<span class="sig-name descname"><span class="pre">filterexpression</span></span><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.filterexpression" title="Link to this definition"></a></dt>
<dd><p>Defines the desired measurements for a subscription. Examples include:</p>
<ul class="simple">
<li><dl class="simple">
<dt>Directly specified signal IDs (UUID values in string format):</dt><dd><p><cite>38A47B0-F10B-4143-9A0A-0DBC4FFEF1E8; {E4BBFE6A-35BD-4E5B-92C9-11FF913E7877}</cite></p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt>Directly specified tag names:</dt><dd><p><cite>DOM_GPLAINS-BUS1:VH; TVA_SHELBY-BUS1:VH</cite></p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt>Directly specified identifiers in “measurement key” format:</dt><dd><p><cite>PPA:15; STAT:20</cite></p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt>A filter expression against a selection view:</dt><dd><p><cite>FILTER ActiveMeasurements WHERE Company=’GPA’ AND SignalType=’FREQ’</cite></p>
</dd>
</dl>
</li>
</ul>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.includetime">
<span class="sig-name descname"><span class="pre">includetime</span></span><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.includetime" title="Link to this definition"></a></dt>
<dd><p>Determines if time should be included in non-compressed, compact measurements.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.lagtime">
<span class="sig-name descname"><span class="pre">lagtime</span></span><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.lagtime" title="Link to this definition"></a></dt>
<dd><p>Defines defines the allowed past time deviation tolerance in seconds (can be sub-second).
Value is used to determine if a measurement timestamp is reasonable.
Only applicable when <cite>enabletimereasonabilitycheck</cite> is <cite>True</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.leadtime">
<span class="sig-name descname"><span class="pre">leadtime</span></span><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.leadtime" title="Link to this definition"></a></dt>
<dd><p>Defines defines the allowed future time deviation tolerance in seconds (can be sub-second).
Value is used to determine if a measurement timestamp is reasonable.
Only applicable when <cite>enabletimereasonabilitycheck</cite> is <cite>True</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.processinginterval">
<span class="sig-name descname"><span class="pre">processinginterval</span></span><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.processinginterval" title="Link to this definition"></a></dt>
<dd><p>Defines the initial playback speed, in milliseconds, for a requested temporal data playback.
With the exception of the values of -1 and 0, this value specifies the desired processing interval for data, i.e.,
basically a delay, or timer interval, over which to process data. A value of -1 means to use the default processing
interval while a value of 0 means to process data as fast as possible.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.publishinterval">
<span class="sig-name descname"><span class="pre">publishinterval</span></span><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.publishinterval" title="Link to this definition"></a></dt>
<dd><p>Defines the down-sampling publish interval to use when <cite>throttled</cite> is <cite>True</cite>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.request_nanvaluefilter">
<span class="sig-name descname"><span class="pre">request_nanvaluefilter</span></span><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.request_nanvaluefilter" title="Link to this definition"></a></dt>
<dd><p>Requests that the publisher filter, i.e., does not send, any NaN values.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.starttime">
<span class="sig-name descname"><span class="pre">starttime</span></span><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.starttime" title="Link to this definition"></a></dt>
<dd><p>Defines the start time for a requested temporal data playback, i.e., a historical subscription.
Simply by specifying a <cite>StartTime</cite> and <cite>StopTime</cite>, a subscription is considered a historical subscription.
Note that the publisher may not support historical subscriptions, in which case the subscribe will fail.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.stoptime">
<span class="sig-name descname"><span class="pre">stoptime</span></span><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.stoptime" title="Link to this definition"></a></dt>
<dd><p>Defines the stop time for a requested temporal data playback, i.e., a historical subscription.
Simply by specifying a <cite>StartTime</cite> and <cite>StopTime</cite>, a subscription is considered a historical subscription.
Note that the publisher may not support historical subscriptions, in which case the subscribe will fail.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.throttled">
<span class="sig-name descname"><span class="pre">throttled</span></span><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.throttled" title="Link to this definition"></a></dt>
<dd><p>Determines if data will be published using down-sampling.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.udpdatachannel">
<span class="sig-name descname"><span class="pre">udpdatachannel</span></span><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.udpdatachannel" title="Link to this definition"></a></dt>
<dd><p>Requests that a UDP channel be used for data publication.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.use_millisecondresolution">
<span class="sig-name descname"><span class="pre">use_millisecondresolution</span></span><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.use_millisecondresolution" title="Link to this definition"></a></dt>
<dd><p>Determines if time should be restricted to milliseconds in non-compressed, compact measurements.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sttp.transport.subscriptioninfo.SubscriptionInfo.uselocalclockasrealtime">
<span class="sig-name descname"><span class="pre">uselocalclockasrealtime</span></span><a class="headerlink" href="#sttp.transport.subscriptioninfo.SubscriptionInfo.uselocalclockasrealtime" title="Link to this definition"></a></dt>
<dd><p>Determines if publisher should use local clock as real time. If false,
the timestamp of the latest measurement will be used as real-time.
Only applicable when <cite>enabletimereasonabilitycheck</cite> is <cite>True</cite>.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-sttp.transport">
<span id="module-contents"></span><h2>Module contents<a class="headerlink" href="#module-sttp.transport" title="Link to this heading"></a></h2>
</section>
</section>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
  
 
<hr />
<p>
    <a href="https://github.com/sttp/pyapi">GitHub Home</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://pypi.org/project/sttpapi/" target="_blank">PyPi Package: sttpapi</a>
    &nbsp;&nbsp;•&nbsp;&nbsp;
    <a href="https://sttp.github.io/documentation/" target="_blank">General STTP Documentation</a>
</p>
<br /><br />
Copyright © 2022, <a href="https://gridprotectionalliance.org/" target="_blank">Grid&nbsp;Protection&nbsp;Alliance</a>
<a href="https://github.com/sttp/pyapi">
    <img align="right" style="margin-top: -50px"
        src="https://raw.githubusercontent.com/sttp/pyapi/main/docs/img/LockPython_64High.png">
</a>


</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>