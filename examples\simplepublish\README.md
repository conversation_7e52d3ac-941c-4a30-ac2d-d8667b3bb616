# Simple STTP Publisher Example

This example demonstrates how to create a simple STTP publisher that generates and publishes synthetic measurement data to connected subscribers.

## Overview

The simple publisher example shows how to:

1. Create an STTP publisher instance
2. Define measurement metadata
3. Generate synthetic measurement data
4. Publish measurements to connected subscribers
5. Handle client connection events

## Features

- **Synthetic Data Generation**: Creates time-varying synthetic measurements including frequency, voltage, current, and power measurements
- **Real-time Publishing**: Publishes measurements at 10 Hz (10 times per second)
- **Client Management**: Tracks and reports subscriber connections and disconnections
- **Configurable Port**: Allows specifying the listening port via command line

## Usage

### Basic Usage

```bash
python main.py
```

This starts the publisher on the default port 7175.

### Custom Port

```bash
python main.py --port 8080
```

This starts the publisher on port 8080.

### Command Line Options

- `--port`: Port number to listen on (default: 7175)

## Generated Measurements

The publisher generates the following synthetic measurements:

| Signal Type | Description | Unit | Behavior |
|-------------|-------------|------|----------|
| FREQ | Frequency | Hz | ~60 Hz with small sinusoidal variations |
| VPHM | Voltage Magnitude | V | ~120V with sinusoidal variations |
| VPHA | Voltage Angle | Degrees | Slowly rotating angle |
| IPHM | Current Magnitude | A | ~10A with sinusoidal variations |
| IPHA | Current Angle | Degrees | Lagging voltage angle by 30° |
| MW | Real Power | MW | ~100 MW with sinusoidal variations |
| MVAR | Reactive Power | MVAR | ~50 MVAR with cosine variations |

## Testing with Subscribers

You can test the publisher with any STTP subscriber, including the examples in this repository:

### Using the Simple Subscribe Example

1. Start the publisher:
   ```bash
   cd examples/simplepublish
   python main.py
   ```

2. In another terminal, start a subscriber:
   ```bash
   cd examples/simplesubscribe
   python main.py localhost 7175
   ```

The subscriber should connect and start receiving the published measurements.

### Using External STTP Tools

The publisher is compatible with other STTP implementations and tools, such as:
- Grid Solutions Framework (GSF) STTP tools
- openHistorian STTP Gateway
- Other STTP-compliant subscribers

## Code Structure

### Main Components

- **Publisher Setup**: Creates and configures the STTP publisher
- **Metadata Creation**: Defines measurement metadata with signal IDs, types, and descriptions
- **Data Generation**: Creates time-varying synthetic measurement values
- **Publishing Loop**: Continuously publishes measurements at regular intervals
- **Event Handling**: Logs client connections, disconnections, and status messages

### Key Functions

- `create_sample_metadata()`: Creates sample measurement definitions
- `generate_measurements()`: Generates synthetic measurement values
- `publish_data()`: Main publishing loop that runs in a separate thread

## Output Example

```
Created 7 measurement definitions
STATUS: DataPublisher listening on port 7175
Publisher started on port 7175
Press Ctrl+C to stop...
Starting data publication...
Client connected: 127.0.0.1:52341 (a1b2c3d4-e5f6-7890-abcd-ef1234567890)
Published 70 measurements to 1 subscribers
Published 140 measurements to 1 subscribers
...
```

## Notes

- The publisher will continue running until stopped with Ctrl+C
- Multiple subscribers can connect simultaneously
- Each subscriber receives all published measurements
- The synthetic data is deterministic based on the current time
- Measurements are published with high-precision timestamps using STTP ticks
