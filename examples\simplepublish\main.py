#!/usr/bin/env python3

# ******************************************************************************************************
#  main.py - Gbtc
#
#  Copyright © 2022, Grid Protection Alliance.  All Rights Reserved.
#
#  Licensed to the Grid Protection Alliance (GPA) under one or more contributor license agreements. See
#  the NOTICE file distributed with this work for additional information regarding copyright ownership.
#  The GPA licenses this file to you under the MIT License (MIT), the "License"; you may not use this
#  file except in compliance with the License. You may obtain a copy of the License at:
#
#      http://opensource.org/licenses/MIT
#
#  Unless agreed to in writing, the subject software distributed under the License is distributed on an
#  "AS-IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. Refer to the
#  License for the specific language governing permissions and limitations.
#
#  Code Modification History:
#  ----------------------------------------------------------------------------------------------------
#  12/23/2024 - J. Ritchie Carroll
#       Generated original version of source code.
#
# ******************************************************************************************************

"""
Simple STTP Publisher Example

This example demonstrates how to create a simple STTP publisher that generates
and publishes synthetic measurement data to connected subscribers.
"""

import argparse
import sys
import time
import math
import threading
from uuid import uuid4
from typing import List
import numpy as np

# Add the src directory to the path so we can import sttp
sys.path.insert(0, '../../src')

from sttp import Publisher, Measurement
from sttp.metadata.record.measurement import MeasurementRecord
from sttp.ticks import Ticks
from gsf import Limits

MAXPORT = Limits.MAXUINT16


def create_sample_metadata() -> List[MeasurementRecord]:
    """
    Creates sample measurement metadata for the publisher.
    """
    metadata = []
    
    # Create some sample measurements
    measurements = [
        ("FREQ", "Frequency", "Hz"),
        ("VPHM", "Voltage Magnitude", "V"),
        ("VPHA", "Voltage Angle", "Degrees"),
        ("IPHM", "Current Magnitude", "A"),
        ("IPHA", "Current Angle", "Degrees"),
        ("MW", "Real Power", "MW"),
        ("MVAR", "Reactive Power", "MVAR")
    ]
    
    for i, (signal_type, description, unit) in enumerate(measurements):
        signal_id = uuid4()
        
        record = MeasurementRecord(
            signalid=signal_id,
            id=np.uint64(i + 1),
            source="TESTPUB",
            signaltypename=signal_type,
            signalreference=f"TESTPUB-DEV1:{signal_type}",
            pointtag=f"TESTPUB.DEV1.{signal_type}",
            deviceacronym="DEV1",
            description=f"{description} ({unit})"
        )
        
        metadata.append(record)
    
    return metadata


def generate_measurements(metadata: List[MeasurementRecord], timestamp: np.uint64) -> List[Measurement]:
    """
    Generates synthetic measurement data.
    """
    measurements = []
    
    # Use timestamp to create time-varying values
    time_seconds = Ticks.to_datetime(timestamp).timestamp()
    
    for i, record in enumerate(metadata):
        if record.signaltypename == "FREQ":
            # Frequency around 60 Hz with small variations
            value = 60.0 + 0.1 * math.sin(time_seconds * 0.1)
        elif record.signaltypename == "VPHM":
            # Voltage magnitude around 120V with variations
            value = 120.0 + 5.0 * math.sin(time_seconds * 0.2)
        elif record.signaltypename == "VPHA":
            # Voltage angle slowly rotating
            value = (time_seconds * 10) % 360
        elif record.signaltypename == "IPHM":
            # Current magnitude with variations
            value = 10.0 + 2.0 * math.sin(time_seconds * 0.15)
        elif record.signaltypename == "IPHA":
            # Current angle lagging voltage
            value = ((time_seconds * 10) - 30) % 360
        elif record.signaltypename == "MW":
            # Real power with variations
            value = 100.0 + 20.0 * math.sin(time_seconds * 0.05)
        elif record.signaltypename == "MVAR":
            # Reactive power
            value = 50.0 + 10.0 * math.cos(time_seconds * 0.08)
        else:
            value = 0.0
        
        measurement = Measurement(record.signalid, np.float64(value), timestamp)
        measurements.append(measurement)
    
    return measurements


def publish_data(publisher: Publisher, metadata: List[MeasurementRecord]):
    """
    Continuously publishes measurement data.
    """
    print("Starting data publication...")
    
    measurement_count = 0
    
    try:
        while True:
            # Generate current timestamp
            timestamp = Ticks.utcnow()
            
            # Generate measurements
            measurements = generate_measurements(metadata, timestamp)
            
            # Publish measurements
            publisher.publish_measurements(measurements)
            
            measurement_count += len(measurements)
            
            if measurement_count % 70 == 0:  # Print every 10 cycles (7 measurements * 10)
                print(f"Published {measurement_count} measurements to {publisher.subscriber_count} subscribers")
            
            # Wait for next publication (10 Hz rate)
            time.sleep(0.1)
            
    except KeyboardInterrupt:
        print("\nStopping data publication...")


def main():
    parser = argparse.ArgumentParser(description="Simple STTP Publisher Example")
    parser.add_argument("--port", type=int, default=7175, help="Port to listen on (default: 7175)")
    args = parser.parse_args()

    if args.port < 1 or args.port > MAXPORT:
        print(f"Port number \"{args.port}\" is out of range: must be 1 to {MAXPORT}")
        sys.exit(2)

    # Create publisher
    publisher = Publisher(port=np.uint16(args.port))

    # Set up logging callbacks
    publisher.set_statusmessage_logger(lambda msg: print(f"STATUS: {msg}"))
    publisher.set_errormessage_logger(lambda msg: print(f"ERROR: {msg}"))
    publisher.set_clientconnected_logger(lambda client_id, address: print(f"Client connected: {address} ({client_id})"))
    publisher.set_clientdisconnected_logger(lambda client_id, address: print(f"Client disconnected: {address} ({client_id})"))

    try:
        # Create sample metadata
        metadata = create_sample_metadata()
        
        # Add metadata to publisher
        for record in metadata:
            publisher.add_measurement_metadata(record)
        
        print(f"Created {len(metadata)} measurement definitions")
        
        # Start the publisher
        error = publisher.start()
        if error:
            print(f"Failed to start publisher: {error}")
            sys.exit(1)
        
        print(f"Publisher started on port {args.port}")
        print("Press Ctrl+C to stop...")
        
        # Start publishing data in a separate thread
        publish_thread = threading.Thread(target=publish_data, args=(publisher, metadata), daemon=True)
        publish_thread.start()
        
        # Wait for user to stop
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\nShutting down...")
        
    finally:
        publisher.dispose()
        print("Publisher stopped")


if __name__ == "__main__":
    main()
