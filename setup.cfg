[metadata]
name = sttpapi
version = 0.6.4
author = <PERSON><PERSON>
author_email = <EMAIL>
description = Streaming Telemetry Transport Protocol API
long_description = file: README.md
long_description_content_type = text/markdown
keywords = STTP, IEEE 2664, streaming, telemetry, time-series, protocol, synchrophasor
license_file = LICENSE
url = https://github.com/sttp/pyapi
download_url = https://pypi.org/project/sttpapi/
project_urls =
    Bug Tracker = https://github.com/sttp/pyapi/issues
    Documentation = https://sttp.github.io/pyapi/
    Source Code = https://github.com/sttp/pyapi
classifiers =
    Development Status :: 5 - Production/Stable
    Intended Audience :: Science/Research
    Intended Audience :: Developers
    Topic :: Scientific/Engineering :: Interface Engine/Protocol Translator
    Programming Language :: Python :: 3.9
    License :: OSI Approved :: MIT License
    Operating System :: OS Independent

[options]
package_dir=
    =src
packages = find:
python_requires = >=3.9
install_requires =
    numpy>=1.22
    pycryptodome>=3.15.0
    python-dateutil>=2.8.2
    antlr4-python3-runtime>=4.13

[options.packages.find]
where=src