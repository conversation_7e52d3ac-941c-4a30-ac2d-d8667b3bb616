﻿# Generated from FilterExpressionSyntax.g4 by ANTLR 4.13.1
# encoding: utf-8
from antlr4 import *
from io import StringIO
import sys
if sys.version_info[1] > 5:
	from typing import TextIO
else:
	from typing.io import TextIO

def serializedATN():
    return [
        4,1,98,243,2,0,7,0,2,1,7,1,2,2,7,2,2,3,7,3,2,4,7,4,2,5,7,5,2,6,7,
        6,2,7,7,7,2,8,7,8,2,9,7,9,2,10,7,10,2,11,7,11,2,12,7,12,2,13,7,13,
        2,14,7,14,2,15,7,15,2,16,7,16,2,17,7,17,2,18,7,18,2,19,7,19,2,20,
        7,20,2,21,7,21,2,22,7,22,2,23,7,23,2,24,7,24,1,0,1,0,3,0,53,8,0,
        1,0,1,0,1,1,1,1,1,1,1,2,5,2,61,8,2,10,2,12,2,64,9,2,1,2,1,2,4,2,
        68,8,2,11,2,12,2,69,1,2,5,2,73,8,2,10,2,12,2,76,9,2,1,2,5,2,79,8,
        2,10,2,12,2,82,9,2,1,3,1,3,1,3,3,3,87,8,3,1,4,1,4,1,5,1,5,1,5,3,
        5,94,8,5,1,5,1,5,1,5,1,5,1,5,1,5,1,5,1,5,5,5,104,8,5,10,5,12,5,107,
        9,5,3,5,109,8,5,1,6,3,6,112,8,6,1,6,1,6,1,7,3,7,117,8,7,1,7,1,7,
        3,7,121,8,7,1,8,1,8,1,8,5,8,126,8,8,10,8,12,8,129,9,8,1,9,1,9,1,
        9,1,9,1,9,3,9,136,8,9,1,9,1,9,1,9,1,9,5,9,142,8,9,10,9,12,9,145,
        9,9,1,10,1,10,1,10,1,10,1,10,1,10,1,10,1,10,1,10,3,10,156,8,10,1,
        10,1,10,3,10,160,8,10,1,10,1,10,1,10,3,10,165,8,10,1,10,1,10,3,10,
        169,8,10,1,10,1,10,1,10,1,10,1,10,1,10,1,10,3,10,178,8,10,1,10,5,
        10,181,8,10,10,10,12,10,184,9,10,1,11,1,11,1,11,1,11,1,11,1,11,1,
        11,1,11,1,11,1,11,1,11,3,11,197,8,11,1,11,1,11,1,11,1,11,1,11,1,
        11,1,11,1,11,5,11,207,8,11,10,11,12,11,210,9,11,1,12,1,12,1,13,1,
        13,1,14,1,14,1,15,1,15,1,16,1,16,1,17,1,17,1,18,1,18,1,19,1,19,1,
        20,1,20,1,20,3,20,231,8,20,1,20,1,20,1,21,1,21,1,22,1,22,1,23,1,
        23,1,24,1,24,1,24,0,3,18,20,22,25,0,2,4,6,8,10,12,14,16,18,20,22,
        24,26,28,30,32,34,36,38,40,42,44,46,48,0,12,1,0,90,92,1,0,3,4,2,
        0,31,31,41,41,2,0,7,7,60,60,3,0,3,4,7,8,60,60,2,0,9,9,32,32,1,0,
        9,18,3,0,19,20,30,30,64,64,2,0,21,25,85,85,2,0,3,4,26,28,10,0,29,
        29,34,40,42,42,44,45,47,47,49,55,57,59,61,62,66,77,79,83,4,0,63,
        63,86,86,88,90,93,94,249,0,52,1,0,0,0,2,56,1,0,0,0,4,62,1,0,0,0,
        6,86,1,0,0,0,8,88,1,0,0,0,10,90,1,0,0,0,12,111,1,0,0,0,14,116,1,
        0,0,0,16,122,1,0,0,0,18,135,1,0,0,0,20,146,1,0,0,0,22,196,1,0,0,
        0,24,211,1,0,0,0,26,213,1,0,0,0,28,215,1,0,0,0,30,217,1,0,0,0,32,
        219,1,0,0,0,34,221,1,0,0,0,36,223,1,0,0,0,38,225,1,0,0,0,40,227,
        1,0,0,0,42,234,1,0,0,0,44,236,1,0,0,0,46,238,1,0,0,0,48,240,1,0,
        0,0,50,53,3,4,2,0,51,53,3,2,1,0,52,50,1,0,0,0,52,51,1,0,0,0,53,54,
        1,0,0,0,54,55,5,0,0,1,55,1,1,0,0,0,56,57,5,98,0,0,57,58,6,1,-1,0,
        58,3,1,0,0,0,59,61,5,1,0,0,60,59,1,0,0,0,61,64,1,0,0,0,62,60,1,0,
        0,0,62,63,1,0,0,0,63,65,1,0,0,0,64,62,1,0,0,0,65,74,3,6,3,0,66,68,
        5,1,0,0,67,66,1,0,0,0,68,69,1,0,0,0,69,67,1,0,0,0,69,70,1,0,0,0,
        70,71,1,0,0,0,71,73,3,6,3,0,72,67,1,0,0,0,73,76,1,0,0,0,74,72,1,
        0,0,0,74,75,1,0,0,0,75,80,1,0,0,0,76,74,1,0,0,0,77,79,5,1,0,0,78,
        77,1,0,0,0,79,82,1,0,0,0,80,78,1,0,0,0,80,81,1,0,0,0,81,5,1,0,0,
        0,82,80,1,0,0,0,83,87,3,8,4,0,84,87,3,10,5,0,85,87,3,18,9,0,86,83,
        1,0,0,0,86,84,1,0,0,0,86,85,1,0,0,0,87,7,1,0,0,0,88,89,7,0,0,0,89,
        9,1,0,0,0,90,93,5,43,0,0,91,92,5,78,0,0,92,94,3,12,6,0,93,91,1,0,
        0,0,93,94,1,0,0,0,94,95,1,0,0,0,95,96,3,44,22,0,96,97,5,84,0,0,97,
        108,3,18,9,0,98,99,5,65,0,0,99,100,5,33,0,0,100,105,3,14,7,0,101,
        102,5,2,0,0,102,104,3,14,7,0,103,101,1,0,0,0,104,107,1,0,0,0,105,
        103,1,0,0,0,105,106,1,0,0,0,106,109,1,0,0,0,107,105,1,0,0,0,108,
        98,1,0,0,0,108,109,1,0,0,0,109,11,1,0,0,0,110,112,7,1,0,0,111,110,
        1,0,0,0,111,112,1,0,0,0,112,113,1,0,0,0,113,114,5,88,0,0,114,13,
        1,0,0,0,115,117,3,28,14,0,116,115,1,0,0,0,116,117,1,0,0,0,117,118,
        1,0,0,0,118,120,3,48,24,0,119,121,7,2,0,0,120,119,1,0,0,0,120,121,
        1,0,0,0,121,15,1,0,0,0,122,127,3,18,9,0,123,124,5,2,0,0,124,126,
        3,18,9,0,125,123,1,0,0,0,126,129,1,0,0,0,127,125,1,0,0,0,127,128,
        1,0,0,0,128,17,1,0,0,0,129,127,1,0,0,0,130,131,6,9,-1,0,131,132,
        3,24,12,0,132,133,3,18,9,3,133,136,1,0,0,0,134,136,3,20,10,0,135,
        130,1,0,0,0,135,134,1,0,0,0,136,143,1,0,0,0,137,138,10,2,0,0,138,
        139,3,32,16,0,139,140,3,18,9,3,140,142,1,0,0,0,141,137,1,0,0,0,142,
        145,1,0,0,0,143,141,1,0,0,0,143,144,1,0,0,0,144,19,1,0,0,0,145,143,
        1,0,0,0,146,147,6,10,-1,0,147,148,3,22,11,0,148,182,1,0,0,0,149,
        150,10,3,0,0,150,151,3,30,15,0,151,152,3,20,10,4,152,181,1,0,0,0,
        153,155,10,2,0,0,154,156,3,24,12,0,155,154,1,0,0,0,155,156,1,0,0,
        0,156,157,1,0,0,0,157,159,5,56,0,0,158,160,3,28,14,0,159,158,1,0,
        0,0,159,160,1,0,0,0,160,161,1,0,0,0,161,181,3,20,10,3,162,164,10,
        5,0,0,163,165,3,24,12,0,164,163,1,0,0,0,164,165,1,0,0,0,165,166,
        1,0,0,0,166,168,5,46,0,0,167,169,3,28,14,0,168,167,1,0,0,0,168,169,
        1,0,0,0,169,170,1,0,0,0,170,171,5,5,0,0,171,172,3,16,8,0,172,173,
        5,6,0,0,173,181,1,0,0,0,174,175,10,4,0,0,175,177,5,48,0,0,176,178,
        3,24,12,0,177,176,1,0,0,0,177,178,1,0,0,0,178,179,1,0,0,0,179,181,
        5,63,0,0,180,149,1,0,0,0,180,153,1,0,0,0,180,162,1,0,0,0,180,174,
        1,0,0,0,181,184,1,0,0,0,182,180,1,0,0,0,182,183,1,0,0,0,183,21,1,
        0,0,0,184,182,1,0,0,0,185,186,6,11,-1,0,186,197,3,42,21,0,187,197,
        3,46,23,0,188,197,3,40,20,0,189,190,3,26,13,0,190,191,3,22,11,4,
        191,197,1,0,0,0,192,193,5,5,0,0,193,194,3,18,9,0,194,195,5,6,0,0,
        195,197,1,0,0,0,196,185,1,0,0,0,196,187,1,0,0,0,196,188,1,0,0,0,
        196,189,1,0,0,0,196,192,1,0,0,0,197,208,1,0,0,0,198,199,10,2,0,0,
        199,200,3,36,18,0,200,201,3,22,11,3,201,207,1,0,0,0,202,203,10,1,
        0,0,203,204,3,34,17,0,204,205,3,22,11,2,205,207,1,0,0,0,206,198,
        1,0,0,0,206,202,1,0,0,0,207,210,1,0,0,0,208,206,1,0,0,0,208,209,
        1,0,0,0,209,23,1,0,0,0,210,208,1,0,0,0,211,212,7,3,0,0,212,25,1,
        0,0,0,213,214,7,4,0,0,214,27,1,0,0,0,215,216,7,5,0,0,216,29,1,0,
        0,0,217,218,7,6,0,0,218,31,1,0,0,0,219,220,7,7,0,0,220,33,1,0,0,
        0,221,222,7,8,0,0,222,35,1,0,0,0,223,224,7,9,0,0,224,37,1,0,0,0,
        225,226,7,10,0,0,226,39,1,0,0,0,227,228,3,38,19,0,228,230,5,5,0,
        0,229,231,3,16,8,0,230,229,1,0,0,0,230,231,1,0,0,0,231,232,1,0,0,
        0,232,233,5,6,0,0,233,41,1,0,0,0,234,235,7,11,0,0,235,43,1,0,0,0,
        236,237,5,87,0,0,237,45,1,0,0,0,238,239,5,87,0,0,239,47,1,0,0,0,
        240,241,5,87,0,0,241,49,1,0,0,0,26,52,62,69,74,80,86,93,105,108,
        111,116,120,127,135,143,155,159,164,168,177,180,182,196,206,208,
        230
    ]

class FilterExpressionSyntaxParser ( Parser ):

    grammarFileName = "FilterExpressionSyntax.g4"

    atn = ATNDeserializer().deserialize(serializedATN())

    decisionsToDFA = [ DFA(ds, i) for i, ds in enumerate(atn.decisionToState) ]

    sharedContextCache = PredictionContextCache()

    literalNames = [ "<INVALID>", "';'", "','", "'-'", "'+'", "'('", "')'", 
                     "'!'", "'~'", "'==='", "'<'", "'<='", "'>'", "'>='", 
                     "'='", "'=='", "'!='", "'!=='", "'<>'", "'&&'", "'||'", 
                     "'<<'", "'>>'", "'&'", "'|'", "'^'", "'*'", "'/'", 
                     "'%'" ]

    symbolicNames = [ "<INVALID>", "<INVALID>", "<INVALID>", "<INVALID>", 
                      "<INVALID>", "<INVALID>", "<INVALID>", "<INVALID>", 
                      "<INVALID>", "<INVALID>", "<INVALID>", "<INVALID>", 
                      "<INVALID>", "<INVALID>", "<INVALID>", "<INVALID>", 
                      "<INVALID>", "<INVALID>", "<INVALID>", "<INVALID>", 
                      "<INVALID>", "<INVALID>", "<INVALID>", "<INVALID>", 
                      "<INVALID>", "<INVALID>", "<INVALID>", "<INVALID>", 
                      "<INVALID>", "K_ABS", "K_AND", "K_ASC", "K_BINARY", 
                      "K_BY", "K_CEILING", "K_COALESCE", "K_CONVERT", "K_CONTAINS", 
                      "K_DATEADD", "K_DATEDIFF", "K_DATEPART", "K_DESC", 
                      "K_ENDSWITH", "K_FILTER", "K_FLOOR", "K_IIF", "K_IN", 
                      "K_INDEXOF", "K_IS", "K_ISDATE", "K_ISINTEGER", "K_ISGUID", 
                      "K_ISNULL", "K_ISNUMERIC", "K_LASTINDEXOF", "K_LEN", 
                      "K_LIKE", "K_LOWER", "K_MAXOF", "K_MINOF", "K_NOT", 
                      "K_NOW", "K_NTHINDEXOF", "K_NULL", "K_OR", "K_ORDER", 
                      "K_POWER", "K_REGEXMATCH", "K_REGEXVAL", "K_REPLACE", 
                      "K_REVERSE", "K_ROUND", "K_SQRT", "K_SPLIT", "K_STARTSWITH", 
                      "K_STRCOUNT", "K_STRCMP", "K_SUBSTR", "K_TOP", "K_TRIM", 
                      "K_TRIMLEFT", "K_TRIMRIGHT", "K_UPPER", "K_UTCNOW", 
                      "K_WHERE", "K_XOR", "BOOLEAN_LITERAL", "IDENTIFIER", 
                      "INTEGER_LITERAL", "NUMERIC_LITERAL", "GUID_LITERAL", 
                      "MEASUREMENT_KEY_LITERAL", "POINT_TAG_LITERAL", "STRING_LITERAL", 
                      "DATETIME_LITERAL", "SINGLE_LINE_COMMENT", "MULTILINE_COMMENT", 
                      "SPACES", "UNEXPECTED_CHAR" ]

    RULE_parse = 0
    RULE_err = 1
    RULE_filterExpressionStatementList = 2
    RULE_filterExpressionStatement = 3
    RULE_identifierStatement = 4
    RULE_filterStatement = 5
    RULE_topLimit = 6
    RULE_orderingTerm = 7
    RULE_expressionList = 8
    RULE_expression = 9
    RULE_predicateExpression = 10
    RULE_valueExpression = 11
    RULE_notOperator = 12
    RULE_unaryOperator = 13
    RULE_exactMatchModifier = 14
    RULE_comparisonOperator = 15
    RULE_logicalOperator = 16
    RULE_bitwiseOperator = 17
    RULE_mathOperator = 18
    RULE_functionName = 19
    RULE_functionExpression = 20
    RULE_literalValue = 21
    RULE_tableName = 22
    RULE_columnName = 23
    RULE_orderByColumnName = 24

    ruleNames =  [ "parse", "err", "filterExpressionStatementList", "filterExpressionStatement", 
                   "identifierStatement", "filterStatement", "topLimit", 
                   "orderingTerm", "expressionList", "expression", "predicateExpression", 
                   "valueExpression", "notOperator", "unaryOperator", "exactMatchModifier", 
                   "comparisonOperator", "logicalOperator", "bitwiseOperator", 
                   "mathOperator", "functionName", "functionExpression", 
                   "literalValue", "tableName", "columnName", "orderByColumnName" ]

    EOF = Token.EOF
    T__0=1
    T__1=2
    T__2=3
    T__3=4
    T__4=5
    T__5=6
    T__6=7
    T__7=8
    T__8=9
    T__9=10
    T__10=11
    T__11=12
    T__12=13
    T__13=14
    T__14=15
    T__15=16
    T__16=17
    T__17=18
    T__18=19
    T__19=20
    T__20=21
    T__21=22
    T__22=23
    T__23=24
    T__24=25
    T__25=26
    T__26=27
    T__27=28
    K_ABS=29
    K_AND=30
    K_ASC=31
    K_BINARY=32
    K_BY=33
    K_CEILING=34
    K_COALESCE=35
    K_CONVERT=36
    K_CONTAINS=37
    K_DATEADD=38
    K_DATEDIFF=39
    K_DATEPART=40
    K_DESC=41
    K_ENDSWITH=42
    K_FILTER=43
    K_FLOOR=44
    K_IIF=45
    K_IN=46
    K_INDEXOF=47
    K_IS=48
    K_ISDATE=49
    K_ISINTEGER=50
    K_ISGUID=51
    K_ISNULL=52
    K_ISNUMERIC=53
    K_LASTINDEXOF=54
    K_LEN=55
    K_LIKE=56
    K_LOWER=57
    K_MAXOF=58
    K_MINOF=59
    K_NOT=60
    K_NOW=61
    K_NTHINDEXOF=62
    K_NULL=63
    K_OR=64
    K_ORDER=65
    K_POWER=66
    K_REGEXMATCH=67
    K_REGEXVAL=68
    K_REPLACE=69
    K_REVERSE=70
    K_ROUND=71
    K_SQRT=72
    K_SPLIT=73
    K_STARTSWITH=74
    K_STRCOUNT=75
    K_STRCMP=76
    K_SUBSTR=77
    K_TOP=78
    K_TRIM=79
    K_TRIMLEFT=80
    K_TRIMRIGHT=81
    K_UPPER=82
    K_UTCNOW=83
    K_WHERE=84
    K_XOR=85
    BOOLEAN_LITERAL=86
    IDENTIFIER=87
    INTEGER_LITERAL=88
    NUMERIC_LITERAL=89
    GUID_LITERAL=90
    MEASUREMENT_KEY_LITERAL=91
    POINT_TAG_LITERAL=92
    STRING_LITERAL=93
    DATETIME_LITERAL=94
    SINGLE_LINE_COMMENT=95
    MULTILINE_COMMENT=96
    SPACES=97
    UNEXPECTED_CHAR=98

    def __init__(self, input:TokenStream, output:TextIO = sys.stdout):
        super().__init__(input, output)
        self.checkVersion("4.13.1")
        self._interp = ParserATNSimulator(self, self.atn, self.decisionsToDFA, self.sharedContextCache)
        self._predicates = None




    class ParseContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def EOF(self):
            return self.getToken(FilterExpressionSyntaxParser.EOF, 0)

        def filterExpressionStatementList(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.FilterExpressionStatementListContext,0)


        def err(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.ErrContext,0)


        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_parse

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterParse" ):
                listener.enterParse(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitParse" ):
                listener.exitParse(self)




    def parse(self):

        localctx = FilterExpressionSyntaxParser.ParseContext(self, self._ctx, self.state)
        self.enterRule(localctx, 0, self.RULE_parse)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 52
            self._errHandler.sync(self)
            token = self._input.LA(1)
            if token in [1, 3, 4, 5, 7, 8, 29, 34, 35, 36, 37, 38, 39, 40, 42, 43, 44, 45, 47, 49, 50, 51, 52, 53, 54, 55, 57, 58, 59, 60, 61, 62, 63, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 79, 80, 81, 82, 83, 86, 87, 88, 89, 90, 91, 92, 93, 94]:
                self.state = 50
                self.filterExpressionStatementList()
                pass
            elif token in [98]:
                self.state = 51
                self.err()
                pass
            else:
                raise NoViableAltException(self)

            self.state = 54
            self.match(FilterExpressionSyntaxParser.EOF)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class ErrContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser
            self._UNEXPECTED_CHAR = None # Token

        def UNEXPECTED_CHAR(self):
            return self.getToken(FilterExpressionSyntaxParser.UNEXPECTED_CHAR, 0)

        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_err

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterErr" ):
                listener.enterErr(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitErr" ):
                listener.exitErr(self)




    def err(self):

        localctx = FilterExpressionSyntaxParser.ErrContext(self, self._ctx, self.state)
        self.enterRule(localctx, 2, self.RULE_err)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 56
            localctx._UNEXPECTED_CHAR = self.match(FilterExpressionSyntaxParser.UNEXPECTED_CHAR)

            raise RuntimeError("Unexpected character: " + (None if localctx._UNEXPECTED_CHAR is None else localctx._UNEXPECTED_CHAR.text));
               
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class FilterExpressionStatementListContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def filterExpressionStatement(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(FilterExpressionSyntaxParser.FilterExpressionStatementContext)
            else:
                return self.getTypedRuleContext(FilterExpressionSyntaxParser.FilterExpressionStatementContext,i)


        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_filterExpressionStatementList

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterFilterExpressionStatementList" ):
                listener.enterFilterExpressionStatementList(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitFilterExpressionStatementList" ):
                listener.exitFilterExpressionStatementList(self)




    def filterExpressionStatementList(self):

        localctx = FilterExpressionSyntaxParser.FilterExpressionStatementListContext(self, self._ctx, self.state)
        self.enterRule(localctx, 4, self.RULE_filterExpressionStatementList)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 62
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==1:
                self.state = 59
                self.match(FilterExpressionSyntaxParser.T__0)
                self.state = 64
                self._errHandler.sync(self)
                _la = self._input.LA(1)

            self.state = 65
            self.filterExpressionStatement()
            self.state = 74
            self._errHandler.sync(self)
            _alt = self._interp.adaptivePredict(self._input,3,self._ctx)
            while _alt!=2 and _alt!=ATN.INVALID_ALT_NUMBER:
                if _alt==1:
                    self.state = 67 
                    self._errHandler.sync(self)
                    _la = self._input.LA(1)
                    while True:
                        self.state = 66
                        self.match(FilterExpressionSyntaxParser.T__0)
                        self.state = 69 
                        self._errHandler.sync(self)
                        _la = self._input.LA(1)
                        if not (_la==1):
                            break

                    self.state = 71
                    self.filterExpressionStatement() 
                self.state = 76
                self._errHandler.sync(self)
                _alt = self._interp.adaptivePredict(self._input,3,self._ctx)

            self.state = 80
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==1:
                self.state = 77
                self.match(FilterExpressionSyntaxParser.T__0)
                self.state = 82
                self._errHandler.sync(self)
                _la = self._input.LA(1)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class FilterExpressionStatementContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def identifierStatement(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.IdentifierStatementContext,0)


        def filterStatement(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.FilterStatementContext,0)


        def expression(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.ExpressionContext,0)


        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_filterExpressionStatement

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterFilterExpressionStatement" ):
                listener.enterFilterExpressionStatement(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitFilterExpressionStatement" ):
                listener.exitFilterExpressionStatement(self)




    def filterExpressionStatement(self):

        localctx = FilterExpressionSyntaxParser.FilterExpressionStatementContext(self, self._ctx, self.state)
        self.enterRule(localctx, 6, self.RULE_filterExpressionStatement)
        try:
            self.state = 86
            self._errHandler.sync(self)
            la_ = self._interp.adaptivePredict(self._input,5,self._ctx)
            if la_ == 1:
                self.enterOuterAlt(localctx, 1)
                self.state = 83
                self.identifierStatement()
                pass

            elif la_ == 2:
                self.enterOuterAlt(localctx, 2)
                self.state = 84
                self.filterStatement()
                pass

            elif la_ == 3:
                self.enterOuterAlt(localctx, 3)
                self.state = 85
                self.expression(0)
                pass


        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class IdentifierStatementContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def GUID_LITERAL(self):
            return self.getToken(FilterExpressionSyntaxParser.GUID_LITERAL, 0)

        def MEASUREMENT_KEY_LITERAL(self):
            return self.getToken(FilterExpressionSyntaxParser.MEASUREMENT_KEY_LITERAL, 0)

        def POINT_TAG_LITERAL(self):
            return self.getToken(FilterExpressionSyntaxParser.POINT_TAG_LITERAL, 0)

        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_identifierStatement

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterIdentifierStatement" ):
                listener.enterIdentifierStatement(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitIdentifierStatement" ):
                listener.exitIdentifierStatement(self)




    def identifierStatement(self):

        localctx = FilterExpressionSyntaxParser.IdentifierStatementContext(self, self._ctx, self.state)
        self.enterRule(localctx, 8, self.RULE_identifierStatement)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 88
            _la = self._input.LA(1)
            if not(((((_la - 90)) & ~0x3f) == 0 and ((1 << (_la - 90)) & 7) != 0)):
                self._errHandler.recoverInline(self)
            else:
                self._errHandler.reportMatch(self)
                self.consume()
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class FilterStatementContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def K_FILTER(self):
            return self.getToken(FilterExpressionSyntaxParser.K_FILTER, 0)

        def tableName(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.TableNameContext,0)


        def K_WHERE(self):
            return self.getToken(FilterExpressionSyntaxParser.K_WHERE, 0)

        def expression(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.ExpressionContext,0)


        def K_TOP(self):
            return self.getToken(FilterExpressionSyntaxParser.K_TOP, 0)

        def topLimit(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.TopLimitContext,0)


        def K_ORDER(self):
            return self.getToken(FilterExpressionSyntaxParser.K_ORDER, 0)

        def K_BY(self):
            return self.getToken(FilterExpressionSyntaxParser.K_BY, 0)

        def orderingTerm(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(FilterExpressionSyntaxParser.OrderingTermContext)
            else:
                return self.getTypedRuleContext(FilterExpressionSyntaxParser.OrderingTermContext,i)


        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_filterStatement

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterFilterStatement" ):
                listener.enterFilterStatement(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitFilterStatement" ):
                listener.exitFilterStatement(self)




    def filterStatement(self):

        localctx = FilterExpressionSyntaxParser.FilterStatementContext(self, self._ctx, self.state)
        self.enterRule(localctx, 10, self.RULE_filterStatement)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 90
            self.match(FilterExpressionSyntaxParser.K_FILTER)
            self.state = 93
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if _la==78:
                self.state = 91
                self.match(FilterExpressionSyntaxParser.K_TOP)
                self.state = 92
                self.topLimit()


            self.state = 95
            self.tableName()
            self.state = 96
            self.match(FilterExpressionSyntaxParser.K_WHERE)
            self.state = 97
            self.expression(0)
            self.state = 108
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if _la==65:
                self.state = 98
                self.match(FilterExpressionSyntaxParser.K_ORDER)
                self.state = 99
                self.match(FilterExpressionSyntaxParser.K_BY)
                self.state = 100
                self.orderingTerm()
                self.state = 105
                self._errHandler.sync(self)
                _la = self._input.LA(1)
                while _la==2:
                    self.state = 101
                    self.match(FilterExpressionSyntaxParser.T__1)
                    self.state = 102
                    self.orderingTerm()
                    self.state = 107
                    self._errHandler.sync(self)
                    _la = self._input.LA(1)



        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class TopLimitContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def INTEGER_LITERAL(self):
            return self.getToken(FilterExpressionSyntaxParser.INTEGER_LITERAL, 0)

        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_topLimit

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterTopLimit" ):
                listener.enterTopLimit(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitTopLimit" ):
                listener.exitTopLimit(self)




    def topLimit(self):

        localctx = FilterExpressionSyntaxParser.TopLimitContext(self, self._ctx, self.state)
        self.enterRule(localctx, 12, self.RULE_topLimit)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 111
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if _la==3 or _la==4:
                self.state = 110
                _la = self._input.LA(1)
                if not(_la==3 or _la==4):
                    self._errHandler.recoverInline(self)
                else:
                    self._errHandler.reportMatch(self)
                    self.consume()


            self.state = 113
            self.match(FilterExpressionSyntaxParser.INTEGER_LITERAL)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class OrderingTermContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def orderByColumnName(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.OrderByColumnNameContext,0)


        def exactMatchModifier(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.ExactMatchModifierContext,0)


        def K_ASC(self):
            return self.getToken(FilterExpressionSyntaxParser.K_ASC, 0)

        def K_DESC(self):
            return self.getToken(FilterExpressionSyntaxParser.K_DESC, 0)

        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_orderingTerm

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterOrderingTerm" ):
                listener.enterOrderingTerm(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitOrderingTerm" ):
                listener.exitOrderingTerm(self)




    def orderingTerm(self):

        localctx = FilterExpressionSyntaxParser.OrderingTermContext(self, self._ctx, self.state)
        self.enterRule(localctx, 14, self.RULE_orderingTerm)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 116
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if _la==9 or _la==32:
                self.state = 115
                self.exactMatchModifier()


            self.state = 118
            self.orderByColumnName()
            self.state = 120
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if _la==31 or _la==41:
                self.state = 119
                _la = self._input.LA(1)
                if not(_la==31 or _la==41):
                    self._errHandler.recoverInline(self)
                else:
                    self._errHandler.reportMatch(self)
                    self.consume()


        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class ExpressionListContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def expression(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(FilterExpressionSyntaxParser.ExpressionContext)
            else:
                return self.getTypedRuleContext(FilterExpressionSyntaxParser.ExpressionContext,i)


        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_expressionList

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterExpressionList" ):
                listener.enterExpressionList(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitExpressionList" ):
                listener.exitExpressionList(self)




    def expressionList(self):

        localctx = FilterExpressionSyntaxParser.ExpressionListContext(self, self._ctx, self.state)
        self.enterRule(localctx, 16, self.RULE_expressionList)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 122
            self.expression(0)
            self.state = 127
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==2:
                self.state = 123
                self.match(FilterExpressionSyntaxParser.T__1)
                self.state = 124
                self.expression(0)
                self.state = 129
                self._errHandler.sync(self)
                _la = self._input.LA(1)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class ExpressionContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def notOperator(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.NotOperatorContext,0)


        def expression(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(FilterExpressionSyntaxParser.ExpressionContext)
            else:
                return self.getTypedRuleContext(FilterExpressionSyntaxParser.ExpressionContext,i)


        def predicateExpression(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.PredicateExpressionContext,0)


        def logicalOperator(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.LogicalOperatorContext,0)


        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_expression

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterExpression" ):
                listener.enterExpression(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitExpression" ):
                listener.exitExpression(self)



    def expression(self, _p:int=0):
        _parentctx = self._ctx
        _parentState = self.state
        localctx = FilterExpressionSyntaxParser.ExpressionContext(self, self._ctx, _parentState)
        _prevctx = localctx
        _startState = 18
        self.enterRecursionRule(localctx, 18, self.RULE_expression, _p)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 135
            self._errHandler.sync(self)
            la_ = self._interp.adaptivePredict(self._input,13,self._ctx)
            if la_ == 1:
                self.state = 131
                self.notOperator()
                self.state = 132
                self.expression(3)
                pass

            elif la_ == 2:
                self.state = 134
                self.predicateExpression(0)
                pass


            self._ctx.stop = self._input.LT(-1)
            self.state = 143
            self._errHandler.sync(self)
            _alt = self._interp.adaptivePredict(self._input,14,self._ctx)
            while _alt!=2 and _alt!=ATN.INVALID_ALT_NUMBER:
                if _alt==1:
                    if self._parseListeners is not None:
                        self.triggerExitRuleEvent()
                    _prevctx = localctx
                    localctx = FilterExpressionSyntaxParser.ExpressionContext(self, _parentctx, _parentState)
                    self.pushNewRecursionContext(localctx, _startState, self.RULE_expression)
                    self.state = 137
                    if not self.precpred(self._ctx, 2):
                        from antlr4.error.Errors import FailedPredicateException
                        raise FailedPredicateException(self, "self.precpred(self._ctx, 2)")
                    self.state = 138
                    self.logicalOperator()
                    self.state = 139
                    self.expression(3) 
                self.state = 145
                self._errHandler.sync(self)
                _alt = self._interp.adaptivePredict(self._input,14,self._ctx)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.unrollRecursionContexts(_parentctx)
        return localctx


    class PredicateExpressionContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def valueExpression(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.ValueExpressionContext,0)


        def predicateExpression(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(FilterExpressionSyntaxParser.PredicateExpressionContext)
            else:
                return self.getTypedRuleContext(FilterExpressionSyntaxParser.PredicateExpressionContext,i)


        def comparisonOperator(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.ComparisonOperatorContext,0)


        def K_LIKE(self):
            return self.getToken(FilterExpressionSyntaxParser.K_LIKE, 0)

        def notOperator(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.NotOperatorContext,0)


        def exactMatchModifier(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.ExactMatchModifierContext,0)


        def K_IN(self):
            return self.getToken(FilterExpressionSyntaxParser.K_IN, 0)

        def expressionList(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.ExpressionListContext,0)


        def K_IS(self):
            return self.getToken(FilterExpressionSyntaxParser.K_IS, 0)

        def K_NULL(self):
            return self.getToken(FilterExpressionSyntaxParser.K_NULL, 0)

        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_predicateExpression

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterPredicateExpression" ):
                listener.enterPredicateExpression(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitPredicateExpression" ):
                listener.exitPredicateExpression(self)



    def predicateExpression(self, _p:int=0):
        _parentctx = self._ctx
        _parentState = self.state
        localctx = FilterExpressionSyntaxParser.PredicateExpressionContext(self, self._ctx, _parentState)
        _prevctx = localctx
        _startState = 20
        self.enterRecursionRule(localctx, 20, self.RULE_predicateExpression, _p)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 147
            self.valueExpression(0)
            self._ctx.stop = self._input.LT(-1)
            self.state = 182
            self._errHandler.sync(self)
            _alt = self._interp.adaptivePredict(self._input,21,self._ctx)
            while _alt!=2 and _alt!=ATN.INVALID_ALT_NUMBER:
                if _alt==1:
                    if self._parseListeners is not None:
                        self.triggerExitRuleEvent()
                    _prevctx = localctx
                    self.state = 180
                    self._errHandler.sync(self)
                    la_ = self._interp.adaptivePredict(self._input,20,self._ctx)
                    if la_ == 1:
                        localctx = FilterExpressionSyntaxParser.PredicateExpressionContext(self, _parentctx, _parentState)
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_predicateExpression)
                        self.state = 149
                        if not self.precpred(self._ctx, 3):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 3)")
                        self.state = 150
                        self.comparisonOperator()
                        self.state = 151
                        self.predicateExpression(4)
                        pass

                    elif la_ == 2:
                        localctx = FilterExpressionSyntaxParser.PredicateExpressionContext(self, _parentctx, _parentState)
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_predicateExpression)
                        self.state = 153
                        if not self.precpred(self._ctx, 2):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 2)")
                        self.state = 155
                        self._errHandler.sync(self)
                        _la = self._input.LA(1)
                        if _la==7 or _la==60:
                            self.state = 154
                            self.notOperator()


                        self.state = 157
                        self.match(FilterExpressionSyntaxParser.K_LIKE)
                        self.state = 159
                        self._errHandler.sync(self)
                        _la = self._input.LA(1)
                        if _la==9 or _la==32:
                            self.state = 158
                            self.exactMatchModifier()


                        self.state = 161
                        self.predicateExpression(3)
                        pass

                    elif la_ == 3:
                        localctx = FilterExpressionSyntaxParser.PredicateExpressionContext(self, _parentctx, _parentState)
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_predicateExpression)
                        self.state = 162
                        if not self.precpred(self._ctx, 5):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 5)")
                        self.state = 164
                        self._errHandler.sync(self)
                        _la = self._input.LA(1)
                        if _la==7 or _la==60:
                            self.state = 163
                            self.notOperator()


                        self.state = 166
                        self.match(FilterExpressionSyntaxParser.K_IN)
                        self.state = 168
                        self._errHandler.sync(self)
                        _la = self._input.LA(1)
                        if _la==9 or _la==32:
                            self.state = 167
                            self.exactMatchModifier()


                        self.state = 170
                        self.match(FilterExpressionSyntaxParser.T__4)
                        self.state = 171
                        self.expressionList()
                        self.state = 172
                        self.match(FilterExpressionSyntaxParser.T__5)
                        pass

                    elif la_ == 4:
                        localctx = FilterExpressionSyntaxParser.PredicateExpressionContext(self, _parentctx, _parentState)
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_predicateExpression)
                        self.state = 174
                        if not self.precpred(self._ctx, 4):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 4)")
                        self.state = 175
                        self.match(FilterExpressionSyntaxParser.K_IS)
                        self.state = 177
                        self._errHandler.sync(self)
                        _la = self._input.LA(1)
                        if _la==7 or _la==60:
                            self.state = 176
                            self.notOperator()


                        self.state = 179
                        self.match(FilterExpressionSyntaxParser.K_NULL)
                        pass

             
                self.state = 184
                self._errHandler.sync(self)
                _alt = self._interp.adaptivePredict(self._input,21,self._ctx)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.unrollRecursionContexts(_parentctx)
        return localctx


    class ValueExpressionContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def literalValue(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.LiteralValueContext,0)


        def columnName(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.ColumnNameContext,0)


        def functionExpression(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.FunctionExpressionContext,0)


        def unaryOperator(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.UnaryOperatorContext,0)


        def valueExpression(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(FilterExpressionSyntaxParser.ValueExpressionContext)
            else:
                return self.getTypedRuleContext(FilterExpressionSyntaxParser.ValueExpressionContext,i)


        def expression(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.ExpressionContext,0)


        def mathOperator(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.MathOperatorContext,0)


        def bitwiseOperator(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.BitwiseOperatorContext,0)


        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_valueExpression

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterValueExpression" ):
                listener.enterValueExpression(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitValueExpression" ):
                listener.exitValueExpression(self)



    def valueExpression(self, _p:int=0):
        _parentctx = self._ctx
        _parentState = self.state
        localctx = FilterExpressionSyntaxParser.ValueExpressionContext(self, self._ctx, _parentState)
        _prevctx = localctx
        _startState = 22
        self.enterRecursionRule(localctx, 22, self.RULE_valueExpression, _p)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 196
            self._errHandler.sync(self)
            token = self._input.LA(1)
            if token in [63, 86, 88, 89, 90, 93, 94]:
                self.state = 186
                self.literalValue()
                pass
            elif token in [87]:
                self.state = 187
                self.columnName()
                pass
            elif token in [29, 34, 35, 36, 37, 38, 39, 40, 42, 44, 45, 47, 49, 50, 51, 52, 53, 54, 55, 57, 58, 59, 61, 62, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 79, 80, 81, 82, 83]:
                self.state = 188
                self.functionExpression()
                pass
            elif token in [3, 4, 7, 8, 60]:
                self.state = 189
                self.unaryOperator()
                self.state = 190
                self.valueExpression(4)
                pass
            elif token in [5]:
                self.state = 192
                self.match(FilterExpressionSyntaxParser.T__4)
                self.state = 193
                self.expression(0)
                self.state = 194
                self.match(FilterExpressionSyntaxParser.T__5)
                pass
            else:
                raise NoViableAltException(self)

            self._ctx.stop = self._input.LT(-1)
            self.state = 208
            self._errHandler.sync(self)
            _alt = self._interp.adaptivePredict(self._input,24,self._ctx)
            while _alt!=2 and _alt!=ATN.INVALID_ALT_NUMBER:
                if _alt==1:
                    if self._parseListeners is not None:
                        self.triggerExitRuleEvent()
                    _prevctx = localctx
                    self.state = 206
                    self._errHandler.sync(self)
                    la_ = self._interp.adaptivePredict(self._input,23,self._ctx)
                    if la_ == 1:
                        localctx = FilterExpressionSyntaxParser.ValueExpressionContext(self, _parentctx, _parentState)
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_valueExpression)
                        self.state = 198
                        if not self.precpred(self._ctx, 2):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 2)")
                        self.state = 199
                        self.mathOperator()
                        self.state = 200
                        self.valueExpression(3)
                        pass

                    elif la_ == 2:
                        localctx = FilterExpressionSyntaxParser.ValueExpressionContext(self, _parentctx, _parentState)
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_valueExpression)
                        self.state = 202
                        if not self.precpred(self._ctx, 1):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 1)")
                        self.state = 203
                        self.bitwiseOperator()
                        self.state = 204
                        self.valueExpression(2)
                        pass

             
                self.state = 210
                self._errHandler.sync(self)
                _alt = self._interp.adaptivePredict(self._input,24,self._ctx)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.unrollRecursionContexts(_parentctx)
        return localctx


    class NotOperatorContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def K_NOT(self):
            return self.getToken(FilterExpressionSyntaxParser.K_NOT, 0)

        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_notOperator

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterNotOperator" ):
                listener.enterNotOperator(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitNotOperator" ):
                listener.exitNotOperator(self)




    def notOperator(self):

        localctx = FilterExpressionSyntaxParser.NotOperatorContext(self, self._ctx, self.state)
        self.enterRule(localctx, 24, self.RULE_notOperator)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 211
            _la = self._input.LA(1)
            if not(_la==7 or _la==60):
                self._errHandler.recoverInline(self)
            else:
                self._errHandler.reportMatch(self)
                self.consume()
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class UnaryOperatorContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def K_NOT(self):
            return self.getToken(FilterExpressionSyntaxParser.K_NOT, 0)

        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_unaryOperator

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterUnaryOperator" ):
                listener.enterUnaryOperator(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitUnaryOperator" ):
                listener.exitUnaryOperator(self)




    def unaryOperator(self):

        localctx = FilterExpressionSyntaxParser.UnaryOperatorContext(self, self._ctx, self.state)
        self.enterRule(localctx, 26, self.RULE_unaryOperator)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 213
            _la = self._input.LA(1)
            if not((((_la) & ~0x3f) == 0 and ((1 << _la) & 1152921504606847384) != 0)):
                self._errHandler.recoverInline(self)
            else:
                self._errHandler.reportMatch(self)
                self.consume()
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class ExactMatchModifierContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def K_BINARY(self):
            return self.getToken(FilterExpressionSyntaxParser.K_BINARY, 0)

        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_exactMatchModifier

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterExactMatchModifier" ):
                listener.enterExactMatchModifier(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitExactMatchModifier" ):
                listener.exitExactMatchModifier(self)




    def exactMatchModifier(self):

        localctx = FilterExpressionSyntaxParser.ExactMatchModifierContext(self, self._ctx, self.state)
        self.enterRule(localctx, 28, self.RULE_exactMatchModifier)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 215
            _la = self._input.LA(1)
            if not(_la==9 or _la==32):
                self._errHandler.recoverInline(self)
            else:
                self._errHandler.reportMatch(self)
                self.consume()
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class ComparisonOperatorContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser


        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_comparisonOperator

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterComparisonOperator" ):
                listener.enterComparisonOperator(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitComparisonOperator" ):
                listener.exitComparisonOperator(self)




    def comparisonOperator(self):

        localctx = FilterExpressionSyntaxParser.ComparisonOperatorContext(self, self._ctx, self.state)
        self.enterRule(localctx, 30, self.RULE_comparisonOperator)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 217
            _la = self._input.LA(1)
            if not((((_la) & ~0x3f) == 0 and ((1 << _la) & 523776) != 0)):
                self._errHandler.recoverInline(self)
            else:
                self._errHandler.reportMatch(self)
                self.consume()
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class LogicalOperatorContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def K_AND(self):
            return self.getToken(FilterExpressionSyntaxParser.K_AND, 0)

        def K_OR(self):
            return self.getToken(FilterExpressionSyntaxParser.K_OR, 0)

        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_logicalOperator

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterLogicalOperator" ):
                listener.enterLogicalOperator(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitLogicalOperator" ):
                listener.exitLogicalOperator(self)




    def logicalOperator(self):

        localctx = FilterExpressionSyntaxParser.LogicalOperatorContext(self, self._ctx, self.state)
        self.enterRule(localctx, 32, self.RULE_logicalOperator)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 219
            _la = self._input.LA(1)
            if not(((((_la - 19)) & ~0x3f) == 0 and ((1 << (_la - 19)) & 35184372090883) != 0)):
                self._errHandler.recoverInline(self)
            else:
                self._errHandler.reportMatch(self)
                self.consume()
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class BitwiseOperatorContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def K_XOR(self):
            return self.getToken(FilterExpressionSyntaxParser.K_XOR, 0)

        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_bitwiseOperator

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterBitwiseOperator" ):
                listener.enterBitwiseOperator(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitBitwiseOperator" ):
                listener.exitBitwiseOperator(self)




    def bitwiseOperator(self):

        localctx = FilterExpressionSyntaxParser.BitwiseOperatorContext(self, self._ctx, self.state)
        self.enterRule(localctx, 34, self.RULE_bitwiseOperator)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 221
            _la = self._input.LA(1)
            if not((((_la) & ~0x3f) == 0 and ((1 << _la) & 65011712) != 0) or _la==85):
                self._errHandler.recoverInline(self)
            else:
                self._errHandler.reportMatch(self)
                self.consume()
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class MathOperatorContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser


        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_mathOperator

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterMathOperator" ):
                listener.enterMathOperator(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitMathOperator" ):
                listener.exitMathOperator(self)




    def mathOperator(self):

        localctx = FilterExpressionSyntaxParser.MathOperatorContext(self, self._ctx, self.state)
        self.enterRule(localctx, 36, self.RULE_mathOperator)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 223
            _la = self._input.LA(1)
            if not((((_la) & ~0x3f) == 0 and ((1 << _la) & *********) != 0)):
                self._errHandler.recoverInline(self)
            else:
                self._errHandler.reportMatch(self)
                self.consume()
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class FunctionNameContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def K_ABS(self):
            return self.getToken(FilterExpressionSyntaxParser.K_ABS, 0)

        def K_CEILING(self):
            return self.getToken(FilterExpressionSyntaxParser.K_CEILING, 0)

        def K_COALESCE(self):
            return self.getToken(FilterExpressionSyntaxParser.K_COALESCE, 0)

        def K_CONVERT(self):
            return self.getToken(FilterExpressionSyntaxParser.K_CONVERT, 0)

        def K_CONTAINS(self):
            return self.getToken(FilterExpressionSyntaxParser.K_CONTAINS, 0)

        def K_DATEADD(self):
            return self.getToken(FilterExpressionSyntaxParser.K_DATEADD, 0)

        def K_DATEDIFF(self):
            return self.getToken(FilterExpressionSyntaxParser.K_DATEDIFF, 0)

        def K_DATEPART(self):
            return self.getToken(FilterExpressionSyntaxParser.K_DATEPART, 0)

        def K_ENDSWITH(self):
            return self.getToken(FilterExpressionSyntaxParser.K_ENDSWITH, 0)

        def K_FLOOR(self):
            return self.getToken(FilterExpressionSyntaxParser.K_FLOOR, 0)

        def K_IIF(self):
            return self.getToken(FilterExpressionSyntaxParser.K_IIF, 0)

        def K_INDEXOF(self):
            return self.getToken(FilterExpressionSyntaxParser.K_INDEXOF, 0)

        def K_ISDATE(self):
            return self.getToken(FilterExpressionSyntaxParser.K_ISDATE, 0)

        def K_ISINTEGER(self):
            return self.getToken(FilterExpressionSyntaxParser.K_ISINTEGER, 0)

        def K_ISGUID(self):
            return self.getToken(FilterExpressionSyntaxParser.K_ISGUID, 0)

        def K_ISNULL(self):
            return self.getToken(FilterExpressionSyntaxParser.K_ISNULL, 0)

        def K_ISNUMERIC(self):
            return self.getToken(FilterExpressionSyntaxParser.K_ISNUMERIC, 0)

        def K_LASTINDEXOF(self):
            return self.getToken(FilterExpressionSyntaxParser.K_LASTINDEXOF, 0)

        def K_LEN(self):
            return self.getToken(FilterExpressionSyntaxParser.K_LEN, 0)

        def K_LOWER(self):
            return self.getToken(FilterExpressionSyntaxParser.K_LOWER, 0)

        def K_MAXOF(self):
            return self.getToken(FilterExpressionSyntaxParser.K_MAXOF, 0)

        def K_MINOF(self):
            return self.getToken(FilterExpressionSyntaxParser.K_MINOF, 0)

        def K_NOW(self):
            return self.getToken(FilterExpressionSyntaxParser.K_NOW, 0)

        def K_NTHINDEXOF(self):
            return self.getToken(FilterExpressionSyntaxParser.K_NTHINDEXOF, 0)

        def K_POWER(self):
            return self.getToken(FilterExpressionSyntaxParser.K_POWER, 0)

        def K_REGEXMATCH(self):
            return self.getToken(FilterExpressionSyntaxParser.K_REGEXMATCH, 0)

        def K_REGEXVAL(self):
            return self.getToken(FilterExpressionSyntaxParser.K_REGEXVAL, 0)

        def K_REPLACE(self):
            return self.getToken(FilterExpressionSyntaxParser.K_REPLACE, 0)

        def K_REVERSE(self):
            return self.getToken(FilterExpressionSyntaxParser.K_REVERSE, 0)

        def K_ROUND(self):
            return self.getToken(FilterExpressionSyntaxParser.K_ROUND, 0)

        def K_SPLIT(self):
            return self.getToken(FilterExpressionSyntaxParser.K_SPLIT, 0)

        def K_SQRT(self):
            return self.getToken(FilterExpressionSyntaxParser.K_SQRT, 0)

        def K_STARTSWITH(self):
            return self.getToken(FilterExpressionSyntaxParser.K_STARTSWITH, 0)

        def K_STRCOUNT(self):
            return self.getToken(FilterExpressionSyntaxParser.K_STRCOUNT, 0)

        def K_STRCMP(self):
            return self.getToken(FilterExpressionSyntaxParser.K_STRCMP, 0)

        def K_SUBSTR(self):
            return self.getToken(FilterExpressionSyntaxParser.K_SUBSTR, 0)

        def K_TRIM(self):
            return self.getToken(FilterExpressionSyntaxParser.K_TRIM, 0)

        def K_TRIMLEFT(self):
            return self.getToken(FilterExpressionSyntaxParser.K_TRIMLEFT, 0)

        def K_TRIMRIGHT(self):
            return self.getToken(FilterExpressionSyntaxParser.K_TRIMRIGHT, 0)

        def K_UPPER(self):
            return self.getToken(FilterExpressionSyntaxParser.K_UPPER, 0)

        def K_UTCNOW(self):
            return self.getToken(FilterExpressionSyntaxParser.K_UTCNOW, 0)

        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_functionName

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterFunctionName" ):
                listener.enterFunctionName(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitFunctionName" ):
                listener.exitFunctionName(self)




    def functionName(self):

        localctx = FilterExpressionSyntaxParser.FunctionNameContext(self, self._ctx, self.state)
        self.enterRule(localctx, 38, self.RULE_functionName)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 225
            _la = self._input.LA(1)
            if not(((((_la - 29)) & ~0x3f) == 0 and ((1 << (_la - 29)) & 35465724524081121) != 0)):
                self._errHandler.recoverInline(self)
            else:
                self._errHandler.reportMatch(self)
                self.consume()
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class FunctionExpressionContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def functionName(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.FunctionNameContext,0)


        def expressionList(self):
            return self.getTypedRuleContext(FilterExpressionSyntaxParser.ExpressionListContext,0)


        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_functionExpression

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterFunctionExpression" ):
                listener.enterFunctionExpression(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitFunctionExpression" ):
                listener.exitFunctionExpression(self)




    def functionExpression(self):

        localctx = FilterExpressionSyntaxParser.FunctionExpressionContext(self, self._ctx, self.state)
        self.enterRule(localctx, 40, self.RULE_functionExpression)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 227
            self.functionName()
            self.state = 228
            self.match(FilterExpressionSyntaxParser.T__4)
            self.state = 230
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if (((_la) & ~0x3f) == 0 and ((1 << _la) & -72420449518091848) != 0) or ((((_la - 66)) & ~0x3f) == 0 and ((1 << (_la - 66)) & *********) != 0):
                self.state = 229
                self.expressionList()


            self.state = 232
            self.match(FilterExpressionSyntaxParser.T__5)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class LiteralValueContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def INTEGER_LITERAL(self):
            return self.getToken(FilterExpressionSyntaxParser.INTEGER_LITERAL, 0)

        def NUMERIC_LITERAL(self):
            return self.getToken(FilterExpressionSyntaxParser.NUMERIC_LITERAL, 0)

        def STRING_LITERAL(self):
            return self.getToken(FilterExpressionSyntaxParser.STRING_LITERAL, 0)

        def DATETIME_LITERAL(self):
            return self.getToken(FilterExpressionSyntaxParser.DATETIME_LITERAL, 0)

        def GUID_LITERAL(self):
            return self.getToken(FilterExpressionSyntaxParser.GUID_LITERAL, 0)

        def BOOLEAN_LITERAL(self):
            return self.getToken(FilterExpressionSyntaxParser.BOOLEAN_LITERAL, 0)

        def K_NULL(self):
            return self.getToken(FilterExpressionSyntaxParser.K_NULL, 0)

        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_literalValue

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterLiteralValue" ):
                listener.enterLiteralValue(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitLiteralValue" ):
                listener.exitLiteralValue(self)




    def literalValue(self):

        localctx = FilterExpressionSyntaxParser.LiteralValueContext(self, self._ctx, self.state)
        self.enterRule(localctx, 42, self.RULE_literalValue)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 234
            _la = self._input.LA(1)
            if not(((((_la - 63)) & ~0x3f) == 0 and ((1 << (_la - 63)) & 3464495105) != 0)):
                self._errHandler.recoverInline(self)
            else:
                self._errHandler.reportMatch(self)
                self.consume()
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class TableNameContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def IDENTIFIER(self):
            return self.getToken(FilterExpressionSyntaxParser.IDENTIFIER, 0)

        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_tableName

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterTableName" ):
                listener.enterTableName(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitTableName" ):
                listener.exitTableName(self)




    def tableName(self):

        localctx = FilterExpressionSyntaxParser.TableNameContext(self, self._ctx, self.state)
        self.enterRule(localctx, 44, self.RULE_tableName)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 236
            self.match(FilterExpressionSyntaxParser.IDENTIFIER)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class ColumnNameContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def IDENTIFIER(self):
            return self.getToken(FilterExpressionSyntaxParser.IDENTIFIER, 0)

        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_columnName

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterColumnName" ):
                listener.enterColumnName(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitColumnName" ):
                listener.exitColumnName(self)




    def columnName(self):

        localctx = FilterExpressionSyntaxParser.ColumnNameContext(self, self._ctx, self.state)
        self.enterRule(localctx, 46, self.RULE_columnName)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 238
            self.match(FilterExpressionSyntaxParser.IDENTIFIER)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class OrderByColumnNameContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def IDENTIFIER(self):
            return self.getToken(FilterExpressionSyntaxParser.IDENTIFIER, 0)

        def getRuleIndex(self):
            return FilterExpressionSyntaxParser.RULE_orderByColumnName

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterOrderByColumnName" ):
                listener.enterOrderByColumnName(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitOrderByColumnName" ):
                listener.exitOrderByColumnName(self)




    def orderByColumnName(self):

        localctx = FilterExpressionSyntaxParser.OrderByColumnNameContext(self, self._ctx, self.state)
        self.enterRule(localctx, 48, self.RULE_orderByColumnName)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 240
            self.match(FilterExpressionSyntaxParser.IDENTIFIER)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx



    def sempred(self, localctx:RuleContext, ruleIndex:int, predIndex:int):
        if self._predicates == None:
            self._predicates = dict()
        self._predicates[9] = self.expression_sempred
        self._predicates[10] = self.predicateExpression_sempred
        self._predicates[11] = self.valueExpression_sempred
        pred = self._predicates.get(ruleIndex, None)
        if pred is None:
            raise Exception("No predicate with index:" + str(ruleIndex))
        else:
            return pred(localctx, predIndex)

    def expression_sempred(self, localctx:ExpressionContext, predIndex:int):
            if predIndex == 0:
                return self.precpred(self._ctx, 2)
         

    def predicateExpression_sempred(self, localctx:PredicateExpressionContext, predIndex:int):
            if predIndex == 1:
                return self.precpred(self._ctx, 3)
         

            if predIndex == 2:
                return self.precpred(self._ctx, 2)
         

            if predIndex == 3:
                return self.precpred(self._ctx, 5)
         

            if predIndex == 4:
                return self.precpred(self._ctx, 4)
         

    def valueExpression_sempred(self, localctx:ValueExpressionContext, predIndex:int):
            if predIndex == 5:
                return self.precpred(self._ctx, 2)
         

            if predIndex == 6:
                return self.precpred(self._ctx, 1)
         




