# ******************************************************************************************************
#  publisher.py - Gbtc
#
#  Copyright © 2022, Grid Protection Alliance.  All Rights Reserved.
#
#  Licensed to the Grid Protection Alliance (GPA) under one or more contributor license agreements. See
#  the NOTICE file distributed with this work for additional information regarding copyright ownership.
#  The GPA licenses this file to you under the MIT License (MIT), the "License"; you may not use this
#  file except in compliance with the License. You may obtain a copy of the License at:
#
#      http://opensource.org/licenses/MIT
#
#  Unless agreed to in writing, the subject software distributed under the License is distributed on an
#  "AS-IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. Refer to the
#  License for the specific language governing permissions and limitations.
#
#  Code Modification History:
#  ----------------------------------------------------------------------------------------------------
#  12/23/2024 - J. Ritchie Carroll
#       Generated original version of source code.
#
# ******************************************************************************************************

from typing import List, Optional, Callable
from uuid import UUID
import numpy as np

from .transport.datapublisher import DataPublisher
from .transport.measurement import Measurement
from .metadata.record.measurement import MeasurementRecord
from .metadata.cache import MetadataCache


class Publisher:
    """
    Represents an STTP data publisher.

    Notes
    -----
    The `Publisher` class exists as a simplified implementation of the `DataPublisher`
    class found in the `transport` namespace. This class maintains an internal instance
    of the `DataPublisher` class for publishing functionality and is intended
    to simplify common uses of STTP data publication.
    """

    def __init__(self, port: np.uint16 = ...):
        """
        Creates a new `Publisher`.
        
        Parameters
        ----------
        port : np.uint16, optional
            The port number to listen on for subscriber connections. Defaults to 7175.
        """

        # DataPublisher reference
        self._datapublisher = DataPublisher(port=port)

        # Callback handlers
        self._statusmessage_logger: Optional[Callable[[str], None]] = None
        self._errormessage_logger: Optional[Callable[[str], None]] = None
        self._clientconnected_logger: Optional[Callable[[UUID, str], None]] = None
        self._clientdisconnected_logger: Optional[Callable[[UUID, str], None]] = None

        # Setup internal callbacks
        self._setup_callbacks()

    def _setup_callbacks(self) -> None:
        """
        Sets up internal callback handlers.
        """
        self._datapublisher.statusmessage_callback = self._handle_statusmessage
        self._datapublisher.errormessage_callback = self._handle_errormessage
        self._datapublisher.clientconnected_callback = self._handle_clientconnected
        self._datapublisher.clientdisconnected_callback = self._handle_clientdisconnected

    @property
    def listening(self) -> bool:
        """
        Determines if the `Publisher` is currently listening for connections.
        """
        return self._datapublisher.listening

    @property
    def subscriber_count(self) -> int:
        """
        Gets the number of connected subscribers.
        """
        return self._datapublisher.subscriber_count

    @property
    def port(self) -> np.uint16:
        """
        Gets the port number the publisher is listening on.
        """
        return self._datapublisher.port

    def set_statusmessage_logger(self, callback: Callable[[str], None]) -> None:
        """
        Sets the callback function for status messages.
        
        Parameters
        ----------
        callback : Callable[[str], None]
            Function to call when status messages are generated.
        """
        self._statusmessage_logger = callback

    def set_errormessage_logger(self, callback: Callable[[str], None]) -> None:
        """
        Sets the callback function for error messages.
        
        Parameters
        ----------
        callback : Callable[[str], None]
            Function to call when error messages are generated.
        """
        self._errormessage_logger = callback

    def set_clientconnected_logger(self, callback: Callable[[UUID, str], None]) -> None:
        """
        Sets the callback function for client connection events.
        
        Parameters
        ----------
        callback : Callable[[UUID, str], None]
            Function to call when a client connects. Receives client ID and address.
        """
        self._clientconnected_logger = callback

    def set_clientdisconnected_logger(self, callback: Callable[[UUID, str], None]) -> None:
        """
        Sets the callback function for client disconnection events.
        
        Parameters
        ----------
        callback : Callable[[UUID, str], None]
            Function to call when a client disconnects. Receives client ID and address.
        """
        self._clientdisconnected_logger = callback

    def add_measurement_metadata(self, measurement_record: MeasurementRecord) -> None:
        """
        Adds measurement metadata to the publisher.
        
        Parameters
        ----------
        measurement_record : MeasurementRecord
            The measurement metadata record to add.
        """
        self._datapublisher.add_measurement_metadata(measurement_record)

    def start(self) -> Optional[Exception]:
        """
        Starts the publisher and begins listening for subscriber connections.
        
        Returns
        -------
        Optional[Exception]
            None if successful, otherwise the exception that occurred.
        """
        return self._datapublisher.start()

    def stop(self) -> None:
        """
        Stops the publisher and disconnects all subscribers.
        """
        self._datapublisher.stop()

    def dispose(self) -> None:
        """
        Releases all resources used by the Publisher.
        """
        self._datapublisher.dispose()

    def publish_measurements(self, measurements: List[Measurement]) -> None:
        """
        Publishes measurements to all subscribed clients.
        
        Parameters
        ----------
        measurements : List[Measurement]
            The measurements to publish.
        """
        self._datapublisher.publish_measurements(measurements)

    def publish_measurement(self, measurement: Measurement) -> None:
        """
        Publishes a single measurement to all subscribed clients.
        
        Parameters
        ----------
        measurement : Measurement
            The measurement to publish.
        """
        self._datapublisher.publish_measurements([measurement])

    def create_measurement(self, signalid: UUID, value: float, timestamp: Optional[np.uint64] = None) -> Measurement:
        """
        Creates a new measurement with the specified parameters.
        
        Parameters
        ----------
        signalid : UUID
            The unique identifier for the measurement signal.
        value : float
            The measurement value.
        timestamp : Optional[np.uint64], optional
            The timestamp in STTP ticks. If None, uses current time.
            
        Returns
        -------
        Measurement
            The created measurement.
        """
        from .ticks import Ticks
        
        if timestamp is None:
            timestamp = Ticks.utcnow()
            
        return Measurement(signalid, np.float64(value), timestamp)

    # Internal callback handlers

    def _handle_statusmessage(self, message: str) -> None:
        """
        Handles status messages from the DataPublisher.
        """
        if self._statusmessage_logger:
            self._statusmessage_logger(message)

    def _handle_errormessage(self, message: str) -> None:
        """
        Handles error messages from the DataPublisher.
        """
        if self._errormessage_logger:
            self._errormessage_logger(message)

    def _handle_clientconnected(self, client_id: UUID, address: str) -> None:
        """
        Handles client connection events from the DataPublisher.
        """
        if self._clientconnected_logger:
            self._clientconnected_logger(client_id, address)

    def _handle_clientdisconnected(self, client_id: UUID, address: str) -> None:
        """
        Handles client disconnection events from the DataPublisher.
        """
        if self._clientdisconnected_logger:
            self._clientdisconnected_logger(client_id, address)

    def statusmessage(self, message: str) -> None:
        """
        Executes the defined status message logger callback.
        """
        if self._statusmessage_logger:
            self._statusmessage_logger(message)

    def errormessage(self, message: str) -> None:
        """
        Executes the defined error message logger callback.
        """
        if self._errormessage_logger:
            self._errormessage_logger(message)

    def clientconnected(self, client_id: UUID, address: str) -> None:
        """
        Executes the defined client connected logger callback.
        """
        if self._clientconnected_logger:
            self._clientconnected_logger(client_id, address)

    def clientdisconnected(self, client_id: UUID, address: str) -> None:
        """
        Executes the defined client disconnected logger callback.
        """
        if self._clientdisconnected_logger:
            self._clientdisconnected_logger(client_id, address)
