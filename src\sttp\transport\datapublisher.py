# ******************************************************************************************************
#  datapublisher.py - Gbtc
#
#  Copyright © 2022, Grid Protection Alliance.  All Rights Reserved.
#
#  Licensed to the Grid Protection Alliance (GPA) under one or more contributor license agreements. See
#  the NOTICE file distributed with this work for additional information regarding copyright ownership.
#  The GPA licenses this file to you under the MIT License (MIT), the "License"; you may not use this
#  file except in compliance with the License. You may obtain a copy of the License at:
#
#      http://opensource.org/licenses/MIT
#
#  Unless agreed to in writing, the subject software distributed under the License is distributed on an
#  "AS-IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. Refer to the
#  License for the specific language governing permissions and limitations.
#
#  Code Modification History:
#  ----------------------------------------------------------------------------------------------------
#  12/23/2024 - J. Ritchie Carroll
#       Generated original version of source code.
#
# ******************************************************************************************************

import socket
import threading
import gzip
from typing import Dict, List, Optional, Callable, Set, Tuple
from uuid import UUID, uuid4
from time import time
import numpy as np

from gsf import Empty, Limits, normalize_enumname
from gsf.endianorder import BigEndian
from gsf.binarystream import BinaryStream
from gsf.streamencoder import StreamEncoder
from .measurement import Measurement
from .compactmeasurement import CompactMeasurement
from ..metadata.record.measurement import MeasurementRecord
from ..metadata.cache import MetadataCache
from .constants import OperationalModes, OperationalEncoding, CompressionModes, Defaults
from .constants import DataPacketFlags, ServerCommand, ServerResponse, StateFlags
from .signalindexcache import SignalIndexCache
from .tssc.encoder import Encoder
from ..version import Version

# Constants
PAYLOADHEADER_SIZE = 4
RESPONSEHEADER_SIZE = 6


class SubscriberConnection:
    """
    Represents a connection to a subscriber client.
    """
    
    def __init__(self, socket: socket.socket, address: Tuple[str, int]):
        self.socket = socket
        self.address = address
        self.subscriber_id = uuid4()
        self.connected = True
        self.subscribed = False
        self.operational_modes = np.uint32(0)
        self.encoding = OperationalEncoding.UTF8
        self.compress_payloaddata = False
        self.compress_metadata = False
        self.compress_signalindexcache = False
        self.version = Defaults.VERSION
        self.filter_expression = ""
        self.udp_port = 0
        self.udp_socket: Optional[socket.socket] = None
        self.signal_index_cache = SignalIndexCache()
        self.tssc_encoder: Optional[Encoder] = None
        self.last_publish_time = 0.0
        self.measurements_sent = np.uint64(0)
        
        # Threading
        self.send_lock = threading.Lock()


class DataPublisher:
    """
    Represents an STTP data publisher that can accept subscriber connections and publish measurements.
    """

    DEFAULT_PORT = np.uint16(7175)
    DEFAULT_COMPRESS_PAYLOADDATA = Defaults.COMPRESS_PAYLOADDATA
    DEFAULT_COMPRESS_METADATA = Defaults.COMPRESS_METADATA
    DEFAULT_COMPRESS_SIGNALINDEXCACHE = Defaults.COMPRESS_SIGNALINDEXCACHE
    DEFAULT_VERSION = Defaults.VERSION
    DEFAULT_STTP_SOURCEINFO = Version.STTP_SOURCE
    DEFAULT_STTP_VERSIONINFO = Version.STTP_VERSION
    DEFAULT_STTP_UPDATEDONINFO = Version.STTP_UPDATEDON
    DEFAULT_SOCKET_TIMEOUT = Defaults.SOCKET_TIMEOUT

    def __init__(self,
                 port: np.uint16 = ...,
                 compress_payloaddata: bool = ...,
                 compress_metadata: bool = ...,
                 compress_signalindexcache: bool = ...,
                 version: np.byte = ...,
                 sttp_sourceinfo: str = ...,
                 sttp_versioninfo: str = ...,
                 sttp_updatedoninfo: str = ...,
                 socket_timeout: float = ...
                 ):
        """
        Creates a new `DataPublisher`.
        """

        self.port = DataPublisher.DEFAULT_PORT if port is ... else port
        self.compress_payloaddata = DataPublisher.DEFAULT_COMPRESS_PAYLOADDATA if compress_payloaddata is ... else compress_payloaddata
        self.compress_metadata = DataPublisher.DEFAULT_COMPRESS_METADATA if compress_metadata is ... else compress_metadata
        self.compress_signalindexcache = DataPublisher.DEFAULT_COMPRESS_SIGNALINDEXCACHE if compress_signalindexcache is ... else compress_signalindexcache
        self.version = DataPublisher.DEFAULT_VERSION if version is ... else version
        self.sttp_sourceinfo = DataPublisher.DEFAULT_STTP_SOURCEINFO if sttp_sourceinfo is ... else sttp_sourceinfo
        self.sttp_versioninfo = DataPublisher.DEFAULT_STTP_VERSIONINFO if sttp_versioninfo is ... else sttp_versioninfo
        self.sttp_updatedoninfo = DataPublisher.DEFAULT_STTP_UPDATEDONINFO if sttp_updatedoninfo is ... else sttp_updatedoninfo
        self.socket_timeout = DataPublisher.DEFAULT_SOCKET_TIMEOUT if socket_timeout is ... else socket_timeout

        # Connection management
        self._server_socket: Optional[socket.socket] = None
        self._accept_thread: Optional[threading.Thread] = None
        self._subscribers: Dict[UUID, SubscriberConnection] = {}
        self._subscribers_lock = threading.Lock()
        self._listening = False
        self._disposing = False

        # Metadata and measurements
        self._metadata_cache = MetadataCache()
        self._measurement_queue: List[Measurement] = []
        self._measurement_queue_lock = threading.Lock()
        
        # Publisher identification
        self._publisher_id = uuid4()

        # Callbacks
        self.statusmessage_callback: Optional[Callable[[str], None]] = None
        self.errormessage_callback: Optional[Callable[[str], None]] = None
        self.clientconnected_callback: Optional[Callable[[UUID, str], None]] = None
        self.clientdisconnected_callback: Optional[Callable[[UUID, str], None]] = None
        self.processingcomplete_callback: Optional[Callable[[str], None]] = None

    @property
    def listening(self) -> bool:
        """
        Determines if the `DataPublisher` is currently listening for connections.
        """
        return self._listening

    @property
    def subscriber_count(self) -> int:
        """
        Gets the number of connected subscribers.
        """
        with self._subscribers_lock:
            return len(self._subscribers)

    @property
    def disposing(self) -> bool:
        """
        Determines if `DataPublisher` is being disposed.
        """
        return self._disposing

    def add_measurement_metadata(self, measurement_record: MeasurementRecord) -> None:
        """
        Adds measurement metadata to the publisher's metadata cache.
        """
        self._metadata_cache.add_measurement(measurement_record)
        self._dispatch_statusmessage(f"Added measurement metadata: SignalID={measurement_record.signalid}, Total measurements: {len(self._metadata_cache.measurement_records)}")

    def start(self) -> Optional[Exception]:
        """
        Starts the data publisher and begins listening for subscriber connections.
        """
        if self._listening:
            return RuntimeError("DataPublisher is already listening")

        try:
            self._server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self._server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self._server_socket.bind(('', int(self.port)))
            self._server_socket.listen(5)
            self._server_socket.settimeout(self.socket_timeout)

            self._listening = True
            self._accept_thread = threading.Thread(target=self._accept_connections, name="AcceptThread")
            self._accept_thread.start()

            self._dispatch_statusmessage(f"DataPublisher listening on port {self.port}")
            return None

        except Exception as ex:
            self._listening = False
            return ex

    def stop(self) -> None:
        """
        Stops the data publisher and disconnects all subscribers.
        """
        if not self._listening:
            return

        self._listening = False
        self._disposing = True

        # Close server socket
        if self._server_socket:
            try:
                self._server_socket.close()
            except:
                pass
            self._server_socket = None

        # Disconnect all subscribers
        with self._subscribers_lock:
            for subscriber in list(self._subscribers.values()):
                self._disconnect_subscriber(subscriber)
            self._subscribers.clear()

        # Wait for accept thread to finish
        if self._accept_thread and self._accept_thread.is_alive():
            self._accept_thread.join(timeout=5.0)

        self._dispatch_statusmessage("DataPublisher stopped")

    def dispose(self) -> None:
        """
        Releases all resources used by the DataPublisher.
        """
        self.stop()

    def publish_measurements(self, measurements: List[Measurement]) -> None:
        """
        Publishes measurements to all subscribed clients.
        """
        if not measurements:
            return

        with self._measurement_queue_lock:
            self._measurement_queue.extend(measurements)

        # Send measurements to all subscribed clients
        subscribed_count = 0
        with self._subscribers_lock:
            for subscriber in list(self._subscribers.values()):
                if subscriber.subscribed and subscriber.connected:
                    subscribed_count += 1
                    try:
                        self._send_measurements_to_subscriber(subscriber, measurements)
                    except Exception as ex:
                        self._dispatch_errormessage(f"Failed to send measurements to subscriber {subscriber.subscriber_id}: {ex}")
                        self._disconnect_subscriber(subscriber)

        self._dispatch_statusmessage(f"Published {len(measurements)} measurements to {subscribed_count} subscribers")

    def _accept_connections(self) -> None:
        """
        Accepts incoming subscriber connections.
        """
        while self._listening and not self._disposing:
            try:
                client_socket, address = self._server_socket.accept()
                client_socket.settimeout(self.socket_timeout)

                subscriber = SubscriberConnection(client_socket, address)

                with self._subscribers_lock:
                    self._subscribers[subscriber.subscriber_id] = subscriber

                # Start handling this subscriber in a separate thread
                client_thread = threading.Thread(
                    target=self._handle_subscriber,
                    args=(subscriber,),
                    name=f"SubscriberThread-{subscriber.subscriber_id}"
                )
                client_thread.start()

                self._dispatch_statusmessage(f"Subscriber connected from {address[0]}:{address[1]}")

                if self.clientconnected_callback:
                    self.clientconnected_callback(subscriber.subscriber_id, f"{address[0]}:{address[1]}")

            except socket.timeout:
                continue
            except Exception as ex:
                if self._listening:
                    self._dispatch_errormessage(f"Error accepting connection: {ex}")

    def _handle_subscriber(self, subscriber: SubscriberConnection) -> None:
        """
        Handles communication with a subscriber.
        """
        try:
            while subscriber.connected and self._listening and not self._disposing:
                try:
                    # Read command packet header
                    header_data = self._receive_data(subscriber.socket, PAYLOADHEADER_SIZE)
                    if not header_data:
                        break

                    # Parse packet size
                    packet_size = BigEndian.to_uint32(header_data)

                    if packet_size > 0:
                        # Read command data
                        command_data = self._receive_data(subscriber.socket, int(packet_size))
                        if not command_data:
                            break

                        # Process the command
                        self._process_subscriber_command(subscriber, command_data)

                except socket.timeout:
                    continue
                except Exception as ex:
                    self._dispatch_errormessage(f"Error handling subscriber {subscriber.subscriber_id}: {ex}")
                    break

        finally:
            self._disconnect_subscriber(subscriber)

    def _receive_data(self, sock: socket.socket, length: int) -> Optional[bytes]:
        """
        Receives exactly the specified number of bytes from a socket.
        """
        data = bytearray()
        while len(data) < length:
            try:
                chunk = sock.recv(length - len(data))
                if not chunk:
                    return None
                data.extend(chunk)
            except socket.timeout:
                if self._disposing:
                    return None
                continue
            except Exception:
                return None
        return bytes(data)

    def _disconnect_subscriber(self, subscriber: SubscriberConnection) -> None:
        """
        Disconnects a subscriber and cleans up resources.
        """
        if not subscriber.connected:
            return

        subscriber.connected = False
        subscriber.subscribed = False

        # Close sockets
        try:
            if subscriber.udp_socket:
                subscriber.udp_socket.close()
                subscriber.udp_socket = None
        except Exception:
            pass

        try:
            subscriber.socket.close()
        except Exception:
            pass

        # Remove from subscribers list
        with self._subscribers_lock:
            if subscriber.subscriber_id in self._subscribers:
                del self._subscribers[subscriber.subscriber_id]

        address_str = f"{subscriber.address[0]}:{subscriber.address[1]}"
        self._dispatch_statusmessage(f"Subscriber disconnected from {address_str}")

        if self.clientdisconnected_callback:
            self.clientdisconnected_callback(subscriber.subscriber_id, address_str)

    def _dispatch_statusmessage(self, message: str) -> None:
        """
        Dispatches a status message to the callback if defined.
        """
        if self.statusmessage_callback:
            self.statusmessage_callback(message)

    def _dispatch_errormessage(self, message: str) -> None:
        """
        Dispatches an error message to the callback if defined.
        """
        if self.errormessage_callback:
            self.errormessage_callback(message)

    def _process_subscriber_command(self, subscriber: SubscriberConnection, data: bytes) -> None:
        """
        Processes a command received from a subscriber.
        """
        if len(data) < 1:
            return

        command_code = ServerCommand(data[0])
        command_data = data[1:] if len(data) > 1 else bytes()

        try:
            if command_code == ServerCommand.DEFINEOPERATIONALMODES:
                self._dispatch_statusmessage(f"Received DEFINEOPERATIONALMODES command from subscriber {subscriber.address[0]}:{subscriber.address[1]}")
                self._handle_define_operational_modes(subscriber, command_data)
            elif command_code == ServerCommand.METADATAREFRESH:
                self._dispatch_statusmessage(f"Received METADATAREFRESH command from subscriber {subscriber.address[0]}:{subscriber.address[1]}")
                self._handle_metadata_refresh(subscriber, command_data)
            elif command_code == ServerCommand.SUBSCRIBE:
                self._dispatch_statusmessage(f"Received SUBSCRIBE command from subscriber {subscriber.address[0]}:{subscriber.address[1]}")
                self._handle_subscribe(subscriber, command_data)
            elif command_code == ServerCommand.UNSUBSCRIBE:
                self._dispatch_statusmessage(f"Received UNSUBSCRIBE command from subscriber {subscriber.address[0]}:{subscriber.address[1]}")
                self._handle_unsubscribe(subscriber)
            elif command_code == ServerCommand.CONFIRMNOTIFICATION:
                self._dispatch_statusmessage(f"Received CONFIRMNOTIFICATION command from subscriber {subscriber.address[0]}:{subscriber.address[1]}")
                self._handle_confirm_notification(subscriber, command_data)
            else:
                self._send_command_response(subscriber, ServerResponse.FAILED, command_code,
                                          f"Unsupported command: {command_code}")

        except Exception as ex:
            self._send_command_response(subscriber, ServerResponse.FAILED, command_code, str(ex))

    def _handle_define_operational_modes(self, subscriber: SubscriberConnection, data: bytes) -> None:
        """
        Handles the define operational modes command.
        """
        if len(data) < 4:
            self._send_command_response(subscriber, ServerResponse.FAILED,
                                      ServerCommand.DEFINEOPERATIONALMODES, "Invalid operational modes data")
            return

        operational_modes = BigEndian.to_uint32(data)
        subscriber.operational_modes = operational_modes

        # Extract operational mode flags
        subscriber.version = np.byte(operational_modes & OperationalModes.VERSIONMASK)
        subscriber.encoding = OperationalEncoding(operational_modes & OperationalModes.ENCODINGMASK)
        subscriber.compress_payloaddata = bool(operational_modes & OperationalModes.COMPRESSPAYLOADDATA)
        subscriber.compress_metadata = bool(operational_modes & OperationalModes.COMPRESSMETADATA)
        subscriber.compress_signalindexcache = bool(operational_modes & OperationalModes.COMPRESSSIGNALINDEXCACHE)

        self._send_command_response(subscriber, ServerResponse.SUCCEEDED,
                                  ServerCommand.DEFINEOPERATIONALMODES, "Operational modes defined successfully")

    def _handle_metadata_refresh(self, subscriber: SubscriberConnection, data: bytes) -> None:
        """
        Handles the metadata refresh command.
        """
        try:
            # For now, send all available metadata
            metadata_xml = self._build_metadata_xml()
            self._dispatch_statusmessage(f"Generated metadata XML: {len(metadata_xml)} characters")
            # Debug: Save XML to file for inspection
            with open("debug_metadata.xml", "w", encoding="utf-8") as f:
                f.write(metadata_xml)
            self._dispatch_statusmessage("Saved metadata XML to debug_metadata.xml")

            if subscriber.compress_metadata:
                metadata_bytes = gzip.compress(metadata_xml.encode('utf-8'))
                self._dispatch_statusmessage(f"Compressed metadata: {len(metadata_xml)} -> {len(metadata_bytes)} bytes")
            else:
                metadata_bytes = metadata_xml.encode('utf-8')
                self._dispatch_statusmessage(f"Uncompressed metadata: {len(metadata_bytes)} bytes")

            self._send_server_response(subscriber, ServerResponse.SUCCEEDED,
                                     ServerCommand.METADATAREFRESH, metadata_bytes)

        except Exception as ex:
            self._send_command_response(subscriber, ServerResponse.FAILED,
                                      ServerCommand.METADATAREFRESH, f"Failed to send metadata: {ex}")

    def _handle_subscribe(self, subscriber: SubscriberConnection, data: bytes) -> None:
        """
        Handles the subscribe command.
        """
        try:
            if len(data) < 5:
                self._send_command_response(subscriber, ServerResponse.FAILED,
                                          ServerCommand.SUBSCRIBE, "Invalid subscription data")
                return

            # Parse subscription parameters
            flags = data[0]
            param_length = BigEndian.to_uint32(data[1:5])

            if len(data) < 5 + param_length:
                self._send_command_response(subscriber, ServerResponse.FAILED,
                                          ServerCommand.SUBSCRIBE, "Incomplete subscription data")
                return

            param_string = data[5:5+param_length].decode('utf-8')

            # Parse connection string parameters
            params = self._parse_connection_string(param_string)
            subscriber.filter_expression = params.get('filterExpression', '')

            # Handle UDP data channel if requested
            if 'dataChannel' in params:
                udp_params = self._parse_connection_string(params['dataChannel'])
                if 'localport' in udp_params:
                    subscriber.udp_port = int(udp_params['localport'])

            # Build and send signal index cache
            self._build_signal_index_cache(subscriber)

            subscriber.subscribed = True
            subscriber.last_publish_time = time()

            # Initialize TSSC encoder if compression is enabled
            if subscriber.compress_payloaddata:
                subscriber.tssc_encoder = Encoder()

            self._dispatch_statusmessage(f"Subscriber {subscriber.address[0]}:{subscriber.address[1]} subscribed with filter: {subscriber.filter_expression}")
            self._send_command_response(subscriber, ServerResponse.SUCCEEDED,
                                      ServerCommand.SUBSCRIBE, "Subscription established successfully")

        except Exception as ex:
            self._send_command_response(subscriber, ServerResponse.FAILED,
                                      ServerCommand.SUBSCRIBE, f"Failed to establish subscription: {ex}")

    def _handle_unsubscribe(self, subscriber: SubscriberConnection) -> None:
        """
        Handles the unsubscribe command.
        """
        subscriber.subscribed = False
        subscriber.tssc_encoder = None

        self._send_command_response(subscriber, ServerResponse.SUCCEEDED,
                                  ServerCommand.UNSUBSCRIBE, "Unsubscribed successfully")

    def _handle_confirm_notification(self, subscriber: SubscriberConnection, data: bytes) -> None:
        """
        Handles the confirm notification command.
        """
        # For now, just acknowledge the confirmation
        self._send_command_response(subscriber, ServerResponse.SUCCEEDED,
                                  ServerCommand.CONFIRMNOTIFICATION, "Notification confirmed")

    def _parse_connection_string(self, connection_string: str) -> Dict[str, str]:
        """
        Parses a connection string into key-value pairs.
        """
        params: Dict[str, str] = {}
        if not connection_string:
            return params

        # Split by semicolon and parse key=value pairs
        for param in connection_string.split(';'):
            param = param.strip()
            if '=' in param:
                key, value = param.split('=', 1)
                # Remove curly braces if present
                value = value.strip('{}')
                params[key.strip()] = value.strip()

        return params

    def _build_signal_index_cache(self, subscriber: SubscriberConnection) -> None:
        """
        Builds and sends the signal index cache to a subscriber.
        """
        try:
            # Build signal index cache from metadata
            cache_data = bytearray()

            # Write subscriber ID (16 bytes)
            cache_data.extend(subscriber.subscriber_id.bytes)

            # Get measurements from metadata cache
            measurements = self._metadata_cache.measurement_records

            # Write reference count (4 bytes)
            cache_data.extend(BigEndian.from_uint32(np.uint32(len(measurements))))

            self._dispatch_statusmessage(f"Building signal index cache with {len(measurements)} measurements for subscriber {subscriber.address[0]}:{subscriber.address[1]}")

            # Write each measurement reference
            signal_index = 0
            for measurement in measurements:
                self._dispatch_statusmessage(f"Adding to signal index cache: Index={signal_index}, SignalID={measurement.signalid}, Source={measurement.source}")

                # Signal index (4 bytes)
                cache_data.extend(BigEndian.from_uint32(np.uint32(signal_index)))

                # Signal ID (16 bytes)
                cache_data.extend(measurement.signalid.bytes)

                # Source string
                source_bytes = measurement.source.encode('utf-8')
                cache_data.extend(BigEndian.from_uint32(np.uint32(len(source_bytes))))
                cache_data.extend(source_bytes)

                # ID (8 bytes)
                cache_data.extend(BigEndian.from_uint64(measurement.id))

                # Add to subscriber's signal index cache (publisher-side, so no DataSubscriber instance)
                # We'll manually add the record without calling lookup_metadata
                index = np.uint32(len(subscriber.signal_index_cache._signalidlist))
                subscriber.signal_index_cache._reference[np.int32(signal_index)] = index
                subscriber.signal_index_cache._signalidlist.append(measurement.signalid)
                subscriber.signal_index_cache._sourcelist.append(measurement.source)
                subscriber.signal_index_cache._idlist.append(measurement.id)
                subscriber.signal_index_cache._signalidcache[measurement.signalid] = np.int32(signal_index)
                subscriber.signal_index_cache._binarylength += np.uint32(32 + len(measurement.source))

                self._dispatch_statusmessage(f"Added to signal index cache: SignalID={measurement.signalid}, RuntimeID={signal_index}, Index={index}")
                self._dispatch_statusmessage(f"Signal index cache now has {len(subscriber.signal_index_cache._signalidlist)} entries")

                signal_index += 1
                self._dispatch_statusmessage(f"Completed adding measurement {signal_index-1}, continuing to next...")

            self._dispatch_statusmessage(f"Finished processing all {signal_index} measurements for signal index cache")

            # Prepend total length (4 bytes)
            total_length = len(cache_data) + 4
            final_data = bytearray()
            final_data.extend(BigEndian.from_uint32(np.uint32(total_length)))
            final_data.extend(cache_data)

            # Compress if requested
            self._dispatch_statusmessage(f"Publisher compress_signalindexcache flag: {subscriber.compress_signalindexcache}")
            if subscriber.compress_signalindexcache:
                compressed_data = gzip.compress(final_data)
                self._dispatch_statusmessage(f"Compressed signal index cache: {len(final_data)} -> {len(compressed_data)} bytes")
                final_data = bytearray(compressed_data)
            else:
                self._dispatch_statusmessage(f"Uncompressed signal index cache: {len(final_data)} bytes")

            # Add version byte for STTP version > 1 (cache index = 0) AFTER compression
            versioned_data = bytearray()
            versioned_data.append(0)  # Cache index 0
            versioned_data.extend(final_data)
            final_data = versioned_data

            self._send_server_response(subscriber, ServerResponse.UPDATESIGNALINDEXCACHE,
                                     ServerCommand.SUBSCRIBE, final_data)

        except Exception as ex:
            self._dispatch_errormessage(f"Failed to build signal index cache: {ex}")

    def _send_command_response(self, subscriber: SubscriberConnection,
                             response_code: ServerResponse, command_code: ServerCommand,
                             message: str) -> None:
        """
        Sends a command response with a message to a subscriber.
        """
        message_bytes = message.encode('utf-8')
        self._send_server_response(subscriber, response_code, command_code, message_bytes)

    def _send_server_response(self, subscriber: SubscriberConnection,
                            response_code: ServerResponse, command_code: ServerCommand,
                            data: bytes) -> None:
        """
        Sends a server response to a subscriber.
        """
        try:
            with subscriber.send_lock:
                if not subscriber.connected:
                    return

                # Build response packet with proper STTP format
                # Packet size includes: response code (1) + command code (1) + internal payload size (4) + data
                packet_size = len(data) + 6  # 6 bytes for response header
                buffer = bytearray()

                # Packet size (4 bytes) - read separately by subscriber
                buffer.extend(BigEndian.from_uint32(np.uint32(packet_size)))

                # Response code (1 byte)
                buffer.append(int(response_code))

                # Command code (1 byte)
                buffer.append(int(command_code))

                # Internal payload size (4 bytes) - expected by subscriber but ignored
                buffer.extend(BigEndian.from_uint32(np.uint32(len(data))))

                # Data
                buffer.extend(data)

                # Send the packet
                subscriber.socket.sendall(buffer)

        except Exception as ex:
            self._dispatch_errormessage(f"Failed to send response to subscriber: {ex}")
            self._disconnect_subscriber(subscriber)

    def _build_metadata_xml(self) -> str:
        """
        Builds a proper STTP metadata XML document from the metadata cache.
        """
        xml_parts = ['<?xml version="1.0" standalone="yes"?>']
        xml_parts.append('<DataSet>')

        # Add XSD schema definition
        xml_parts.append('  <xs:schema id="DataSet" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:ext="urn:schemas-microsoft-com:xml-msdata">')
        xml_parts.append('    <xs:element name="DataSet">')
        xml_parts.append('      <xs:complexType>')
        xml_parts.append('        <xs:choice minOccurs="0" maxOccurs="unbounded">')
        xml_parts.append('          <xs:element name="MeasurementDetail">')
        xml_parts.append('            <xs:complexType>')
        xml_parts.append('              <xs:sequence>')
        xml_parts.append('                <xs:element name="DeviceAcronym" type="xs:string" minOccurs="0" />')
        xml_parts.append('                <xs:element name="ID" type="xs:string" minOccurs="0" />')
        xml_parts.append('                <xs:element name="SignalID" ext:DataType="System.Guid" type="xs:string" minOccurs="0" />')
        xml_parts.append('                <xs:element name="PointTag" type="xs:string" minOccurs="0" />')
        xml_parts.append('                <xs:element name="SignalReference" type="xs:string" minOccurs="0" />')
        xml_parts.append('                <xs:element name="SignalAcronym" type="xs:string" minOccurs="0" />')
        xml_parts.append('                <xs:element name="Description" type="xs:string" minOccurs="0" />')
        xml_parts.append('                <xs:element name="Adder" type="xs:double" minOccurs="0" />')
        xml_parts.append('                <xs:element name="Multiplier" type="xs:double" minOccurs="0" />')
        xml_parts.append('              </xs:sequence>')
        xml_parts.append('            </xs:complexType>')
        xml_parts.append('          </xs:element>')
        xml_parts.append('        </xs:choice>')
        xml_parts.append('      </xs:complexType>')
        xml_parts.append('    </xs:element>')
        xml_parts.append('  </xs:schema>')

        # Add measurement records
        for measurement in self._metadata_cache.measurement_records:
            xml_parts.append('  <MeasurementDetail>')
            xml_parts.append(f'    <DeviceAcronym>{measurement.deviceacronym or ""}</DeviceAcronym>')
            xml_parts.append(f'    <ID>{measurement.source}:{measurement.id}</ID>')
            xml_parts.append(f'    <SignalID>{measurement.signalid}</SignalID>')
            xml_parts.append(f'    <PointTag>{measurement.pointtag}</PointTag>')
            xml_parts.append(f'    <SignalReference>{measurement.signalreference}</SignalReference>')
            xml_parts.append(f'    <SignalAcronym>{measurement.signaltypename}</SignalAcronym>')
            xml_parts.append(f'    <Description>{measurement.description or ""}</Description>')
            xml_parts.append(f'    <Adder>{measurement.adder}</Adder>')
            xml_parts.append(f'    <Multiplier>{measurement.multiplier}</Multiplier>')
            xml_parts.append('  </MeasurementDetail>')

        xml_parts.append('</DataSet>')

        return '\n'.join(xml_parts)

    def _send_measurements_to_subscriber(self, subscriber: SubscriberConnection, measurements: List[Measurement]) -> None:
        """
        Sends measurements to a specific subscriber.
        """
        if not subscriber.subscribed or not subscriber.connected:
            self._dispatch_statusmessage(f"Skipping measurements for subscriber {subscriber.address[0]}:{subscriber.address[1]} - subscribed: {subscriber.subscribed}, connected: {subscriber.connected}")
            return

        try:
            # Filter measurements based on subscriber's filter expression
            filtered_measurements = self._filter_measurements(measurements, subscriber.filter_expression)

            self._dispatch_statusmessage(f"Sending {len(filtered_measurements)} of {len(measurements)} measurements to subscriber {subscriber.address[0]}:{subscriber.address[1]}")

            if not filtered_measurements:
                return

            # Send measurements using appropriate compression
            if subscriber.compress_payloaddata and subscriber.tssc_encoder:
                self._send_tssc_measurements(subscriber, filtered_measurements)
            else:
                self._send_compact_measurements(subscriber, filtered_measurements)

            subscriber.measurements_sent += len(filtered_measurements)
            subscriber.last_publish_time = time()

        except Exception as ex:
            self._dispatch_errormessage(f"Failed to send measurements: {ex}")

    def _filter_measurements(self, measurements: List[Measurement], filter_expression: str) -> List[Measurement]:
        """
        Filters measurements based on the subscriber's filter expression.
        """
        if not filter_expression:
            self._dispatch_statusmessage(f"No filter expression, returning all {len(measurements)} measurements")
            return measurements

        # For now, implement basic filtering
        # In a full implementation, this would parse and evaluate the filter expression
        filtered = []
        available_signals = [record.signalid for record in self._metadata_cache.measurement_records]

        for measurement in measurements:
            # Check if measurement signal ID is in the subscriber's signal index cache
            if measurement.signalid in available_signals:
                filtered.append(measurement)

        self._dispatch_statusmessage(f"Filter '{filter_expression}' matched {len(filtered)} of {len(measurements)} measurements")
        return filtered

    def _send_compact_measurements(self, subscriber: SubscriberConnection, measurements: List[Measurement]) -> None:
        """
        Sends measurements using compact format (non-TSSC).
        """
        self._dispatch_statusmessage(f"_send_compact_measurements called with {len(measurements)} measurements")
        for i, measurement in enumerate(measurements):
            self._dispatch_statusmessage(f"  Measurement {i}: SignalID={measurement.signalid}, Value={measurement.value}")

        # Build compact measurement packet
        buffer = bytearray()

        # Data packet flags
        buffer.append(DataPacketFlags.COMPACT)

        # Reserve space for measurement count (4 bytes) - we'll update this later
        count_position = len(buffer)
        buffer.extend([0, 0, 0, 0])  # Placeholder for count

        # Add each measurement
        measurements_added = 0
        measurement_buffers = []
        for measurement in measurements:
            # Get signal index for this measurement
            signal_index = subscriber.signal_index_cache.signalindex(measurement.signalid)
            self._dispatch_statusmessage(f"Looking up SignalID {measurement.signalid} in signal index cache: found index {signal_index}")
            if signal_index < 0:
                self._dispatch_statusmessage(f"Skipping measurement {measurement.signalid} - not in signal index cache")
                continue  # Skip measurements not in cache

            measurements_added += 1
            self._dispatch_statusmessage(f"Processing measurement {measurements_added}: SignalID={measurement.signalid}, SignalIndex={signal_index}, Value={measurement.value}")

            try:
                # Create compact measurement
                self._dispatch_statusmessage(f"Creating CompactMeasurement for measurement {measurements_added}")
                compact = CompactMeasurement(
                    subscriber.signal_index_cache, True, False, [np.int64(0)],
                    measurement.signalid, measurement.value, measurement.timestamp, measurement.flags
                )
                compact.runtimeid = signal_index
                self._dispatch_statusmessage(f"CompactMeasurement created, binary length: {compact.binarylength}")

                # Encode compact measurement
                measurement_buffer = bytearray(compact.binarylength)
                self._dispatch_statusmessage("Calling encode() on CompactMeasurement")
                bytes_encoded, encode_error = compact.encode(measurement_buffer)
                if encode_error:
                    raise encode_error
                self._dispatch_statusmessage(f"Encoded {bytes_encoded} bytes for measurement {measurements_added}")
                measurement_buffers.append(measurement_buffer)
                self._dispatch_statusmessage(f"Successfully encoded measurement {measurements_added}")
            except Exception as ex:
                self._dispatch_errormessage(f"Failed to encode measurement {measurements_added}: {ex}")
                import traceback
                self._dispatch_errormessage(f"Traceback: {traceback.format_exc()}")
                measurements_added -= 1  # Don't count failed measurements
                continue

        # Update measurement count in the buffer
        count_bytes = BigEndian.from_uint32(np.uint32(measurements_added))
        buffer[count_position:count_position+4] = count_bytes

        # Append all measurement buffers
        for measurement_buffer in measurement_buffers:
            buffer.extend(measurement_buffer)

        # Send data packet
        self._dispatch_statusmessage(f"Sending compact data packet with {measurements_added} measurements ({len(buffer)} bytes)")
        self._send_data_packet(subscriber, buffer)

    def _send_tssc_measurements(self, subscriber: SubscriberConnection, measurements: List[Measurement]) -> None:
        """
        Sends measurements using TSSC compression.
        """
        if not subscriber.tssc_encoder:
            return

        encoder = subscriber.tssc_encoder
        encoder.reset()

        # Add measurements to encoder
        for measurement in measurements:
            signal_index = subscriber.signal_index_cache.signalindex(measurement.signalid)
            if signal_index < 0:
                continue

            encoder.add_measurement(
                np.int32(signal_index),
                np.int64(measurement.timestampvalue),
                np.uint32(measurement.flags),
                np.float32(measurement.value)
            )

        # Finish encoding
        encoder.finish_block()

        # Build TSSC packet
        buffer = bytearray()
        buffer.append(85)  # TSSC version
        buffer.append(0)   # Sequence number (low byte)
        buffer.append(0)   # Sequence number (high byte)
        buffer.extend(encoder.get_buffer())

        # Send data packet
        self._send_data_packet(subscriber, buffer)

    def _send_data_packet(self, subscriber: SubscriberConnection, data: bytearray) -> None:
        """
        Sends a data packet to a subscriber.
        """
        with subscriber.send_lock:
            if not subscriber.connected:
                return

            # Build response packet with proper STTP format
            packet_size = len(data) + 6  # 6 bytes for response header
            buffer = bytearray()

            # Packet size (4 bytes)
            buffer.extend(BigEndian.from_uint32(np.uint32(packet_size)))

            # Response code (1 byte)
            buffer.append(int(ServerResponse.DATAPACKET))

            # Command code (1 byte) - not used for data packets
            buffer.append(0)

            # Internal payload size (4 bytes) - expected by subscriber but ignored
            buffer.extend(BigEndian.from_uint32(np.uint32(len(data))))

            # Data
            buffer.extend(data)

            # Send via UDP if configured, otherwise TCP
            if subscriber.udp_port > 0 and subscriber.udp_socket:
                subscriber.udp_socket.sendto(buffer[4:], subscriber.address)  # Skip packet size for UDP
            else:
                subscriber.socket.sendall(buffer)
