# ******************************************************************************************************
#  publisherconnector.py - Gbtc
#
#  Copyright © 2022, Grid Protection Alliance.  All Rights Reserved.
#
#  Licensed to the Grid Protection Alliance (GPA) under one or more contributor license agreements. See
#  the NOTICE file distributed with this work for additional information regarding copyright ownership.
#  The GPA licenses this file to you under the MIT License (MIT), the "License"; you may not use this
#  file except in compliance with the License. You may obtain a copy of the License at:
#
#      http://opensource.org/licenses/MIT
#
#  Unless agreed to in writing, the subject software distributed under the License is distributed on an
#  "AS-IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. Refer to the
#  License for the specific language governing permissions and limitations.
#
#  Code Modification History:
#  ----------------------------------------------------------------------------------------------------
#  12/23/2024 - J. Ritchie Carroll
#       Generated original version of source code.
#
# ******************************************************************************************************

import socket
import threading
from typing import Optional, Callable, Dict, List
from uuid import UUID
from time import time, sleep
import numpy as np

from gsf import Empty
from .constants import Defaults


class PublisherConnector:
    """
    Represents a connector that manages incoming subscriber connections for a DataPublisher.
    """

    DEFAULT_ERRORMESSAGE_CALLBACK: Callable[[str], None] = lambda msg: print(msg)
    DEFAULT_STATUSMESSAGE_CALLBACK: Callable[[str], None] = lambda msg: print(msg)
    DEFAULT_CLIENTCONNECTED_CALLBACK: Callable[[UUID, str], None] = lambda _, __: ...
    DEFAULT_CLIENTDISCONNECTED_CALLBACK: Callable[[UUID, str], None] = lambda _, __: ...
    DEFAULT_PORT = np.uint16(7175)
    DEFAULT_MAX_CONNECTIONS = 100
    DEFAULT_SOCKET_TIMEOUT = Defaults.SOCKET_TIMEOUT

    def __init__(self,
                 port: np.uint16 = ...,
                 max_connections: int = ...,
                 socket_timeout: float = ...
                 ):
        """
        Creates a new `PublisherConnector`.
        """

        self.port = PublisherConnector.DEFAULT_PORT if port is ... else port
        self.max_connections = PublisherConnector.DEFAULT_MAX_CONNECTIONS if max_connections is ... else max_connections
        self.socket_timeout = PublisherConnector.DEFAULT_SOCKET_TIMEOUT if socket_timeout is ... else socket_timeout

        # Callbacks
        self.errormessage_callback = PublisherConnector.DEFAULT_ERRORMESSAGE_CALLBACK
        self.statusmessage_callback = PublisherConnector.DEFAULT_STATUSMESSAGE_CALLBACK
        self.clientconnected_callback = PublisherConnector.DEFAULT_CLIENTCONNECTED_CALLBACK
        self.clientdisconnected_callback = PublisherConnector.DEFAULT_CLIENTDISCONNECTED_CALLBACK

        # Connection management
        self._server_socket: Optional[socket.socket] = None
        self._accept_thread: Optional[threading.Thread] = None
        self._listening = False
        self._disposing = False
        self._connection_count = 0
        self._connection_lock = threading.Lock()

        # Client management
        self._client_handlers: Dict[UUID, threading.Thread] = {}
        self._client_handlers_lock = threading.Lock()

    @property
    def listening(self) -> bool:
        """
        Determines if the `PublisherConnector` is currently listening for connections.
        """
        return self._listening

    @property
    def connection_count(self) -> int:
        """
        Gets the current number of active connections.
        """
        with self._connection_lock:
            return self._connection_count

    @property
    def disposing(self) -> bool:
        """
        Determines if `PublisherConnector` is being disposed.
        """
        return self._disposing

    def start(self, connection_handler: Callable[[socket.socket, tuple], None]) -> Optional[Exception]:
        """
        Starts listening for incoming connections and handles them with the provided handler.
        
        Parameters
        ----------
        connection_handler : Callable[[socket.socket, tuple], None]
            Function to handle each incoming connection. Receives the client socket and address.
        """
        if self._listening:
            return RuntimeError("PublisherConnector is already listening")

        try:
            self._server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self._server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self._server_socket.bind(('', int(self.port)))
            self._server_socket.listen(self.max_connections)
            self._server_socket.settimeout(self.socket_timeout)

            self._listening = True
            self._accept_thread = threading.Thread(
                target=self._accept_connections,
                args=(connection_handler,),
                name="PublisherAcceptThread"
            )
            self._accept_thread.start()

            self.statusmessage_callback(f"PublisherConnector listening on port {self.port}")
            return None

        except Exception as ex:
            self._listening = False
            return ex

    def stop(self) -> None:
        """
        Stops listening for connections and closes all active connections.
        """
        if not self._listening:
            return

        self._listening = False
        self._disposing = True

        # Close server socket
        if self._server_socket:
            try:
                self._server_socket.close()
            except Exception:
                pass
            self._server_socket = None

        # Wait for accept thread to finish
        if self._accept_thread and self._accept_thread.is_alive():
            self._accept_thread.join(timeout=5.0)

        # Wait for all client handler threads to finish
        with self._client_handlers_lock:
            for thread in list(self._client_handlers.values()):
                if thread.is_alive():
                    thread.join(timeout=2.0)
            self._client_handlers.clear()

        self.statusmessage_callback("PublisherConnector stopped")

    def dispose(self) -> None:
        """
        Releases all resources used by the PublisherConnector.
        """
        self.stop()

    def register_client_handler(self, client_id: UUID, handler_thread: threading.Thread) -> None:
        """
        Registers a client handler thread for tracking.
        """
        with self._client_handlers_lock:
            self._client_handlers[client_id] = handler_thread

    def unregister_client_handler(self, client_id: UUID) -> None:
        """
        Unregisters a client handler thread.
        """
        with self._client_handlers_lock:
            if client_id in self._client_handlers:
                del self._client_handlers[client_id]

    def _accept_connections(self, connection_handler: Callable[[socket.socket, tuple], None]) -> None:
        """
        Accepts incoming connections and delegates handling to the provided handler.
        """
        while self._listening and not self._disposing:
            try:
                client_socket, address = self._server_socket.accept()
                client_socket.settimeout(self.socket_timeout)

                # Check connection limit
                with self._connection_lock:
                    if self._connection_count >= self.max_connections:
                        self.errormessage_callback(f"Connection limit reached ({self.max_connections}), rejecting connection from {address[0]}:{address[1]}")
                        try:
                            client_socket.close()
                        except Exception:
                            pass
                        continue

                    self._connection_count += 1

                # Handle the connection
                try:
                    connection_handler(client_socket, address)
                    self.statusmessage_callback(f"Client connected from {address[0]}:{address[1]}")
                except Exception as ex:
                    self.errormessage_callback(f"Error handling connection from {address[0]}:{address[1]}: {ex}")
                    try:
                        client_socket.close()
                    except Exception:
                        pass
                    with self._connection_lock:
                        self._connection_count -= 1

            except socket.timeout:
                continue
            except Exception as ex:
                if self._listening:
                    self.errormessage_callback(f"Error accepting connection: {ex}")

    def connection_closed(self) -> None:
        """
        Notifies the connector that a connection has been closed.
        """
        with self._connection_lock:
            if self._connection_count > 0:
                self._connection_count -= 1

    def _dispatch_statusmessage(self, message: str) -> None:
        """
        Dispatches a status message to the callback.
        """
        if self.statusmessage_callback:
            self.statusmessage_callback(message)

    def _dispatch_errormessage(self, message: str) -> None:
        """
        Dispatches an error message to the callback.
        """
        if self.errormessage_callback:
            self.errormessage_callback(message)
