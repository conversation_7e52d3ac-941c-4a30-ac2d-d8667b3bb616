# ******************************************************************************************************
#  encoder.py - Gbtc
#
#  Copyright © 2022, Grid Protection Alliance.  All Rights Reserved.
#
#  Licensed to the Grid Protection Alliance (GPA) under one or more contributor license agreements. See
#  the NOTICE file distributed with this work for additional information regarding copyright ownership.
#  The GPA licenses this file to you under the MIT License (MIT), the "License"; you may not use this
#  file except in compliance with the License. You may obtain a copy of the License at:
#
#      http://opensource.org/licenses/MIT
#
#  Unless agreed to in writing, the subject software distributed under the License is distributed on an
#  "AS-IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. Refer to the
#  License for the specific language governing permissions and limitations.
#
#  Code Modification History:
#  ----------------------------------------------------------------------------------------------------
#  12/23/2024 - J. Ritchie Carroll
#       Generated original version of source code.
#
# ******************************************************************************************************

from typing import Dict, List, Optional, Tuple
from uuid import UUID
import numpy as np
from gsf.endianorder import BigEndian
from .pointmetadata import PointMetadata, CodeWords
from ..measurement import Measurement

# Constants for TSSC encoding
INT32_0 = np.int32(0)
INT32_4 = np.int32(4)
INT32_8 = np.int32(8)
INT32_12 = np.int32(12)
INT32_16 = np.int32(16)
INT32_20 = np.int32(20)
INT32_24 = np.int32(24)
INT32_MAX = np.int32(**********)

INT64_0 = np.int64(0)
INT64_MAX = np.int64(9223372036854775807)

UINT32_0 = np.uint32(0)
UINT32_4 = np.uint32(4)
UINT32_8 = np.uint32(8)
UINT32_12 = np.uint32(12)
UINT32_16 = np.uint32(16)
UINT32_20 = np.uint32(20)
UINT32_24 = np.uint32(24)

BYTE_0 = np.byte(0)


class Encoder:
    """
    The encoder for the Time-Series Special Compression (TSSC) algorithm of STTP.
    """

    def __init__(self):
        """
        Creates a new TSSC encoder.
        """
        
        self._buffer = bytearray()
        self._position = 0
        
        self._prevtimestamp1 = INT64_0
        self._prevtimestamp2 = INT64_0
        
        self._prevtimedelta1 = INT64_MAX
        self._prevtimedelta2 = INT64_MAX
        self._prevtimedelta3 = INT64_MAX
        self._prevtimedelta4 = INT64_MAX
        
        self._lastpoint = self._new_pointmetadata()
        self._points: Dict[np.int32, Optional[PointMetadata]] = {}
        
        # The number of bits in bitstream cache that are valid. 0 means the bitstream is empty
        self._bitstreamcount = INT32_0
        
        # A cache of bits that need to be flushed to buffer when full. Bits filled starting from the right moving left
        self._bitstreamcache = INT32_0
        
        self.sequencenumber = 0
        """
        SequenceNumber is the sequence used to synchronize encoding and decoding.
        """

    def _new_pointmetadata(self) -> PointMetadata:
        return PointMetadata(self._write_bits, None, None)

    def _bitstream_isempty(self) -> bool:
        return self._bitstreamcount == INT32_0

    def _clear_bitstream(self) -> None:
        self._bitstreamcount = INT32_0
        self._bitstreamcache = INT32_0

    def _write_bits(self, value: np.int32, count: np.int32) -> None:
        """
        Writes the specified number of bits to the bitstream cache.
        """
        if count < 0 or count > 32:
            raise ValueError("count must be between 0 and 32")
            
        if count == 0:
            return
            
        # Ensure we have space in the buffer
        while len(self._buffer) <= self._position + 4:
            self._buffer.extend(bytearray(1024))
            
        # Add bits to cache
        self._bitstreamcache |= (value & ((1 << count) - 1)) << self._bitstreamcount
        self._bitstreamcount += count
        
        # Flush full bytes to buffer
        while self._bitstreamcount >= 8:
            self._buffer[self._position] = self._bitstreamcache & 0xFF
            self._position += 1
            self._bitstreamcache >>= 8
            self._bitstreamcount -= 8

    def _flush_bits(self) -> None:
        """
        Flushes any remaining bits in the cache to the buffer.
        """
        if self._bitstreamcount > 0:
            while len(self._buffer) <= self._position:
                self._buffer.extend(bytearray(1024))
            self._buffer[self._position] = self._bitstreamcache & 0xFF
            self._position += 1
            self._clear_bitstream()

    def reset(self) -> None:
        """
        Resets the encoder state for a new compression sequence.
        """
        self._buffer = bytearray()
        self._position = 0
        self._clear_bitstream()
        
        self._prevtimestamp1 = INT64_0
        self._prevtimestamp2 = INT64_0
        
        self._prevtimedelta1 = INT64_MAX
        self._prevtimedelta2 = INT64_MAX
        self._prevtimedelta3 = INT64_MAX
        self._prevtimedelta4 = INT64_MAX
        
        self._lastpoint = self._new_pointmetadata()
        self._points.clear()
        
        self.sequencenumber = 0

    def set_buffer(self, buffer: bytearray) -> None:
        """
        Sets the working buffer for encoding measurements.
        """
        self._buffer = buffer
        self._position = 0
        self._clear_bitstream()

    def get_buffer(self) -> bytes:
        """
        Gets the encoded buffer with measurements.
        """
        self._flush_bits()
        return bytes(self._buffer[:self._position])

    def add_measurement(self, pointid: np.int32, timestamp: np.int64, stateflags: np.uint32, value: np.float32) -> Optional[Exception]:
        """
        Adds a measurement to the TSSC compression stream.
        """
        try:
            # Setup tracking for metadata associated with measurement ID
            if (nextpoint := self._points.get(pointid)) is None:
                nextpoint = self._new_pointmetadata()
                self._points[pointid] = nextpoint
                nextpoint.prevnextpointid1 = pointid + 1

            # Encode measurement ID
            self._encode_pointid(pointid, self._lastpoint)
            
            # Encode timestamp
            self._encode_timestamp(timestamp)
            
            # Encode state flags
            self._encode_stateflags(stateflags, nextpoint)
            
            # Encode value
            self._encode_value(value, nextpoint)
            
            return None
            
        except Exception as ex:
            return ex

    def finish_block(self) -> None:
        """
        Finishes the current compression block by writing end-of-stream marker.
        """
        self._lastpoint.write_code(CodeWords.ENDOFSTREAM)
        self._flush_bits()

    def _encode_pointid(self, pointid: np.int32, lastpoint: PointMetadata) -> None:
        """
        Encodes the point ID using delta compression.
        """
        if pointid == lastpoint.prevnextpointid1:
            # Point ID matches expected next ID, no encoding needed
            pass
        else:
            # Calculate XOR difference
            xor_value = pointid ^ lastpoint.prevnextpointid1

            if xor_value < 16:  # 4 bits
                lastpoint.write_code(CodeWords.POINTIDXOR4)
                self._write_bits(np.int32(xor_value), np.int32(4))
            elif xor_value < 256:  # 8 bits
                lastpoint.write_code(CodeWords.POINTIDXOR8)
                self._write_byte(int(xor_value))
            elif xor_value < 4096:  # 12 bits
                lastpoint.write_code(CodeWords.POINTIDXOR12)
                self._write_bits(np.int32(xor_value & 0xF), np.int32(4))
                self._write_byte(int((xor_value >> 4) & 0xFF))
            elif xor_value < 65536:  # 16 bits
                lastpoint.write_code(CodeWords.POINTIDXOR16)
                self._write_byte(int(xor_value & 0xFF))
                self._write_byte(int((xor_value >> 8) & 0xFF))
            elif xor_value < 1048576:  # 20 bits
                lastpoint.write_code(CodeWords.POINTIDXOR20)
                self._write_bits(np.int32(xor_value & 0xF), np.int32(4))
                self._write_byte(int((xor_value >> 4) & 0xFF))
                self._write_byte(int((xor_value >> 12) & 0xFF))
            elif xor_value < 16777216:  # 24 bits
                lastpoint.write_code(CodeWords.POINTIDXOR24)
                self._write_byte(int(xor_value & 0xFF))
                self._write_byte(int((xor_value >> 8) & 0xFF))
                self._write_byte(int((xor_value >> 16) & 0xFF))
            else:  # 32 bits
                lastpoint.write_code(CodeWords.POINTIDXOR32)
                self._write_byte(int(xor_value & 0xFF))
                self._write_byte(int((xor_value >> 8) & 0xFF))
                self._write_byte(int((xor_value >> 16) & 0xFF))
                self._write_byte(int((xor_value >> 24) & 0xFF))

        lastpoint.prevnextpointid1 = pointid + 1

    def _write_byte(self, value: int) -> None:
        """
        Writes a single byte to the buffer.
        """
        while len(self._buffer) <= self._position:
            self._buffer.extend(bytearray(1024))
        self._buffer[self._position] = value & 0xFF
        self._position += 1

    def _encode_timestamp(self, timestamp: np.int64) -> None:
        """
        Encodes the timestamp using delta compression.
        """
        if timestamp == self._prevtimestamp1:
            # Timestamp matches previous, no encoding needed
            self._lastpoint.write_code(CodeWords.TIMEDELTA1FORWARD)
        else:
            # Calculate time delta
            timedelta = timestamp - self._prevtimestamp1

            if timedelta == self._prevtimedelta1:
                self._lastpoint.write_code(CodeWords.TIMEDELTA1FORWARD)
            elif timedelta == self._prevtimedelta2:
                self._lastpoint.write_code(CodeWords.TIMEDELTA2FORWARD)
                self._prevtimedelta2 = self._prevtimedelta1
                self._prevtimedelta1 = timedelta
            elif timedelta == self._prevtimedelta3:
                self._lastpoint.write_code(CodeWords.TIMEDELTA3FORWARD)
                self._prevtimedelta3 = self._prevtimedelta2
                self._prevtimedelta2 = self._prevtimedelta1
                self._prevtimedelta1 = timedelta
            elif timedelta == self._prevtimedelta4:
                self._lastpoint.write_code(CodeWords.TIMEDELTA4FORWARD)
                self._prevtimedelta4 = self._prevtimedelta3
                self._prevtimedelta3 = self._prevtimedelta2
                self._prevtimedelta2 = self._prevtimedelta1
                self._prevtimedelta1 = timedelta
            elif timedelta == -self._prevtimedelta1:
                self._lastpoint.write_code(CodeWords.TIMEDELTA1REVERSE)
            elif timedelta == -self._prevtimedelta2:
                self._lastpoint.write_code(CodeWords.TIMEDELTA2REVERSE)
                self._prevtimedelta2 = self._prevtimedelta1
                self._prevtimedelta1 = -timedelta
            elif timedelta == -self._prevtimedelta3:
                self._lastpoint.write_code(CodeWords.TIMEDELTA3REVERSE)
                self._prevtimedelta3 = self._prevtimedelta2
                self._prevtimedelta2 = self._prevtimedelta1
                self._prevtimedelta1 = -timedelta
            elif timedelta == -self._prevtimedelta4:
                self._lastpoint.write_code(CodeWords.TIMEDELTA4REVERSE)
                self._prevtimedelta4 = self._prevtimedelta3
                self._prevtimedelta3 = self._prevtimedelta2
                self._prevtimedelta2 = self._prevtimedelta1
                self._prevtimedelta1 = -timedelta
            else:
                # Encode timestamp with XOR compression
                self._encode_timestamp_xor(timestamp)

        self._prevtimestamp2 = self._prevtimestamp1
        self._prevtimestamp1 = timestamp

    def _encode_timestamp_xor(self, timestamp: np.int64) -> None:
        """
        Encodes timestamp using XOR compression.
        """
        xor_value = timestamp ^ self._prevtimestamp1

        if xor_value < 128:  # 7 bits
            self._lastpoint.write_code(CodeWords.TIMEXOR7BIT)
            self._write_bits(np.int32(xor_value), np.int32(7))
        else:
            # For larger XOR values, use full timestamp encoding
            self._lastpoint.write_code(CodeWords.TIMESTAMP2)
            # Write 8 bytes for full timestamp
            for i in range(8):
                self._write_byte(int((timestamp >> (i * 8)) & 0xFF))

        # Update time deltas
        timedelta = timestamp - self._prevtimestamp1
        self._prevtimedelta4 = self._prevtimedelta3
        self._prevtimedelta3 = self._prevtimedelta2
        self._prevtimedelta2 = self._prevtimedelta1
        self._prevtimedelta1 = timedelta

    def _encode_stateflags(self, stateflags: np.uint32, nextpoint: PointMetadata) -> None:
        """
        Encodes state flags using delta compression.
        """
        if stateflags == nextpoint.prevstateflags1:
            # State flags match previous, no encoding needed
            self._lastpoint.write_code(CodeWords.STATEFLAGS2)
        else:
            # Encode state flags with 7-bit encoding
            self._lastpoint.write_code(CodeWords.STATEFLAGS7BIT32)
            self._encode_7bituint32(stateflags)

        nextpoint.prevstateflags2 = nextpoint.prevstateflags1
        nextpoint.prevstateflags1 = stateflags

    def _encode_value(self, value: np.float32, nextpoint: PointMetadata) -> None:
        """
        Encodes measurement value using delta compression.
        """
        valueraw = np.uint32(np.frombuffer(np.array([value], dtype=np.float32).tobytes(), dtype=np.uint32)[0])

        if valueraw == nextpoint.prevvalue1:
            self._lastpoint.write_code(CodeWords.VALUE1)
        elif valueraw == nextpoint.prevvalue2:
            self._lastpoint.write_code(CodeWords.VALUE2)
            nextpoint.prevvalue2 = nextpoint.prevvalue1
            nextpoint.prevvalue1 = valueraw
        elif valueraw == nextpoint.prevvalue3:
            self._lastpoint.write_code(CodeWords.VALUE3)
            nextpoint.prevvalue3 = nextpoint.prevvalue2
            nextpoint.prevvalue2 = nextpoint.prevvalue1
            nextpoint.prevvalue1 = valueraw
        elif valueraw == UINT32_0:
            self._lastpoint.write_code(CodeWords.VALUEZERO)
            nextpoint.prevvalue3 = nextpoint.prevvalue2
            nextpoint.prevvalue2 = nextpoint.prevvalue1
            nextpoint.prevvalue1 = valueraw
        else:
            # Encode value with XOR compression
            self._encode_value_xor(valueraw, nextpoint)

    def _encode_value_xor(self, valueraw: np.uint32, nextpoint: PointMetadata) -> None:
        """
        Encodes value using XOR compression.
        """
        xor_value = valueraw ^ nextpoint.prevvalue1

        if xor_value < 16:  # 4 bits
            self._lastpoint.write_code(CodeWords.VALUEXOR4)
            self._write_bits(np.int32(xor_value), np.int32(4))
        elif xor_value < 256:  # 8 bits
            self._lastpoint.write_code(CodeWords.VALUEXOR8)
            self._write_byte(int(xor_value))
        elif xor_value < 4096:  # 12 bits
            self._lastpoint.write_code(CodeWords.VALUEXOR12)
            self._write_bits(np.int32(xor_value & 0xF), np.int32(4))
            self._write_byte(int((xor_value >> 4) & 0xFF))
        elif xor_value < 65536:  # 16 bits
            self._lastpoint.write_code(CodeWords.VALUEXOR16)
            self._write_byte(int(xor_value & 0xFF))
            self._write_byte(int((xor_value >> 8) & 0xFF))
        elif xor_value < 1048576:  # 20 bits
            self._lastpoint.write_code(CodeWords.VALUEXOR20)
            self._write_bits(np.int32(xor_value & 0xF), np.int32(4))
            self._write_byte(int((xor_value >> 4) & 0xFF))
            self._write_byte(int((xor_value >> 12) & 0xFF))
        elif xor_value < 16777216:  # 24 bits
            self._lastpoint.write_code(CodeWords.VALUEXOR24)
            self._write_byte(int(xor_value & 0xFF))
            self._write_byte(int((xor_value >> 8) & 0xFF))
            self._write_byte(int((xor_value >> 16) & 0xFF))
        elif xor_value < 268435456:  # 28 bits
            self._lastpoint.write_code(CodeWords.VALUEXOR28)
            self._write_bits(np.int32(xor_value & 0xF), np.int32(4))
            self._write_byte(int((xor_value >> 4) & 0xFF))
            self._write_byte(int((xor_value >> 12) & 0xFF))
            self._write_byte(int((xor_value >> 20) & 0xFF))
        else:  # 32 bits
            self._lastpoint.write_code(CodeWords.VALUEXOR32)
            self._write_byte(int(xor_value & 0xFF))
            self._write_byte(int((xor_value >> 8) & 0xFF))
            self._write_byte(int((xor_value >> 16) & 0xFF))
            self._write_byte(int((xor_value >> 24) & 0xFF))

        nextpoint.prevvalue3 = nextpoint.prevvalue2
        nextpoint.prevvalue2 = nextpoint.prevvalue1
        nextpoint.prevvalue1 = valueraw

    def _encode_7bituint32(self, value: np.uint32) -> None:
        """
        Encodes a 32-bit unsigned integer using 7-bit encoding.
        """
        while value >= 128:
            self._write_byte(int((value & 0x7F) | 0x80))
            value = np.uint32(value >> 7)
        self._write_byte(int(value & 0x7F))
