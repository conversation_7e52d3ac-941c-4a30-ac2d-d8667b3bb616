#!/usr/bin/env python3

"""
Debug script to isolate DataSet parsing issue
"""

import sys
sys.path.insert(0, '../src')

from sttp.data.dataset import DataSet

def test_dataset_parsing():
    """Test DataSet parsing from XML"""
    
    # Read the generated XML
    with open("debug_metadata.xml", "r", encoding="utf-8") as f:
        xml_data = f.read()
    
    print(f"XML data length: {len(xml_data)} characters")
    print("First 500 characters:")
    print(xml_data[:500])
    print("...")
    
    try:
        print("\nParsing DataSet from XML...")
        dataset, err = DataSet.from_xml(xml_data.encode('utf-8'))
        
        if err is not None:
            print(f"ERROR: Failed to parse DataSet: {err}")
            return
        
        print(f"✅ DataSet parsed successfully!")
        print(f"   Tables: {dataset.tablecount}")
        print(f"   Table names: {dataset.tablenames()}")
        
        for i, table in enumerate(dataset.tables()):
            print(f"   Table {i}: '{table.name}' with {table.rowcount} rows")
        
        print("\nTesting table access...")
        try:
            measurement_table = dataset["MeasurementDetail"]
            print(f"✅ dataset['MeasurementDetail'] = {measurement_table}")
            print(f"   Type: {type(measurement_table)}")
            if measurement_table is not None:
                print(f"   Rows: {measurement_table.rowcount}")
        except Exception as ex:
            print(f"❌ Error accessing MeasurementDetail table: {ex}")
            import traceback
            traceback.print_exc()
        
        print("\nTesting table iteration...")
        try:
            measurement_table = dataset.table("MeasurementDetail")
            if measurement_table is not None:
                print(f"✅ dataset.table('MeasurementDetail') = {measurement_table}")
                print(f"   Iterating over {measurement_table.rowcount} rows...")
                for i, row in enumerate(measurement_table):
                    print(f"   Row {i}: {row} (type: {type(row)})")
                    if i >= 2:  # Only show first 3 rows
                        break
            else:
                print("❌ MeasurementDetail table is None")
        except Exception as ex:
            print(f"❌ Error iterating over table: {ex}")
            import traceback
            traceback.print_exc()
            
    except Exception as ex:
        print(f"❌ Error parsing DataSet: {ex}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_dataset_parsing()
