#!/usr/bin/env python3

"""
Debug script to isolate the subscription issue
"""

import sys
import time
from uuid import uuid4
import numpy as np

# Add the src directory to the path so we can import sttp
sys.path.insert(0, '../src')

from sttp import Publisher, Subscriber, Config
from sttp.metadata.record.measurement import MeasurementRecord

def debug_subscription():
    print("🔍 DEBUG: Subscription Issue Investigation")
    print("=" * 60)
    
    # Create publisher exactly like the working demo
    print("1. Creating Publisher (like working demo)...")
    publisher = Publisher(port=np.uint16(7189))  # Use different port
    
    # Set up publisher callbacks
    publisher.set_statusmessage_logger(
        lambda msg: print(f"   PUB STATUS: {msg}")
    )
    publisher.set_clientconnected_logger(
        lambda cid, addr: print(f"   PUB EVENT: Client connected from {addr}")
    )
    publisher.set_clientdisconnected_logger(
        lambda cid, addr: print(f"   PUB EVENT: Client disconnected from {addr}")
    )
    
    # Add one simple measurement
    signal_id = uuid4()
    metadata = MeasurementRecord(
        signalid=signal_id,
        id=np.uint64(1),
        source="DEBUG",
        signaltypename="FREQ",
        pointtag="DEBUG.DEV1.FREQ",
        description="Debug Frequency (Hz)",
        deviceacronym="DEV1",
        signalreference="DEBUG-DEV1:FREQ"
    )
    publisher.add_measurement_metadata(metadata)
    print(f"   Added 1 measurement definition: {signal_id}")
    
    # Start publisher
    print("\n2. Starting Publisher...")
    error = publisher.start()
    if error:
        print(f"   ❌ Failed to start publisher: {error}")
        return False
    
    print(f"   ✅ Publisher listening on port {publisher.port}")
    
    # Create subscriber exactly like the working demo
    print("\n3. Creating Subscriber (like working demo)...")
    subscriber = Subscriber()
    
    # Set up subscriber callbacks
    subscriber_events = []
    measurements_received = []
    
    subscriber.set_statusmessage_logger(
        lambda msg: print(f"   SUB STATUS: {msg}")
    )
    subscriber.set_errormessage_logger(
        lambda msg: print(f"   SUB ERROR: {msg}")
    )
    subscriber.set_connectionestablished_receiver(
        lambda: print("   SUB EVENT: Connection established")
    )
    
    def on_measurements_received(measurements):
        measurements_received.extend(measurements)
        print(f"   SUB EVENT: Received {len(measurements)} measurements")
        for i, measurement in enumerate(measurements):
            print(f"     Measurement {i}: ID={measurement.signalid}, Value={measurement.value}")

    subscriber.set_newmeasurements_receiver(on_measurements_received)
    subscriber.set_metadatanotification_receiver(
        lambda dataset: (subscriber_events.append(f"METADATA: {len(dataset.tables())} tables"),
                        print(f"   SUB EVENT: Metadata received with {len(dataset.tables())} tables"))
    )
    
    # Connect subscriber (no explicit config - use defaults)
    print("\n4. Connecting Subscriber (using defaults)...")
    connect_error = subscriber.connect(f"localhost:{publisher.port}")
    if connect_error:
        print(f"   ❌ Connection failed: {connect_error}")
        publisher.dispose()
        return False
    
    # Wait for connection
    print("   Waiting for connection...")
    timeout = 5.0
    start_time = time.time()
    
    while not subscriber.connected and (time.time() - start_time) < timeout:
        time.sleep(0.1)
    
    if subscriber.connected:
        print("   ✅ Subscriber connected successfully!")
    else:
        print("   ❌ Connection timeout")
        publisher.dispose()
        subscriber.dispose()
        return False
    
    # Wait for metadata processing and auto-subscription
    print("\n5. Waiting for metadata processing and auto-subscription...")
    timeout = 10.0
    start_time = time.time()
    
    while (time.time() - start_time) < timeout:
        time.sleep(0.1)
        # Check if we have metadata events
        metadata_events = [e for e in subscriber_events if "METADATA:" in e]
        if metadata_events:
            print(f"   ✅ Metadata processed: {metadata_events[0]}")
            # Wait a bit more for auto-subscription to complete
            time.sleep(3)
            break
    
    # Check subscription status
    print(f"\n6. Subscription Status Check:")
    print(f"   Publisher subscriber count: {publisher.subscriber_count}")
    print(f"   Publisher listening: {publisher.listening}")
    print(f"   Subscriber connected: {subscriber.connected}")
    
    # Try to publish a measurement
    print("\n7. Testing Measurement Publishing...")
    measurement = publisher.create_measurement(signal_id, 60.0)
    print(f"   Created measurement: SignalID={signal_id}, Value={measurement.value}")
    
    initial_received = len(measurements_received)
    publisher.publish_measurements([measurement])
    
    # Wait for measurement to be received
    time.sleep(2)
    
    measurements_received_count = len(measurements_received) - initial_received
    print(f"   Measurements received: {measurements_received_count}")
    
    # Try manual subscription if auto didn't work
    if publisher.subscriber_count == 0:
        print("\n8. Trying Manual Subscription...")
        try:
            error = subscriber.subscribe()
            if error:
                print(f"   ❌ Manual subscription failed: {error}")
            else:
                print("   ✅ Manual subscription successful")
                time.sleep(2)
                
                # Try publishing again
                print("   Testing measurement publishing after manual subscription...")
                initial_received = len(measurements_received)
                publisher.publish_measurements([measurement])
                time.sleep(2)
                
                measurements_received_count = len(measurements_received) - initial_received
                print(f"   Measurements received after manual subscription: {measurements_received_count}")
                
        except Exception as e:
            print(f"   ❌ Manual subscription error: {e}")
    
    # Cleanup
    print("\n9. Cleaning up...")
    try:
        subscriber.dispose()
        publisher.dispose()
        print("   ✅ Cleanup completed")
    except Exception as e:
        print(f"   ⚠️  Cleanup error: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 DEBUG SUMMARY:")
    print(f"   Publisher subscriber count: {publisher.subscriber_count}")
    print(f"   Total measurements received: {len(measurements_received)}")
    if len(measurements_received) > 0:
        print("   ✅ Subscription and data transmission working!")
    else:
        print("   ❌ Subscription or data transmission not working")
    
    return len(measurements_received) > 0

if __name__ == "__main__":
    debug_subscription()
