#!/usr/bin/env python3

# ******************************************************************************************************
#  test_basic_integration.py - Gbtc
#
#  Copyright © 2022, Grid Protection Alliance.  All Rights Reserved.
#
#  Licensed to the Grid Protection Alliance (GPA) under one or more contributor license agreements. See
#  the NOTICE file distributed with this work for additional information regarding copyright ownership.
#  The GPA licenses this file to you under the MIT License (MIT), the "License"; you may not use this
#  file except in compliance with the License. You may obtain a copy of the License at:
#
#      http://opensource.org/licenses/MIT
#
#  Unless agreed to in writing, the subject software distributed under the License is distributed on an
#  "AS-IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. Refer to the
#  License for the specific language governing permissions and limitations.
#
#  Code Modification History:
#  ----------------------------------------------------------------------------------------------------
#  12/23/2024 - J. Ritchie Carroll
#       Generated original version of source code.
#
# ******************************************************************************************************

"""
Basic integration test for STTP Publisher functionality.

This test focuses on verifying that the publisher can:
1. Start and listen for connections
2. Accept subscriber connections
3. Handle basic protocol operations
"""

import sys
import time
import socket
import threading
from uuid import uuid4
import numpy as np

# Add the src directory to the path so we can import sttp
sys.path.insert(0, '../src')

from sttp import Publisher
from sttp.metadata.record.measurement import MeasurementRecord


def test_publisher_basic_functionality():
    """Test basic publisher functionality."""
    print("=== Basic Publisher Functionality Test ===")
    
    # Test 1: Publisher creation and startup
    print("1. Testing publisher creation and startup...")
    publisher = Publisher(port=np.uint16(7184))
    
    # Add some test metadata
    signal_id = uuid4()
    metadata = MeasurementRecord(
        signalid=signal_id,
        id=np.uint64(1),
        source="TEST",
        signaltypename="FREQ",
        pointtag="TEST.DEV1.FREQ",
        description="Test Frequency"
    )
    publisher.add_measurement_metadata(metadata)
    
    # Start publisher
    error = publisher.start()
    if error:
        print(f"   ❌ Failed to start publisher: {error}")
        return False
    
    print("   ✅ Publisher started successfully")
    
    # Test 2: Verify publisher is listening
    print("2. Testing that publisher is listening...")
    if not publisher.listening:
        print("   ❌ Publisher is not listening")
        publisher.dispose()
        return False
    
    print(f"   ✅ Publisher is listening on port {publisher.port}")
    
    # Test 3: Test socket connection
    print("3. Testing socket connection...")
    try:
        test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        test_socket.settimeout(2.0)
        result = test_socket.connect_ex(('localhost', int(publisher.port)))
        test_socket.close()
        
        if result == 0:
            print("   ✅ Socket connection successful")
        else:
            print(f"   ❌ Socket connection failed with code: {result}")
            publisher.dispose()
            return False
    except Exception as ex:
        print(f"   ❌ Socket connection test failed: {ex}")
        publisher.dispose()
        return False
    
    # Test 4: Test measurement publishing (basic)
    print("4. Testing measurement publishing...")
    try:
        measurement = publisher.create_measurement(signal_id, 60.0)
        publisher.publish_measurement(measurement)
        print("   ✅ Measurement publishing successful")
    except Exception as ex:
        print(f"   ❌ Measurement publishing failed: {ex}")
        publisher.dispose()
        return False
    
    # Test 5: Test publisher shutdown
    print("5. Testing publisher shutdown...")
    try:
        publisher.dispose()
        if not publisher.listening:
            print("   ✅ Publisher shutdown successful")
        else:
            print("   ❌ Publisher still listening after shutdown")
            return False
    except Exception as ex:
        print(f"   ❌ Publisher shutdown failed: {ex}")
        return False
    
    print("=== All Basic Tests Passed! ===\n")
    return True


def test_multiple_connections():
    """Test that publisher can handle multiple connection attempts."""
    print("=== Multiple Connection Test ===")
    
    publisher = Publisher(port=np.uint16(7185))
    
    # Add test metadata
    signal_id = uuid4()
    metadata = MeasurementRecord(
        signalid=signal_id,
        id=np.uint64(1),
        source="TEST",
        signaltypename="FREQ",
        pointtag="TEST.DEV1.FREQ",
        description="Test Frequency"
    )
    publisher.add_measurement_metadata(metadata)
    
    error = publisher.start()
    if error:
        print(f"   ❌ Failed to start publisher: {error}")
        return False
    
    print("1. Testing multiple simultaneous connections...")
    
    # Create multiple test connections
    sockets = []
    success_count = 0
    
    for i in range(3):
        try:
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            test_socket.settimeout(2.0)
            result = test_socket.connect_ex(('localhost', int(publisher.port)))
            
            if result == 0:
                sockets.append(test_socket)
                success_count += 1
                print(f"   Connection {i+1}: ✅ Success")
            else:
                print(f"   Connection {i+1}: ❌ Failed with code {result}")
                test_socket.close()
        except Exception as ex:
            print(f"   Connection {i+1}: ❌ Exception: {ex}")
    
    # Give publisher time to handle connections
    time.sleep(0.5)
    
    # Close all test sockets
    for sock in sockets:
        try:
            sock.close()
        except Exception:
            pass
    
    # Clean up
    publisher.dispose()
    
    if success_count > 0:
        print(f"   ✅ Successfully handled {success_count} connections")
        print("=== Multiple Connection Test Passed! ===\n")
        return True
    else:
        print("   ❌ No connections succeeded")
        return False


def test_publisher_with_callbacks():
    """Test publisher callback functionality."""
    print("=== Publisher Callback Test ===")
    
    status_messages = []
    error_messages = []
    connection_events = []
    
    publisher = Publisher(port=np.uint16(7186))
    
    # Set up callbacks
    publisher.set_statusmessage_logger(lambda msg: status_messages.append(msg))
    publisher.set_errormessage_logger(lambda msg: error_messages.append(msg))
    publisher.set_clientconnected_logger(lambda cid, addr: connection_events.append(f"Connected: {addr}"))
    publisher.set_clientdisconnected_logger(lambda cid, addr: connection_events.append(f"Disconnected: {addr}"))
    
    # Add test metadata
    signal_id = uuid4()
    metadata = MeasurementRecord(
        signalid=signal_id,
        id=np.uint64(1),
        source="TEST",
        signaltypename="FREQ",
        pointtag="TEST.DEV1.FREQ",
        description="Test Frequency"
    )
    publisher.add_measurement_metadata(metadata)
    
    # Start publisher
    error = publisher.start()
    if error:
        print(f"   ❌ Failed to start publisher: {error}")
        return False
    
    # Give time for status messages
    time.sleep(0.2)
    
    print("1. Checking status messages...")
    if len(status_messages) > 0:
        print(f"   ✅ Received {len(status_messages)} status messages")
        for msg in status_messages[:3]:  # Show first 3
            print(f"      - {msg}")
    else:
        print("   ⚠️  No status messages received")
    
    # Test connection event
    print("2. Testing connection event...")
    try:
        test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        test_socket.settimeout(2.0)
        test_socket.connect(('localhost', int(publisher.port)))
        
        # Give time for connection event
        time.sleep(0.2)
        test_socket.close()
        time.sleep(0.2)
        
        if len(connection_events) > 0:
            print(f"   ✅ Received {len(connection_events)} connection events")
            for event in connection_events:
                print(f"      - {event}")
        else:
            print("   ⚠️  No connection events received")
    except Exception as ex:
        print(f"   ❌ Connection test failed: {ex}")
    
    # Clean up
    publisher.dispose()
    
    print("=== Callback Test Completed ===\n")
    return True


def main():
    """Run all basic integration tests."""
    print("STTP Publisher Basic Integration Tests")
    print("=" * 50)
    
    tests = [
        test_publisher_basic_functionality,
        test_multiple_connections,
        test_publisher_with_callbacks
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("❌ Test failed\n")
        except Exception as ex:
            print(f"❌ Test failed with exception: {ex}\n")
    
    print("=" * 50)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All basic integration tests passed!")
        print("\nThe STTP Publisher implementation successfully:")
        print("  ✅ Starts and listens for connections")
        print("  ✅ Accepts multiple socket connections")
        print("  ✅ Handles callback events")
        print("  ✅ Publishes measurements without errors")
        print("  ✅ Shuts down cleanly")
        return True
    else:
        print("❌ Some tests failed")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
