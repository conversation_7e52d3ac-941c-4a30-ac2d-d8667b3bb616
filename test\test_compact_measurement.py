#!/usr/bin/env python3

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

import numpy as np
from uuid import uuid4
from sttp.transport.compactmeasurement import CompactMeasurement
from sttp.transport.signalindexcache import SignalIndexCache
from sttp.transport.constants import State<PERSON>lags

def test_compact_measurement():
    print("Testing CompactMeasurement creation...")
    
    try:
        # Create a signal index cache
        signal_index_cache = SignalIndexCache()
        print("✅ SignalIndexCache created")
        
        # Create test parameters
        signal_id = uuid4()
        value = np.float64(60.0)
        timestamp = np.uint64(1000000)
        flags = StateFlags.NORMAL
        
        print(f"Test parameters:")
        print(f"  SignalID: {signal_id}")
        print(f"  Value: {value}")
        print(f"  Timestamp: {timestamp}")
        print(f"  Flags: {flags}")
        
        # Try to create CompactMeasurement
        print("Creating CompactMeasurement...")
        compact = CompactMeasurement(
            signal_index_cache,
            True,  # includetime
            False, # usemillisecondresolution
            [np.int64(0)],    # basetimeoffsets
            signal_id,
            value,
            timestamp,
            flags
        )
        print("✅ CompactMeasurement created successfully")
        print(f"Binary length: {compact.binarylength}")
        
        # Try to set runtime ID
        print("Setting runtime ID...")
        compact.runtimeid = np.int32(0)
        print("✅ Runtime ID set successfully")
        
        # Try to encode
        print("Encoding CompactMeasurement...")
        buffer = bytearray(compact.binarylength)
        bytes_encoded, error = compact.encode(buffer)
        if error:
            print(f"❌ Encoding failed: {error}")
        else:
            print(f"✅ Encoded {bytes_encoded} bytes successfully")
            
    except Exception as ex:
        print(f"❌ Error: {ex}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_compact_measurement()
