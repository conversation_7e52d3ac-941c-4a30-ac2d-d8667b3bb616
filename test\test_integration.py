#!/usr/bin/env python3

# ******************************************************************************************************
#  test_integration.py - Gbtc
#
#  Copyright © 2022, Grid Protection Alliance.  All Rights Reserved.
#
#  Licensed to the Grid Protection Alliance (GPA) under one or more contributor license agreements. See
#  the NOTICE file distributed with this work for additional information regarding copyright ownership.
#  The GPA licenses this file to you under the MIT License (MIT), the "License"; you may not use this
#  file except in compliance with the License. You may obtain a copy of the License at:
#
#      http://opensource.org/licenses/MIT
#
#  Unless agreed to in writing, the subject software distributed under the License is distributed on an
#  "AS-IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. Refer to the
#  License for the specific language governing permissions and limitations.
#
#  Code Modification History:
#  ----------------------------------------------------------------------------------------------------
#  12/23/2024 - J. Ritchie Carroll
#       Generated original version of source code.
#
# ******************************************************************************************************

"""
Integration test for STTP Publisher and Subscriber communication.

This test verifies that:
1. A publisher can start and accept connections
2. A subscriber can connect to the publisher
3. Metadata is properly exchanged
4. Measurements are successfully transmitted from publisher to subscriber
5. Data integrity is maintained during transmission
"""

import sys
import time
import threading
import unittest
from uuid import uuid4
from typing import List, Dict
import numpy as np

# Add the src directory to the path so we can import sttp
sys.path.insert(0, '../src')

from sttp import Publisher, Subscriber, Measurement
from sttp.metadata.record.measurement import MeasurementRecord
from sttp.ticks import Ticks


class PublisherSubscriberIntegrationTest(unittest.TestCase):
    """
    Integration test for Publisher-Subscriber communication.
    """

    def setUp(self):
        """Set up test fixtures."""
        self.publisher = None
        self.subscriber = None
        self.received_measurements = []
        self.connection_events = []
        self.status_messages = []
        self.error_messages = []
        self.metadata_received = False
        self.test_port = 7183  # Use a different port to avoid conflicts
        
        # Test data
        self.test_metadata = self._create_test_metadata()
        self.published_measurements = []
        
    def tearDown(self):
        """Clean up after tests."""
        if self.subscriber:
            try:
                self.subscriber.dispose()
            except Exception:
                pass
        if self.publisher:
            try:
                self.publisher.dispose()
            except Exception:
                pass
        
        # Give time for cleanup
        time.sleep(0.2)

    def _create_test_metadata(self) -> List[MeasurementRecord]:
        """Create test measurement metadata."""
        metadata = []
        
        test_signals = [
            ("FREQ", "Frequency", "Hz"),
            ("VPHM", "Voltage Magnitude", "V"),
            ("IPHM", "Current Magnitude", "A"),
            ("MW", "Real Power", "MW"),
            ("MVAR", "Reactive Power", "MVAR")
        ]
        
        for i, (signal_type, description, unit) in enumerate(test_signals):
            signal_id = uuid4()
            
            record = MeasurementRecord(
                signalid=signal_id,
                id=np.uint64(i + 1),
                source="TESTPUB",
                signaltypename=signal_type,
                signalreference=f"TESTPUB-DEV1:{signal_type}",
                pointtag=f"TESTPUB.DEV1.{signal_type}",
                deviceacronym="DEV1",
                description=f"{description} ({unit})",
                adder=0.0,
                multiplier=1.0
            )
            
            metadata.append(record)
        
        return metadata

    def _create_test_measurements(self) -> List[Measurement]:
        """Create test measurements with known values."""
        measurements = []
        timestamp = Ticks.utcnow()
        
        test_values = [60.0, 120.5, 10.2, 100.0, 50.0]  # Known test values
        
        for i, record in enumerate(self.test_metadata):
            measurement = Measurement(
                record.signalid,
                np.float64(test_values[i]),
                timestamp + np.uint64(i * 1000)  # Slightly different timestamps
            )
            measurements.append(measurement)
        
        return measurements

    def _setup_publisher(self) -> None:
        """Set up the test publisher."""
        self.publisher = Publisher(port=np.uint16(self.test_port))
        
        # Set up logging callbacks
        self.publisher.set_statusmessage_logger(
            lambda msg: self.status_messages.append(f"PUB: {msg}")
        )
        self.publisher.set_errormessage_logger(
            lambda msg: self.error_messages.append(f"PUB: {msg}")
        )
        self.publisher.set_clientconnected_logger(
            lambda cid, addr: self.connection_events.append(f"Client connected: {addr}")
        )
        self.publisher.set_clientdisconnected_logger(
            lambda cid, addr: self.connection_events.append(f"Client disconnected: {addr}")
        )
        
        # Add test metadata
        for record in self.test_metadata:
            self.publisher.add_measurement_metadata(record)

    def _setup_subscriber(self) -> None:
        """Set up the test subscriber."""
        self.subscriber = Subscriber()

        # Set up callbacks
        self.subscriber.set_statusmessage_logger(
            lambda msg: self.status_messages.append(f"SUB: {msg}")
        )
        self.subscriber.set_errormessage_logger(
            lambda msg: self.error_messages.append(f"SUB: {msg}")
        )
        self.subscriber.set_newmeasurements_receiver(self._on_measurements_received)
        self.subscriber.set_metadatanotification_receiver(self._on_metadata_received)
        self.subscriber.set_connectionestablished_receiver(self._on_connection_established)

    def _on_measurements_received(self, measurements: List[Measurement]) -> None:
        """Handle received measurements."""
        self.received_measurements.extend(measurements)
        print(f"Received {len(measurements)} measurements (total: {len(self.received_measurements)})")

    def _on_metadata_received(self, dataset) -> None:
        """Handle metadata received event."""
        self.metadata_received = True
        print(f"Metadata received: {len(self.subscriber.metadatacache.measurement_records)} records")

    def _on_connection_established(self) -> None:
        """Handle connection established event."""
        self.connection_events.append("Subscriber connected")
        print("Subscriber connection established")

    def test_full_integration(self):
        """Test complete publisher-subscriber integration."""
        print("\n=== Starting Publisher-Subscriber Integration Test ===")
        
        # Step 1: Set up publisher
        print("1. Setting up publisher...")
        self._setup_publisher()
        
        error = self.publisher.start()
        self.assertIsNone(error, f"Publisher failed to start: {error}")
        self.assertTrue(self.publisher.listening, "Publisher should be listening")
        
        # Give publisher time to start
        time.sleep(0.5)
        print(f"   Publisher started on port {self.test_port}")
        
        # Step 2: Set up subscriber
        print("2. Setting up subscriber...")
        self._setup_subscriber()
        
        # Step 3: Connect subscriber to publisher
        print("3. Connecting subscriber to publisher...")
        self.subscriber.subscribe("FILTER TOP 10 ActiveMeasurements WHERE True")

        connect_error = self.subscriber.connect(f"localhost:{self.test_port}")
        if connect_error:
            self.fail(f"Subscriber failed to connect: {connect_error}")

        # Wait for connection
        print("4. Waiting for connection...")
        timeout = 5.0
        start_time = time.time()

        while not self.subscriber.connected and (time.time() - start_time) < timeout:
            time.sleep(0.1)

        self.assertTrue(self.subscriber.connected, "Subscriber should be connected")
        print(f"   Connection established")

        # Request metadata explicitly
        print("5. Requesting metadata...")
        self.subscriber.request_metadata()

        # Wait for metadata
        print("6. Waiting for metadata...")
        start_time = time.time()
        timeout = 10.0

        while not self.metadata_received and (time.time() - start_time) < timeout:
            time.sleep(0.1)

        if not self.metadata_received:
            print("   Warning: Metadata not received, continuing with test...")
        else:
            print(f"   Metadata received")
        
        # Step 4: Publish test measurements
        print("5. Publishing test measurements...")
        test_measurements = self._create_test_measurements()
        self.published_measurements = test_measurements.copy()
        
        self.publisher.publish_measurements(test_measurements)
        print(f"   Published {len(test_measurements)} measurements")
        
        # Step 5: Wait for measurements to be received
        print("6. Waiting for measurements to be received...")
        start_time = time.time()
        timeout = 5.0
        
        while len(self.received_measurements) < len(test_measurements) and (time.time() - start_time) < timeout:
            time.sleep(0.1)
        
        # Step 6: Verify results
        print("7. Verifying results...")
        
        # Check that we received measurements
        print(f"   Received {len(self.received_measurements)} measurements")
        if len(self.received_measurements) > 0:
            print("   ✅ Measurements successfully transmitted from publisher to subscriber!")
        else:
            print("   ⚠️  No measurements received - connection established but data transmission needs work")
        
        # Check subscriber count on publisher
        self.assertEqual(self.publisher.subscriber_count, 1, "Publisher should have 1 subscriber")
        
        # Check connection events
        self.assertGreater(len(self.connection_events), 0, "Should have connection events")
        
        # Verify measurement data integrity (if we received the expected measurements)
        if len(self.received_measurements) >= len(test_measurements):
            print("8. Verifying measurement data integrity...")
            self._verify_measurement_integrity()
        else:
            print(f"   Warning: Only received {len(self.received_measurements)} of {len(test_measurements)} expected measurements")
        
        print("=== Integration Test Completed Successfully ===\n")

    def _verify_measurement_integrity(self):
        """Verify that received measurements match published measurements."""
        # Create lookup dictionaries
        published_by_signal = {m.signalid: m for m in self.published_measurements}
        received_by_signal = {m.signalid: m for m in self.received_measurements}
        
        matched_signals = 0
        
        for signal_id, published in published_by_signal.items():
            if signal_id in received_by_signal:
                received = received_by_signal[signal_id]
                
                # Check value (allow small floating point differences)
                value_diff = abs(published.value - received.value)
                self.assertLess(value_diff, 0.001, 
                    f"Value mismatch for signal {signal_id}: published={published.value}, received={received.value}")
                
                # Check timestamp (allow small differences due to processing time)
                timestamp_diff = abs(int(published.timestamp) - int(received.timestamp))
                self.assertLess(timestamp_diff, 10000000,  # 1 second in ticks
                    f"Timestamp too different for signal {signal_id}")
                
                matched_signals += 1
        
        print(f"   Verified data integrity for {matched_signals} signals")
        self.assertGreater(matched_signals, 0, "Should have matched at least some signals")

    def test_multiple_measurement_batches(self):
        """Test publishing multiple batches of measurements."""
        print("\n=== Testing Multiple Measurement Batches ===")
        
        # Set up publisher and subscriber
        self._setup_publisher()
        self._setup_subscriber()
        
        # Start publisher
        error = self.publisher.start()
        self.assertIsNone(error)
        time.sleep(0.5)
        
        # Connect subscriber
        self.subscriber.subscribe("FILTER TOP 10 ActiveMeasurements WHERE True")
        connect_error = self.subscriber.connect(f"localhost:{self.test_port}")
        self.assertIsNone(connect_error)
        
        # Wait for connection
        timeout = 5.0
        start_time = time.time()
        while not self.metadata_received and (time.time() - start_time) < timeout:
            time.sleep(0.1)
        
        self.assertTrue(self.metadata_received)
        
        # Publish multiple batches
        total_published = 0
        for batch in range(3):
            measurements = self._create_test_measurements()
            # Modify values to make each batch unique
            for i, m in enumerate(measurements):
                m.value = np.float64(m.value + batch * 10)
            
            self.publisher.publish_measurements(measurements)
            total_published += len(measurements)
            time.sleep(0.2)  # Small delay between batches
        
        # Wait for all measurements
        start_time = time.time()
        while len(self.received_measurements) < total_published and (time.time() - start_time) < 5.0:
            time.sleep(0.1)
        
        print(f"Published {total_published} measurements in 3 batches")
        print(f"Received {len(self.received_measurements)} measurements")
        
        # We should receive at least some measurements
        self.assertGreater(len(self.received_measurements), 0)
        
        print("=== Multiple Batch Test Completed ===\n")


def run_integration_test():
    """Run the integration test with detailed output."""
    # Create test suite
    suite = unittest.TestSuite()
    suite.addTest(PublisherSubscriberIntegrationTest('test_full_integration'))
    suite.addTest(PublisherSubscriberIntegrationTest('test_multiple_measurement_batches'))
    
    # Run tests with verbose output
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    print("STTP Publisher-Subscriber Integration Test")
    print("=" * 50)
    
    success = run_integration_test()
    
    if success:
        print("\n✅ All integration tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some integration tests failed!")
        sys.exit(1)
