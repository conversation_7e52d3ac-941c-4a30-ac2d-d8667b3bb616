#!/usr/bin/env python3

# ******************************************************************************************************
#  test_publisher.py - Gbtc
#
#  Copyright © 2022, Grid Protection Alliance.  All Rights Reserved.
#
#  Licensed to the Grid Protection Alliance (GPA) under one or more contributor license agreements. See
#  the NOTICE file distributed with this work for additional information regarding copyright ownership.
#  The GPA licenses this file to you under the MIT License (MIT), the "License"; you may not use this
#  file except in compliance with the License. You may obtain a copy of the License at:
#
#      http://opensource.org/licenses/MIT
#
#  Unless agreed to in writing, the subject software distributed under the License is distributed on an
#  "AS-IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. Refer to the
#  License for the specific language governing permissions and limitations.
#
#  Code Modification History:
#  ----------------------------------------------------------------------------------------------------
#  12/23/2024 - J. Ritchie Carroll
#       Generated original version of source code.
#
# ******************************************************************************************************

import unittest
import sys
import time
import threading
from uuid import uuid4
from typing import List

# Add the src directory to the path so we can import sttp
sys.path.insert(0, '../src')

from sttp import Publisher, Subscriber, Measurement
from sttp.metadata.record.measurement import MeasurementRecord
from sttp.ticks import Ticks
import numpy as np


class TestPublisher(unittest.TestCase):
    """
    Test cases for the STTP Publisher functionality.
    """

    def setUp(self):
        """Set up test fixtures."""
        self.publisher = None
        self.subscriber = None
        self.received_measurements = []
        self.connection_events = []

    def tearDown(self):
        """Clean up after tests."""
        if self.subscriber:
            self.subscriber.dispose()
        if self.publisher:
            self.publisher.dispose()

    def test_publisher_creation(self):
        """Test that a publisher can be created successfully."""
        publisher = Publisher(port=np.uint16(7176))
        self.assertIsNotNone(publisher)
        self.assertEqual(publisher.port, 7176)
        self.assertFalse(publisher.listening)
        self.assertEqual(publisher.subscriber_count, 0)
        publisher.dispose()

    def test_publisher_start_stop(self):
        """Test that a publisher can start and stop successfully."""
        publisher = Publisher(port=np.uint16(7177))
        
        # Test starting
        error = publisher.start()
        self.assertIsNone(error)
        self.assertTrue(publisher.listening)
        
        # Test stopping
        publisher.stop()
        self.assertFalse(publisher.listening)
        
        publisher.dispose()

    def test_metadata_addition(self):
        """Test adding measurement metadata to the publisher."""
        publisher = Publisher(port=np.uint16(7178))
        
        # Create sample metadata
        signal_id = uuid4()
        metadata = MeasurementRecord(
            signalid=signal_id,
            id=np.uint64(1),
            source="TEST",
            signaltypename="FREQ",
            pointtag="TEST.DEV1.FREQ",
            description="Test Frequency"
        )
        
        # Add metadata (should not raise exception)
        publisher.add_measurement_metadata(metadata)
        
        publisher.dispose()

    def test_measurement_creation(self):
        """Test creating measurements using the publisher helper method."""
        publisher = Publisher(port=np.uint16(7179))
        
        signal_id = uuid4()
        measurement = publisher.create_measurement(signal_id, 60.0)
        
        self.assertEqual(measurement.signalid, signal_id)
        self.assertEqual(measurement.value, 60.0)
        self.assertGreater(measurement.timestamp, 0)
        
        publisher.dispose()

    def test_callback_setup(self):
        """Test that callbacks can be set up correctly."""
        publisher = Publisher(port=np.uint16(7180))
        
        status_messages = []
        error_messages = []
        connection_events = []
        
        publisher.set_statusmessage_logger(lambda msg: status_messages.append(msg))
        publisher.set_errormessage_logger(lambda msg: error_messages.append(msg))
        publisher.set_clientconnected_logger(lambda cid, addr: connection_events.append(('connected', cid, addr)))
        publisher.set_clientdisconnected_logger(lambda cid, addr: connection_events.append(('disconnected', cid, addr)))
        
        # Start publisher to trigger status message
        error = publisher.start()
        self.assertIsNone(error)
        
        # Give it a moment to start
        time.sleep(0.1)
        
        # Should have received at least one status message
        self.assertGreater(len(status_messages), 0)
        
        publisher.dispose()

    def test_measurement_publishing(self):
        """Test that measurements can be published without errors."""
        publisher = Publisher(port=np.uint16(7181))
        
        # Add some metadata
        signal_id = uuid4()
        metadata = MeasurementRecord(
            signalid=signal_id,
            id=np.uint64(1),
            source="TEST",
            signaltypename="FREQ",
            pointtag="TEST.DEV1.FREQ",
            description="Test Frequency"
        )
        publisher.add_measurement_metadata(metadata)
        
        # Start publisher
        error = publisher.start()
        self.assertIsNone(error)
        
        # Create and publish measurements
        measurements = [
            publisher.create_measurement(signal_id, 60.0),
            publisher.create_measurement(signal_id, 60.1),
            publisher.create_measurement(signal_id, 59.9)
        ]
        
        # Publishing should not raise exceptions
        publisher.publish_measurements(measurements)
        publisher.publish_measurement(measurements[0])
        
        publisher.dispose()

    def test_port_validation(self):
        """Test that invalid ports are handled correctly."""
        # Test with port 0 (should use default)
        publisher = Publisher(port=np.uint16(0))
        self.assertEqual(publisher.port, 0)  # Should accept 0 as specified
        publisher.dispose()

    def create_test_metadata(self) -> List[MeasurementRecord]:
        """Create test metadata for integration tests."""
        metadata = []
        signal_types = ["FREQ", "VPHM", "IPHM"]
        
        for i, signal_type in enumerate(signal_types):
            signal_id = uuid4()
            record = MeasurementRecord(
                signalid=signal_id,
                id=np.uint64(i + 1),
                source="TEST",
                signaltypename=signal_type,
                pointtag=f"TEST.DEV1.{signal_type}",
                description=f"Test {signal_type}"
            )
            metadata.append(record)
        
        return metadata

    def measurement_received_callback(self, measurements: List[Measurement]):
        """Callback for received measurements in integration tests."""
        self.received_measurements.extend(measurements)

    def connection_callback(self):
        """Callback for connection events in integration tests."""
        self.connection_events.append('connected')

    def test_publisher_subscriber_integration(self):
        """Integration test with publisher and subscriber."""
        # This test requires both publisher and subscriber to work
        # Skip if we can't import subscriber properly
        try:
            from sttp import Subscriber
        except ImportError:
            self.skipTest("Subscriber not available for integration test")
        
        # Use different ports to avoid conflicts
        pub_port = 7182
        
        # Create and start publisher
        self.publisher = Publisher(port=np.uint16(pub_port))
        
        # Add test metadata
        metadata = self.create_test_metadata()
        for record in metadata:
            self.publisher.add_measurement_metadata(record)
        
        # Start publisher
        error = self.publisher.start()
        self.assertIsNone(error)
        
        # Give publisher time to start
        time.sleep(0.2)
        
        # Create subscriber
        self.subscriber = Subscriber()
        self.subscriber.set_newmeasurements_receiver(self.measurement_received_callback)
        self.subscriber.set_connectionestablished_receiver(self.connection_callback)
        
        # Connect subscriber
        self.subscriber.subscribe("FILTER TOP 5 ActiveMeasurements WHERE True")
        connect_error = self.subscriber.connect(f"localhost:{pub_port}")
        
        if connect_error:
            self.skipTest(f"Could not connect subscriber: {connect_error}")
        
        # Give time for connection
        time.sleep(0.5)
        
        # Publish some measurements
        measurements = []
        for record in metadata:
            measurement = self.publisher.create_measurement(record.signalid, 100.0 + len(measurements))
            measurements.append(measurement)
        
        self.publisher.publish_measurements(measurements)
        
        # Give time for measurements to be received
        time.sleep(0.5)
        
        # Verify connection was established
        self.assertGreater(len(self.connection_events), 0)
        
        # Note: Actual measurement reception depends on full STTP protocol implementation
        # This test mainly verifies that the publisher can start and accept connections


if __name__ == '__main__':
    unittest.main()
